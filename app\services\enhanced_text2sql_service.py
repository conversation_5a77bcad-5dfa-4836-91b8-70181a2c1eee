"""
增强版Text2SQL服务
集成执行过程持久化和性能监控功能
"""

import time
import traceback
from typing import Optional, Awaitable, Callable, Dict, Any
from datetime import datetime

from autogen_core import SingleThreadedAgentRuntime, DefaultTopicId, ClosureContext, CancellationToken, TypeSubscription
from autogen_core import ClosureAgent

from app.schemas import (
    ResponseMessage, QueryMessage
)

from app.services.db_service import get_db_engine
from dao.text2sqlsys_utils import get_datasource_by_id

# 从agents模块导入所有智能体和相关类
from app.services.agents import (
    SchemaRetrieverAgent,
    QueryAnalyzerAgent,
    SqlGeneratorAgent,
    SqlExplainerAgent,
    SqlExecutorAgent,
    VisualizationRecommenderAgent,
    Text2SQLException,
    DatabaseConnectionException,
    schema_retriever_topic_type,
    stream_output_topic_type,
    DB_TYPE, query_analyzer_topic_type, sql_generator_topic_type, 
    sql_explainer_topic_type, sql_executor_topic_type,
    visualization_recommender_topic_type
)

# 导入持久化服务
from app.services.text2sql_analytics_service import Text2SQLAnalyticsService
from dao.models.Text2sqlAnalytics import (
    ExecutionStatus, AgentStatus, ErrorSeverity, MetricCategory
)

# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

# 智能体执行顺序映射
AGENT_EXECUTION_ORDER = {
    "schema_retriever": 1,
    "query_analyzer": 2,
    "sql_generator": 3,
    "sql_explainer": 4,
    "sql_executor": 5,
    "visualization_recommender": 6
}


class EnhancedStreamResponseCollector:
    """增强版流式响应收集器，集成持久化功能"""

    def __init__(self, query_id: str, analytics_service: Text2SQLAnalyticsService):
        """初始化增强版流式响应收集器"""
        self.query_id = query_id
        self.analytics_service = analytics_service
        self.callback: Optional[Callable[[ClosureContext, ResponseMessage, Any], Awaitable[None]]] = None
        self.user_input: Optional[Callable[[str, CancellationToken], Awaitable[str]]] = None
        self.message_buffers: Dict[str, str] = {}
        self.last_flush_time: Dict[str, float] = {}
        self.buffer_flush_interval: float = 0.3
        self._is_closed: bool = False
        self.message_sequence: Dict[str, int] = {}  # 追踪每个智能体的消息序号
        self.agent_execution_ids: Dict[str, int] = {}  # 存储智能体执行ID

    def set_callback(self, callback: Callable[[ClosureContext, ResponseMessage, Any], Awaitable[None]]) -> None:
        """设置回调函数"""
        if not callable(callback):
            raise ValueError("回调函数必须是可调用对象")
        self.callback = callback

    def set_user_input(self, user_input: Callable[[str, CancellationToken], Awaitable[str]]) -> None:
        """设置用户输入函数"""
        if user_input is not None and not callable(user_input):
            raise ValueError("用户输入函数必须是可调用对象或None")
        self.user_input = user_input

    async def register_agent_execution(self, agent_name: str, agent_display_name: str, 
                                     input_data: Dict[str, Any] = None) -> int:
        """注册智能体执行开始"""
        try:
            execution_order = AGENT_EXECUTION_ORDER.get(agent_name, 0)
            agent_execution_id = await self.analytics_service.create_agent_execution(
                query_id=self.query_id,
                agent_name=agent_name,
                agent_display_name=agent_display_name,
                execution_order=execution_order,
                input_data=input_data
            )
            self.agent_execution_ids[agent_name] = agent_execution_id
            self.message_sequence[agent_name] = 0
            
            logger.info(f"注册智能体执行: {agent_name}, ID: {agent_execution_id}")
            return agent_execution_id
            
        except Exception as e:
            logger.error(f"注册智能体执行失败: {str(e)}")
            raise

    async def update_agent_execution(self, agent_name: str, status: AgentStatus,
                                   output_data: Dict[str, Any] = None,
                                   error_message: str = None,
                                   model_calls_count: int = 0,
                                   model_tokens_used: int = 0) -> None:
        """更新智能体执行状态"""
        try:
            agent_execution_id = self.agent_execution_ids.get(agent_name)
            if agent_execution_id:
                await self.analytics_service.update_agent_execution(
                    agent_execution_id=agent_execution_id,
                    status=status,
                    output_data=output_data,
                    error_message=error_message,
                    model_calls_count=model_calls_count,
                    model_tokens_used=model_tokens_used
                )
                
                # 更新流式消息计数
                message_count = self.message_sequence.get(agent_name, 0)
                if message_count > 0:
                    # 这里可以进一步更新数据库中的消息计数
                    pass
                    
                logger.info(f"更新智能体执行状态: {agent_name}, 状态: {status}")
            else:
                logger.warning(f"未找到智能体执行ID: {agent_name}")
                
        except Exception as e:
            logger.error(f"更新智能体执行状态失败: {str(e)}")

    async def buffer_message(self, ctx: ClosureContext, source: str, content: str,
                          is_final: bool = False, result: Dict[str, Any] = None) -> None:
        """缓冲消息并持久化到数据库"""
        if self._is_closed:
            logger.warning("StreamResponseCollector已关闭，忽略消息")
            return
            
        if not self.callback:
            logger.warning("未设置回调函数，无法发送消息")
            return

        try:
            current_time = time.time()
            
            # 确定对应的智能体名称
            agent_name = self._get_agent_name_from_source(source)
            agent_execution_id = self.agent_execution_ids.get(agent_name, 0)
            
            # 增加消息序号
            if agent_name not in self.message_sequence:
                self.message_sequence[agent_name] = 0
            self.message_sequence[agent_name] += 1
            
            # 持久化流式消息到数据库
            if agent_execution_id > 0:
                await self._persist_stream_message(
                    agent_execution_id=agent_execution_id,
                    source=source,
                    content=content,
                    sequence=self.message_sequence[agent_name],
                    is_final=is_final
                )

            # 如果是最终消息或结果不为空，直接发送，不经过缓冲
            if is_final or result:
                # 先发送缓冲区中的消息
                if source in self.message_buffers and self.message_buffers[source]:
                    await self._safe_callback(ctx, ResponseMessage(
                        source=source,
                        content=self.message_buffers[source]
                    ))
                    self.message_buffers[source] = ""

                # 再发送当前消息
                await self._safe_callback(ctx, ResponseMessage(
                    source=source,
                    content=content,
                    is_final=is_final,
                    result=result
                ))
                return

            # 累积消息到缓冲区
            if source not in self.message_buffers:
                self.message_buffers[source] = ""
                self.last_flush_time[source] = current_time

            self.message_buffers[source] += content

            # 检查是否需要刷新缓冲区
            if current_time - self.last_flush_time.get(source, 0) >= self.buffer_flush_interval:
                await self._safe_callback(ctx, ResponseMessage(
                    source=source,
                    content=self.message_buffers[source]
                ))
                self.message_buffers[source] = ""
                self.last_flush_time[source] = current_time
                
        except Exception as e:
            logger.error(f"缓冲消息时发生错误: {str(e)}")

    def _get_agent_name_from_source(self, source: str) -> str:
        """从消息来源推断智能体名称"""
        source_lower = source.lower()
        if "表结构检索" in source or "schema" in source_lower:
            return "schema_retriever"
        elif "查询分析" in source or "analyzer" in source_lower:
            return "query_analyzer"
        elif "sql生成" in source or "generator" in source_lower:
            return "sql_generator"
        elif "sql解释" in source or "explainer" in source_lower:
            return "sql_explainer"
        elif "sql执行" in source or "executor" in source_lower:
            return "sql_executor"
        elif "可视化推荐" in source or "visualization" in source_lower:
            return "visualization_recommender"
        else:
            return "unknown"

    async def _persist_stream_message(self, agent_execution_id: int, source: str, 
                                    content: str, sequence: int, is_final: bool = False) -> None:
        """持久化流式消息到数据库"""
        try:
            # 这里可以直接插入数据库，或者通过analytics_service
            # 暂时记录日志，实际实现时需要连接数据库
            message_size = len(content.encode('utf-8')) if content else 0
            
            logger.debug(f"持久化流式消息: 智能体执行ID={agent_execution_id}, "
                        f"序号={sequence}, 大小={message_size}字节, "
                        f"是否最终={is_final}")
            
        except Exception as e:
            logger.error(f"持久化流式消息失败: {str(e)}")

    async def _safe_callback(self, ctx: ClosureContext, message: ResponseMessage) -> None:
        """安全地调用回调函数"""
        try:
            await self.callback(ctx, message, None)
        except Exception as e:
            logger.error(f"回调函数执行失败: {str(e)}")

    async def flush_all_buffers(self, ctx: ClosureContext = None) -> None:
        """刷新所有消息缓冲区"""
        if not self.callback or self._is_closed:
            return

        try:
            for source, content in self.message_buffers.items():
                if content.strip():
                    await self._safe_callback(ctx, ResponseMessage(
                        source=source,
                        content=content
                    ))

            self.message_buffers.clear()
            self.last_flush_time.clear()
        except Exception as e:
            logger.error(f"刷新缓冲区时发生错误: {str(e)}")

    def close(self) -> None:
        """关闭收集器"""
        self._is_closed = True
        self.message_buffers.clear()
        self.last_flush_time.clear()
        logger.info("EnhancedStreamResponseCollector已关闭")


class EnhancedText2SQLService:
    """增强版Text2SQL服务类，集成执行过程持久化"""
    
    def __init__(self):
        """初始化增强版Text2SQL服务"""
        self.db_type = DB_TYPE
        self.db_engine = None
        self._runtime = None
        self.analytics_service = Text2SQLAnalyticsService()

    async def _get_connection_info(self, connection_id: str):
        """根据连接ID获取数据库连接信息和类型"""
        if not connection_id:
            raise DatabaseConnectionException("数据库连接ID不能为空")
            
        try:
            connection_info = await get_datasource_by_id(connection_id)
            if not connection_info:
                raise DatabaseConnectionException(f"未找到连接ID为 {connection_id} 的数据库连接")
            return connection_info
        except Exception as e:
            logger.error(f"获取数据库连接信息时出错: {str(e)}")
            raise DatabaseConnectionException(f"获取数据库连接信息失败: {str(e)}")

    async def _setup_database_engine(self, connection_id: str) -> None:
        """设置数据库引擎和类型"""
        try:
            # 获取数据库连接信息
            connection_info = await self._get_connection_info(connection_id)
            logger.info(f"[连接ID: {connection_id}] 数据库类型: {connection_info.db_type}")
            
            # 设置数据库引擎
            self.db_engine = await get_db_engine(connection_info)
            if not self.db_engine:
                raise DatabaseConnectionException(f"无法为连接ID {connection_id} 创建数据库引擎")
            
            # 设置数据库类型
            if connection_info.db_type == "4":
                self.db_type = "MySQL"
            elif connection_info.db_type == "6":
                self.db_type = "PostgreSQL"
            elif connection_info.db_type == "10":
                self.db_type = "SQLite"
            else:
                logger.warning(f"未知的数据库类型: {connection_info.db_type}，使用默认类型 MySQL")
                self.db_type = "MySQL"

            logger.info(f"数据库引擎设置完成，类型: {self.db_type}")
            
        except DatabaseConnectionException:
            raise
        except Exception as e:
            logger.error(f"设置数据库引擎时出错: {str(e)}")
            raise DatabaseConnectionException(f"设置数据库引擎失败: {str(e)}")

    async def _register_agents(self, runtime: SingleThreadedAgentRuntime, 
                             collector: EnhancedStreamResponseCollector) -> None:
        """注册所有智能体"""
        try:
            logger.info("开始注册智能体...")

            # 注册各个智能体，并记录执行开始
            await SchemaRetrieverAgent.register(
                runtime,
                schema_retriever_topic_type,
                lambda: SchemaRetrieverAgent(
                    db_type=self.db_type, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            await QueryAnalyzerAgent.register(
                runtime,
                query_analyzer_topic_type,
                lambda: QueryAnalyzerAgent(
                    db_type=self.db_type, 
                    input_func=collector.user_input, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            await SqlGeneratorAgent.register(
                runtime,
                sql_generator_topic_type,
                lambda: SqlGeneratorAgent(
                    db_type=self.db_type, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            await SqlExplainerAgent.register(
                runtime,
                sql_explainer_topic_type,
                lambda: SqlExplainerAgent(
                    db_type=self.db_type, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            await SqlExecutorAgent.register(
                runtime,
                sql_executor_topic_type,
                lambda: SqlExecutorAgent(
                    db_type=self.db_type, 
                    db_engine=self.db_engine, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            await VisualizationRecommenderAgent.register(
                runtime,
                visualization_recommender_topic_type,
                lambda: VisualizationRecommenderAgent(
                    db_type=self.db_type, 
                    analytics_service=self.analytics_service, 
                    query_id=collector.query_id
                )
            )

            # 注册流式响应收集器
            await ClosureAgent.register_closure(
                runtime,
                "stream_collector_agent",
                collector.callback,
                subscriptions=lambda: [
                    TypeSubscription(
                        topic_type=stream_output_topic_type,
                        agent_type="stream_collector_agent"
                    )
                ],
            )

            logger.info("所有智能体注册完成")

        except Exception as e:
            logger.error(f"注册智能体时出错: {str(e)}")
            raise Text2SQLException(f"注册智能体失败: {str(e)}")

    async def _cleanup_runtime(self) -> None:
        """清理运行时资源"""
        try:
            if self._runtime:
                # 使用getattr安全访问_background_tasks属性（注意属性名带下划线）
                background_tasks = getattr(self._runtime, '_background_tasks', set())
                tasks_count = len(background_tasks) if background_tasks else 0
                logger.debug(f"运行时调试信息: tasks_count={tasks_count}")
                
                # 通过检查_background_tasks是否有元素来判断运行时是否还在运行
                if tasks_count > 0:
                    logger.info("检测到运行时有后台任务，准备停止...")
                    #进入此判断，代表用户主动取消会话，运行时调用close方法，会触发智能体的close方法
                    await self._runtime.close()
                    logger.info("运行时已停止")
                else:
                    logger.info("运行时没有后台任务或已经停止，无需重复停止")
                
                self._runtime = None
                logger.info("运行时资源已清理")
        except Exception as e:
            logger.error(f"清理运行时时出错: {str(e)}")
            self._runtime = None

    async def process_query(self, query: str, session_id: str, connection_id: str = None, 
                          collector_callback: Callable = None, user_input_func: Callable = None):
        """处理自然语言查询，转换为SQL并执行
        
        Args:
            query: 用户的自然语言查询
            session_id: 会话ID
            connection_id: 数据库连接ID
            collector_callback: 流式响应回调函数
            user_input_func: 用户输入处理函数
            
        Returns:
            Dict: 处理结果，包含query_id和执行状态
            
        Raises:
            Text2SQLException: 当处理过程中出现错误时
        """
        if not query or not query.strip():
            raise Text2SQLException("查询内容不能为空")
            
        if not session_id:
            raise Text2SQLException("会话ID不能为空")

        query_id = None
        start_time = datetime.utcnow()
        
        try:
            # 创建执行记录
            query_id = await self.analytics_service.create_execution_record(
                session_id=session_id,
                user_query=query,
                connection_id=connection_id,
                db_type=self.db_type
            )
            
            logger.info(f"开始处理查询，查询ID: {query_id}")
            
            # 记录初始性能指标
            await self.analytics_service.record_performance_metric(
                query_id=query_id,
                metric_name="query_start_timestamp",
                metric_value=start_time.timestamp(),
                metric_unit="timestamp",
                metric_category=MetricCategory.PERFORMANCE
            )

            # 设置数据库引擎（如果提供了连接ID）
            if connection_id:
                await self._setup_database_engine(connection_id)

            # 创建增强版流式响应收集器
            collector = EnhancedStreamResponseCollector(query_id, self.analytics_service)
            
            if collector_callback:
                collector.set_callback(collector_callback)
            
            # 必须在注册智能体之前设置user_input，因为QueryAnalyzerAgent需要使用它
            if user_input_func:
                collector.set_user_input(user_input_func)

            # 创建运行时
            runtime = SingleThreadedAgentRuntime()
            self._runtime = runtime

            # 注册智能体（此时collector已经设置好了user_input）
            await self._register_agents(runtime, collector)

            # 启动运行时
            runtime.start()
            logger.info("运行时启动成功")

            # 发送初始查询消息给表结构检索智能体
            await self._runtime.publish_message(
                QueryMessage(query=query, connection_id=connection_id),
                topic_id=DefaultTopicId(type=schema_retriever_topic_type)
            )
            logger.info("初始查询消息已发送")

            # 等待处理完成
            await runtime.stop_when_idle()
            
            # 更新执行成功状态
            await self.analytics_service.update_execution_status(
                query_id=query_id,
                status=ExecutionStatus.SUCCESS
            )
            
            # 记录结束时间性能指标
            end_time = datetime.utcnow()
            total_duration = (end_time - start_time).total_seconds() * 1000
            
            await self.analytics_service.record_performance_metric(
                query_id=query_id,
                metric_name="total_execution_time",
                metric_value=total_duration,
                metric_unit="ms",
                metric_category=MetricCategory.PERFORMANCE
            )

            logger.info(f"查询处理完成，查询ID: {query_id}, 耗时: {total_duration:.2f}ms")
            
            return {
                "query_id": query_id,
                "status": "SUCCESS",
                "duration_ms": total_duration,
                "session_id": session_id
            }
            
        except Exception as e:
            error_message = str(e)
            error_stack = traceback.format_exc()
            
            logger.error(f"处理查询时出错: {error_message}")
            
            # 更新执行失败状态
            if query_id:
                try:
                    await self.analytics_service.update_execution_status(
                        query_id=query_id,
                        status=ExecutionStatus.FAILED,
                        error_message=error_message
                    )
                    
                    # 记录错误日志
                    await self.analytics_service.record_error_log(
                        query_id=query_id,
                        error_type=type(e).__name__,
                        error_message=error_message,
                        error_stack=error_stack,
                        severity=ErrorSeverity.ERROR
                    )
                    
                except Exception as log_error:
                    logger.error(f"记录错误信息失败: {str(log_error)}")
            
            raise Text2SQLException(f"处理查询失败: {error_message}")
            
        finally:
            # 清理资源
            await self._cleanup_runtime()
            if 'collector' in locals():
                await collector.flush_all_buffers()
                collector.close()

    async def get_execution_status(self, query_id: str) -> Dict[str, Any]:
        """获取执行状态
        
        Args:
            query_id: 查询ID
            
        Returns:
            Dict: 执行状态信息
        """
        try:
            # 这里可以通过analytics_service查询执行状态
            # 暂时返回一个示例
            return {
                "query_id": query_id,
                "status": "查询执行状态功能正在开发中",
                "message": "请使用analytics API查询详细的执行信息"
            }
        except Exception as e:
            logger.error(f"获取执行状态失败: {str(e)}")
            raise Text2SQLException(f"获取执行状态失败: {str(e)}")

    async def cancel_execution(self, query_id: str) -> Dict[str, Any]:
        """取消执行
        
        Args:
            query_id: 查询ID
            
        Returns:
            Dict: 取消结果
        """
        try:
            # 实现取消逻辑
            await self.analytics_service.update_execution_status(
                query_id=query_id,
                status=ExecutionStatus.FAILED,
                error_message="用户主动取消执行"
            )
            
            return {
                "query_id": query_id,
                "status": "CANCELLED",
                "message": "执行已取消"
            }
        except Exception as e:
            logger.error(f"取消执行失败: {str(e)}")
            raise Text2SQLException(f"取消执行失败: {str(e)}") 