"""
缓存管理服务层
提供缓存预热、状态监控、手动管理等业务逻辑
"""
import asyncio
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from app.core.cache_warmup import run_cache_warmup, should_run_warmup, CacheWarmupManager, CacheWarmupTask
from app.core.cache import StatisticsCache, get_cache_status, clear_all_statistics_cache
from config.cache_config import redis_config
from utils.logger import get_logger

logger = get_logger()


class CacheManagementService:
    """缓存管理服务"""
    
    def __init__(self):
        # 全局预热状态跟踪
        self.warmup_status = {
            "is_running": False,
            "last_run": None,
            "last_result": None
        }
    
    async def get_cache_management_status(self) -> Dict[str, Any]:
        """
        获取缓存管理状态
        """
        try:
            # 获取Redis和缓存状态
            cache_status = get_cache_status()
            
            # 获取配置信息
            config_info = {
                "cache_enabled": redis_config.is_cache_enabled(),
                "redis_host": redis_config.REDIS_HOST,
                "redis_port": redis_config.REDIS_PORT,
                "redis_db": redis_config.REDIS_DB,
                "cache_times": {
                    "overview": redis_config.STATISTICS_OVERVIEW_CACHE_TIME,
                    "trend": redis_config.STATISTICS_TREND_CACHE_TIME,
                    "distribution": redis_config.STATISTICS_DISTRIBUTION_CACHE_TIME,
                    "detailed": redis_config.STATISTICS_DETAILED_CACHE_TIME,
                    "chart": redis_config.STATISTICS_CHART_CACHE_TIME,
                }
            }
            
            return {
                "redis_status": cache_status,
                "warmup_status": self.warmup_status,
                "config": config_info
            }
            
        except Exception as e:
            logger.error(f"获取缓存状态失败: {e}")
            raise e
    
    async def trigger_cache_warmup(
        self, 
        include_database_stats: bool = True,
        include_knowledge_stats: bool = True,
        max_concurrent: int = 3,
        background_task_func: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        手动触发缓存预热
        """
        if self.warmup_status["is_running"]:
            return {
                "status": "already_running",
                "message": "缓存预热任务正在运行中，请等待完成后再试"
            }
        
        if not should_run_warmup():
            return {
                "status": "skipped",
                "message": "缓存未启用或Redis连接不可用，无法执行预热"
            }
        
        # 生成任务ID
        task_id = f"warmup_{int(datetime.now().timestamp())}"
        
        # 标记为运行中
        self.warmup_status["is_running"] = True
        self.warmup_status["last_run"] = datetime.now().isoformat()
        
        logger.info(f"手动触发缓存预热任务: {task_id}")
        
        async def warmup_task():
            """后台预热任务"""
            try:
                manager = CacheWarmupManager()
                
                # 根据请求添加对应的预热任务
                if include_database_stats:
                    manager.add_database_stats_tasks()
                    logger.info("已添加数据库统计预热任务")
                
                if include_knowledge_stats:
                    manager.add_knowledge_stats_tasks()
                    logger.info("已添加知识统计预热任务")
                
                # 执行预热
                result = await manager.run_warmup(max_concurrent=max_concurrent)
                
                # 更新状态
                self.warmup_status["last_result"] = result
                self.warmup_status["is_running"] = False
                
                logger.info(f"手动缓存预热任务完成: {task_id}, 结果: {result}")
                
            except Exception as e:
                logger.error(f"手动缓存预热任务失败: {task_id}, 错误: {e}")
                self.warmup_status["last_result"] = {
                    "status": "error",
                    "error": str(e),
                    "completed_at": datetime.now().isoformat()
                }
                self.warmup_status["is_running"] = False
        
        # 如果提供了后台任务函数，使用它；否则直接执行
        if background_task_func:
            background_task_func(warmup_task)
        else:
            # 创建任务但不等待
            asyncio.create_task(warmup_task())
        
        return {
            "status": "started",
            "message": "缓存预热任务已启动，正在后台执行",
            "task_id": task_id
        }
    
    async def get_warmup_status(self) -> Dict[str, Any]:
        """
        获取预热任务状态
        """
        return {
            "warmup_status": self.warmup_status,
            "can_start_new": not self.warmup_status["is_running"] and should_run_warmup()
        }
    
    async def quick_cache_warmup(self, background_task_func: Optional[Callable] = None) -> Dict[str, Any]:
        """
        快速缓存预热（仅预热高优先级数据）
        """
        if self.warmup_status["is_running"]:
            raise Exception("预热任务正在运行中")
        
        if not should_run_warmup():
            raise Exception("缓存未启用或Redis连接不可用")
        
        task_id = f"quick_warmup_{int(datetime.now().timestamp())}"
        self.warmup_status["is_running"] = True
        self.warmup_status["last_run"] = datetime.now().isoformat()
        
        async def quick_warmup_task():
            """快速预热任务 - 仅处理高优先级数据"""
            try:
                manager = CacheWarmupManager()
                
                # 仅添加高优先级任务 (priority = 1)
                # 数据库仪表板概览
                manager.add_task(CacheWarmupTask(
                    name="数据库仪表板概览",
                    endpoint="get_dashboard_overview",
                    cache_type="overview",
                    data_func=manager._get_dashboard_overview_data,
                    priority=1
                ))
                
                # 知识统计概览
                manager.add_task(CacheWarmupTask(
                    name="知识统计概览",
                    endpoint="get_statistics_overview",
                    cache_type="overview",
                    data_func=manager._get_statistics_overview_data,
                    priority=1
                ))
                
                # 知识统计周趋势
                manager.add_task(CacheWarmupTask(
                    name="知识统计周趋势",
                    endpoint="get_weekly_trend",
                    cache_type="trend",
                    data_func=manager._get_weekly_trend_data,
                    priority=1
                ))
                
                # 执行预热（更高并发）
                result = await manager.run_warmup(max_concurrent=5)
                
                self.warmup_status["last_result"] = result
                self.warmup_status["is_running"] = False
                
                logger.info(f"快速缓存预热完成: {task_id}, 结果: {result}")
                
            except Exception as e:
                logger.error(f"快速缓存预热失败: {task_id}, 错误: {e}")
                self.warmup_status["last_result"] = {
                    "status": "error",
                    "error": str(e),
                    "completed_at": datetime.now().isoformat()
                }
                self.warmup_status["is_running"] = False
        
        # 如果提供了后台任务函数，使用它；否则直接执行
        if background_task_func:
            background_task_func(quick_warmup_task)
        else:
            # 创建任务但不等待
            asyncio.create_task(quick_warmup_task())
        
        return {
            "status": "started",
            "message": "快速缓存预热已启动",
            "task_id": task_id,
            "note": "仅预热最重要的概览数据"
        }
    
    async def clear_all_cache(self) -> Dict[str, Any]:
        """
        清除所有统计缓存
        """
        try:
            cleared_count = clear_all_statistics_cache()
            
            logger.info(f"手动清除所有统计缓存，共清除 {cleared_count} 个键")
            
            return {
                "status": "success",
                "message": f"成功清除 {cleared_count} 个缓存键",
                "cleared_count": cleared_count,
                "cleared_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            raise e
    
    async def clear_cache_by_pattern(self, pattern: str) -> Dict[str, Any]:
        """
        按模式清除缓存
        """
        try:
            cache = StatisticsCache()
            cleared_count = cache.clear_pattern(pattern)
            
            logger.info(f"手动清除模式缓存: {pattern}，共清除 {cleared_count} 个键")
            
            return {
                "status": "success",
                "message": f"成功清除模式 '{pattern}' 的 {cleared_count} 个缓存键",
                "pattern": pattern,
                "cleared_count": cleared_count,
                "cleared_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"清除模式缓存失败: {e}")
            raise e
    
    async def list_cache_keys(self, pattern: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
        """
        列出缓存键
        """
        try:
            cache = StatisticsCache()
            
            if not cache.redis_client.is_connected():
                raise Exception("Redis连接不可用")
            
            # 构建搜索模式
            search_pattern = f"{cache.cache_prefix}*"
            if pattern:
                search_pattern = f"{cache.cache_prefix}{pattern}"
            
            # 获取键列表
            keys = cache.redis_client.client.keys(search_pattern)
            
            # 限制返回数量
            limited_keys = keys[:limit]
            
            # 获取键的详细信息
            key_info = []
            for key in limited_keys:
                try:
                    ttl = cache.redis_client.client.ttl(key)
                    key_type = cache.redis_client.client.type(key)
                    
                    key_info.append({
                        "key": key,
                        "type": key_type,
                        "ttl": ttl if ttl > 0 else None,
                        "expired": ttl == -2,
                        "persistent": ttl == -1
                    })
                except Exception as e:
                    key_info.append({
                        "key": key,
                        "error": str(e)
                    })
            
            return {
                "total_matched": len(keys),
                "returned_count": len(limited_keys),
                "pattern": search_pattern,
                "keys": key_info
            }
            
        except Exception as e:
            logger.error(f"列出缓存键失败: {e}")
            raise e
    
    async def get_cache_performance(self) -> Dict[str, Any]:
        """
        获取缓存性能统计
        """
        try:
            cache = StatisticsCache()
            
            if not cache.redis_client.is_connected():
                raise Exception("Redis连接不可用")
            
            # 获取Redis信息
            redis_info = cache.redis_client.client.info()
            
            # 计算缓存相关统计
            stats_keys = cache.redis_client.client.keys(f"{cache.cache_prefix}*")
            
            performance_data = {
                "redis_performance": {
                    "memory_used": redis_info.get("used_memory_human"),
                    "memory_peak": redis_info.get("used_memory_peak_human"),
                    "hits": redis_info.get("keyspace_hits", 0),
                    "misses": redis_info.get("keyspace_misses", 0),
                    "hit_rate": 0.0,
                    "total_connections": redis_info.get("total_connections_received", 0),
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "uptime_days": round(redis_info.get("uptime_in_seconds", 0) / 86400, 1)
                },
                "cache_statistics": {
                    "total_cache_keys": len(stats_keys),
                    "cache_prefix": cache.cache_prefix,
                    "cache_enabled": redis_config.is_cache_enabled()
                },
                "collected_at": datetime.now().isoformat()
            }
            
            # 计算命中率
            total_requests = performance_data["redis_performance"]["hits"] + performance_data["redis_performance"]["misses"]
            if total_requests > 0:
                hit_rate = (performance_data["redis_performance"]["hits"] / total_requests) * 100
                performance_data["redis_performance"]["hit_rate"] = round(hit_rate, 2)
            
            return performance_data
            
        except Exception as e:
            logger.error(f"获取缓存性能统计失败: {e}")
            raise e
    
    async def test_cache_operations(self) -> Dict[str, Any]:
        """
        测试缓存操作（开发调试用）
        """
        try:
            cache = StatisticsCache()
            
            if not cache.redis_client.is_connected():
                raise Exception("Redis连接不可用")
            
            test_key = "test_cache_operation"
            test_data = {
                "test": True,
                "timestamp": datetime.now().isoformat(),
                "message": "这是一个测试缓存数据"
            }
            
            # 测试设置缓存
            set_result = cache.set(test_key, test_data, expire_seconds=60)
            
            # 测试获取缓存
            get_result = cache.get(test_key)
            
            # 测试删除缓存
            delete_result = cache.delete(test_key)
            
            return {
                "test_results": {
                    "set_cache": set_result,
                    "get_cache": get_result is not None,
                    "cache_data_match": get_result == {"data": test_data, "cached_at": get_result.get("cached_at"), "expire_at": get_result.get("expire_at")} if get_result else False,
                    "delete_cache": delete_result
                },
                "redis_connection": cache.redis_client.is_connected(),
                "test_completed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"缓存操作测试失败: {e}")
            raise e
    
    async def cache_health_check(self) -> Dict[str, Any]:
        """
        缓存系统健康检查
        """
        try:
            cache = StatisticsCache()
            health_status = {
                "redis_connected": cache.redis_client.is_connected(),
                "cache_enabled": redis_config.is_cache_enabled(),
                "warmup_available": should_run_warmup(),
                "last_warmup": self.warmup_status.get("last_run"),
                "warmup_running": self.warmup_status.get("is_running", False),
                "check_time": datetime.now().isoformat()
            }
            
            # 简单的ping测试
            if health_status["redis_connected"]:
                try:
                    ping_result = cache.redis_client.client.ping()
                    health_status["ping_test"] = ping_result
                except Exception as ping_error:
                    health_status["ping_test"] = False
                    health_status["ping_error"] = str(ping_error)
            
            overall_healthy = all([
                health_status["redis_connected"],
                health_status["cache_enabled"],
                health_status.get("ping_test", False)
            ])
            
            health_status["overall_status"] = "healthy" if overall_healthy else "unhealthy"
            
            return health_status
            
        except Exception as e:
            logger.error(f"缓存健康检查失败: {e}")
            return {
                "overall_status": "error",
                "error": str(e),
                "check_time": datetime.now().isoformat()
            }


# 创建全局服务实例
cache_management_service = CacheManagementService() 