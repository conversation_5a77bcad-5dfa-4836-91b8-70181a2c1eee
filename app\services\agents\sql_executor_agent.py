import time
from typing import List, Dict, Any
from autogen_core import message_handler, TopicId, MessageContext, type_subscription

from .base_agent import (
    BaseAgent, SQLExecutionException, AGENT_NAMES,
    sql_executor_topic_type, visualization_recommender_topic_type
)
from app.schemas import SqlExplanationMessage, SqlResultMessage

# 获取logger实例
from utils.logger import get_logger
logger = get_logger()


@type_subscription(topic_type=sql_executor_topic_type)
class SqlExecutorAgent(BaseAgent):
    """SQL执行智能体，负责模拟执行SQL并返回结果"""
    
    def __init__(self, db_type=None, db_engine=None, analytics_service=None, query_id: str = None):
        super().__init__("sql_executor_agent", db_type, analytics_service, query_id)
        self.db_engine = db_engine

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["sql_executor"]

    def _clean_sql_for_execution(self, sql: str) -> str:
        """清理SQL语句以便执行
        
        Args:
            sql: 原始SQL语句
            
        Returns:
            str: 清理后的SQL语句
        """
        if not sql:
            return ""
            
        # 移除代码块标记
        cleaned_sql = sql.replace("```sql", "").replace("```", "")
        
        # 移除多余的空白字符
        cleaned_sql = cleaned_sql.strip()
        
        return cleaned_sql

    async def _execute_sql(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询
        
        Args:
            sql: SQL语句
            
        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        if not self.db_engine:
            raise SQLExecutionException("数据库引擎未初始化")
            
        if not sql or not sql.strip():
            raise SQLExecutionException("SQL语句不能为空")

        cleaned_sql = self._clean_sql_for_execution(sql)
        
        try:
            import pandas as pd
            import sqlalchemy
            
            # 使用异步SQLAlchemy Engine执行查询
            async with self.db_engine.begin() as conn:
                result = await conn.execute(sqlalchemy.text(cleaned_sql))
                columns = result.keys()
                data = [dict(zip(columns, row)) for row in result.fetchall()]
                
                # 转换为pandas DataFrame以保持与原有代码的兼容性
                results_df = pd.DataFrame(data)
                return results_df.to_dict("records")
                
        except Exception as e:
            logger.error(f"SQL执行失败: {str(e)}")
            raise SQLExecutionException(f"SQL执行失败: {str(e)}")

    @message_handler
    async def handle_message(self, message: SqlExplanationMessage, ctx: MessageContext) -> None:
        """处理SQL解释消息并执行SQL"""
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "sql": message.sql,
                "explanation": getattr(message, 'explanation', "")
            })
            
            # 验证消息
            if not self.validate_message(message, ['query', 'sql']):
                error = SQLExecutionException("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            await self.send_stream_message("正在执行SQL查询...\n\n", message_sequence=1)
            
            try:
                results = await self._execute_sql(message.sql)
                
                # 记录查询结果到数据库
                if self.analytics_service and self.query_id:
                    await self.analytics_service.record_query_result(
                        query_id=self.query_id,
                        sql_statement=message.sql,
                        execution_status="SUCCESS",
                        results_count=len(results),
                        results_size_kb=len(str(results).encode('utf-8')) / 1024,
                        execution_time_ms=int((time.time() - self.execution_start_time) * 1000) if self.execution_start_time else 0,
                        columns_info=[list(results[0].keys()) if results else []],
                        sample_data=results[:5] if results else []  # 保存前5行作为样本
                    )
                    
            except SQLExecutionException as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                
                # 记录失败的查询结果
                if self.analytics_service and self.query_id:
                    await self.analytics_service.record_query_result(
                        query_id=self.query_id,
                        sql_statement=message.sql,
                        execution_status="FAILED",
                        error_message=str(e),
                        execution_time_ms=int((time.time() - self.execution_start_time) * 1000) if self.execution_start_time else 0
                    )
                    
                await self.handle_exception(e, "SQL执行")
                return
            except Exception as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                
                # 记录失败的查询结果
                if self.analytics_service and self.query_id:
                    await self.analytics_service.record_query_result(
                        query_id=self.query_id,
                        sql_statement=message.sql,
                        execution_status="FAILED",
                        error_message=str(e),
                        execution_time_ms=int((time.time() - self.execution_start_time) * 1000) if self.execution_start_time else 0
                    )
                    
                await self.handle_exception(e, "SQL执行")
                return
            
            # 发送执行完成的消息
            await self.send_stream_message(f"SQL执行完成，获取到{len(results)}条结果\n\n", message_sequence=2)

            # 添加2秒延迟
            import asyncio
            await asyncio.sleep(2)

            # 发送数据结果，这样前端会显示数据表格
            await self.send_stream_message(
                "查询结果数据",
                is_final=True,
                result={"results": results},
                message_sequence=3
            )

            # 完成执行，记录成功状态
            output_data = {
                "results_count": len(results),
                "results_size_kb": len(str(results).encode('utf-8')) / 1024,
                "execution_completed": True
            }
            await self.finish_execution("SUCCESS", output_data=output_data)

            # 继续正常的智能体流程，将结果传递给可视化推荐智能体
            await self.publish_message(
                SqlResultMessage(
                    query=message.query,
                    sql=message.sql,
                    explanation=getattr(message, 'explanation', ""),
                    results=results
                ),
                topic_id=TopicId(type=visualization_recommender_topic_type, source=self.id.key)
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理SQL解释消息") 