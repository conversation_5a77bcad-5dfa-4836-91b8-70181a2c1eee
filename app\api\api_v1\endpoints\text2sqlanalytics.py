"""
Text2SQL性能分析API路由
"""

from datetime import datetime, date
from typing import List, Dict, Optional
from fastapi import APIRouter, HTTPException, Depends, Query as QueryParam

from dao.models.Text2sqlAnalytics import (
    PerformanceMetrics, AgentPerformanceMetrics, ExecutionSummary,
    PerformanceOverview, Text2SQLAnalytics,
    ExecutionStatus
)
from app.services.text2sql_analytics_service import Text2SQLAnalyticsService
from utils.logger import get_logger

logger = get_logger()

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)

# 依赖注入
def get_analytics_service() -> Text2SQLAnalyticsService:
    """获取分析服务实例"""
    return Text2SQLAnalyticsService()


@router.get("/overview", response_model=PerformanceMetrics, summary="获取性能概览")
async def get_performance_overview(
    days: int = QueryParam(default=7, ge=1, le=365, description="统计天数，默认7天"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取Text2SQL服务的性能概览数据
    
    - **days**: 统计的天数范围
    - 返回: 性能指标概览，包括执行成功率、平均耗时等
    """
    try:
        logger.info(f"获取性能概览，统计天数: {days}")
        overview = await analytics_service.get_performance_overview(days)
        return overview
    except Exception as e:
        logger.error(f"获取性能概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能概览失败: {str(e)}")


@router.get("/agents/performance", response_model=List[AgentPerformanceMetrics], summary="获取智能体性能统计")
async def get_agent_performance(
    days: int = QueryParam(default=7, ge=1, le=365, description="统计天数，默认7天"), 
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取各个智能体的性能统计数据
    
    - **days**: 统计的天数范围
    - 返回: 每个智能体的执行统计，包括成功率、平均耗时、LLM调用次数等
    """
    try:
        logger.info(f"获取智能体性能统计，统计天数: {days}")
        performance = await analytics_service.get_agent_performance(days)
        return performance
    except Exception as e:
        logger.error(f"获取智能体性能统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取智能体性能统计失败: {str(e)}")


@router.get("/daily", response_model=List[PerformanceOverview], summary="获取每日性能统计")
async def get_daily_performance(
    days: int = QueryParam(default=30, ge=1, le=365, description="统计天数，默认30天"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取每日的性能统计数据，用于趋势分析
    
    - **days**: 统计的天数范围
    - 返回: 每日的执行统计数据，可用于绘制趋势图表
    """
    try:
        logger.info(f"获取每日性能统计，统计天数: {days}")
        daily_stats = await analytics_service.get_daily_performance(days)
        return daily_stats
    except Exception as e:
        logger.error(f"获取每日性能统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取每日性能统计失败: {str(e)}")


@router.get("/executions/recent", response_model=List[ExecutionSummary], summary="获取最近执行记录")
async def get_recent_executions(
    limit: int = QueryParam(default=20, ge=1, le=100, description="返回数量限制，默认20条"),
    status: Optional[ExecutionStatus] = QueryParam(default=None, description="过滤执行状态"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取最近的执行记录
    
    - **limit**: 返回的记录数量
    - **status**: 可选的状态过滤
    - 返回: 最近的执行记录摘要列表
    """
    try:
        logger.info(f"获取最近执行记录，数量: {limit}, 状态过滤: {status}")
        executions = await analytics_service.get_recent_executions(limit)
        
        # 如果指定了状态过滤
        if status:
            executions = [exec for exec in executions if exec.execution_status == status]
        
        return executions
    except Exception as e:
        logger.error(f"获取最近执行记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最近执行记录失败: {str(e)}")


@router.get("/errors/summary", response_model=Dict[str, int], summary="获取错误统计摘要")
async def get_error_summary(
    days: int = QueryParam(default=7, ge=1, le=365, description="统计天数，默认7天"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取错误类型的统计摘要
    
    - **days**: 统计的天数范围  
    - 返回: 各种错误类型及其出现次数的统计
    """
    try:
        logger.info(f"获取错误统计摘要，统计天数: {days}")
        error_summary = await analytics_service.get_error_summary(days)
        return error_summary
    except Exception as e:
        logger.error(f"获取错误统计摘要失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错误统计摘要失败: {str(e)}")


@router.get("/comprehensive", response_model=Text2SQLAnalytics, summary="获取综合分析数据")
async def get_comprehensive_analytics(
    days: int = QueryParam(default=7, ge=1, le=365, description="统计天数，默认7天"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取综合的分析数据，包含所有统计信息
    
    - **days**: 统计的天数范围
    - 返回: 包含性能概览、每日统计、智能体性能、最近执行记录和错误摘要的综合数据
    """
    try:
        logger.info(f"获取综合分析数据，统计天数: {days}")
        analytics = await analytics_service.get_comprehensive_analytics(days)
        return analytics
    except Exception as e:
        logger.error(f"获取综合分析数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取综合分析数据失败: {str(e)}")


@router.get("/metrics/custom", summary="获取自定义指标")
async def get_custom_metrics(
    metric_names: List[str] = QueryParam(..., description="指标名称列表"),
    query_ids: Optional[List[str]] = QueryParam(default=None, description="可选的查询ID列表"),
    start_date: Optional[date] = QueryParam(default=None, description="开始日期"),
    end_date: Optional[date] = QueryParam(default=None, description="结束日期"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取自定义的性能指标数据
    
    - **metric_names**: 要查询的指标名称列表
    - **query_ids**: 可选的查询ID过滤
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - 返回: 自定义指标的数据
    """
    try:
        logger.info(f"获取自定义指标: {metric_names}")
        # 这里可以实现更复杂的自定义指标查询逻辑
        # 暂时返回一个示例响应
        return {
            "metric_names": metric_names,
            "query_ids": query_ids,
            "date_range": {
                "start_date": start_date,
                "end_date": end_date
            },
            "message": "自定义指标查询功能正在开发中"
        }
    except Exception as e:
        logger.error(f"获取自定义指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取自定义指标失败: {str(e)}")


@router.get("/health", summary="健康检查")
async def health_check():
    """
    分析服务健康检查接口
    
    - 返回: 服务状态信息
    """
    try:
        return {
            "status": "healthy",
            "service": "Text2SQL Analytics Service", 
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


# 实时指标接口（可用于监控面板）
@router.get("/realtime/stats", summary="获取实时统计")
async def get_realtime_stats(
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    获取实时的统计数据，用于监控面板
    
    - 返回: 实时的关键指标统计
    """
    try:
        # 获取最近1小时的数据
        recent_overview = await analytics_service.get_performance_overview(days=1)
        recent_executions = await analytics_service.get_recent_executions(limit=5)
        
        # 计算实时指标
        current_time = datetime.utcnow()
        
        # 统计最近10分钟的执行情况
        recent_10min = [
            exec for exec in recent_executions 
            if exec.start_time and (current_time - exec.start_time).total_seconds() <= 600
        ]
        
        running_count = len([exec for exec in recent_10min if exec.execution_status == ExecutionStatus.RUNNING])
        success_count = len([exec for exec in recent_10min if exec.execution_status == ExecutionStatus.SUCCESS])
        failed_count = len([exec for exec in recent_10min if exec.execution_status == ExecutionStatus.FAILED])
        
        return {
            "timestamp": current_time.isoformat(),
            "recent_10min": {
                "total_executions": len(recent_10min),
                "running": running_count,
                "success": success_count,
                "failed": failed_count
            },
            "today_overview": {
                "total_executions": recent_overview.total_executions,
                "success_rate": recent_overview.success_rate,
                "avg_duration_ms": recent_overview.avg_duration_ms
            },
            "latest_executions": [
                {
                    "query_id": exec.query_id,
                    "status": exec.execution_status,
                    "duration_ms": exec.total_duration_ms,
                    "start_time": exec.start_time.isoformat() if exec.start_time else None
                }
                for exec in recent_executions[:3]
            ]
        }
    except Exception as e:
        logger.error(f"获取实时统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取实时统计失败: {str(e)}")


# 导出数据接口
@router.get("/export/csv", summary="导出CSV数据")
async def export_csv_data(
    data_type: str = QueryParam(..., description="数据类型: executions, agents, errors"),
    days: int = QueryParam(default=30, ge=1, le=365, description="统计天数，默认30天"),
    analytics_service: Text2SQLAnalyticsService = Depends(get_analytics_service)
):
    """
    导出统计数据为CSV格式
    
    - **data_type**: 要导出的数据类型
    - **days**: 统计的天数范围
    - 返回: CSV格式的数据文件
    """
    try:
        logger.info(f"导出CSV数据，类型: {data_type}, 天数: {days}")
        
        if data_type == "executions":
            executions = await analytics_service.get_recent_executions(limit=1000)
            # 这里可以使用pandas或csv模块生成CSV数据
            return {"message": f"执行记录CSV导出功能正在开发中，共{len(executions)}条记录"}
        elif data_type == "agents":
            agents = await analytics_service.get_agent_performance(days)
            return {"message": f"智能体性能CSV导出功能正在开发中，共{len(agents)}个智能体"}
        elif data_type == "errors":
            errors = await analytics_service.get_error_summary(days)
            return {"message": f"错误统计CSV导出功能正在开发中，共{len(errors)}种错误类型"}
        else:
            raise HTTPException(status_code=400, detail="不支持的数据类型")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出CSV数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出CSV数据失败: {str(e)}") 