"""
# pip install llama-index-embeddings-huggingface
# pip install llama-index-embeddings-instructor
# pip install llama-index-embeddings-ollama
# pip install pymilvus[model]
"""
from typing import Dict

import torch
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai import OpenAI as LLamaIndexOpenAI
from pymilvus import model
from config.config import RAGConfig

from llama_index.llms.openai.utils import ALL_AVAILABLE_MODELS, CHAT_MODELS
# from pandasai.llm import OpenAI as PandasAI
from openai import OpenAI
# from pandasai.schemas.df_config import Config

configuration = RAGConfig()
DEEPSEEK_MODELS: Dict[str, int] = {
    "deepseek-chat": 128000,
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B":128000,
    "qwen3-30b-int4": 128000,
    "qwen3-14b-int4": 128000,
    "qwen3-4b-int4": 128000,
    "Qwen/Qwen3-14B": 128000,
}
ALL_AVAILABLE_MODELS.update(DEEPSEEK_MODELS)
CHAT_MODELS.update(DEEPSEEK_MODELS)

from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.openai._model_info import _MODEL_INFO, _MODEL_TOKEN_LIMITS

DEEPSEEK_MODELS: Dict[str, ModelInfo]  = {
    "deepseek-chat": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
    },
}
DEEPSEEK_TOKEN_LIMITS: Dict[str, int] = {
    "deepseek-chat": 128000,
}
_MODEL_INFO.update(DEEPSEEK_MODELS)
_MODEL_TOKEN_LIMITS.update(DEEPSEEK_TOKEN_LIMITS)

# -------------------------LLM Settings-------------------------

#-----------------------------------rag系统问答----------------------------------------------
def deepseek_llm(**kwargs):
    return LLamaIndexOpenAI(model=configuration.llm_model_name, api_key=configuration.llm_api_key, api_base=configuration.llm_api_base, **kwargs)
#硅基流动
def siliconflow_llm(**kwargs):
    return LLamaIndexOpenAI(model=configuration.sf_llm_model_name, api_key=configuration.sf_llm_api_key, api_base=configuration.sf_llm_api_base, **kwargs)
def moonshot_llm(**kwargs):
    return OpenAI(api_key=configuration.moonshot_api_key, base_url=configuration.moonshot_api_base, **kwargs)
#视觉大模型
def vllm(**kwargs):
    return OpenAI(api_key=configuration.vllm_api_key, base_url=configuration.vllm_base_url, **kwargs)
#-----------------------------------text2ql系统问答----------------------------------------------
def openai_llm(**kwargs):
    return OpenAI(api_key=configuration.llm_api_key, base_url=configuration.llm_api_base, **kwargs)

def openai_llm_sf(**kwargs):
    return OpenAI(api_key=configuration.sf_llm_api_key, base_url=configuration.sf_llm_api_base, **kwargs)

def autogen_llm(**kwargs):
    return OpenAIChatCompletionClient(
            model=configuration.llm_model_name,
            base_url=configuration.llm_api_base,
            api_key=configuration.llm_api_key,
        )

# def pandasai_config():
#     return Config(llm=pandasai_llm(), custom_whitelisted_dependencies=["timeit"], open_charts=False, save_charts=False, verbose=True)

# def ollama_deepseek_llm(**kwargs):
#     llm = Ollama(base_url="http://***************:11434", model="deepseekq8:32b", request_timeout=60.0)
#     return llm
#
# from llama_index.multi_modal_llms.ollama import OllamaMultiModal
# def ollama_qwenvl_llm(**kwargs):
#     llm = OllamaMultiModal(base_url="http://***************:11434", model="bsahane/Qwen2.5-VL-7B-Instruct:Q4_K_M_benxh", request_timeout=60.0)
#     return llm

# def pandasai_llm(**kwargs):
#     return PandasAI(model=configuration.llm_model_name, api_token=configuration.llm_api_key, api_base=configuration.llm_api_base, **kwargs)

# ------------------------Embedding Settings------------------------

def vanna_milvus_bge_embedding_function(**kwargs):
    return model.dense.SentenceTransformerEmbeddingFunction(
        # model_name="BAAI/bge-small-zh-v1.5",
        model_name=configuration.model_path_inuse,
        # model_name="maidalun1020/bce-embedding-base_v1",
        # cache_folder=configuration.model_cache_folder,
        device="cpu", # Specify the device to use, e.g., 'cpu' or 'cuda:0'
    )

def rag_rerank(**kwargs):
    return SentenceTransformerRerank(
        model="E:/aimodesl/BAAI--bge-reranker-v2-m3",
        device="cpu"
    )

# 嵌入模型单例缓存
_embedding_model_cache = {}

# 本地模型
def local_bge_small_embed_model(**kwargs):
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    
    # 创建缓存键
    cache_key = "bge-small-zh-v1.5"
    
    if cache_key not in _embedding_model_cache:
        embed_model = HuggingFaceEmbedding(model_name="E:/aimodesl/BAAI--bge-small-zh-v1.5",
                                           device="cuda:0",
                                           **kwargs)
        _embedding_model_cache[cache_key] = embed_model
    
    return _embedding_model_cache[cache_key]

def local_bge_large_embed_model(**kwargs):
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    
    # 创建缓存键
    cache_key = "bge-large-zh-v1.5"
    
    if cache_key not in _embedding_model_cache:
        embed_model = HuggingFaceEmbedding(model_name=configuration.model_path_inuse,
                                           # cache_folder=configuration.model_cache_folder,
                                           device="cuda:0",
                                           **kwargs)
        _embedding_model_cache[cache_key] = embed_model
    
    return _embedding_model_cache[cache_key]

def local_qwen_embed_model(**kwargs):
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    from transformers import BitsAndBytesConfig
    
    # 创建缓存键
    cache_key = "Qwen3-Embedding-0.6B"
    
    if cache_key not in _embedding_model_cache:
        # 8位量化配置
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_skip_modules=["lm_head"],
        )
        
        embed_model = HuggingFaceEmbedding(
            model_name=configuration.model_path_qwen,
            trust_remote_code=True,
            max_length=4096,  # 控制序列长度的调优设置点
            device="cuda:0",
            model_kwargs={
                "device_map": "auto",
                "quantization_config": quantization_config,
                "torch_dtype": torch.float16,
                "max_memory": {0: configuration.model_max_memory_gpu, "cpu": configuration.model_max_memory_cpu}
            },
            tokenizer_kwargs={
                "padding_side": configuration.tokenizer_padding_side,
            },
            **kwargs
        )
        _embedding_model_cache[cache_key] = embed_model
    
    return _embedding_model_cache[cache_key]

def local_bce_base_embed_model(**kwargs):
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    
    # 创建缓存键
    cache_key = "bce-embedding-base_v1"
    
    if cache_key not in _embedding_model_cache:
        embed_model = HuggingFaceEmbedding(model_name="maidalun1020/bce-embedding-base_v1",
                                           cache_folder=configuration.model_cache_folder,
                                           **kwargs)
        _embedding_model_cache[cache_key] = embed_model
    
    return _embedding_model_cache[cache_key]
# #model scope本地模型
# def local_nlp_embed_model(**kwargs):
#     from llama_index.embeddings.modelscope.base import ModelScopeEmbedding
#     embed_model = ModelScopeEmbedding(model_name="iic/nlp_gte_sentence-embedding_chinese-base",
#                                       model_revision="master",
#                                        **kwargs)
#     return embed_model
#
#
# # 在线模型
# def ollama_nomic_embed_model(**kwargs):
#     from llama_index.embeddings.ollama import OllamaEmbedding
#
#     ollama_embedding = OllamaEmbedding(
#         model_name="nomic-embed-text:latest",
#         base_url="http://***********:11434",
#     )
#     return ollama_embedding
# # 移动云在线模型
# def ollama_embed_model_bce(**kwargs):
#     from llama_index.embeddings.ollama import OllamaEmbedding
#
#     ollama_embedding = OllamaEmbedding(
#         # model_name="lrs33/bce-embedding-base_v1:latest",
#         # base_url="http://***************:11434",
#         model_name="lrs33/bce-embedding-base_v1:latest",
#         # base_url="http://localhost:11434",
#     )
#     return ollama_embedding
# def pymilvus_bge_m3_embedding_function(**kwargs):
#     from pymilvus.model.hybrid import BGEM3EmbeddingFunction
#
#     bge_m3_ef = BGEM3EmbeddingFunction(
#         model_name='BAAI/bge-m3',  # Specify the model name
#         device='cpu',  # Specify the device to use, e.g., 'cpu' or 'cuda:0'
#         use_fp16=False  # Specify whether to use fp16. Set to `False` if `device` is `cpu`.
#     )
#     return bge_m3_ef

# --------------------------Test Login User Data--------------------------
#
#
# import pandas as pd
#
# # 读取CSV文件
# df = pd.read_csv('database/users.csv')
# user_data = df.values.tolist()
