import asyncio
import os

from datasets.search import BaseIndex
from llama_index.core import  Document, StorageContext, VectorStoreIndex
from llama_index.core.async_utils import run_jobs
from llama_index.vector_stores.milvus import MilvusVectorStore
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered
from tempfile import NamedTemporaryFile

from rag.experience.markdown_splitter import MarkdownTextSplitter
from config import settings
from utils.CustomizedEmbeddingFunction import CustomizedEmbeddingFunction
from utils.file_types import is_supported_extension, get_processing_method, FileProcessingMethod
from rag.base_rag import RAG
from utils.rag_utils import describe_image
from persistent.minio_storage_client import get_minio_client
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

class UniversalRAG(RAG):

    async def load_data(self)-> list[Document]:
        documents = []
        tasks = []
        for file_path in self.files:
            file_name = os.path.basename(file_path)
            file_extension = os.path.splitext(file_name.lower())[1]
            # 检查文件类型是否支持
            file_extension = file_extension.lower()
            if not is_supported_extension(file_extension):
                raise ValueError(f"Unsupported file type: {file_extension}")

            # 确定处理方法
            try:
                processing_method = get_processing_method(file_extension)
            except ValueError as e:
                raise ValueError(str(e))

            if processing_method == FileProcessingMethod.MARKDOWN:
                # 直接读取 Markdown 文件
                async def process_markdown():
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        markdown_text = f.read()
                        doc = Document(text=markdown_text,
                                       metadata={"source": f.name, "type": "markdown", "file_name": file_name})
                        documents.append(doc)
                task = process_markdown(file_path)
                tasks.append(task)
            elif processing_method == FileProcessingMethod.TEXT:
                async def process_txt():
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            text_content = f.read()
                        # 检测文件内容是否为空
                        if not text_content.strip():
                            #如果文件内容为空，则抛出异常
                            raise ValueError("File is empty or contains only whitespace")
                        # 根据文件类型进行特殊处理
                        if file_extension in [".json", ".xml", ".yaml", ".yml"]:
                            # 结构化数据文件，保持原格式
                            markdown_text = f"```{file_extension[1:]}\n{text_content}\n```"
                        elif file_extension in [".py", ".js", ".java", ".c", ".cpp", ".cs", ".go", ".rs"]:
                            # 代码文件，添加代码块格式
                            markdown_text = f"```{file_extension[1:]}\n{text_content}\n```"
                        elif file_extension in [".csv"]:
                            # CSV 文件，尝试转换为 Markdown 表格
                            try:
                                lines = text_content.strip().split('\n')
                                if len(lines) > 1:
                                    # 创建表头分隔符
                                    header = lines[0].split(',')
                                    separator = '|' + '|'.join(['---' for _ in header]) + '|'
                                    # 转换为 Markdown 表格
                                    table_rows = ['|' + '|'.join(line.split(',')) + '|' for line in lines]
                                    table_rows.insert(1, separator)
                                    markdown_text = '\n'.join(table_rows)
                                else:
                                    # 如果只有一行，仍然使用代码块
                                    markdown_text = f"```csv\n{text_content}\n```"
                            except Exception:
                                # 如果转换失败，回退到代码块
                                markdown_text = f"```csv\n{text_content}\n```"
                        else:
                            # 普通文本文件，直接使用内容
                            markdown_text = text_content
                        doc = Document(text=markdown_text,
                                       metadata={"source": file_name, "type": "text", "file_name": file_name})
                        documents.append(doc)

                    except Exception as e:
                        raise ValueError(f"Error processing text file: {str(e)}")
                task = process_txt()
                tasks.append(task)
            elif processing_method == FileProcessingMethod.PDF_CONVERTER:
                async def process_marker():
                    try:
                        # 初始化转换器
                        converter = PdfConverter(
                            # config={"output_format": "markdown",
                            #         "output_dir": "output",
                            #         "use_llm": True,
                            #         "gemini_api_key":"AIzaSyDQaqz1xHa7uBmtaDducdtAzrEiWZEtIuM",
                            #         "llm_service": "marker.services.openai.OpenAIService",
                            #         "openai_base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                            #         "openai_model": "qwen-vl-max-latest",
                            #         "openai_api_key": "sk-9d64fad24f9e402c81e99add13cfae97"
                            #         },
                            artifact_dict=create_model_dict(),
                            # llm_service="marker.services.openai.OpenAIService",
                        )

                        # 转换文件为 Markdown
                        rendered = converter(file_path)
                        markdown_text, _, images = text_from_rendered(rendered)
                        # 检查转换结果是否为空
                        if not markdown_text.strip():
                            raise ValueError("Document conversion produced empty result")
                        
                        # 创建主文档
                        doc = Document(text=markdown_text,
                                       metadata={"source": file_path, "type": "markdown", "file_name": os.path.splitext(file_name.lower())[0]+".md"})
                        documents.append(doc)
                        
                        # 处理图片 - 优化版本：并行处理图像描述
                        if images:
                            # 获取MinioStorageClient实例（连接池模式）
                            minio_client = get_minio_client()
                            
                            # 第一步：收集所有图像信息
                            image_data = []
                            for i, (img_name, img_obj) in enumerate(images.items()):
                                try:
                                    # 创建临时文件保存图像
                                    with NamedTemporaryFile(suffix=".png", delete=False) as tmp_img:
                                        img_obj.save(tmp_img.name)  # 保存图像到临时文件
                                        
                                        image_data.append({
                                            'index': i,
                                            'img_name': img_name,
                                            'tmp_path': tmp_img.name,
                                            'object_key': f"image_references/{os.path.splitext(file_name.lower())[0]}/{img_name}"
                                        })
                                except Exception as e:
                                    logger.error(f"准备图片 {i+1} 时出错: {str(e)}")
                                    continue
                            
                            # 第二步：并行上传图像到Minio
                            async def upload_image_to_minio(img_info):
                                """上传单个图像到Minio"""
                                try:
                                    with open(img_info['tmp_path'], "rb") as f:
                                        img_result = await minio_client.upload_file(
                                            object_key=img_info['object_key'],
                                            data=f.read(),
                                            mime="image/png"
                                        )
                                    
                                    # 获取图像URL
                                    image_url = img_result.get("url", "")
                                    if not image_url:
                                        logger.error(f"Failed to upload image {img_info['img_name']} to Minio")
                                        return None
                                    
                                    return {
                                        **img_info,
                                        'image_url': image_url
                                    }
                                except Exception as e:
                                    logger.error(f"Error uploading image {img_info['img_name']}: {e}")
                                    return None
                            
                            # 并行上传所有图像
                            upload_tasks = [upload_image_to_minio(img_info) for img_info in image_data]
                            uploaded_images = await run_jobs(upload_tasks, show_progress=False, workers=5)
                            
                            # 过滤掉上传失败的图像
                            uploaded_images = [img for img in uploaded_images if img is not None]
                            
                            # 第三步：并行处理图像描述
                            async def process_image_description(img_info):
                                """处理单个图像的描述"""
                                try:
                                    # 获取图片描述
                                    image_description = await describe_image(img_info['tmp_path'])
                                    
                                    # 清理临时文件
                                    if os.path.exists(img_info['tmp_path']):
                                        os.remove(img_info['tmp_path'])
                                    
                                    return {
                                        **img_info,
                                        'image_description': image_description
                                    }
                                except Exception as e:
                                    logger.error(f"Error processing image description for {img_info['img_name']}: {e}")
                                    # 确保临时文件被清理
                                    if os.path.exists(img_info['tmp_path']):
                                        os.remove(img_info['tmp_path'])
                                    return None
                            
                            # 并行处理所有图像描述
                            if uploaded_images:
                                logger.info(f"开始并行处理 {len(uploaded_images)} 个图像的描述...")
                                description_tasks = [process_image_description(img_info) for img_info in uploaded_images]
                                
                                # 使用 run_jobs 进行并发处理
                                try:
                                    processed_images = await run_jobs(description_tasks, show_progress=False, workers=5)
                                    # 过滤掉None结果
                                    processed_images = [img for img in processed_images if img is not None]
                                except Exception as e:
                                    logger.error(f"批量处理图像描述时出错: {e}")
                                    processed_images = []
                                
                                logger.info(f"成功处理 {len(processed_images)} 个图像的描述")
                            else:
                                processed_images = []
                            
                            # 第四步：构建文档对象
                            for img_info in processed_images:
                                try:
                                    # 创建图片文档
                                    image_doc = Document(
                                        text=img_info['image_description'],
                                        metadata={
                                            "source": file_path,
                                            "type": "image",
                                            "file_name": os.path.splitext(file_name.lower())[0]+".md",
                                            "image_index": img_info['index'],
                                            "total_images": len(images),
                                            "image_path": img_info['image_url']  # 使用Minio URL替代本地路径
                                        }
                                    )
                                    documents.append(image_doc)
                                    logger.info(f"已处理图片 {img_info['index']+1}/{len(images)}")
                                except Exception as e:
                                    logger.error(f"创建图片文档 {img_info['img_name']} 时出错: {str(e)}")
                                    continue
                            
                    except Exception as e:
                        raise ValueError(f"Error converting document: {str(e)}")
                task = process_marker()
                tasks.append(task)
            else:
                raise ValueError( f"Unknown processing method: {processing_method}")
        # await asyncio.gather(*tasks)
        await run_jobs(tasks, show_progress=True, workers=3)
        return documents

    async def create_index_universal(self, collection_name="default") -> BaseIndex:
        # 加载数据
        data = await self.load_data()
        # 创建一个句子分割器
        node_parser = MarkdownTextSplitter(
                    chunk_size=1024,  # 每块的最大token数
                    chunk_overlap=200,  # 块之间的重叠token数
                )
        # 从文档中获取节点
        nodes = node_parser.get_nodes_from_documents(data)
        # 创建向量存储索引
        vector_store = MilvusVectorStore(
            uri=settings.configuration.milvus_uri,
            collection_name=collection_name, dim=int(settings.configuration.embedding_model_dim_bge_large), overwrite=False,
            # 开启混合检索
            sparse_embedding_function=CustomizedEmbeddingFunction(),
            enable_sparse=True,
        )
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        #此处可以传入想要使用的嵌入模型，如果不传入嵌入模型，则使用默认嵌入模型（从llama_index.core.settings中导入）
        index = VectorStoreIndex(nodes, storage_context=storage_context, show_progress=True)

        return index

if __name__ == "__main__":
    rag = UniversalRAG(files=["E:/aitest/account.pdf"])
    asyncio.run(rag.create_index_universal())
