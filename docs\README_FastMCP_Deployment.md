# YQ AI V3 MCP Server - FastMCP部署指南

基于[FastMCP](https://gofastmcp.com/)框架的多传输协议支持，提供STDIO、Streamable HTTP和SSE三种部署方式。

## 🌟 概述

YQ AI V3 MCP Server 现在基于FastMCP框架，原生支持多种传输协议，为不同场景提供最佳的部署选择。

## 🚀 支持的传输协议

### 1. STDIO（默认）
- **用途**: 本地工具和命令行集成
- **适用场景**: Claude Desktop集成、本地脚本
- **优势**: 最广泛的兼容性，本地安全执行
- **限制**: 需要本地运行MCP代码

### 2. Streamable HTTP（推荐）⭐
- **用途**: Web部署、微服务、网络访问
- **适用场景**: Web应用集成、API服务、生产部署
- **优势**: 现代化、高效、适合Web部署
- **端口**: 默认8001

### 3. SSE（已弃用）
- **用途**: 基于Server-Sent Events的通信
- **适用场景**: 现有SSE部署（不推荐新项目）
- **状态**: 已弃用，建议迁移到Streamable HTTP

## 🔧 安装和启动

### 前置要求

```bash
# 安装FastMCP
pip install fastmcp

# 安装项目依赖
pip install -r requirements.txt
```

### 快速启动

```bash
# 1. 默认STDIO模式
python app/api/api_v1/mcp_server.py

# 2. Streamable HTTP模式（推荐）
python app/api/api_v1/mcp_server.py --transport streamable-http --port 8001

# 3. 使用FastMCP CLI（推荐）
fastmcp run app/api/api_v1/mcp_server.py --transport streamable-http --port 8001
```

### 详细启动选项

```bash
# 查看所有选项
python app/api/api_v1/mcp_server.py --help

# 自定义配置
python app/api/api_v1/mcp_server.py \
  --transport streamable-http \
  --host 0.0.0.0 \
  --port 8001 \
  --path /mcp \
  --log-level info \
  --verbose
```

## 📡 使用FastMCP CLI（推荐）

[FastMCP CLI](https://gofastmcp.com/deployment/running-server)提供了更灵活的部署方式：

```bash
# 生产部署
fastmcp run app/api/api_v1/mcp_server.py --transport streamable-http --port 8001

# 开发模式（带MCP Inspector）
fastmcp dev app/api/api_v1/mcp_server.py

# 传递额外参数
fastmcp run app/api/api_v1/mcp_server.py --transport streamable-http -- --verbose

# 指定配置
fastmcp run app/api/api_v1/mcp_server.py --transport sse --port 9000
```

## 🌐 HTTP端点（仅Streamable HTTP模式）

当使用Streamable HTTP传输时，服务器提供以下端点：

- `GET /` - 根路径响应
- `GET /health` - 健康检查
- `GET /info` - 服务器详细信息
- `POST /mcp` - MCP协议端点（主要通信接口）

### 健康检查示例

```bash
curl http://localhost:8001/health
```

响应：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "server": "YQ AI V3 MCP Server",
  "transport": "HTTP",
  "version": "1.0.0"
}
```

## 🐍 客户端连接

### Python客户端示例

```python
import asyncio
from mcp import Client
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client

async def test_stdio_client():
    """STDIO客户端示例"""
    async with stdio_client(
        command="python",
        args=["app/api/api_v1/mcp_server.py"]
    ) as client:
        # 调用工具
        result = await client.call_tool("query_rag", {
            "query": "什么是人工智能？"
        })
        print("RAG查询结果:", result)

async def test_http_client():
    """HTTP客户端示例"""
    async with sse_client("http://localhost:8001/mcp") as client:
        # 获取服务器信息
        resources = await client.list_resources()
        print("可用资源:", resources)
        
        # 调用工具
        result = await client.call_tool("get_database_connections")
        print("数据库连接:", result)

# 运行示例
if __name__ == "__main__":
    # 测试STDIO连接
    asyncio.run(test_stdio_client())
    
    # 测试HTTP连接（需要先启动HTTP服务器）
    asyncio.run(test_http_client())
```

### JavaScript/Node.js客户端

```javascript
// 安装依赖: npm install @modelcontextprotocol/sdk

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

async function connectToMCPServer() {
  const transport = new SSEClientTransport(
    new URL('http://localhost:8001/mcp')
  );
  
  const client = new Client(
    {
      name: "yq-ai-client",
      version: "1.0.0"
    },
    {
      capabilities: {}
    }
  );

  await client.connect(transport);
  
  // 调用工具
  const result = await client.request(
    { method: "tools/call" },
    {
      name: "query_rag",
      arguments: {
        query: "介绍一下知识图谱"
      }
    }
  );
  
  console.log('查询结果:', result);
  
  await client.close();
}
```

## 🏗️ Claude Desktop集成

### 配置文件

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "yq-ai-v3": {
      "command": "python",
      "args": [
        "/path/to/yq_ai_v3/app/api/api_v1/mcp_server.py"
      ]
    }
  }
}
```

### 使用FastMCP CLI启动

```json
{
  "mcpServers": {
    "yq-ai-v3": {
      "command": "fastmcp",
      "args": [
        "run",
        "/path/to/yq_ai_v3/app/api/api_v1/mcp_server.py"
      ]
    }
  }
}
```

## 🔧 生产部署

### 1. 使用Systemd（Linux）

创建服务文件 `/etc/systemd/system/yq-ai-mcp.service`：

```ini
[Unit]
Description=YQ AI V3 MCP Server
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/yq_ai_v3
ExecStart=/usr/bin/python app/api/api_v1/mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001
Restart=always
RestartSec=3
Environment=PYTHONPATH=/path/to/yq_ai_v3

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable yq-ai-mcp
sudo systemctl start yq-ai-mcp
sudo systemctl status yq-ai-mcp
```

### 2. 使用Docker

创建 `Dockerfile`：

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install fastmcp

# 复制代码
COPY . .

# 暴露端口
EXPOSE 8001

# 启动服务
CMD ["python", "app/api/api_v1/mcp_server.py", "--transport", "streamable-http", "--host", "0.0.0.0", "--port", "8001"]
```

构建和运行：
```bash
docker build -t yq-ai-mcp .
docker run -p 8001:8001 yq-ai-mcp
```

### 3. 使用Docker Compose

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  yq-ai-mcp:
    build: .
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

启动：
```bash
docker-compose up -d
```

## 📊 监控和调试

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8001/health

# 获取详细信息
curl http://localhost:8001/info
```

### 日志配置

```bash
# 启用详细日志
python app/api/api_v1/mcp_server.py --transport streamable-http --verbose --log-level debug

# 使用FastMCP CLI
fastmcp run app/api/api_v1/mcp_server.py --transport streamable-http -- --verbose
```

### 性能监控

```python
# 监控脚本示例
import requests
import time
import json

def monitor_mcp_server(url="http://localhost:8001"):
    while True:
        try:
            # 健康检查
            health_response = requests.get(f"{url}/health", timeout=5)
            health_data = health_response.json()
            
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] "
                  f"Status: {health_data['status']}")
            
            # 获取服务器信息
            info_response = requests.get(f"{url}/info", timeout=5)
            info_data = info_response.json()
            
            print(f"Modules: {len(info_data['modules'])}")
            
        except Exception as e:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Error: {e}")
        
        time.sleep(30)  # 每30秒检查一次

if __name__ == "__main__":
    monitor_mcp_server()
```

## 🔧 高级配置

### 环境变量配置

```bash
# 设置环境变量
export MCP_TRANSPORT=streamable-http
export MCP_HOST=0.0.0.0
export MCP_PORT=8001
export MCP_LOG_LEVEL=info

# 启动服务器
python app/api/api_v1/mcp_server.py
```

### 负载均衡

使用nginx进行负载均衡：

```nginx
upstream mcp_backend {
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location /mcp {
        proxy_pass http://mcp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /health {
        proxy_pass http://mcp_backend;
    }
}
```

## ⚠️ 注意事项

1. **传输协议选择**:
   - 本地开发：使用STDIO
   - Web部署：使用Streamable HTTP
   - 避免使用SSE（已弃用）

2. **安全考虑**:
   - 生产环境中配置适当的访问控制
   - 使用HTTPS进行外部访问
   - 定期更新依赖包

3. **性能优化**:
   - 监控内存和CPU使用情况
   - 配置适当的日志级别
   - 使用负载均衡处理高并发

4. **故障排除**:
   - 检查端口是否被占用
   - 验证防火墙设置
   - 查看详细日志输出

## 📚 参考资源

- [FastMCP官方文档](https://gofastmcp.com/)
- [FastMCP部署指南](https://gofastmcp.com/deployment/running-server)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [Claude Desktop MCP集成](https://docs.anthropic.com/claude/docs/mcp)

## 🤝 支持

如有问题，请：
1. 查看日志文件
2. 检查配置是否正确
3. 参考FastMCP官方文档
4. 提交Issue到项目仓库 