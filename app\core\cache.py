from abc import ABC, abstractmethod
import uuid
import json
import hashlib
from typing import Any, Optional, Union, Callable
from functools import wraps
from datetime import datetime, timedelta
from app.message.myredis import RedisClient
from config.cache_config import redis_config
from utils.logger import get_logger
import asyncio

logger = get_logger()


class Cache(ABC):
    @abstractmethod
    def generate_id(self, *args, **kwargs):
        pass

    @abstractmethod
    def get(self, id, field):
        pass

    @abstractmethod
    def get_all(self, field_list) -> list:
        pass

    @abstractmethod
    def set(self, id, field, value):
        pass

    @abstractmethod
    def delete(self, id):
        pass


class MemoryCache(Cache):
    def __init__(self):
        self.cache = {}

    def generate_id(self, *args, **kwargs):
        return str(uuid.uuid4())

    def set(self, id, field, value):
        if id not in self.cache:
            self.cache[id] = {}

        self.cache[id][field] = value

    def get(self, id, field):
        if id not in self.cache:
            return None

        if field not in self.cache[id]:
            return None

        return self.cache[id][field]

    def get_all(self, field_list) -> list:
        return [
            {
                "id": id,
                **{
                    field: self.get(id=id, field=field)
                    for field in field_list
                }
            }
            for id in self.cache
        ]

    def delete(self, id):
        if id in self.cache:
            del self.cache[id]


class StatisticsCache:
    """
    统计数据缓存类
    专门用于缓存统计接口的数据
    """
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        """
        初始化缓存客户端
        
        Args:
            redis_client: Redis客户端实例，如不提供则创建默认实例
        """
        # 直接使用优化后的RedisClient，它已经集成了配置管理
        self.redis_client = redis_client or RedisClient()
        
        self.cache_prefix = redis_config.STATISTICS_CACHE_PREFIX
        self.cache_enabled = redis_config.is_cache_enabled()
        
    def _generate_cache_key(self, endpoint: str, params: dict = None) -> str:
        """
        生成缓存键
        
        Args:
            endpoint: 接口端点名称
            params: 接口参数
            
        Returns:
            str: 缓存键
        """
        if params:
            # 将参数序列化并生成哈希值
            params_str = json.dumps(params, sort_keys=True, ensure_ascii=False)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()
            return f"{self.cache_prefix}{endpoint}:{params_hash}"
        else:
            return f"{self.cache_prefix}{endpoint}"
    
    def get(self, endpoint: str, params: dict = None) -> Optional[Any]:
        """
        从缓存获取数据
        
        Args:
            endpoint: 接口端点名称
            params: 接口参数
            
        Returns:
            Any: 缓存的数据，如果不存在返回None
        """
        if not self.cache_enabled or not self.redis_client.is_connected():
            return None
            
        try:
            cache_key = self._generate_cache_key(endpoint, params)
            cached_data = self.redis_client.client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                logger.debug(f"从缓存获取数据: {cache_key}")
                return data
                
        except Exception as e:
            logger.error(f"从缓存获取数据失败: {e}")
            
        return None
    
    def _serialize_data(self, data: Any) -> str:
        """
        序列化数据，处理各种数据类型包括Pydantic模型
        
        Args:
            data: 要序列化的数据
            
        Returns:
            str: 序列化后的JSON字符串
        """
        from utils.encode_utils import safe_json_dumps
        return safe_json_dumps(data)
    
    def set(self, endpoint: str, data: Any, expire_seconds: int = None, params: dict = None) -> bool:
        """
        设置缓存数据
        
        Args:
            endpoint: 接口端点名称
            data: 要缓存的数据
            expire_seconds: 过期时间（秒），如果为None则使用默认配置
            params: 接口参数
            
        Returns:
            bool: 设置是否成功
        """
        if not self.cache_enabled or not self.redis_client.is_connected():
            return False
        
        # 如果没有指定过期时间，使用默认配置
        if expire_seconds is None:
            expire_seconds = redis_config.STATISTICS_OVERVIEW_CACHE_TIME
            
        try:
            cache_key = self._generate_cache_key(endpoint, params)
            
            # 添加缓存时间戳
            cache_data = {
                "data": data,
                "cached_at": datetime.now().isoformat(),
                "expire_at": (datetime.now() + timedelta(seconds=expire_seconds)).isoformat()
            }
            
            # 使用自定义序列化器
            serialized_data = self._serialize_data(cache_data)
            
            result = self.redis_client.client.setex(
                cache_key, 
                expire_seconds, 
                serialized_data
            )
            
            if result:
                logger.debug(f"缓存数据成功: {cache_key}, 过期时间: {expire_seconds}秒")
                return True
                
        except Exception as e:
            logger.error(f"设置缓存数据失败: {e}")
            
        return False
    
    def delete(self, endpoint: str, params: dict = None) -> bool:
        """
        删除缓存数据
        
        Args:
            endpoint: 接口端点名称 
            params: 接口参数
            
        Returns:
            bool: 删除是否成功
        """
        if not self.cache_enabled or not self.redis_client.is_connected():
            return False
            
        try:
            cache_key = self._generate_cache_key(endpoint, params)
            result = self.redis_client.client.delete(cache_key)
            
            if result:
                logger.debug(f"删除缓存成功: {cache_key}")
                return True
                
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            
        return False
    
    def clear_pattern(self, pattern: str) -> int:
        """
        批量删除符合模式的缓存
        
        Args:
            pattern: 缓存键模式，如 "stats_cache:database_*"
            
        Returns:
            int: 删除的键数量
        """
        if not self.cache_enabled or not self.redis_client.is_connected():
            return 0
            
        try:
            keys = self.redis_client.client.keys(f"{self.cache_prefix}{pattern}")
            if keys:
                result = self.redis_client.client.delete(*keys)
                logger.info(f"批量删除缓存成功，删除了 {result} 个键")
                return result
        except Exception as e:
            logger.error(f"批量删除缓存失败: {e}")
            
        return 0
    
    def get_cache_info(self) -> dict:
        """
        获取缓存系统信息
        
        Returns:
            dict: 缓存系统信息
        """
        try:
            if not self.redis_client.is_connected():
                return {
                    "status": "disconnected",
                    "cache_enabled": self.cache_enabled,
                    "redis_config": {
                        "host": redis_config.REDIS_HOST,
                        "port": redis_config.REDIS_PORT,
                        "db": redis_config.REDIS_DB
                    }
                }
            
            # 获取Redis信息
            info = self.redis_client.client.info()
            keys = self.redis_client.client.keys(f"{self.cache_prefix}*")
            
            return {
                "status": "connected",
                "cache_enabled": self.cache_enabled,
                "total_keys": len(keys),
                "cache_prefix": self.cache_prefix,
                "redis_info": {
                    "version": info.get("redis_version"),
                    "memory_used": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "uptime": info.get("uptime_in_seconds")
                },
                "config": {
                    "overview_cache_time": redis_config.STATISTICS_OVERVIEW_CACHE_TIME,
                    "trend_cache_time": redis_config.STATISTICS_TREND_CACHE_TIME,
                    "distribution_cache_time": redis_config.STATISTICS_DISTRIBUTION_CACHE_TIME,
                    "detailed_cache_time": redis_config.STATISTICS_DETAILED_CACHE_TIME,
                    "chart_cache_time": redis_config.STATISTICS_CHART_CACHE_TIME
                }
            }
            
        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "cache_enabled": self.cache_enabled
            }


def cache_statistics(expire_seconds: int = None, cache_key_params: list = None, cache_type: str = "overview"):
    """
    统计接口缓存装饰器
    
    Args:
        expire_seconds: 缓存过期时间（秒），如果为None则根据cache_type自动选择
        cache_key_params: 用于生成缓存键的参数名列表，如果为None则使用所有可序列化参数
        cache_type: 缓存类型，用于自动选择过期时间 (overview, trend, distribution, detailed, chart)
    
    Usage:
        @cache_statistics(cache_type="trend", cache_key_params=['days'])
        async def get_stats(days: int = 30, service: Service = Depends(...)):
            # 接口逻辑
            return data
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache = StatisticsCache()
            
            # 确定缓存过期时间
            if expire_seconds is None:
                actual_expire_seconds = redis_config.get_cache_time_by_type(cache_type)
            else:
                actual_expire_seconds = expire_seconds
            
            # 生成缓存参数 - 过滤掉Service实例等不可序列化的对象
            cache_params = {}
            if cache_key_params:
                # 使用指定的参数列表
                for param in cache_key_params:
                    if param in kwargs:
                        cache_params[param] = kwargs[param]
            else:
                # 自动过滤可序列化的参数
                for key, value in kwargs.items():
                    if _is_serializable_param(key, value):
                        cache_params[key] = value
            
            # 尝试从缓存获取数据
            endpoint_name = func.__name__
            cached_result = cache.get(endpoint_name, cache_params)
            
            if cached_result:
                logger.debug(f"接口 {endpoint_name} 使用缓存数据 (类型: {cache_type})")
                return cached_result["data"]
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache.set(endpoint_name, result, actual_expire_seconds, cache_params)
            logger.debug(f"接口 {endpoint_name} 数据已缓存 (类型: {cache_type}, 过期时间: {actual_expire_seconds}秒)")
            
            return result
            
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            cache = StatisticsCache()
            
            # 确定缓存过期时间
            if expire_seconds is None:
                actual_expire_seconds = redis_config.get_cache_time_by_type(cache_type)
            else:
                actual_expire_seconds = expire_seconds
            
            # 生成缓存参数 - 过滤掉Service实例等不可序列化的对象
            cache_params = {}
            if cache_key_params:
                # 使用指定的参数列表
                for param in cache_key_params:
                    if param in kwargs:
                        cache_params[param] = kwargs[param]
            else:
                # 自动过滤可序列化的参数
                for key, value in kwargs.items():
                    if _is_serializable_param(key, value):
                        cache_params[key] = value
            
            # 尝试从缓存获取数据
            endpoint_name = func.__name__
            cached_result = cache.get(endpoint_name, cache_params)
            
            if cached_result:
                logger.debug(f"接口 {endpoint_name} 使用缓存数据 (类型: {cache_type})")
                return cached_result["data"]
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            cache.set(endpoint_name, result, actual_expire_seconds, cache_params)
            logger.debug(f"接口 {endpoint_name} 数据已缓存 (类型: {cache_type}, 过期时间: {actual_expire_seconds}秒)")
            
            return result
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


def _is_serializable_param(param_name: str, param_value: Any) -> bool:
    """
    检查参数是否可序列化，用于缓存键生成
    
    Args:
        param_name: 参数名
        param_value: 参数值
        
    Returns:
        bool: 是否可序列化
    """
    # 排除Service实例和其他不可序列化的对象
    if param_name.endswith('service') or param_name.startswith('service'):
        return False
    
    # 检查是否是Service类的实例
    if hasattr(param_value, '__class__'):
        class_name = param_value.__class__.__name__.lower()
        if 'service' in class_name or 'statisticsservice' in class_name or 'databasestatisticsservice' in class_name:
            return False
    
    # 检查基本可序列化类型
    try:
        # 尝试序列化以验证
        json.dumps(param_value, default=str)
        return True
    except (TypeError, ValueError):
        # 如果序列化失败，不包含在缓存键中
        logger.debug(f"参数 {param_name} 不可序列化，跳过缓存键生成: {type(param_value)}")
        return False


def invalidate_cache(endpoint: str, params: dict = None):
    """
    手动清除缓存的工具函数
    
    Args:
        endpoint: 接口端点名称
        params: 接口参数
    """
    cache = StatisticsCache()
    cache.delete(endpoint, params)


def clear_all_statistics_cache():
    """
    清除所有统计缓存
    """
    cache = StatisticsCache()
    return cache.clear_pattern("*")


def get_cache_status():
    """
    获取缓存状态信息
    """
    cache = StatisticsCache()
    return cache.get_cache_info()


# 常用的缓存配置
class CacheConfig:
    """缓存配置常量（兼容旧版本）"""
    
    # 不同统计数据的默认缓存时间（秒）
    OVERVIEW_CACHE_TIME = redis_config.STATISTICS_OVERVIEW_CACHE_TIME
    TREND_CACHE_TIME = redis_config.STATISTICS_TREND_CACHE_TIME
    DISTRIBUTION_CACHE_TIME = redis_config.STATISTICS_DISTRIBUTION_CACHE_TIME
    DETAILED_CACHE_TIME = redis_config.STATISTICS_DETAILED_CACHE_TIME
    CHART_CACHE_TIME = redis_config.STATISTICS_CHART_CACHE_TIME
    
    # 缓存键前缀
    DATABASE_STATS_PREFIX = redis_config.DATABASE_STATS_PREFIX
    KNOWLEDGE_STATS_PREFIX = redis_config.KNOWLEDGE_STATS_PREFIX


# 缓存健康检查任务
async def cache_health_check():
    """
    缓存系统健康检查
    """
    try:
        cache = StatisticsCache()
        if cache.redis_client.is_connected():
            # 执行简单的ping测试
            cache.redis_client.client.ping()
            logger.info("缓存系统健康检查通过")
            return True
        else:
            logger.warning("缓存系统连接失败")
            return False
    except Exception as e:
        logger.error(f"缓存系统健康检查失败: {e}")
        return False


# 缓存清理任务
async def cleanup_expired_cache():
    """
    清理过期的缓存（如果Redis配置了自动清理，这个任务可能不是必需的）
    """
    if not redis_config.AUTO_CLEAR_EXPIRED_CACHE:
        return
        
    try:
        cache = StatisticsCache()
        if cache.redis_client.is_connected():
            # Redis会自动清理过期的键，这里主要是记录日志
            info = cache.get_cache_info()
            logger.info(f"缓存清理任务执行，当前缓存键数量: {info.get('total_keys', 0)}")
    except Exception as e:
        logger.error(f"缓存清理任务失败: {e}")
