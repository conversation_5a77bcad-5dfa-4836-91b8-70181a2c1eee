"""
RAG对话历史相关的数据库操作工具类
"""
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy import select, update, delete, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from dao.DatabaseEngine import DatabaseEngine
from dao.models.RagChatModels import RagChatSession, RagChatHistory
from utils.logger import get_logger

logger = get_logger()


class RagChatSessionDAO:
    """RAG对话会话 DAO 操作类"""
    
    @staticmethod
    async def create_session(
        session_id: str,
        user_id: str,
        collection_name: str,
        session_title: Optional[str] = None,
        session_config: Optional[Dict[str, Any]] = None,
        create_by: Optional[str] = None
    ) -> RagChatSession:
        """
        创建新的对话会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            collection_name: 知识库集合名称
            session_title: 会话标题
            session_config: 会话配置参数
            create_by: 创建人
            
        Returns:
            RagChatSession: 创建的会话对象
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                # 生成UUID作为主键
                chat_session = RagChatSession(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    user_id=user_id,
                    collection_name=collection_name,
                    session_title=session_title or f"对话会话-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    status=1,  # 活跃状态
                    total_messages=0,
                    session_config=session_config,
                    create_by=create_by or user_id,
                    create_time=datetime.now()
                )
                
                db_session.add(chat_session)
                await db_session.commit()
                await db_session.refresh(chat_session)
                
                logger.info(f"创建对话会话成功: session_id={session_id}, user_id={user_id}")
                return chat_session
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"创建对话会话失败: {str(e)}")
                raise
    
    @staticmethod
    async def get_session_by_id(session_id: str) -> Optional[RagChatSession]:
        """
        根据会话ID获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[RagChatSession]: 会话对象，如果不存在则返回None
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = select(RagChatSession).where(
                    RagChatSession.session_id == session_id,
                    RagChatSession.status != 3  # 排除已删除状态
                ).options(selectinload(RagChatSession.chat_histories))
                
                result = await db_session.execute(stmt)
                session = result.scalar_one_or_none()
                
                if session:
                    logger.debug(f"获取对话会话成功: session_id={session_id}")
                else:
                    logger.debug(f"未找到对话会话: session_id={session_id}")
                
                return session
                
            except Exception as e:
                logger.error(f"获取对话会话失败: {str(e)}")
                raise
    
    @staticmethod
    async def get_user_sessions(
        user_id: str,
        page: int = 1,
        page_size: int = 20,
        status: Optional[int] = None
    ) -> List[RagChatSession]:
        """
        获取用户的对话会话列表
        
        Args:
            user_id: 用户ID
            page: 页码
            page_size: 每页大小
            status: 会话状态过滤
            
        Returns:
            List[RagChatSession]: 会话列表
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = select(RagChatSession).where(
                    RagChatSession.user_id == user_id
                )
                
                if status is not None:
                    stmt = stmt.where(RagChatSession.status == status)
                else:
                    stmt = stmt.where(RagChatSession.status != 3)  # 排除已删除
                
                stmt = stmt.order_by(desc(RagChatSession.create_time))
                stmt = stmt.offset((page - 1) * page_size).limit(page_size)
                
                result = await db_session.execute(stmt)
                sessions = result.scalars().all()
                
                logger.debug(f"获取用户会话列表成功: user_id={user_id}, count={len(sessions)}")
                return list(sessions)
                
            except Exception as e:
                logger.error(f"获取用户会话列表失败: {str(e)}")
                raise

    @staticmethod
    async def get_user_sessions_count(
            user_id: str,
            status: Optional[int] = None
    ) -> int:
        """
        获取用户的对话会话总数

        Args:
            user_id: 用户ID
            status: 会话状态过滤

        Returns:
            int: 会话总数
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = select(func.count(RagChatSession.id)).where(
                    RagChatSession.user_id == user_id
                )

                if status is not None:
                    stmt = stmt.where(RagChatSession.status == status)
                else:
                    stmt = stmt.where(RagChatSession.status != 3)  # 排除已删除

                result = await db_session.execute(stmt)
                total = result.scalar() or 0

                logger.debug(f"获取用户会话总数成功: user_id={user_id}, total={total}")
                return total

            except Exception as e:
                logger.error(f"获取用户会话总数失败: {str(e)}")
                raise
    
    @staticmethod
    async def update_session_status(session_id: str, status: int) -> bool:
        """
        更新会话状态
        
        Args:
            session_id: 会话ID
            status: 新状态
            
        Returns:
            bool: 更新是否成功
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = update(RagChatSession).where(
                    RagChatSession.session_id == session_id
                ).values(
                    status=status,
                    update_time=datetime.now()
                )
                
                result = await db_session.execute(stmt)
                await db_session.commit()
                
                success = result.rowcount > 0
                if success:
                    logger.info(f"更新会话状态成功: session_id={session_id}, status={status}")
                else:
                    logger.warning(f"更新会话状态失败，会话不存在: session_id={session_id}")
                
                return success
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"更新会话状态失败: {str(e)}")
                raise
    
    @staticmethod
    async def update_session_title(session_id: str, title: str) -> bool:
        """
        更新会话标题
        
        Args:
            session_id: 会话ID
            title: 新标题
            
        Returns:
            bool: 更新是否成功
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = update(RagChatSession).where(
                    RagChatSession.session_id == session_id
                ).values(
                    session_title=title,
                    update_time=datetime.now()
                )
                
                result = await db_session.execute(stmt)
                await db_session.commit()
                
                success = result.rowcount > 0
                if success:
                    logger.info(f"更新会话标题成功: session_id={session_id}, title={title}")
                else:
                    logger.warning(f"更新会话标题失败，会话不存在: session_id={session_id}")
                
                return success
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"更新会话标题失败: {str(e)}")
                raise
    
    @staticmethod
    async def update_session_message_count(session_id: str) -> bool:
        """
        更新会话消息计数和最后消息时间
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 更新是否成功
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                # 获取消息总数
                count_stmt = select(func.count(RagChatHistory.id)).where(
                    RagChatHistory.session_id == session_id
                )
                count_result = await db_session.execute(count_stmt)
                total_messages = count_result.scalar() or 0
                
                # 更新会话信息
                stmt = update(RagChatSession).where(
                    RagChatSession.session_id == session_id
                ).values(
                    total_messages=total_messages,
                    last_message_time=datetime.now(),
                    update_time=datetime.now()
                )
                
                result = await db_session.execute(stmt)
                await db_session.commit()
                
                success = result.rowcount > 0
                if success:
                    logger.debug(f"更新会话消息计数成功: session_id={session_id}, total_messages={total_messages}")
                
                return success
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"更新会话消息计数失败: {str(e)}")
                raise
    
    @staticmethod
    async def search_sessions_by_title(
        user_id: str,
        keyword: str,
        page: int = 1,
        page_size: int = 20
    ) -> List[RagChatSession]:
        """
        根据关键词搜索用户的会话标题
        
        Args:
            user_id: 用户ID
            keyword: 搜索关键词
            page: 页码
            page_size: 每页大小
            
        Returns:
            List[RagChatSession]: 匹配的会话列表
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                # 构建搜索查询语句
                stmt = select(RagChatSession).where(
                    RagChatSession.user_id == user_id,
                    RagChatSession.status != 3,  # 排除已删除的会话
                    RagChatSession.session_title.ilike(f'%{keyword}%')  # 使用 ilike 进行模糊搜索（不区分大小写）
                )
                
                # 按创建时间降序排列
                stmt = stmt.order_by(desc(RagChatSession.create_time))
                
                # 分页
                stmt = stmt.offset((page - 1) * page_size).limit(page_size)
                
                result = await db_session.execute(stmt)
                sessions = result.scalars().all()
                
                logger.debug(f"搜索会话标题成功: user_id={user_id}, keyword={keyword}, count={len(sessions)}")
                return list(sessions)
                
            except Exception as e:
                logger.error(f"搜索会话标题失败: {str(e)}")
                raise


class RagChatHistoryDAO:
    """RAG对话历史 DAO 操作类"""
    
    @staticmethod
    async def add_message(
        session_id: str,
        role: str,
        content: str,
        token_count: Optional[int] = None,
        source_info: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> RagChatHistory:
        """
        添加对话消息
        
        Args:
            session_id: 会话ID
            role: 角色 (user/assistant)
            content: 消息内容
            token_count: Token数量
            source_info: 数据来源信息
            metadata: 消息元数据
            
        Returns:
            RagChatHistory: 创建的历史记录对象
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                # 获取当前会话的消息顺序号
                order_stmt = select(func.max(RagChatHistory.message_order)).where(
                    RagChatHistory.session_id == session_id
                )
                order_result = await db_session.execute(order_stmt)
                max_order = order_result.scalar() or 0
                
                # 创建历史记录
                history = RagChatHistory(
                    id=str(uuid.uuid4()),
                    session_id=session_id,
                    role=role,
                    content=content,
                    message_order=max_order + 1,
                    token_count=token_count,
                    source_info=source_info,
                    message_metadata=metadata,
                    create_time=datetime.now()
                )
                
                db_session.add(history)
                await db_session.commit()
                await db_session.refresh(history)
                
                # 异步更新会话消息计数
                await RagChatSessionDAO.update_session_message_count(session_id)
                
                logger.debug(f"添加对话消息成功: session_id={session_id}, role={role}")
                return history
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"添加对话消息失败: {str(e)}")
                raise
    
    @staticmethod
    async def get_session_history(
        session_id: str,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[RagChatHistory]:
        """
        获取会话的对话历史
        
        Args:
            session_id: 会话ID
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            List[RagChatHistory]: 对话历史列表
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = select(RagChatHistory).where(
                    RagChatHistory.session_id == session_id
                ).order_by(asc(RagChatHistory.message_order))
                
                if offset > 0:
                    stmt = stmt.offset(offset)
                
                if limit is not None:
                    stmt = stmt.limit(limit)
                
                result = await db_session.execute(stmt)
                histories = result.scalars().all()
                
                logger.debug(f"获取会话历史成功: session_id={session_id}, count={len(histories)}")
                return list(histories)
                
            except Exception as e:
                logger.error(f"获取会话历史失败: {str(e)}")
                raise
    
    @staticmethod
    async def get_recent_history(
        session_id: str,
        max_pairs: int = 5
    ) -> List[Dict[str, str]]:
        """
        获取最近的对话历史，格式化为接口需要的格式
        
        Args:
            session_id: 会话ID
            max_pairs: 最大对话轮数
            
        Returns:
            List[Dict[str, str]]: 格式化的对话历史
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                # 获取最近的消息，限制数量为 max_pairs * 2
                stmt = select(RagChatHistory).where(
                    RagChatHistory.session_id == session_id
                ).order_by(desc(RagChatHistory.message_order)).limit(max_pairs * 2)
                
                result = await db_session.execute(stmt)
                histories = result.scalars().all()
                
                # 按时间正序排列
                histories = sorted(histories, key=lambda x: x.message_order)
                
                # 格式化为接口需要的格式
                formatted_history = []
                for history in histories:
                    formatted_history.append({
                        "role": history.role,
                        "content": history.content
                    })
                
                logger.debug(f"获取最近对话历史成功: session_id={session_id}, count={len(formatted_history)}")
                return formatted_history
                
            except Exception as e:
                logger.error(f"获取最近对话历史失败: {str(e)}")
                raise
    
    @staticmethod
    async def clear_session_history(session_id: str) -> bool:
        """
        清空会话的对话历史
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 清空是否成功
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                stmt = delete(RagChatHistory).where(
                    RagChatHistory.session_id == session_id
                )
                
                result = await db_session.execute(stmt)
                await db_session.commit()
                
                # 更新会话消息计数
                await RagChatSessionDAO.update_session_message_count(session_id)
                
                logger.info(f"清空会话历史成功: session_id={session_id}, deleted_count={result.rowcount}")
                return True
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"清空会话历史失败: {str(e)}")
                raise
    
    @staticmethod
    async def delete_old_messages(days: int = 30) -> int:
        """
        删除指定天数之前的历史消息
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的消息数量
        """
        db_session = await DatabaseEngine.get_session()
        async with db_session:
            try:
                from datetime import timedelta
                cutoff_date = datetime.now() - timedelta(days=days)
                
                stmt = delete(RagChatHistory).where(
                    RagChatHistory.create_time < cutoff_date
                )
                
                result = await db_session.execute(stmt)
                await db_session.commit()
                
                deleted_count = result.rowcount
                logger.info(f"删除历史消息成功: deleted_count={deleted_count}, days={days}")
                return deleted_count
                
            except Exception as e:
                await db_session.rollback()
                logger.error(f"删除历史消息失败: {str(e)}")
                raise 