from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from dao.ChainlitDatabaseEngine import ChainlitDatabaseEngine
from app.services.knowledge_statistics_service import StatisticsService
import asyncio
# 导入缓存相关模块
from app.core.cache import cache_statistics, CacheConfig, invalidate_cache, clear_all_statistics_cache

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
        503: {"description": "服务不可用"}
    }
)

# ==================== 依赖注入函数 ====================

def get_knowledge_statistics_service() -> StatisticsService:
    """
    获取知识统计服务实例
    使用依赖注入模式，避免重复创建service实例
    """
    return StatisticsService()

# ==================== 数据模型 ====================

class StatisticsOverview(BaseModel):
    """统计概览数据模型"""
    total_conversations: int  # 总对话数
    active_users: int  # 活跃用户数
    avg_session_duration: float  # 平均会话时长（分钟）
    satisfaction_rate: float  # 满意度（百分比）

class DailyTrend(BaseModel):
    """每日趋势数据模型"""
    date: str  # 日期
    conversations: int  # 对话数量

class WeeklyTrendResponse(BaseModel):
    """最近7天趋势响应模型"""
    trends: List[DailyTrend]
    total_conversations: int

class HourlyStats(BaseModel):
    """小时统计数据"""
    hour: int
    conversations: int

class UserStats(BaseModel):
    """用户统计"""
    new_users: int
    returning_users: int
    total_active_users: int

class PopularQuestions(BaseModel):
    """热门问题"""
    question: str
    count: int
    percentage: float

class SatisfactionStats(BaseModel):
    """满意度统计"""
    total_feedbacks: int
    positive_feedbacks: int
    negative_feedbacks: int
    satisfaction_rate: float

# ==================== 工具函数 ====================

async def get_db_session():
    """获取数据库会话"""
    engine = ChainlitDatabaseEngine.get_engine()
    async with AsyncSession(engine) as session:
        yield session

# ==================== 统计接口 ====================

@router.get("/overview", response_model=StatisticsOverview)
@cache_statistics(cache_type="overview")
async def get_statistics_overview(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取AI对话统计概览
    """
    try:
        data = await service.get_statistics_overview_data()
        
        return StatisticsOverview(
            total_conversations=data["total_conversations"],
            active_users=data["active_users"],
            avg_session_duration=data["avg_session_duration"],
            satisfaction_rate=data["satisfaction_rate"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询统计概览失败: {str(e)}")

@router.get("/weekly-trend", response_model=WeeklyTrendResponse)
@cache_statistics(cache_type="trend")
async def get_weekly_trend(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取最近7天的对话趋势
    """
    try:
        data = await service.get_weekly_trend_data()
        
        trends = []
        for trend in data["trends"]:
            trends.append(DailyTrend(
                date=trend["date"],
                conversations=trend["conversations"]
            ))

        return WeeklyTrendResponse(
            trends=trends,
            total_conversations=data["total_conversations"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询周趋势失败: {str(e)}")

@router.get("/hourly-stats")
@cache_statistics(cache_type="chart")
async def get_hourly_stats(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取24小时内的对话分布统计
    """
    try:
        data = await service.get_hourly_stats_data()
        
        stats = []
        for item in data:
            stats.append(HourlyStats(
                hour=item["hour"],
                conversations=item["conversations"]
            ))

        return {"hourly_stats": stats}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询小时统计失败: {str(e)}")

@router.get("/user-stats", response_model=UserStats)
@cache_statistics(cache_type="overview")
async def get_user_stats(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取用户统计信息
    """
    try:
        data = await service.get_user_stats_data()
        
        return UserStats(
            new_users=data["new_users"],
            returning_users=data["returning_users"],
            total_active_users=data["total_active_users"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询用户统计失败: {str(e)}")

@router.get("/popular-questions")
@cache_statistics(cache_type="distribution")
async def get_popular_questions(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取热门问题统计（基于steps表中的用户输入）
    """
    try:
        data = await service.get_popular_questions_data()
        
        questions = []
        for item in data:
            questions.append(PopularQuestions(
                question=item["question"],
                count=item["count"],
                percentage=item["percentage"]
            ))

        return {"popular_questions": questions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询热门问题失败: {str(e)}")

@router.get("/satisfaction-stats", response_model=SatisfactionStats)
@cache_statistics(cache_type="overview")
async def get_satisfaction_stats(service: StatisticsService = Depends(get_knowledge_statistics_service)):
    """
    获取详细的满意度统计
    """
    try:
        data = await service.get_satisfaction_stats_data()
        
        return SatisfactionStats(
            total_feedbacks=data["total_feedbacks"],
            positive_feedbacks=data["positive_feedbacks"],
            negative_feedbacks=data["negative_feedbacks"],
            satisfaction_rate=data["satisfaction_rate"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询满意度统计失败: {str(e)}")

@router.get("/monthly-trend")
@cache_statistics(cache_type="trend")
async def get_monthly_trend():
    """
    获取最近12个月的对话趋势
    """
    try:
        engine = ChainlitDatabaseEngine.get_engine()
        async with AsyncSession(engine) as session:
            # 获取最近12个月的数据
            twelve_months_ago = (datetime.now() - timedelta(days=365)).isoformat()
            
            monthly_query = text("""
                SELECT 
                    TO_CHAR(TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM') as month,
                    COUNT(*) as conversation_count
                FROM threads 
                WHERE "createdAt" >= :start_date
                GROUP BY TO_CHAR(TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM')
                ORDER BY month ASC
            """)
            
            result = await session.execute(monthly_query, {"start_date": twelve_months_ago})
            monthly_data = result.fetchall()

            # 格式化数据
            trends = []
            for row in monthly_data:
                month_date = datetime.strptime(row.month, '%Y-%m')
                month_name = month_date.strftime('%Y年%m月')
                trends.append({
                    "month": month_name,
                    "conversations": row.conversation_count
                })

            return {"monthly_trends": trends}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询月度趋势失败: {str(e)}")

@router.get("/database-health")
@cache_statistics(cache_type="chart")
async def get_database_health():
    """
    获取数据库健康状态和基本信息
    """
    try:
        engine = ChainlitDatabaseEngine.get_engine()
        async with AsyncSession(engine) as session:
            # 检查各表的记录数
            tables_query = text("""
                SELECT 
                    (SELECT COUNT(*) FROM users) as users_count,
                    (SELECT COUNT(*) FROM threads) as threads_count,
                    (SELECT COUNT(*) FROM steps) as steps_count,
                    (SELECT COUNT(*) FROM feedbacks) as feedbacks_count
            """)
            
            result = await session.execute(tables_query)
            counts = result.fetchone()
            
            # 获取最新的对话时间
            latest_conversation_query = text("""
                SELECT MAX("createdAt") as latest_conversation
                FROM threads
            """)
            
            result = await session.execute(latest_conversation_query)
            latest_conversation = result.scalar()

            return {
                "database_status": "healthy",
                "tables": {
                    "users": counts.users_count,
                    "threads": counts.threads_count,
                    "steps": counts.steps_count,
                    "feedbacks": counts.feedbacks_count
                },
                "latest_conversation": latest_conversation,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询数据库健康状态失败: {str(e)}")

# ==================== 高级统计接口 ====================

@router.get("/comprehensive-report")
@cache_statistics(cache_type="detailed", cache_key_params=['days'])
async def get_comprehensive_report(
    days: int = Query(default=30, ge=1, le=365, description="统计天数，1-365天"), 
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    获取综合统计报告
    """
    try:
        report = await service.get_comprehensive_report_data(days)
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成综合报告失败: {str(e)}")

@router.get("/performance-metrics")
@cache_statistics(cache_type="detailed", cache_key_params=['days'])
async def get_performance_metrics(
    days: int = Query(default=30, ge=1, le=365, description="统计天数"), 
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    获取系统性能指标
    """
    try:
        metrics = await service.get_performance_metrics(days)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询性能指标失败: {str(e)}")

@router.get("/usage-patterns")
@cache_statistics(cache_type="detailed", cache_key_params=['days'])
async def get_usage_patterns(
    days: int = Query(default=30, ge=1, le=365, description="统计天数"), 
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    获取用户使用模式分析
    """
    try:
        patterns = await service.get_usage_patterns(days)
        return {
            "status": "success", 
            "data": patterns
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询使用模式失败: {str(e)}")

@router.get("/content-analysis")
@cache_statistics(cache_type="detailed", cache_key_params=['days'])
async def get_content_analysis(
    days: int = Query(default=30, ge=1, le=365, description="统计天数"), 
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    获取对话内容分析
    """
    try:
        analysis = await service.get_content_analysis(days)
        return {
            "status": "success",
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询内容分析失败: {str(e)}")

@router.get("/user-engagement")
@cache_statistics(cache_type="detailed", cache_key_params=['days'])
async def get_user_engagement(
    days: int = Query(default=30, ge=1, le=365, description="统计天数"), 
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    获取用户参与度分析
    """
    try:
        engagement = await service.get_user_engagement_stats(days)
        return {
            "status": "success",
            "data": engagement
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询用户参与度失败: {str(e)}")

@router.get("/export-data")
@cache_statistics(cache_type="chart", cache_key_params=['format', 'days'])
async def export_statistics_data(
    format: str = Query(default="json", regex="^(json|csv)$", description="导出格式: json 或 csv"),
    days: int = Query(default=30, ge=1, le=365, description="统计天数"),
    service: StatisticsService = Depends(get_knowledge_statistics_service)
):
    """
    导出统计数据
    """
    try:
        if format == "json":
            report = await service.generate_comprehensive_report(days)
            return {
                "status": "success",
                "format": "json",
                "data": report,
                "export_time": datetime.now().isoformat()
            }
        else:
            # CSV格式的简化数据
            conversation_stats = await service.get_conversation_stats(days)
            user_engagement = await service.get_user_engagement_stats(days)
            satisfaction = await service.get_satisfaction_metrics(days)
            
            csv_data = {
                "summary": [
                    ["指标", "数值"],
                    ["总对话数", conversation_stats["total_conversations"]],
                    ["期间对话数", conversation_stats["period_conversations"]],
                    ["活跃用户数", user_engagement["active_users"]],
                    ["新用户数", user_engagement["new_users"]],
                    ["满意度", f"{satisfaction['satisfaction_rate']}%"],
                    ["平均评分", satisfaction["average_rating"]]
                ]
            }
            
            return {
                "status": "success",
                "format": "csv",
                "data": csv_data,
                "export_time": datetime.now().isoformat()
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出数据失败: {str(e)}")

# ==================== 缓存管理接口 ====================

@router.delete("/cache/clear")
async def clear_knowledge_statistics_cache():
    """
    清除知识统计相关的所有缓存
    """
    try:
        # 清除特定模式的缓存
        from app.core.cache import StatisticsCache
        cache = StatisticsCache()
        
        # 清除所有知识统计相关的缓存
        cleared_count = cache.clear_pattern("get_statistics_overview*")
        cleared_count += cache.clear_pattern("get_weekly_trend*")
        cleared_count += cache.clear_pattern("get_hourly_stats*")
        cleared_count += cache.clear_pattern("get_user_stats*")
        cleared_count += cache.clear_pattern("get_popular_questions*")
        cleared_count += cache.clear_pattern("get_satisfaction_stats*")
        cleared_count += cache.clear_pattern("get_monthly_trend*")
        cleared_count += cache.clear_pattern("get_database_health*")
        cleared_count += cache.clear_pattern("get_comprehensive_report*")
        cleared_count += cache.clear_pattern("get_performance_metrics*")
        cleared_count += cache.clear_pattern("get_usage_patterns*")
        cleared_count += cache.clear_pattern("get_content_analysis*")
        cleared_count += cache.clear_pattern("get_user_engagement*")
        cleared_count += cache.clear_pattern("export_statistics_data*")
        
        return {
            "status": "success",
            "message": f"成功清除 {cleared_count} 个知识统计缓存",
            "cleared_count": cleared_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.delete("/cache/clear/{endpoint}")
async def clear_specific_knowledge_cache(endpoint: str):
    """
    清除特定接口的缓存
    
    Args:
        endpoint: 接口名称，如 'get_statistics_overview'
    """
    try:
        invalidate_cache(endpoint)
        return {
            "status": "success",
            "message": f"成功清除接口 {endpoint} 的缓存"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/cache/status")
async def get_knowledge_cache_status():
    """
    获取缓存状态信息
    """
    try:
        from app.core.cache import StatisticsCache
        cache = StatisticsCache()
        
        if not cache.redis_client.is_connected():
            return {
                "status": "disconnected",
                "message": "Redis连接不可用",
                "cache_enabled": False
            }
        
        # 获取缓存键信息
        keys = cache.redis_client.client.keys(f"{cache.cache_prefix}*")
        knowledge_keys = [key for key in keys if any(pattern in key for pattern in [
            "get_statistics_overview", "get_weekly_trend", "get_hourly_stats", 
            "get_user_stats", "get_popular_questions", "get_satisfaction_stats",
            "get_monthly_trend", "get_database_health", "get_comprehensive_report",
            "get_performance_metrics", "get_usage_patterns", "get_content_analysis",
            "get_user_engagement", "export_statistics_data"
        ])]
        
        return {
            "status": "connected",
            "message": "缓存系统正常运行",
            "cache_enabled": True,
            "total_cached_keys": len(keys),
            "knowledge_cached_keys": len(knowledge_keys),
            "cache_prefix": cache.cache_prefix
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取缓存状态失败: {str(e)}",
            "cache_enabled": False
        } 