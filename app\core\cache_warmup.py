"""
Redis缓存预热模块
在应用启动时预加载热点数据到缓存中，提升用户首次访问体验
"""
import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from app.core.cache import StatisticsCache
from config.cache_config import redis_config
from utils.logger import get_logger
import traceback

logger = get_logger()


class CacheWarmupTask:
    """单个缓存预热任务"""
    
    def __init__(self, name: str, endpoint: str, cache_type: str, 
                 data_func: Callable, params: Dict = None, priority: int = 1):
        """
        初始化预热任务
        
        Args:
            name: 任务名称
            endpoint: 缓存端点名称
            cache_type: 缓存类型
            data_func: 获取数据的函数
            params: 缓存参数
            priority: 优先级 (1-5, 1为最高优先级)
        """
        self.name = name
        self.endpoint = endpoint
        self.cache_type = cache_type
        self.data_func = data_func
        self.params = params or {}
        self.priority = priority
        self.status = "pending"  # pending, running, completed, failed
        self.error_message = None
        self.start_time = None
        self.end_time = None
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        return self.priority < other.priority


class CacheWarmupManager:
    """缓存预热管理器"""
    
    def __init__(self):
        self.cache = StatisticsCache()
        self.warmup_tasks: List[CacheWarmupTask] = []
        self.completed_tasks: List[CacheWarmupTask] = []
        self.failed_tasks: List[CacheWarmupTask] = []
        self.total_tasks = 0
        self.completed_count = 0
        self.failed_count = 0
        self.start_time = None
        self.end_time = None
    
    def add_task(self, task: CacheWarmupTask):
        """添加预热任务"""
        self.warmup_tasks.append(task)
        self.total_tasks += 1
        logger.debug(f"添加缓存预热任务: {task.name}")
    
    def add_database_stats_tasks(self):
        """添加数据库统计相关的预热任务"""
        
        # 1. 仪表板概览 (高优先级)
        self.add_task(CacheWarmupTask(
            name="数据库仪表板概览",
            endpoint="get_dashboard_overview",
            cache_type="overview",
            data_func=self._get_dashboard_overview_data,
            priority=1
        ))
        
        # 2. 基础概览统计 (高优先级)
        self.add_task(CacheWarmupTask(
            name="数据库基础概览",
            endpoint="get_database_overview",
            cache_type="overview",
            data_func=self._get_database_overview_data,
            priority=1
        ))
        
        # 3. 图表数据 (中优先级)
        self.add_task(CacheWarmupTask(
            name="关系分布图表数据",
            endpoint="get_chart_distribution_data",
            cache_type="chart",
            data_func=self._get_chart_distribution_data,
            priority=2
        ))
        
        self.add_task(CacheWarmupTask(
            name="数据库类型分布图表",
            endpoint="get_database_type_chart_data",
            cache_type="chart",
            data_func=self._get_database_type_chart_data,
            priority=2
        ))
        
        # 4. 分布统计 (中优先级)
        self.add_task(CacheWarmupTask(
            name="按数据源分布统计",
            endpoint="get_distribution_by_datasource",
            cache_type="distribution",
            data_func=self._get_distribution_by_datasource_data,
            priority=3
        ))
        
        self.add_task(CacheWarmupTask(
            name="按数据库类型分布统计",
            endpoint="get_distribution_by_database_type",
            cache_type="distribution",
            data_func=self._get_distribution_by_database_type_data,
            priority=3
        ))
        
        # 5. 趋势分析 (低优先级)
        self.add_task(CacheWarmupTask(
            name="30天数据库趋势分析",
            endpoint="get_database_trend_analysis",
            cache_type="trend",
            data_func=self._get_database_trend_analysis_data,
            params={"days": 30},
            priority=4
        ))
    
    def add_knowledge_stats_tasks(self):
        """添加知识统计相关的预热任务"""
        
        # 1. 统计概览 (高优先级)
        self.add_task(CacheWarmupTask(
            name="知识统计概览",
            endpoint="get_statistics_overview",
            cache_type="overview",
            data_func=self._get_statistics_overview_data,
            priority=1
        ))
        
        # 2. 周趋势 (高优先级)
        self.add_task(CacheWarmupTask(
            name="知识统计周趋势",
            endpoint="get_weekly_trend",
            cache_type="trend",
            data_func=self._get_weekly_trend_data,
            priority=1
        ))
        
        # 3. 用户统计 (中优先级)
        self.add_task(CacheWarmupTask(
            name="用户统计数据",
            endpoint="get_user_stats",
            cache_type="overview",
            data_func=self._get_user_stats_data,
            priority=2
        ))
        
        # 4. 满意度统计 (中优先级)
        self.add_task(CacheWarmupTask(
            name="满意度统计数据",
            endpoint="get_satisfaction_stats",
            cache_type="overview",
            data_func=self._get_satisfaction_stats_data,
            priority=2
        ))
        
        # 5. 小时统计 (中优先级)
        self.add_task(CacheWarmupTask(
            name="小时分布统计",
            endpoint="get_hourly_stats",
            cache_type="chart",
            data_func=self._get_hourly_stats_data,
            priority=3
        ))
        
        # 6. 热门问题 (低优先级)
        self.add_task(CacheWarmupTask(
            name="热门问题统计",
            endpoint="get_popular_questions",
            cache_type="distribution",
            data_func=self._get_popular_questions_data,
            priority=4
        ))
        
        # 7. 综合报告 (低优先级)
        self.add_task(CacheWarmupTask(
            name="30天综合报告",
            endpoint="get_comprehensive_report",
            cache_type="detailed",
            data_func=self._get_comprehensive_report_data,
            params={"days": 30},
            priority=5
        ))
    
    async def execute_task(self, task: CacheWarmupTask) -> bool:
        """执行单个预热任务"""
        task.status = "running"
        task.start_time = datetime.now()
        
        try:
            logger.info(f"开始执行缓存预热任务: {task.name}")
            
            # 执行数据获取函数
            if asyncio.iscoroutinefunction(task.data_func):
                data = await task.data_func()
            else:
                data = task.data_func()
            
            # 获取缓存过期时间
            expire_seconds = redis_config.get_cache_time_by_type(task.cache_type)
            
            # 设置缓存
            success = self.cache.set(
                endpoint=task.endpoint,
                data=data,
                expire_seconds=expire_seconds,
                params=task.params
            )
            
            if success:
                task.status = "completed"
                task.end_time = datetime.now()
                duration = (task.end_time - task.start_time).total_seconds()
                logger.info(f"缓存预热任务完成: {task.name} (耗时: {duration:.2f}秒)")
                return True
            else:
                task.status = "failed"
                task.error_message = "缓存设置失败"
                logger.warning(f"缓存预热任务失败: {task.name} - 缓存设置失败")
                return False
                
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            task.end_time = datetime.now()
            logger.error(f"缓存预热任务异常: {task.name} - {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def run_warmup(self, max_concurrent: int = 3) -> Dict[str, Any]:
        """
        运行缓存预热
        
        Args:
            max_concurrent: 最大并发任务数
            
        Returns:
            Dict: 预热结果统计
        """
        if not redis_config.is_cache_enabled():
            logger.warning("缓存未启用，跳过缓存预热")
            return {
                "status": "skipped",
                "reason": "cache_disabled",
                "message": "缓存功能未启用"
            }
        
        if not self.cache.redis_client.is_connected():
            logger.warning("Redis连接不可用，跳过缓存预热")
            return {
                "status": "skipped",
                "reason": "redis_disconnected",
                "message": "Redis连接不可用"
            }
        
        self.start_time = datetime.now()
        logger.info(f"开始缓存预热，共 {self.total_tasks} 个任务，最大并发: {max_concurrent}")
        
        # 按优先级排序任务
        self.warmup_tasks.sort()
        
        # 使用信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_semaphore(task: CacheWarmupTask):
            async with semaphore:
                success = await self.execute_task(task)
                if success:
                    self.completed_tasks.append(task)
                    self.completed_count += 1
                else:
                    self.failed_tasks.append(task)
                    self.failed_count += 1
        
        # 并发执行所有任务
        tasks = [execute_with_semaphore(task) for task in self.warmup_tasks]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        result = {
            "status": "completed",
            "total_tasks": self.total_tasks,
            "completed_count": self.completed_count,
            "failed_count": self.failed_count,
            "success_rate": (self.completed_count / self.total_tasks * 100) if self.total_tasks > 0 else 0,
            "total_duration_seconds": round(duration, 2),
            "failed_tasks": [
                {
                    "name": task.name,
                    "endpoint": task.endpoint,
                    "error": task.error_message
                }
                for task in self.failed_tasks
            ]
        }
        
        logger.info(
            f"缓存预热完成: 成功 {self.completed_count}/{self.total_tasks} 个任务, "
            f"耗时 {duration:.2f}秒, 成功率 {result['success_rate']:.1f}%"
        )
        
        return result
    
    # ========== 数据获取函数（使用Service层）==========
    
    async def _get_dashboard_overview_data(self) -> Dict[str, Any]:
        """获取数据库仪表板概览数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_dashboard_overview_data()
    
    async def _get_database_overview_data(self) -> Dict[str, Any]:
        """获取数据库基础概览数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_database_overview_data()
    
    async def _get_chart_distribution_data(self) -> Dict[str, Any]:
        """获取关系分布图表数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_chart_distribution_data()
    
    async def _get_database_type_chart_data(self) -> Dict[str, Any]:
        """获取数据库类型分布图表数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_database_type_chart_data()
    
    async def _get_distribution_by_datasource_data(self) -> List[Dict[str, Any]]:
        """获取按数据源分布的数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_distribution_by_datasource_data()
    
    async def _get_distribution_by_database_type_data(self) -> List[Dict[str, Any]]:
        """获取按数据库类型分布的数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_distribution_by_database_type_data()
    
    async def _get_database_trend_analysis_data(self, days: int = 30) -> Dict[str, Any]:
        """获取数据库趋势分析数据"""
        from app.services.database_statistics_service import DatabaseStatisticsService
        service = DatabaseStatisticsService()
        return await service.get_database_trend_analysis_data(days)
    
    # ========== 知识统计数据获取函数（使用Service层）==========
    
    async def _get_statistics_overview_data(self) -> Dict[str, Any]:
        """获取知识统计概览数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_statistics_overview_data()
    
    async def _get_weekly_trend_data(self) -> Dict[str, Any]:
        """获取周趋势数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_weekly_trend_data()
    
    async def _get_user_stats_data(self) -> Dict[str, Any]:
        """获取用户统计数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_user_stats_data()
    
    async def _get_satisfaction_stats_data(self) -> Dict[str, Any]:
        """获取满意度统计数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_satisfaction_stats_data()
    
    async def _get_hourly_stats_data(self) -> List[Dict[str, Any]]:
        """获取小时统计数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_hourly_stats_data()
    
    async def _get_popular_questions_data(self) -> List[Dict[str, Any]]:
        """获取热门问题数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_popular_questions_data()
    
    async def _get_comprehensive_report_data(self, days: int = 30) -> Dict[str, Any]:
        """获取综合报告数据"""
        from app.services.knowledge_statistics_service import StatisticsService
        service = StatisticsService()
        return await service.get_comprehensive_report_data(days)


async def run_cache_warmup() -> Dict[str, Any]:
    """
    运行缓存预热的主函数
    
    Returns:
        Dict: 预热结果
    """
    manager = CacheWarmupManager()
    
    # 添加数据库统计预热任务
    manager.add_database_stats_tasks()
    
    # 添加知识统计预热任务
    manager.add_knowledge_stats_tasks()
    
    # 执行预热
    result = await manager.run_warmup(max_concurrent=3)
    
    return result


async def scheduled_cache_warmup():
    """
    定时缓存预热任务（可用于定期刷新缓存）
    """
    try:
        logger.info("开始定时缓存预热")
        result = await run_cache_warmup()
        logger.info(f"定时缓存预热完成: {result}")
    except Exception as e:
        logger.error(f"定时缓存预热失败: {e}")
        logger.error(traceback.format_exc())


# 检查缓存预热是否需要执行
def should_run_warmup() -> bool:
    """
    检查是否应该运行缓存预热
    """
    # 检查缓存是否启用
    if not redis_config.is_cache_enabled():
        return False
    
    # 检查Redis连接
    cache = StatisticsCache()
    if not cache.redis_client.is_connected():
        return False
    
    return True


if __name__ == "__main__":
    # 测试缓存预热
    async def test_warmup():
        result = await run_cache_warmup()
        print(f"缓存预热结果: {result}")
    
    asyncio.run(test_warmup()) 