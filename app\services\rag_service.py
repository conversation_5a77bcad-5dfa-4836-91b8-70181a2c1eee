"""
RAG 服务层
提供文档嵌入、向量检索、流式查询等业务逻辑
"""
import os
import asyncio
import json
import time
import pickle
import numpy as np
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator, Tu<PERSON>
from pymilvus import MilvusClient

# 添加LlamaIndex相关导入
from llama_index.core.chat_engine.types import ChatMode

from app.message.myredis import RedisClient, RedisPublisher
from dao.knowledge_utils import get_file_by_id
from rag.base_rag import RAG
from rag.multimodal_rag import MultiModalRAG
from utils.rag_utils import calculate_file_id
from config import settings
from utils.r import R
import utils.ui_utils as cl_utils
from utils.logger import get_logger
from utils.file_types import validate_file_type

logger = get_logger()


class RAGService:
    """RAG 服务类"""
    
    def __init__(self):
        self.client = MilvusClient(uri=settings.configuration.milvus_uri)
        self.embedding_tasks = {}
        self.tasks_file = "embedding_tasks.pickle"
        self.thread_pool = ThreadPoolExecutor(max_workers=5)
        
        # 加载已有的任务状态
        self.load_tasks()
    
    def load_tasks(self):
        """加载已有的任务状态"""
        try:
            if os.path.exists(self.tasks_file):
                with open(self.tasks_file, "rb") as f:
                    self.embedding_tasks = pickle.load(f)
                    logger.info(f"已加载 {len(self.embedding_tasks)} 个嵌入任务状态")
        except Exception as e:
            logger.error(f"加载任务状态失败: {e}")
            self.embedding_tasks = {}
    
    def save_tasks(self):
        """保存任务状态"""
        try:
            with open(self.tasks_file, "wb") as f:
                pickle.dump(self.embedding_tasks, f)
            logger.info(f"已保存 {len(self.embedding_tasks)} 个嵌入任务状态")
        except Exception as e:
            logger.error(f"保存任务状态失败: {e}")
    
    def format_sse_message(self, data: dict, event: str = "message") -> str:
        """格式化SSE消息"""
        json_data = json.dumps(data, ensure_ascii=False)
        return f"event: {event}\ndata: {json_data}\n\n"
    
    def process_response(self, response: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理查询结果，将NumPy类型转换为Python原生类型"""
        if not response:
            return []
        
        # 自定义JSON编码器处理NumPy类型
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                if isinstance(obj, np.float32):
                    return float(obj)
                if isinstance(obj, np.int64):
                    return int(obj)
                return super().default(obj)
        
        # 使用自定义编码器处理NumPy类型
        json_str = json.dumps(response, cls=NumpyEncoder)
        return json.loads(json_str)
    
    async def stream_rag_query(
        self, 
        question: str, 
        collection_name: str, 
        top_k: int, 
        use_hybrid: bool, 
        use_rerank: bool,
        chat_history: Optional[List[Dict[str, str]]] = None
    ) -> AsyncGenerator[str, None]:
        """
        流式RAG查询生成器，支持持续对话
        
        Args:
            question: 用户问题
            collection_name: 集合名称
            top_k: 检索数量
            use_hybrid: 是否使用混合检索
            use_rerank: 是否使用重排序
            chat_history: 对话历史，格式为[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
        """
        try:
            # 使用local_bge_large_embed_model
            embed_model = settings.local_bge_large_embed_model()
            
            # 参考base_rag的load_index方法逻辑
            index = await RAG.load_index(collection_name=collection_name, embed_model=embed_model)
            
            # 准备节点后处理器
            node_postprocessors = []
            if use_rerank:
                node_postprocessors.append(settings.rag_rerank(top_n=top_k))
            
            # 创建聊天引擎 - 使用支持对话历史的模式
            system_prompt = """
# AI助手指令集：系统操作专家

## 1. 角色与知识背景
你是一个 "宇擎智库" 系统的顶级操作专家。你完全掌握该系统的所有功能、操作流程以及每一个功能的菜单路径。

## 2. 核心指令与格式要求
你的核心任务是根据上下文为用户提供准确的系统操作指导。在你的所有回答中，只要涉及到引导用户到某个具体功能的步骤，你都 **必须** 使用指定的组合格式来展示菜单路径和跳转链接。

## 3. 路径与链接输出格式
所有功能位置的指引都必须严格遵循以下格式进行输出，以便用户能够清晰地识别路径并快速访问。

**格式模板**：
【路径】: 顶级菜单 → 子菜单 → ... → 最终操作/页面
【链接】: [此处填写直接跳转链接]

### 格式说明：
- **【路径】**: 必须包含。以 `【路径】:` 作为固定开头，层级之间用 `→` 分隔。
- **【链接】】**: 如果存在，必须包含。以 `【链接】:` 作为固定开头。

## 4. 示例
**场景**: 用户询问"数据源管理"功能在哪里。

**你的标准回答**:
你好，'数据源管理' 功能位于 **'数据库管理'** 模块下。

你可以按照以下路径找到它：
> **【路径】**: `AI后台管理` → `数据库管理` → `数据源管理`

如果想直接跳转，请点击以下链接：
> **【链接】**:  `/text2sql/dbmanage/pro/datasource`

## 5. 约束与行为
* **准确性优先**: 永远根据上下文提供最准确的路径和链接。
* **未知信息处理**: 如果你不知道某个功能的路径或链接，请诚实地说明。例如："我已记录该功能的路径，但暂时没有它的直接跳转链接。" **绝不** 能编造路径或链接。
* **格式一致性**: 任何情况下都不要偏离指定的【路径】和【链接】格式。
* **专注本职**: 你的回答应该聚焦于解决用户的系统操作问题。
"""

            if use_hybrid:
                # 使用混合检索模式，支持对话历史
                chat_engine = index.as_chat_engine(
                    chat_mode=ChatMode.CONTEXT,
                    similarity_top_k=top_k,
                    vector_store_query_mode="hybrid",
                    node_postprocessors=node_postprocessors if node_postprocessors else None,
                    streaming=True,
                    system_prompt=system_prompt
                )
            else:
                # 使用向量检索模式，支持对话历史
                chat_engine = index.as_chat_engine(
                    chat_mode=ChatMode.CONTEXT,
                    similarity_top_k=top_k,
                    node_postprocessors=node_postprocessors if node_postprocessors else None,
                    streaming=True,
                    system_prompt=system_prompt
                )
            
            # 如果提供了对话历史，添加到聊天引擎
            if chat_history:
                from llama_index.core.base.llms.types import ChatMessage
                for msg in chat_history:
                    role = msg.get("role", "user")
                    content = msg.get("content", "")
                    if role and content:
                        chat_engine.chat_history.append(
                            ChatMessage(role=role, content=content)
                        )
                logger.info(f"已加载 {len(chat_history)} 条对话历史")
            
            # 使用astream_chat进行异步流式聊天
            streaming_response = await chat_engine.astream_chat(question)
            
            # 异步流式返回聊天结果
            # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
            ori_accumulated_content = ""  # 用于累积内容，以便正确处理跨token的<think>标签
            act_accumulated_content = ""
            is_in_think_tag = False  # 标记是否在<think>标签内
            
            async for token in streaming_response.async_response_gen():
                # 累积token内容
                ori_accumulated_content += token
                
                # 检查是否包含<think>或</think>标签
                if '<think>' in token.lower():
                    is_in_think_tag = True
                if '</think>' in token.lower():
                    is_in_think_tag = False
                    # 跳过这个token，不发送
                    continue
                act_accumulated_content+= token
                # 如果不在<think>标签内，则发送token
                if not is_in_think_tag:
                    yield self.format_sse_message({
                        "type": "token",
                        "content": token,
                        "timestamp": time.time()
                    })
                
                # 添加小延迟确保流式效果，避免客户端缓冲
                await asyncio.sleep(0.01)  # 10ms延迟，可根据需要调整
            
            # 提取图片信息
            source_images = []
            if hasattr(streaming_response, 'source_nodes') and streaming_response.source_nodes:
                for source_node in streaming_response.source_nodes:
                    if hasattr(source_node, 'metadata') and source_node.metadata:
                        # 检查是否是图片类型
                        if source_node.metadata.get("type") == "image":
                            image_info = {
                                "url": source_node.metadata.get("image", ""),
                                "name": source_node.metadata.get("source", ""),
                                "alt": source_node.metadata.get("source", "图片")
                            }
                            if image_info["url"]:  # 确保有有效的URL
                                source_images.append(image_info)
            
            # 发送完成消息，包含完整的对话历史和图片信息
            final_chat_history = []
            if chat_engine.chat_history:
                for msg in chat_engine.chat_history:
                    final_chat_history.append({
                        "role": msg.role,
                        "content": msg.content
                    })
            
            yield self.format_sse_message({
                "type": "complete",
                "message": "对话完成",
                "question": question,
                "answer": act_accumulated_content,
                "collection_name": collection_name,
                "chat_history": final_chat_history,
                "source_images": source_images,  # 添加图片信息
                "timestamp": time.time()
            })
            
        except Exception as e:
            # 发送错误消息
            logger.error(f"RAG流式查询出错: {str(e)}")
            yield self.format_sse_message({
                "type": "error",
                "message": f"处理查询时出错: {str(e)}",
                "error_details": str(e)
            })
    
    async def embed_document_task(self, rag, collection_name: str, doc_id: Optional[str] = None):
        """异步执行文档嵌入任务"""
        if doc_id:
            # 更新任务状态为处理中
            self.embedding_tasks[doc_id] = {
                "status": "processing",
                "start_time": time.time(),
                "message": "文档嵌入处理中"
            }
            self.save_tasks()
        
        try:
            # 使用线程池执行CPU密集型的嵌入操作
            loop = asyncio.get_running_loop()
            # 注意：在线程池中执行的方法必须是同步方法
            await loop.run_in_executor(self.thread_pool, lambda: rag.create_index_sync(collection_name))
            
            # 尝试创建Redis客户端发送完成通知
            try:
                # 创建Redis客户端发送完成通知
                redis_client = RedisClient(
                    host='localhost',
                    port=6379,
                    db=0,
                    password='123456'
                )
                
                # 创建发布者
                publisher = RedisPublisher(redis_client)
                
                # 发布频道名称
                channel = "embedding"
                
                # 发布JSON消息
                data = {
                    "docId": doc_id,
                    "message": "文档嵌入成功",
                    "timestamp": time.time()
                }
                receivers = publisher.publish_dict(channel, data)
                logger.info(f"发布JSON消息{data}到'{channel}'频道，{receivers}个订阅者接收")
            except Exception as redis_err:
                # Redis连接失败不应该影响整个嵌入任务的成功状态
                logger.error(f"Redis通知发送失败: {redis_err}")
            
            if doc_id:
                # 更新任务状态为完成
                self.embedding_tasks[doc_id] = {
                    "status": "completed",
                    "start_time": self.embedding_tasks[doc_id]["start_time"],
                    "end_time": time.time(),
                    "message": "文档嵌入成功"
                }
                self.save_tasks()
                
        except Exception as e:
            logger.error(f"嵌入运行出错: {e}")
            if doc_id:
                # 更新任务状态为失败
                self.embedding_tasks[doc_id] = {
                    "status": "failed",
                    "start_time": self.embedding_tasks[doc_id]["start_time"],
                    "end_time": time.time(),
                    "message": f"嵌入失败: {str(e)}"
                }
                self.save_tasks()
    
    async def delete_document_and_related_data(self, collection_name: str, file_name: str) -> Tuple[bool, str, int]:
        """
        删除文档及其相关数据的公共方法
        
        Args:
            collection_name: 集合名称
            file_name: 文件名
            
        Returns:
            tuple: (是否成功, 消息, 删除的实体数量)
        """
        try:
            # 检查集合是否存在
            if collection_name not in self.client.list_collections():
                return False, f"集合 {collection_name} 不存在", 0

            # 构建查询条件
            filter_expr = f"file_name == '{file_name}'"

            # 首先查询符合条件的文档数量和ID
            response = self.client.query(
                collection_name=collection_name,
                filter=filter_expr,
                output_fields=["id"],
                limit=10000
            )

            if not response:
                return False, f"未找到文件名为 {file_name} 的文档", 0
            count = len(response)

            # 从响应中解析出关联的分块ID
            chunk_ids = []
            for item in response:
                if "id" in item:
                    chunk_id = item.get("id")
                    if chunk_id and chunk_id not in chunk_ids:
                        chunk_ids.append(chunk_id)
            logger.info(f"为文件 {file_name} 找到 {len(chunk_ids)} 个分块ID")

            # 删除向量数据库文档
            self.client.delete(
                collection_name=collection_name,
                filter=filter_expr
            )

            # 根据分块ID删除图谱数据
            if chunk_ids:
                try:
                    # 导入知识图谱管理器
                    from utils.kg_utils import KnowledgeGraphManager

                    # 获取知识图谱管理器实例
                    kg_manager = KnowledgeGraphManager.get_instance()

                    # 调用方法删除或更新图谱数据
                    kg_result = await kg_manager.delete_by_document_chunks(
                        file_name=file_name,
                        chunk_ids=chunk_ids,
                        collection_name=collection_name
                    )

                    logger.info(f"图谱数据处理结果: {kg_result}")

                    # 从实体向量集合中删除相关的实体数据
                    try:
                        # 根据collection_name和file_id删除实体向量
                        entity_collections = [
                            settings.configuration.kg_entity_collection,
                            settings.configuration.kg_relation_collection,
                            settings.configuration.kg_contextual_entity_collection,
                        ]

                        for entity_collection in entity_collections:
                            full_collection_name = f"{settings.configuration.kg_milvus_collection_prefix}{entity_collection}_{collection_name}"

                            # 检查集合是否存在
                            if full_collection_name in self.client.list_collections():
                                file_id = calculate_file_id(file_name)
                                file_id_expr = f"file_id == '{file_id}'"
                                # 删除符合条件的实体
                                delete_result = self.client.delete(
                                    collection_name=full_collection_name,
                                    filter=file_id_expr
                                )
                                logger.info(f"已从 {full_collection_name} 删除相关实体向量，删除结果: {delete_result}")
                    except Exception as vector_error:
                        logger.error(f"删除实体向量数据时出错: {vector_error}")
                        # 继续执行，不影响主流程

                except Exception as kg_error:
                    logger.error(f"处理图谱数据时出错: {kg_error}")
                    # 继续执行，不影响主流程

            return True, f"已删除文件 {file_name} 相关的 {count} 个实体", count
        except Exception as e:
            logger.error(f"删除文档时发生错误: {str(e)}")
            return False, f"删除文档失败: {str(e)}", 0
    
    async def upload_files_by_docid(
        self,
        collection_name: str,
        use_kg: bool,
        doc_id: str,
        embedding_model: str,
        background_task_func: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        根据文档ID上传文件进行嵌入，默认使用多模态模式
        """
        file_list = []

        # 检查是否已存在处理中的任务
        existing_task = self.embedding_tasks.get(doc_id)
        if existing_task and existing_task.get("status") == "processing":
            logger.info(f"该文档已有嵌入任务正在处理中，开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(existing_task['start_time']))}")

        # 处理doc_id指定的文件
        file_meta = await get_file_by_id(doc_id)
        # str转json
        file_meta = json.loads(file_meta)
        # 判断file_path是远程文件还是本地文件
        file_ref_path = file_meta.get("filePath")

        if RAG.is_remote_file(file_ref_path):
            file_path = file_ref_path
        else:
            file_path = "D:\\jeecgbootai\\upFiles" + file_ref_path
        # 将file_path添加到file_list中
        file_list.append(file_path)
        # 根据文件路径创建rag对象
        if not file_list:
            return {"success": False, "message": "没有有效的文件可处理"}

        # 验证文件类型
        file_name = os.path.basename(file_ref_path)
        try:
            validate_file_type(file_name)
        except Exception as e:
            logger.error(f"文件类型验证失败: {str(e)}")
            return {"success": False, "message": str(e)}

        # 根据传入的嵌入模型名称，选择使用哪个模型进行嵌入
        selected_embed_model = cl_utils.get_embed_model_by_name(embedding_model)

        rag = MultiModalRAG(files=file_list, use_kg=use_kg, embed_model=selected_embed_model)

        # 实测该方法适用于本地以及远程文件（包含文件后缀名）
        file_name = os.path.basename(file_ref_path)
        logger.info("嵌入文档前先删除旧文档")
        # 使用公共方法删除文档及相关数据
        success, message, _ = await self.delete_document_and_related_data(collection_name, file_name)
        if not success:
            logger.warning(f"删除旧文档失败: {message}，但将继续执行嵌入任务")
        
        # 无论删除操作是否成功，都执行嵌入任务
        if background_task_func:
            background_task_func(self.embed_document_task, rag, collection_name, doc_id)
        else:
            asyncio.create_task(self.embed_document_task(rag, collection_name, doc_id))
        
        return {"success": True, "message": "嵌入任务已提交，正在后台处理中"}
    
    async def upload_files(
        self,
        files: List[Any],
        collection_name: str = "default",
        doc_id: str = "",
        background_task_func: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        上传文件进行嵌入，默认使用多模态模式
        """
        file_list = []
        
        # 如果提供了docId，检查是否已存在处理中的任务
        if doc_id:
            existing_task = self.embedding_tasks.get(doc_id)
            if existing_task and existing_task.get("status") == "processing":
                return {
                    "success": True, 
                    "message": f"该文档已有嵌入任务正在处理中，开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(existing_task['start_time']))}"
                }
        
        for file in files:
            file_path = os.path.join("documents", os.path.basename(file.filename))
            with open(file_path, "wb+") as f:
                f.write(await file.read())
                file_list.append(file_path)
        
        rag = MultiModalRAG(files=file_list)
        
        # 使用任务队列和线程池进行处理
        if background_task_func:
            background_task_func(self.embed_document_task, rag, collection_name, doc_id)
        else:
            asyncio.create_task(self.embed_document_task(rag, collection_name, doc_id))
        
        return {"success": True, "message": "嵌入任务已提交，正在后台处理中"}
    
    async def get_embedding_status(self, doc_id: str) -> Dict[str, Any]:
        """获取文档嵌入状态"""
        if not doc_id:
            return {"success": False, "message": "请提供有效的文档ID"}
        
        # 获取任务状态
        task_info = self.embedding_tasks.get(doc_id)
        
        if not task_info:
            return {"success": False, "message": f"未找到文档ID为{doc_id}的嵌入任务"}
        
        return {"success": True, "data": task_info}
    
    async def get_all_embedding_tasks(self) -> Dict[str, Any]:
        """获取所有文档嵌入任务的状态"""
        return {"success": True, "data": self.embedding_tasks}
    
    async def clear_embedding_task(self, doc_id: str) -> Dict[str, Any]:
        """清除指定的嵌入任务记录"""
        if doc_id in self.embedding_tasks:
            del self.embedding_tasks[doc_id]
            self.save_tasks()
            return {"success": True, "message": f"已清除文档ID为{doc_id}的嵌入任务记录"}
        return {"success": False, "message": f"未找到文档ID为{doc_id}的嵌入任务记录"}
    
    async def clear_completed_tasks(self) -> Dict[str, Any]:
        """清除所有已完成或失败的任务"""
        count = 0
        for doc_id in list(self.embedding_tasks.keys()):
            if self.embedding_tasks[doc_id]["status"] in ["completed", "failed"]:
                del self.embedding_tasks[doc_id]
                count += 1
        
        self.save_tasks()
        return {"success": True, "message": f"已清除{count}个已完成或失败的任务记录"}
    
    async def list_collections(self, identifier: str = "admin") -> Dict[str, Any]:
        """获取用户可访问的知识库集合列表"""
        try:
            # 使用ui_utils中的逻辑获取用户可访问的集合
            collections_dict = await cl_utils.list_collections(identifier)
            return {"success": True, "data": collections_dict}
        except Exception as e:
            logger.error(f"获取集合列表时发生错误: {str(e)}")
            return {"success": False, "message": f"获取集合列表失败: {str(e)}"}
    
    async def delete_collection(self, collection_name: str) -> Dict[str, Any]:
        """删除指定的Milvus集合"""
        try:
            if collection_name in self.client.list_collections():
                self.client.drop_collection(collection_name)
                # 删除跟集合相关的图谱数据
                entity_collections = [
                    settings.configuration.kg_entity_collection,
                    settings.configuration.kg_relation_collection,
                    settings.configuration.kg_contextual_entity_collection,
                ]
                
                for entity_collection in entity_collections:
                    full_collection_name = f"{settings.configuration.kg_milvus_collection_prefix}{entity_collection}_{collection_name}"
                    if full_collection_name in self.client.list_collections():
                        self.client.drop_collection(full_collection_name)
                
                # 删除跟集合相关的知识图谱
                from utils.kg_utils import KnowledgeGraphManager
                
                # 获取知识图谱管理器实例
                kg_manager = KnowledgeGraphManager.get_instance()
                
                # 异步删除与collection_name相关的所有图谱数据
                try:
                    # 创建异步任务
                    delete_result = await kg_manager.delete_by_collection_name(collection_name)
                    logger.info(f"知识图谱删除结果: {delete_result}")
                except Exception as kg_error:
                    logger.error(f"删除知识图谱数据时出错: {kg_error}")
                    # 继续执行，不影响主流程

                return {"success": True, "message": f"集合 {collection_name} 已删除"}
            return {"success": False, "message": f"集合 {collection_name} 不存在"}
        except Exception as e:
            logger.error(f"删除集合时发生错误: {str(e)}")
            return {"success": False, "message": f"删除集合失败: {str(e)}"}
    
    async def query_documents(self, collection_name: str, file_name: Optional[str] = None) -> Dict[str, Any]:
        """查询文档实体，可以通过file_name过滤"""
        try:
            # 检查集合是否存在
            if collection_name not in self.client.list_collections():
                return {"success": False, "message": f"集合 {collection_name} 不存在"}
            
            # 如果提供了file_name，则按file_name过滤
            if file_name:
                filter_expr = f"file_name == '{file_name}'"
            else:
                filter_expr = ""
            
            # 执行查询
            response = self.client.query(
                collection_name=collection_name,
                filter=filter_expr,
                output_fields=["id", "file_name"],
                limit=10000
            )
            
            # 处理响应数据，转换NumPy类型
            processed_response = self.process_response(response)
            
            return {"success": True, "data": processed_response}
        except Exception as e:
            logger.error(f"查询文档时发生错误: {str(e)}")
            return {"success": False, "message": f"查询文档失败: {str(e)}"}
    
    async def delete_document(self, collection_name: str, file_name: str) -> Dict[str, Any]:
        """删除文档实体，根据file_name"""
        if not file_name:
            return {"success": False, "message": "必须提供file_name参数"}
        
        # 使用公共方法删除文档及相关数据
        success, message, _ = await self.delete_document_and_related_data(collection_name, file_name)
        return {"success": success, "message": message}
    
    async def qa_test(self, question: str) -> Dict[str, Any]:
        """QA测试接口"""
        try:
            index = await RAG.load_index(collection_name="default")
            # 开启混合检索
            retriever = index.as_retriever(similarity_top_k=5, vector_store_query_mode="hybrid")
            nodes = retriever.retrieve(question)
            column = [node.text for node in nodes]
            return {"success": True, "data": column}
        except Exception as e:
            logger.error(f"QA测试失败: {str(e)}")
            return {"success": False, "message": f"QA测试失败: {str(e)}"}
    
    async def get_embedding_text(self, query: str):
        """获取文本的嵌入向量"""
        try:
            embedder = settings.local_bge_large_embed_model()
            result_embedding = embedder.get_text_embedding(query)
            return result_embedding
        except Exception as e:
            logger.error(f"获取文本嵌入失败: {str(e)}")
            raise e

    async def create_collection(self, collection_name: str, embedding_model: str) -> Dict[str, Any]:
        """
        使用MilvusVectorStore创建新的Milvus集合
        
        Args:
            collection_name: 集合名称
            embedding_model: 嵌入模型名称
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            # 检查集合是否已存在
            if collection_name in self.client.list_collections():
                return {"success": False, "message": f"集合 {collection_name} 已存在"}
            
            # 根据嵌入模型名称获取模型实例
            import utils.ui_utils as cl_utils
            selected_embed_model = cl_utils.get_embed_model_by_name(embedding_model)
            
            if not selected_embed_model:
                return {"success": False, "message": f"不支持的嵌入模型: {embedding_model}"}
            
            # 获取嵌入维度
            test_embedding = selected_embed_model.get_text_embedding("test")
            embedding_dim = len(test_embedding)
            
            logger.info(f"开始创建集合: {collection_name}，使用嵌入模型: {embedding_model}，向量维度: {embedding_dim}")
            
            # 使用MilvusVectorStore创建集合
            from llama_index.vector_stores.milvus import MilvusVectorStore
            from utils.CustomizedEmbeddingFunction import CustomizedEmbeddingFunction
            
            # 创建MilvusVectorStore实例，这会自动创建集合
            # 注意：MilvusVectorStore在初始化时会自动创建集合（如果不存在）
            vector_store = MilvusVectorStore(
                uri=settings.configuration.milvus_uri,
                collection_name=collection_name,
                dim=embedding_dim,
                overwrite=False,  # 不覆盖已存在的集合
                # 开启混合检索
                sparse_embedding_function=CustomizedEmbeddingFunction(),
                enable_sparse=True,
            )
            
            # 验证集合是否创建成功
            if collection_name not in self.client.list_collections():
                raise Exception(f"集合 {collection_name} 创建失败")
                
            logger.info(f"集合 {collection_name} 创建成功，已具备完整的向量检索功能")
            
            logger.info(f"成功创建集合: {collection_name}，嵌入维度: {embedding_dim}，嵌入模型: {embedding_model}")
            
            return {
                "success": True, 
                "message": f"集合 {collection_name} 创建成功",
                "data": {
                    "collection_name": collection_name,
                    "embedding_model": embedding_model,
                    "embedding_dim": embedding_dim
                }
            }
            
        except Exception as e:
            logger.error(f"创建集合 {collection_name} 失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {"success": False, "message": f"创建集合失败: {str(e)}"}


# 创建全局服务实例
rag_service = RAGService() 