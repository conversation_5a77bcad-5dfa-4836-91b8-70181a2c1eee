"""
Text2SQL执行过程持久化的数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, Field
from sqlalchemy import (
    Column, String, Text, Integer, BigInteger, DECIMAL, DateTime, 
    Boolean, JSON, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

# 枚举类定义
class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS" 
    FAILED = "FAILED"
    TIMEOUT = "TIMEOUT"

class AgentStatus(str, Enum):
    """智能体状态枚举"""
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"

class ErrorSeverity(str, Enum):
    """错误严重程度枚举"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class MetricCategory(str, Enum):
    """指标分类枚举"""
    PERFORMANCE = "performance"
    RESOURCE = "resource"
    QUALITY = "quality"

# SQLAlchemy数据库模型
class Text2SQLExecution(Base):
    """Text2SQL主执行记录表"""
    __tablename__ = 'text2sql_executions'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    session_id = Column(String(64), nullable=False, comment='会话ID，用于关联前端会话')
    query_id = Column(String(64), nullable=False, unique=True, comment='查询唯一标识，UUID格式')
    user_query = Column(Text, nullable=False, comment='用户原始查询')
    optimized_query = Column(Text, comment='大模型优化后的查询')
    connection_id = Column(String(64), comment='数据库连接ID')
    db_type = Column(String(32), comment='数据库类型：MySQL, PostgreSQL, SQLite等')
    execution_status = Column(String(32), nullable=False, default='RUNNING', comment='执行状态')
    start_time = Column(DateTime, nullable=False, default=datetime.utcnow, comment='开始执行时间')
    end_time = Column(DateTime, comment='结束执行时间')
    total_duration_ms = Column(BigInteger, comment='总执行时长(毫秒)')
    error_message = Column(Text, comment='错误信息')
    final_sql = Column(Text, comment='最终生成的SQL语句')
    sql_explanation = Column(Text, comment='SQL解释')
    query_results_count = Column(Integer, comment='查询结果行数')
    query_results_size_kb = Column(DECIMAL(10, 2), comment='查询结果大小(KB)')
    visualization_recommendation = Column(JSON, comment='可视化推荐结果')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    agent_executions = relationship("Text2SQLAgentExecution", back_populates="execution", cascade="all, delete-orphan")
    stream_messages = relationship("Text2SQLStreamMessage", back_populates="execution", cascade="all, delete-orphan")
    performance_metrics = relationship("Text2SQLPerformanceMetric", back_populates="execution", cascade="all, delete-orphan")
    error_logs = relationship("Text2SQLErrorLog", back_populates="execution", cascade="all, delete-orphan")
    schema_retrievals = relationship("Text2SQLSchemaRetrieval", back_populates="execution", cascade="all, delete-orphan")
    query_results = relationship("Text2SQLQueryResult", back_populates="execution", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_session_id', 'session_id'),
        Index('idx_connection_id', 'connection_id'),
        Index('idx_status_time', 'execution_status', 'start_time'),
        Index('idx_created_at', 'created_at'),
    )

class Text2SQLAgentExecution(Base):
    """智能体执行详情表"""
    __tablename__ = 'text2sql_agent_executions'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    agent_name = Column(String(64), nullable=False, comment='智能体名称')
    agent_display_name = Column(String(128), nullable=False, comment='智能体显示名称')
    execution_order = Column(Integer, nullable=False, comment='执行顺序')
    status = Column(String(32), nullable=False, default='RUNNING', comment='执行状态')
    start_time = Column(DateTime, nullable=False, default=datetime.utcnow, comment='开始执行时间')
    end_time = Column(DateTime, comment='结束执行时间')
    duration_ms = Column(BigInteger, comment='执行时长(毫秒)')
    input_data = Column(JSON, comment='输入数据')
    output_data = Column(JSON, comment='输出数据')
    stream_messages_count = Column(Integer, default=0, comment='流式消息数量')
    error_message = Column(Text, comment='错误信息')
    model_calls_count = Column(Integer, default=0, comment='LLM调用次数')
    model_tokens_used = Column(Integer, default=0, comment='LLM使用的token数量')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="agent_executions")
    stream_messages = relationship("Text2SQLStreamMessage", back_populates="agent_execution", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_agent_name', 'agent_name'),
        Index('idx_execution_order', 'execution_order'),
        Index('idx_status_time', 'status', 'start_time'),
    )

class Text2SQLStreamMessage(Base):
    """流式消息记录表"""
    __tablename__ = 'text2sql_stream_messages'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    agent_execution_id = Column(BigInteger, ForeignKey('text2sql_agent_executions.id', ondelete='CASCADE'), 
                               nullable=False, comment='关联智能体执行ID')
    message_sequence = Column(Integer, nullable=False, comment='消息序号')
    source = Column(String(128), nullable=False, comment='消息来源')
    content = Column(Text, comment='消息内容')
    content_format = Column(String(32), default='markdown', comment='内容格式')
    is_final = Column(Boolean, default=False, comment='是否为最终消息')
    message_size_bytes = Column(Integer, comment='消息大小(字节)')
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow, comment='消息时间戳')

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="stream_messages")
    agent_execution = relationship("Text2SQLAgentExecution", back_populates="stream_messages")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_agent_execution_id', 'agent_execution_id'),
        Index('idx_sequence', 'message_sequence'),
        Index('idx_timestamp', 'timestamp'),
    )

class Text2SQLPerformanceMetric(Base):
    """性能指标统计表"""
    __tablename__ = 'text2sql_performance_metrics'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    metric_name = Column(String(128), nullable=False, comment='指标名称')
    metric_value = Column(DECIMAL(15, 4), comment='指标数值')
    metric_unit = Column(String(32), comment='指标单位')
    metric_category = Column(String(64), comment='指标分类')
    collected_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment='收集时间')

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="performance_metrics")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_metric_name', 'metric_name'),
        Index('idx_category', 'metric_category'),
        Index('idx_collected_at', 'collected_at'),
    )

class Text2SQLErrorLog(Base):
    """错误日志表"""
    __tablename__ = 'text2sql_error_logs'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    agent_name = Column(String(64), comment='出错的智能体名称')
    error_type = Column(String(128), nullable=False, comment='错误类型')
    error_message = Column(Text, nullable=False, comment='错误信息')
    error_stack = Column(Text, comment='错误堆栈')
    error_context = Column(JSON, comment='错误上下文信息')
    severity = Column(String(32), default='ERROR', comment='严重程度')
    occurred_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment='发生时间')

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="error_logs")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_agent_name', 'agent_name'),
        Index('idx_error_type', 'error_type'),
        Index('idx_severity', 'severity'),
        Index('idx_occurred_at', 'occurred_at'),
    )

class Text2SQLSchemaRetrieval(Base):
    """表结构检索记录表"""
    __tablename__ = 'text2sql_schema_retrievals'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    connection_id = Column(String(64), nullable=False, comment='数据库连接ID')
    original_query = Column(Text, nullable=False, comment='原始查询')
    optimized_query = Column(Text, comment='大模型优化后的查询')
    vector_search_results_count = Column(Integer, comment='向量搜索结果数量')
    initial_tables_count = Column(Integer, comment='初始相关表数量')
    final_tables_count = Column(Integer, comment='最终关联表数量')
    kg_query_depth = Column(Integer, comment='知识图谱查询深度')
    schema_info_size_kb = Column(DECIMAL(10, 2), comment='表结构信息大小(KB)')
    execution_time_ms = Column(BigInteger, comment='执行时间(毫秒)')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="schema_retrievals")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_connection_id', 'connection_id'),
        Index('idx_tables_count', 'final_tables_count'),
    )

class Text2SQLQueryResult(Base):
    """SQL执行结果表"""
    __tablename__ = 'text2sql_query_results'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    query_id = Column(String(64), ForeignKey('text2sql_executions.query_id', ondelete='CASCADE'), 
                     nullable=False, comment='关联查询ID')
    sql_statement = Column(Text, nullable=False, comment='执行的SQL语句')
    execution_status = Column(String(32), nullable=False, comment='执行状态')
    results_count = Column(Integer, comment='结果行数')
    results_size_kb = Column(DECIMAL(10, 2), comment='结果大小(KB)')
    execution_time_ms = Column(BigInteger, comment='SQL执行时间(毫秒)')
    columns_info = Column(JSON, comment='列信息')
    sample_data = Column(JSON, comment='样本数据(前几行)')
    error_message = Column(Text, comment='执行错误信息')
    executed_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # 关系
    execution = relationship("Text2SQLExecution", back_populates="query_results")

    # 索引
    __table_args__ = (
        Index('idx_query_id', 'query_id'),
        Index('idx_execution_status', 'execution_status'),
        Index('idx_results_count', 'results_count'),
        Index('idx_executed_at', 'executed_at'),
    )

# Pydantic响应模型
class ExecutionSummary(BaseModel):
    """执行摘要"""
    query_id: str
    session_id: str
    user_query: str
    execution_status: ExecutionStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration_ms: Optional[int] = None
    db_type: Optional[str] = None
    connection_id: Optional[str] = None

class AgentExecutionSummary(BaseModel):
    """智能体执行摘要"""
    agent_name: str
    agent_display_name: str
    execution_order: int
    status: AgentStatus
    duration_ms: Optional[int] = None
    model_calls_count: int = 0
    model_tokens_used: int = 0
    stream_messages_count: int = 0

class PerformanceMetrics(BaseModel):
    """性能指标"""
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_duration_ms: Optional[float] = None
    avg_results_count: Optional[float] = None
    avg_results_size_kb: Optional[float] = None
    success_rate: float = Field(ge=0, le=1)

class AgentPerformanceMetrics(BaseModel):
    """智能体性能指标"""
    agent_name: str
    agent_display_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_duration_ms: Optional[float] = None
    avg_model_calls: Optional[float] = None
    avg_tokens_used: Optional[float] = None
    max_duration_ms: Optional[int] = None
    min_duration_ms: Optional[int] = None
    success_rate: float = Field(ge=0, le=1)

class ExecutionDetail(BaseModel):
    """执行详情"""
    execution: ExecutionSummary
    agents: List[AgentExecutionSummary]
    performance_metrics: List[Dict[str, Any]] = []
    error_logs: List[Dict[str, Any]] = []

class PerformanceOverview(BaseModel):
    """性能概览"""
    execution_date: str
    metrics: PerformanceMetrics
    unique_connections: int
    unique_sessions: int

class Text2SQLAnalytics(BaseModel):
    """Text2SQL分析数据"""
    overview: PerformanceMetrics
    daily_stats: List[PerformanceOverview]
    agent_performance: List[AgentPerformanceMetrics]
    recent_executions: List[ExecutionSummary]
    error_summary: Dict[str, int] 