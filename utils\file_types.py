"""
File type configurations and utilities.
This module defines supported file types and their processing methods.
"""
from enum import Enum
from typing import Dict, List, Set, Optional
from fastapi import UploadFile, HTTPException


class FileProcessingMethod(str, Enum):
    """Enum for file processing methods"""
    PDF_CONVERTER = "pdf_converter"  # Use PdfConverter for complex documents
    MARKDOWN = "markdown"  # Direct processing for markdown files
    TEXT = "text"  # Simple text processing


# Supported file extensions by processing method
SUPPORTED_FILE_TYPES: Dict[FileProcessingMethod, Set[str]] = {
    FileProcessingMethod.PDF_CONVERTER: {
        ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp",  # PDF and images
        ".pptx", ".ppt", ".docx", ".doc", ".xlsx", ".xls",  # Office documents
        ".html", ".epub"  # Web and ebook formats
    },
    FileProcessingMethod.MARKDOWN: {
        ".md", ".markdown"  # Markdown files
    },
    FileProcessingMethod.TEXT: {
        ".txt", ".csv", ".json", ".xml", ".yml", ".yaml",  # Plain text formats
        ".py", ".js", ".java", ".c", ".cpp", ".cs", ".go", ".rs",  # Code files
        ".html", ".css", ".sql", ".sh"  # Other text-based formats
    }
}

# All supported file extensions (flattened)
ALL_SUPPORTED_EXTENSIONS: Set[str] = set()
for extensions in SUPPORTED_FILE_TYPES.values():
    ALL_SUPPORTED_EXTENSIONS.update(extensions)

# API允许的文件类型（更严格的限制）
API_ALLOWED_EXTENSIONS: Set[str] = {
    ".jpg", ".jpeg", ".png",  # 图片
    ".txt", ".md",           # 文本
    ".pdf",                  # PDF
    ".docx", ".doc",         # Word文档
    ".xlsx", ".xls",         # Excel文档
    ".pptx", ".ppt"          # PowerPoint文档
}


def get_processing_method(file_extension: str) -> FileProcessingMethod:
    """
    Determine the processing method for a given file extension.
    
    Args:
        file_extension: The file extension (including the dot)
        
    Returns:
        The appropriate processing method
        
    Raises:
        ValueError: If the file extension is not supported
    """
    file_extension = file_extension.lower()
    
    for method, extensions in SUPPORTED_FILE_TYPES.items():
        if file_extension in extensions:
            return method
            
    raise ValueError(f"Unsupported file extension: {file_extension}")


def is_supported_extension(file_extension: str) -> bool:
    """
    Check if a file extension is supported.
    
    Args:
        file_extension: The file extension (including the dot)
        
    Returns:
        True if supported, False otherwise
    """
    return file_extension.lower() in ALL_SUPPORTED_EXTENSIONS


def is_api_allowed_extension(file_extension: str) -> bool:
    """
    Check if a file extension is allowed by the API endpoints.
    
    Args:
        file_extension: The file extension (including the dot)
        
    Returns:
        True if allowed, False otherwise
    """
    return file_extension.lower() in API_ALLOWED_EXTENSIONS


def get_file_extension(filename: str) -> str:
    """
    Get file extension from filename.
    
    Args:
        filename: The filename
        
    Returns:
        The file extension (including the dot), or empty string if no extension
    """
    import os
    return os.path.splitext(filename.lower())[1]


def validate_file_type(filename: str, raise_exception: bool = True) -> bool:
    """
    Validate if a file type is allowed for API endpoints.
    
    Args:
        filename: The filename to validate
        raise_exception: Whether to raise HTTPException if validation fails
        
    Returns:
        True if valid, False otherwise
        
    Raises:
        HTTPException: If validation fails and raise_exception is True
    """
    if not filename:
        if raise_exception:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        return False
    
    file_extension = get_file_extension(filename)
    
    if not file_extension:
        if raise_exception:
            raise HTTPException(status_code=400, detail=f"文件 '{filename}' 没有文件扩展名")
        return False
    
    if not is_api_allowed_extension(file_extension):
        allowed_types = ", ".join(sorted(API_ALLOWED_EXTENSIONS))
        if raise_exception:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型 '{file_extension}'。支持的文件类型：{allowed_types}"
            )
        return False
    
    return True


def validate_upload_files(files: List[UploadFile], raise_exception: bool = True) -> bool:
    """
    Validate multiple upload files.
    
    Args:
        files: List of upload files to validate
        raise_exception: Whether to raise HTTPException if validation fails
        
    Returns:
        True if all files are valid, False otherwise
        
    Raises:
        HTTPException: If validation fails and raise_exception is True
    """
    if not files:
        if raise_exception:
            raise HTTPException(status_code=400, detail="请选择要上传的文件")
        return False
    
    for file in files:
        if not validate_file_type(file.filename, raise_exception):
            return False
    
    return True


def validate_file_paths(file_paths: List[str], raise_exception: bool = True) -> bool:
    """
    Validate multiple file paths.
    
    Args:
        file_paths: List of file paths to validate
        raise_exception: Whether to raise HTTPException if validation fails
        
    Returns:
        True if all files are valid, False otherwise
        
    Raises:
        HTTPException: If validation fails and raise_exception is True
    """
    import os
    
    if not file_paths:
        return True  # 空列表是有效的
    
    for file_path in file_paths:
        if not file_path:
            if raise_exception:
                raise HTTPException(status_code=400, detail="文件路径不能为空")
            return False
        
        filename = os.path.basename(file_path)
        if not validate_file_type(filename, raise_exception):
            return False
    
    return True


def get_supported_file_types_description() -> str:
    """
    Get a description of supported file types for API documentation.
    
    Returns:
        A string describing supported file types
    """
    return "支持的文件类型：jpg、png、jpeg（图片），txt、md（文本），pdf（PDF文档），docx、doc（Word文档），xlsx、xls（Excel文档），pptx、ppt（PowerPoint文档）"
