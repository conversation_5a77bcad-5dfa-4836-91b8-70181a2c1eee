from typing import List, Optional

from sqlalchemy import DateTime, ForeignKeyConstraint, Index, JSON, String, Text, text
from sqlalchemy.dialects.mysql import INTEGER, TINYINT
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
import datetime

class Base(DeclarativeBase):
    pass


class Text2sqlDatasource(Base):
    __tablename__ = 'text2sql_sys_datasource'
    __table_args__ = (
        Index('uk_sdc_rule_code', 'code', unique=True),
    )

    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    code: Mapped[str] = mapped_column(String(100), comment='数据源编码')
    name: Mapped[Optional[str]] = mapped_column(String(100), comment='数据源名称')
    remark: Mapped[Optional[str]] = mapped_column(String(200), comment='备注')
    db_type: Mapped[Optional[str]] = mapped_column(String(10), comment='数据库类型')
    db_driver: Mapped[Optional[str]] = mapped_column(String(100), comment='驱动类')
    db_url: Mapped[Optional[str]] = mapped_column(String(500), comment='数据源地址')
    db_name: Mapped[Optional[str]] = mapped_column(String(100), comment='数据库名称')
    db_username: Mapped[Optional[str]] = mapped_column(String(100), comment='用户名')
    db_password: Mapped[Optional[str]] = mapped_column(String(100), comment='密码')
    create_by: Mapped[Optional[str]] = mapped_column(String(50), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='创建日期')
    update_by: Mapped[Optional[str]] = mapped_column(String(50), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新日期')
    sys_org_code: Mapped[Optional[str]] = mapped_column(String(64), comment='所属部门')
    tenant_id: Mapped[Optional[int]] = mapped_column(INTEGER(10), server_default=text("'0'"), comment='租户ID')
    db_host: Mapped[Optional[str]] = mapped_column(String(100), comment='主机地址')
    db_port: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='端口号')

    text2sql_sys_schematable: Mapped[List['Text2sqlSchematable']] = relationship('Text2sqlSchematable', back_populates='connection')
    text2sql_sys_schemarelationship: Mapped[List['Text2sqlSchemarelationship']] = relationship('Text2sqlSchemarelationship', back_populates='connection')


class Text2sqlSchematable(Base):
    __tablename__ = 'text2sql_sys_schematable'
    __table_args__ = (
        ForeignKeyConstraint(['connection_id'], ['text2sql_sys_datasource.id'], name='text2sql_sys_schematable_fk1'),
        Index('connection_id', 'connection_id'),
        Index('ix_schematable_id', 'id')
    )

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    connection_id: Mapped[str] = mapped_column(String(100))
    table_name: Mapped[str] = mapped_column(String(255))
    description: Mapped[Optional[str]] = mapped_column(Text)
    ui_metadata: Mapped[Optional[dict]] = mapped_column(JSON)
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)

    connection: Mapped['Text2sqlDatasource'] = relationship('Text2sqlDatasource', back_populates='text2sql_sys_schematable')
    text2sql_sys_schemacolumn: Mapped[List['Text2sqlSchemacolumn']] = relationship('Text2sqlSchemacolumn', back_populates='table')
    text2sql_sys_schemarelationship: Mapped[List['Text2sqlSchemarelationship']] = relationship('Text2sqlSchemarelationship', foreign_keys='[Text2sqlSchemarelationship.source_table_id]', back_populates='source_table')
    text2sql_sys_schemarelationship_: Mapped[List['Text2sqlSchemarelationship']] = relationship('Text2sqlSchemarelationship', foreign_keys='[Text2sqlSchemarelationship.target_table_id]', back_populates='target_table')


class Text2sqlSchemacolumn(Base):
    __tablename__ = 'text2sql_sys_schemacolumn'
    __table_args__ = (
        ForeignKeyConstraint(['table_id'], ['text2sql_sys_schematable.id'], name='text2sql_sys_schemacolumn_ibfk_1'),
        Index('ix_schemacolumn_id', 'id'),
        Index('table_id', 'table_id')
    )

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    table_id: Mapped[int] = mapped_column(INTEGER(11))
    column_name: Mapped[str] = mapped_column(String(255))
    data_type: Mapped[str] = mapped_column(String(100))
    description: Mapped[Optional[str]] = mapped_column(Text)
    is_primary_key: Mapped[Optional[int]] = mapped_column(TINYINT(1))
    is_foreign_key: Mapped[Optional[int]] = mapped_column(TINYINT(1))
    is_unique: Mapped[Optional[int]] = mapped_column(TINYINT(1))
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)

    table: Mapped['Text2sqlSchematable'] = relationship('Text2sqlSchematable', back_populates='text2sql_sys_schemacolumn')
    text2sql_sys_schemarelationship: Mapped[List['Text2sqlSchemarelationship']] = relationship('Text2sqlSchemarelationship', foreign_keys='[Text2sqlSchemarelationship.source_column_id]', back_populates='source_column')
    text2sql_sys_schemarelationship_: Mapped[List['Text2sqlSchemarelationship']] = relationship('Text2sqlSchemarelationship', foreign_keys='[Text2sqlSchemarelationship.target_column_id]', back_populates='target_column')
    text2sql_sys_valuemapping: Mapped[List['Text2sqlValuemapping']] = relationship('Text2sqlValuemapping', back_populates='column')


class Text2sqlSchemarelationship(Base):
    __tablename__ = 'text2sql_sys_schemarelationship'
    __table_args__ = (
        ForeignKeyConstraint(['connection_id'], ['text2sql_sys_datasource.id'], name='text2sql_sys_schemarelationship_fk1'),
        ForeignKeyConstraint(['source_column_id'], ['text2sql_sys_schemacolumn.id'], name='text2sql_sys_schemarelationship_ibfk_3'),
        ForeignKeyConstraint(['source_table_id'], ['text2sql_sys_schematable.id'], name='text2sql_sys_schemarelationship_ibfk_2'),
        ForeignKeyConstraint(['target_column_id'], ['text2sql_sys_schemacolumn.id'], name='text2sql_sys_schemarelationship_ibfk_5'),
        ForeignKeyConstraint(['target_table_id'], ['text2sql_sys_schematable.id'], name='text2sql_sys_schemarelationship_ibfk_4'),
        Index('connection_id', 'connection_id'),
        Index('ix_schemarelationship_id', 'id'),
        Index('source_column_id', 'source_column_id'),
        Index('source_table_id', 'source_table_id'),
        Index('target_column_id', 'target_column_id'),
        Index('target_table_id', 'target_table_id')
    )

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    connection_id: Mapped[str] = mapped_column(String(100))
    source_table_id: Mapped[int] = mapped_column(INTEGER(11))
    source_column_id: Mapped[int] = mapped_column(INTEGER(11))
    target_table_id: Mapped[int] = mapped_column(INTEGER(11))
    target_column_id: Mapped[int] = mapped_column(INTEGER(11))
    relationship_type: Mapped[Optional[str]] = mapped_column(String(50))
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)

    connection: Mapped['Text2sqlDatasource'] = relationship('Text2sqlDatasource', back_populates='text2sql_sys_schemarelationship')
    source_column: Mapped['Text2sqlSchemacolumn'] = relationship('Text2sqlSchemacolumn', foreign_keys=[source_column_id], back_populates='text2sql_sys_schemarelationship')
    source_table: Mapped['Text2sqlSchematable'] = relationship('Text2sqlSchematable', foreign_keys=[source_table_id], back_populates='text2sql_sys_schemarelationship')
    target_column: Mapped['Text2sqlSchemacolumn'] = relationship('Text2sqlSchemacolumn', foreign_keys=[target_column_id], back_populates='text2sql_sys_schemarelationship_')
    target_table: Mapped['Text2sqlSchematable'] = relationship('Text2sqlSchematable', foreign_keys=[target_table_id], back_populates='text2sql_sys_schemarelationship_')


class Text2sqlValuemapping(Base):
    __tablename__ = 'text2sql_sys_valuemapping'
    __table_args__ = (
        ForeignKeyConstraint(['column_id'], ['text2sql_sys_schemacolumn.id'], name='text2sql_sys_valuemapping_ibfk_1'),
        Index('column_id', 'column_id'),
        Index('ix_valuemapping_id', 'id'),
        Index('ix_valuemapping_nl_term', 'nl_term')
    )

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    column_id: Mapped[int] = mapped_column(INTEGER(11))
    nl_term: Mapped[str] = mapped_column(String(255))
    db_value: Mapped[str] = mapped_column(String(255))
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)

    column: Mapped['Text2sqlSchemacolumn'] = relationship('Text2sqlSchemacolumn', back_populates='text2sql_sys_valuemapping')


class Text2sqlQuestionHistory(Base):
    __tablename__ = 'text2sql_base_question_history'
    __table_args__ = (
        Index('ix_question_history_id', 'id'),
        Index('ix_question_history_session_id', 'session_id'),
        Index('ix_question_history_created_at', 'created_at'),
    )

    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    session_id: Mapped[Optional[str]] = mapped_column(String(100), comment='会话ID')
    question: Mapped[str] = mapped_column(Text, comment='用户问题')
    sql: Mapped[Optional[str]] = mapped_column(Text, comment='生成的SQL')
    execution_status: Mapped[Optional[str]] = mapped_column(String(20), comment='执行状态: success, failed, running')
    query_results: Mapped[Optional[str]] = mapped_column(Text, comment='查询结果JSON数据')
    results_count: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='查询结果行数')
    plotly_figure: Mapped[Optional[str]] = mapped_column(Text, comment='Plotly图表JSON数据')
    followup_questions: Mapped[Optional[str]] = mapped_column(Text, comment='后续问题JSON数据')
    created_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    updated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新时间')
