import asyncio
import re
from autogen_agentchat.agents import AssistantAgent
from autogen_core import message_handler, TopicId, MessageContext, type_subscription
from autogen_core.memory import ListMemory, MemoryContent

from .base_agent import (
    BaseAgent, SQLGenerationException, AGENT_NAMES,
    sql_generator_topic_type, sql_explainer_topic_type
)
from app.schemas import AnalysisMessage, SqlMessage

# 获取logger实例
from utils.logger import get_logger
logger = get_logger()


@type_subscription(topic_type=sql_generator_topic_type)
class SqlGeneratorAgent(BaseAgent):
    """SQL生成智能体，负责将自然语言转换为SQL"""
    
    def __init__(self, db_type=None, analytics_service=None, query_id: str = None):
        super().__init__("sql_generator_agent", db_type, analytics_service, query_id)
        self._prompt_template = f"""
        你是一名专业的SQL转换专家。你的任务是基于上下文信息及SQL命令生成报告，将用户的自然语言查询转换为精确的SQL语句。

        ## 生成SQL的指导原则：

        1.  **严格遵循报告中的分析：** 仔细阅读并理解上述的SQL命令生成报告，包括查询意图、需要使用的表和字段、连接方式、筛选条件、分组和排序要求。
        2.  **生成有效的SQL语句：** 仅输出符合 [[db_type]] 数据库语法的有效SQL语句，不要添加任何额外的解释或说明。
        3.  **准确表达筛选条件：** 报告中如有筛选条件描述，务必在生成的SQL语句中准确实现。
        4.  **正确使用表连接：** 按照报告中"需要的表连接描述"进行表连接，并确保连接条件正确。
        5.  **实现分组和聚合：** 如果报告中指示需要进行分组（GROUP BY）或聚合操作（例如 SUM, COUNT, AVG），请在SQL语句中正确实现。
        6.  **实现排序：** 按照报告中"排序描述"的要求，使用 ORDER BY 子句对结果进行排序。
        7.  **考虑数据库特性：** 生成的SQL语句应符合 [[db_type]] 数据库的特定语法和函数。
        8.  **SQL格式规范：** 使用清晰可读的SQL格式，适当添加换行和缩进，以提高可读性。
        9.  **避免使用不支持的语法：** 不要使用 [[db_type]] 数据库不支持的特殊语法或函数。
        10. **仅生成SQL：** 最终输出结果必须是纯粹的SQL查询语句，没有任何额外的文本。
        11. **注意值映射：** 如果提供了值映射信息，请根据映射关系在SQL中使用正确的数据库值。
        12. **避免重复字段和语句：** 确保生成的SQL语句中不包含重复的字段名称或重复的SQL子句，如重复的FROM子句。例如，避免生成如 `SELECT field1, field1` 或 `FROM table1 FROM table1` 这样的语句。
        13. **检查语法错误：** 在生成SQL语句后，仔细检查是否有语法错误，如拼写错误、缺失逗号或括号不匹配等。确保生成的SQL语句是完整的，不包含多余的关键字或缺失必要的关键字。

        特别注意：最终只生成一条您认为最符合用户查询需求的SQL语句。
        """

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["sql_generator"]

    def _prepare_prompt(self, schema_context: str = None, mappings_str: str = "") -> str:
        """准备提示模板

        Args:
            schema_context: 可选的表结构信息
            mappings_str: 值映射字符串，默认为空字符串

        Returns:
            str: 准备好的提示
        """
        prompt = self._prompt_template.replace("[[db_type]]", self.db_type)
        if schema_context:
            prompt += f"\n\n## 数据库结构信息\n{schema_context}"
        return prompt

    def _clean_sql_content(self, sql_content: str) -> str:
        """清理SQL内容，移除不必要的标签和内容
        
        Args:
            sql_content: 原始SQL内容
            
        Returns:
            str: 清理后的SQL内容
        """
        if not sql_content:
            return ""
            
        # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
        cleaned_content = re.sub(r'<think>.*?</think>', '', sql_content, flags=re.DOTALL)
        
        # 去除可能的代码块标记
        cleaned_content = re.sub(r'```sql\s*', '', cleaned_content)
        cleaned_content = re.sub(r'```\s*$', '', cleaned_content)
        
        return cleaned_content.strip()

    async def _generate_sql(self, message: AnalysisMessage) -> str:
        """生成SQL语句
        
        Args:
            message: 分析消息
            
        Returns:
            str: 生成的SQL语句
        """
        try:
            # 从memory_content重建ListMemory对象
            memory = ListMemory()
            memory_contents = []
            
            for item in message.memory_content:
                try:
                    memory_contents.append(MemoryContent(
                        content=item["content"],
                        mime_type=item["mime_type"],
                        metadata=item.get("metadata", None)
                    ))
                except Exception as e:
                    logger.warning(f"重建记忆内容项时出错: {str(e)}")
                    
            memory.content = memory_contents

            # 准备系统提示
            mappings_str = getattr(message, 'mappings_str', "") or ""
            system_message = self._prepare_prompt(message.schema_context, mappings_str)
            
            agent = AssistantAgent(
                name="sql_generator",
                model_client=self.model_client,
                system_message=system_message,
                memory=[memory],  # 使用重建的memory
                model_client_stream=True,
            )

            result = await agent.run(task="严格按照上下文信息生成sql语句")
            
            if not result or not result.messages:
                raise SQLGenerationException("智能体未返回有效结果")
                
            sql_content = result.messages[-1].content
            return self._clean_sql_content(sql_content)
            
        except Exception as e:
            logger.error(f"生成SQL时出错: {str(e)}")
            raise SQLGenerationException(f"生成SQL时出错: {str(e)}")

    @message_handler
    async def handle_analysis_message(self, message: AnalysisMessage, ctx: MessageContext) -> None:
        """处理AnalysisMessage类型的消息"""
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "memory_content_size": len(message.memory_content) if message.memory_content else 0,
                "schema_context_size": len(message.schema_context) if message.schema_context else 0
            })
            
            # 验证消息
            if not self.validate_message(message, ['query']):
                error = SQLGenerationException("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            # 发送开始生成的消息
            await self.send_stream_message("正在生成SQL语句...\n\n", message_sequence=1)

            try:
                sql_content = await self._generate_sql(message)
                # 记录模型调用统计
                await self.increment_model_usage(calls_count=1, tokens_used=len(sql_content))
                
                if not sql_content:
                    raise SQLGenerationException("生成的SQL语句为空")
                    
            except SQLGenerationException as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.handle_exception(e, "SQL生成")
                return
            except Exception as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.handle_exception(e, "SQL生成")
                return

            # 首先将SQL内容发送为流式消息，供前端实时更新
            await self.send_stream_message(f"```sql\n{sql_content}\n```", message_sequence=2)
            await asyncio.sleep(3)
            # 发送SQL内容为final_sql类型，触发前端SQL语句区域显示
            await self.send_stream_message(
                "\n\nSQL语句已生成",
                is_final=True,
                message_sequence=3
                # result={"sql": sql_content}  # 使用正确的格式包含SQL语句
            )

            # 完成执行，记录成功状态
            output_data = {
                "sql_content": sql_content,
                "sql_length": len(sql_content),
                "generation_completed": True
            }
            await self.finish_execution("SUCCESS", output_data=output_data)

            # 传递给SQL解释智能体
            await self.publish_message(
                SqlMessage(
                    query=message.query, 
                    sql=sql_content, 
                    schema_context=message.schema_context
                ),
                topic_id=TopicId(type=sql_explainer_topic_type, source=self.id.key)
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理分析消息") 