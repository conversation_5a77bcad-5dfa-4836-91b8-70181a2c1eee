import re
import time
from typing import <PERSON><PERSON>
from autogen_core import type_subscription, message_handler, TopicId, MessageContext

from .base_agent import (
    BaseAgent, SchemaRetrievalException, AGENT_NAMES,
    schema_retriever_topic_type, query_analyzer_topic_type
)
from app.schemas import QueryMessage, SchemaContextMessage

# 获取logger实例
from utils.logger import get_logger
logger = get_logger()


@type_subscription(topic_type=schema_retriever_topic_type)
class SchemaRetrieverAgent(BaseAgent):
    """表结构检索智能体，负责从图数据库获取相关表结构"""

    def __init__(self, db_type=None, analytics_service=None, query_id: str = None):
        """初始化表结构检索智能体"""
        super().__init__("schema_retriever_agent", db_type, analytics_service, query_id)

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["schema_retriever"]

    async def retrieve_schema(self, connection_id: str, query: str) -> Tuple[str, str]:
        """先利用大模型对用户的问题做优化，然后依据大模型优化后的问题从向量数据库中匹配到相关的表，然后根据匹配到的表去图数据库中查询关联的表，最多查询深度为4，对表进行去重，整理成表结构信息
        和大模型对话使用response = await model_client.create([UserMessage(content=prompt, source="user")])方法即可

        Args:
            connection_id: 数据库连接ID
            query: 用户查询

        Returns:
            Tuple[str, str]: 表结构信息字典和值映射字符串
        """
        try:
            from autogen_agentchat.messages import UserMessage
            from rag.entity_vectorizer import EntityVectorizer
            from utils.kg_utils import KnowledgeGraphManager
            
            if not connection_id:
                raise SchemaRetrievalException("数据库连接ID不能为空")
            
            if not query or not query.strip():
                raise SchemaRetrievalException("用户查询不能为空")
            
            logger.info(f"开始检索表结构，连接ID: {connection_id}, 查询: {query}")
            
            # 步骤1: 使用大模型优化用户查询
            optimization_prompt = f"""
            你是一个数据库查询优化专家。请分析用户的自然语言查询，提取出关键的数据库相关概念、实体和操作意图。
            
            用户查询: {query}
            
            请输出优化后的查询关键词，用于在数据库表结构中搜索相关表。要求：
            1. 提取核心业务概念和实体名词
            2. 识别可能的表名、字段名相关词汇
            3. 保留重要的操作动词（如查询、统计、分析等）
            4. 去除无关的修饰词和语气词
            5. 输出格式为简洁的关键词组合，用空格分隔
            
            优化后的查询关键词:
            """
            
            optimized_query = query  # 默认使用原始查询
            try:
                response = await self.model_client.create([UserMessage(content=optimization_prompt, source="user")])
                # 记录模型调用统计
                await self.increment_model_usage(calls_count=1, tokens_used=len(optimization_prompt) + (len(response.content) if response and response.content else 0))
                
                if response and response.content:
                    optimized_query = response.content.strip()
                    # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
                    optimized_query = re.sub(r'<think>.*?</think>', '', optimized_query, flags=re.DOTALL)
                    logger.info(f"大模型优化后的查询: {optimized_query}")
                else:
                    logger.warning("大模型返回空响应，使用原始查询")
            except Exception as e:
                logger.warning(f"大模型优化查询失败，使用原始查询: {str(e)}")
            
            # 步骤2: 从向量数据库中搜索相关表
            try:
                vectorizer = EntityVectorizer()
                search_results = await vectorizer.search_text2sql_tables(
                    query=optimized_query,
                    connection_id=str(connection_id),
                    top_k=10  # 获取前10个相关表
                )
            except Exception as e:
                logger.error(f"向量数据库搜索失败: {str(e)}")
                raise SchemaRetrievalException(f"向量数据库搜索失败: {str(e)}")
            
            if not search_results:
                logger.warning(f"向量数据库中未找到相关表，连接ID: {connection_id}")
                return "", ""
            
            logger.info(f"向量搜索找到 {len(search_results)} 个相关表")
            
            # 提取表ID和表名
            initial_table_ids = []
            initial_table_names = []
            for result in search_results:
                table_id = result.get("table_id")
                table_name = result.get("table_name")
                if table_id and table_name:
                    initial_table_ids.append(table_id)
                    initial_table_names.append(table_name)
            
            if not initial_table_ids:
                logger.warning("未从搜索结果中提取到有效的表ID")
                return "", ""
            
            # 步骤3: 从图数据库中查询关联表
            try:
                kg_manager = KnowledgeGraphManager.get_instance()
                driver = await kg_manager.neo4j_driver
                
                if not driver:
                    raise SchemaRetrievalException("无法获取图数据库连接")
                
                all_related_table_ids = set(initial_table_ids)  # 使用set去重
                
                # 使用APOC的apoc.path.expand一次性查询4层深度的关联表
                logger.info(f"使用APOC查询关联表，初始表数量: {len(initial_table_ids)}")
                
                with driver.session(database=kg_manager.neo4j_db) as session:
                    # 构建初始表的图数据库ID列表
                    graph_table_ids = [f"table_{connection_id}_{tid}" for tid in initial_table_ids]
                    
                    # 使用APOC的apoc.path.expand查询关联表
                    apoc_query = """
                    UNWIND $initial_table_ids AS start_table_id
                    MATCH (start:Table {id: start_table_id})
                    WHERE start.connection_id = $connection_id
                    CALL apoc.path.expand(start, "REFERENCES", null, 1, 4) YIELD path
                    WITH path, nodes(path) AS path_nodes
                    UNWIND path_nodes AS node
                    WITH DISTINCT node
                    WHERE node.connection_id = $connection_id
                    RETURN DISTINCT 
                        node.id as table_id,
                        node.name as table_name,
                        node.description as table_description
                    LIMIT 100
                    """
                    
                    try:
                        result = session.run(apoc_query, {
                            "connection_id": connection_id,
                            "initial_table_ids": graph_table_ids
                        })
                        
                        # 收集所有关联表
                        apoc_found_tables = 0
                        for record in result:
                            table_id = record["table_id"]
                            # 从图数据库ID格式中提取实际的表ID
                            if table_id and table_id.startswith(f"table_{connection_id}_"):
                                try:
                                    actual_table_id = int(table_id.split("_")[-1])
                                    all_related_table_ids.add(actual_table_id)
                                    apoc_found_tables += 1
                                except (ValueError, IndexError) as e:
                                    logger.warning(f"解析表ID失败: {table_id}, 错误: {str(e)}")
                        
                        logger.info(f"APOC查询成功，找到 {apoc_found_tables} 个关联表")
                        
                    except Exception as apoc_error:
                        logger.warning(f"APOC查询失败，回退到传统逐层查询: {str(apoc_error)}")
                        
                        # 如果APOC查询失败，回退到原来的逐层查询方式
                        current_table_ids = initial_table_ids
                        for depth in range(4):
                            if not current_table_ids:
                                break
                                
                            logger.info(f"第 {depth + 1} 层关联查询，当前表数量: {len(current_table_ids)}")
                            
                            # 查询与当前表有关系的其他表
                            fallback_query = """
                            MATCH (source:Table)-[r:REFERENCES]-(target:Table)
                            WHERE source.connection_id = $connection_id 
                            AND (source.id IN $current_table_ids OR target.id IN $current_table_ids)
                            RETURN DISTINCT 
                                CASE 
                                    WHEN source.id IN $current_table_ids THEN target.id
                                    ELSE source.id 
                                END as related_table_id,
                                CASE 
                                    WHEN source.id IN $current_table_ids THEN target.name
                                    ELSE source.name 
                                END as related_table_name
                            LIMIT 50
                            """
                            
                            # 构建当前层的表ID列表（图数据库中的格式）
                            graph_table_ids = [f"table_{connection_id}_{tid}" for tid in current_table_ids]
                            
                            result = session.run(fallback_query, {
                                "connection_id": connection_id,
                                "current_table_ids": graph_table_ids
                            })
                            
                            # 收集新发现的关联表
                            new_table_ids = []
                            for record in result:
                                related_table_id = record["related_table_id"]
                                # 从图数据库ID格式中提取实际的表ID
                                if related_table_id and related_table_id.startswith(f"table_{connection_id}_"):
                                    try:
                                        actual_table_id = int(related_table_id.split("_")[-1])
                                        if actual_table_id not in all_related_table_ids:
                                            all_related_table_ids.add(actual_table_id)
                                            new_table_ids.append(actual_table_id)
                                    except (ValueError, IndexError) as e:
                                        logger.warning(f"解析关联表ID失败: {related_table_id}, 错误: {str(e)}")
                            
                            # 更新下一层查询的表ID列表
                            current_table_ids = new_table_ids
                            
                            if not new_table_ids:
                                logger.info(f"第 {depth + 1} 层未发现新的关联表，停止扩展")
                                break
                            else:
                                logger.info(f"第 {depth + 1} 层发现 {len(new_table_ids)} 个新关联表")
                                
            except Exception as e:
                logger.error(f"图数据库查询失败: {str(e)}")
                # 如果图数据库查询失败，至少使用初始搜索到的表
                all_related_table_ids = set(initial_table_ids)
            
            logger.info(f"总共找到 {len(all_related_table_ids)} 个相关表（包括关联表）")
            
            # 步骤4: 获取所有相关表的详细结构信息
            try:
                from dao.text2sqlsys_utils import get_schema_data_by_connection_id
                
                # 获取连接下的所有表结构数据
                schema_data = await get_schema_data_by_connection_id(connection_id)
                if not schema_data:
                    raise SchemaRetrievalException("无法获取数据库结构信息")
                    
                tables = schema_data.get("tables", [])
                table_id_to_obj = schema_data.get("table_id_to_obj", {})
                column_id_to_obj = schema_data.get("column_id_to_obj", {})
                table_id_to_columns = schema_data.get("table_id_to_columns", {})
                relationships = schema_data.get("relationships", [])
                
            except Exception as e:
                logger.error(f"获取表结构数据失败: {str(e)}")
                raise SchemaRetrievalException(f"获取表结构数据失败: {str(e)}")
            
            # 筛选出相关的表
            relevant_tables = []
            for table in tables:
                if hasattr(table, 'id') and table.id in all_related_table_ids:
                    relevant_tables.append(table)
            
            # 步骤5: 构建表结构信息字符串
            schema_context = ""
            
            if relevant_tables:
                schema_context += f"数据库类型: {self.db_type}\n\n"
                schema_context += f"相关表结构信息 (共{len(relevant_tables)}个表):\n\n"
                
                for table in relevant_tables:
                    try:
                        schema_context += f"表名: {table.table_name}\n"
                        if hasattr(table, 'description') and table.description:
                            schema_context += f"描述: {table.description}\n"
                        
                        # 获取表的列信息
                        columns = table_id_to_columns.get(table.id, [])
                        if columns:
                            schema_context += "列信息:\n"
                            for column in columns:
                                try:
                                    column_info = f"  - {column.column_name} ({column.data_type})"
                                    if hasattr(column, 'is_primary_key') and column.is_primary_key:
                                        column_info += " [主键]"
                                    if hasattr(column, 'is_foreign_key') and column.is_foreign_key:
                                        column_info += " [外键]"
                                    if hasattr(column, 'description') and column.description:
                                        column_info += f" - {column.description}"
                                    schema_context += column_info + "\n"
                                except Exception as e:
                                    logger.warning(f"处理列信息时出错: {str(e)}")
                        
                        schema_context += "\n"
                    except Exception as e:
                        logger.warning(f"处理表信息时出错: {str(e)}")
                
                # 添加表之间的关系信息
                try:
                    relevant_relationships = []
                    for rel in relationships:
                        source_table = table_id_to_obj.get(rel.source_table_id)
                        target_table = table_id_to_obj.get(rel.target_table_id)
                        
                        if (source_table and source_table.id in all_related_table_ids and
                            target_table and target_table.id in all_related_table_ids):
                            relevant_relationships.append(rel)
                    
                    if relevant_relationships:
                        schema_context += "表关系信息:\n"
                        for rel in relevant_relationships:
                            try:
                                source_table = table_id_to_obj.get(rel.source_table_id)
                                target_table = table_id_to_obj.get(rel.target_table_id)
                                source_column = column_id_to_obj.get(rel.source_column_id)
                                target_column = column_id_to_obj.get(rel.target_column_id)
                                
                                if all([source_table, target_table, source_column, target_column]):
                                    rel_info = f"  - {source_table.table_name}.{source_column.column_name} -> {target_table.table_name}.{target_column.column_name}"
                                    if hasattr(rel, 'relationship_type') and rel.relationship_type:
                                        rel_info += f" ({rel.relationship_type})"
                                    schema_context += rel_info + "\n"
                            except Exception as e:
                                logger.warning(f"处理关系信息时出错: {str(e)}")
                except Exception as e:
                    logger.warning(f"处理表关系时出错: {str(e)}")
            
            # 值映射功能，对列名的值进行映射，方便后续查询分析智能体使用
            mappings_str = ""
            
            # 获取相关表的列值映射
            try:
                from dao.text2sqlsys_utils import get_valuemapping_by_column_id
                
                value_mappings = {}
                
                # 遍历相关表的所有列，获取值映射
                for table in relevant_tables:
                    columns = table_id_to_columns.get(table.id, [])
                    for column in columns:
                        try:
                            # 根据列ID获取该列的值映射
                            column_mappings = await get_valuemapping_by_column_id(column.id)
                            
                            if column_mappings:
                                table_col = f"{table.table_name}.{column.column_name}"
                                # 构建该列的映射字典：{自然语言术语: 数据库值}
                                value_mappings[table_col] = {
                                    mapping.nl_term: mapping.db_value 
                                    for mapping in column_mappings
                                }
                        except Exception as e:
                            logger.warning(f"获取列 {column.column_name} 的值映射时出错: {str(e)}")
                
                # 格式化值映射为字符串
                if value_mappings:
                    mappings_str = "-- Value Mappings:\n"
                    for column, mappings in value_mappings.items():
                        mappings_str += f"-- For {column}:\n"
                        for nl_term, db_value in mappings.items():
                            mappings_str += f"--   '{nl_term}' in natural language refers to '{db_value}' in the database\n"
                    mappings_str += "\n"
                    
                    logger.info(f"获取到 {len(value_mappings)} 个列的值映射信息")
                else:
                    logger.info("未找到任何列的值映射信息")
                
            except Exception as e:
                logger.warning(f"获取值映射时出错: {str(e)}")
                mappings_str = ""
            
            logger.info(f"表结构检索完成，返回 {len(relevant_tables)} 个相关表的结构信息")
            
            return schema_context, mappings_str
            
        except SchemaRetrievalException:
            # 重新抛出自定义异常
            raise
        except Exception as e:
            logger.error(f"检索表结构时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise SchemaRetrievalException(f"检索表结构时出错: {str(e)}")

    @message_handler
    async def handle_message(self, message: QueryMessage, ctx: MessageContext) -> None:
        """处理查询消息
        Args:
            message: 查询消息
            ctx: 消息上下文
        """
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "connection_id": message.connection_id
            })
            
            # 验证消息
            if not self.validate_message(message, ['query']):
                error = SchemaRetrievalException("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            connection_id = message.connection_id
            schema_context = ""

            # 发送开始处理的消息
            await self.send_stream_message("正在从图数据库解析相关表结构...\n\n", message_sequence=1)

            mappings_str = ""
            if connection_id:
                logger.info(f"表结构检索智能体收到连接ID: {connection_id}")

                try:
                    # 从图数据库获取相关表结构信息和值映射
                    schema_context, mappings_str = await self.retrieve_schema(connection_id, message.query)
                    
                    # 记录Schema检索结果
                    if self.analytics_service and self.query_id:
                        table_count = len([line for line in schema_context.split('\n') if line.startswith('表名:')])
                        await self.analytics_service.record_schema_retrieval(
                            query_id=self.query_id,
                            connection_id=connection_id,
                            original_query=message.query,
                            optimized_query=message.query,  # 这里可以从retrieve_schema方法返回优化后的查询
                            vector_search_results_count=10,  # 这里可以从retrieve_schema方法返回实际数量
                            initial_tables_count=table_count,
                            final_tables_count=table_count,
                            kg_query_depth=4,
                            schema_info_size_kb=len(schema_context.encode('utf-8')) / 1024 if schema_context else 0,
                            execution_time_ms=int((time.time() - self.execution_start_time) * 1000) if self.execution_start_time else 0
                        )

                    # 输出找到的相关表信息
                    if schema_context and schema_context.strip():
                        # 从schema_context字符串中提取表名信息
                        table_lines = [line for line in schema_context.split('\n') if line.startswith('表名:')]
                        if table_lines:
                            # 提取表名列表
                            table_names = [line.replace('表名:', '').strip() for line in table_lines]
                            # 使用新的 markdown 格式化方法
                            formatted_tables = self.format_table_list_as_markdown(table_names, "已找到以下相关表")
                            await self.send_stream_message(formatted_tables, content_format="markdown", message_sequence=2)
                        else:
                            await self.send_stream_message("已获取表结构信息，正在传递给查询分析智能体", message_sequence=2)
                    else:
                        await self.send_stream_message("未找到相关表结构，将使用默认数据库结构继续分析", message_sequence=2)
                        
                except SchemaRetrievalException as e:
                    await self.record_error(e, {"connection_id": connection_id, "query": message.query})
                    await self.handle_exception(e, "表结构检索")
                    # 即使检索失败，也继续流程，使用空的表结构
                    schema_context = ""
                    mappings_str = ""
                except Exception as e:
                    await self.record_error(e, {"connection_id": connection_id, "query": message.query})
                    await self.handle_exception(e, "表结构检索")
                    schema_context = ""
                    mappings_str = ""
            else:
                await self.send_stream_message("未提供数据库连接ID，将使用默认数据库结构继续分析\n\n", message_sequence=2)

            # 发送完成消息
            await self.send_stream_message("\n\n表结构检索完成，由查询分析智能体进行下一步处理\n\n", message_sequence=3)

            # 完成执行，记录成功状态
            output_data = {
                "schema_context": schema_context,
                "mappings_str": mappings_str,
                "schema_size_kb": len(schema_context.encode('utf-8')) / 1024 if schema_context else 0
            }
            await self.finish_execution("SUCCESS", output_data=output_data)

            # 将表结构信息和值映射传递给查询分析智能体
            await self.publish_message(
                SchemaContextMessage(
                    query=message.query,
                    schema_context=schema_context,
                    mappings_str=mappings_str
                ),
                topic_id=TopicId(type=query_analyzer_topic_type, source=self.id.key)
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理查询消息") 