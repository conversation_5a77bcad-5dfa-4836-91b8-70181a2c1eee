"""
异步版本的知识库工具函数
提供异步的数据库操作方法
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from dao.DatabaseEngine import DatabaseEngine
from dao.models.AiragKnowledgeDoc import AiragKnowledgeDoc
from dao.models.AiragKnowledge import AiragKnowledge
from dao.models.AiragModel import AiragModel
from dao.models.SysTables import SysUser
from utils.logger import get_logger

logger = get_logger()


async def get_async_session() -> AsyncSession:
    """获取异步数据库会话"""
    return await DatabaseEngine.get_session()


async def get_collections(identifier: str):
    """异步获取用户可访问的知识库集合"""
    async with await get_async_session() as session:
        try:
            # 首先通过identifier获取用户的org_code
            user_statement = select(SysUser.org_code).where(SysUser.username == identifier)
            user_result = await session.execute(user_statement)
            user_row = user_result.scalar_one_or_none()
            
            if not user_row:
                # 如果没有找到用户，返回空列表
                return []
                
            user_org_code = user_row
            
            # 查询所有启用状态的知识库
            knowledge_statement = select(AiragKnowledge.id, AiragKnowledge.name, AiragKnowledge.applicable_dept).where(AiragKnowledge.status == 'enable')
            knowledge_result = await session.execute(knowledge_statement)
            knowledge_rows = knowledge_result.fetchall()
            
            # 筛选出匹配的知识库
            matched_collections = []
            for knowledge in knowledge_rows:
                # 如果applicable_dept为空，代表适用所有部门，所有用户都能访问
                if not knowledge.applicable_dept or knowledge.applicable_dept.strip() == '':
                    matched_collections.append({
                        'id': knowledge.id,
                        'name': knowledge.name
                    })
                    continue
                    
                # applicable_dept是逗号分隔的部门编码列表
                applicable_depts = [dept.strip() for dept in knowledge.applicable_dept.split(',')]
                
                # 检查用户的org_code是否在适用部门列表中
                if user_org_code in applicable_depts:
                    matched_collections.append({
                        'id': knowledge.id,
                        'name': knowledge.name
                    })
            
            return matched_collections
        except Exception as e:
            logger.error(f"Error getting collections: {str(e)}")
            raise
        finally:
            await session.close()


async def get_knowledge_info(knowledge_id):
    """异步获取知识库是否使用知识图谱及其嵌入模型信息"""
    async with await get_async_session() as session:
        try:
            # 联合查询知识库和模型信息
            statement = select(
                AiragKnowledge.use_kg,
                AiragModel.model_name
            ).select_from(
                AiragKnowledge
            ).outerjoin(
                AiragModel, 
                AiragKnowledge.embed_id == AiragModel.id
            ).where(
                AiragKnowledge.id == knowledge_id
            )
            
            result = await session.execute(statement)
            row = result.first()
            
            if row is None:
                return None
                
            return row.use_kg, row.model_name
        except Exception as e:
            logger.error(f"Error getting knowledge use kg: {str(e)}")
            raise
        finally:
            await session.close()


async def get_file_by_id(doc_id):
    """异步根据文档ID获取文件元数据"""
    async with await get_async_session() as session:
        try:
            statement = select(AiragKnowledgeDoc).where(AiragKnowledgeDoc.id == doc_id)
            result = await session.execute(statement)
            doc = result.scalar_one()
            return doc.metadata_
        except Exception as e:
            logger.error(f"Error getting file by id: {str(e)}")
            raise
        finally:
            await session.close()


if __name__ == "__main__":
    import asyncio
    
    async def main():
        # 测试异步函数
        # await get_collections("test_user")
        result = await get_file_by_id("1916347237550907393")
        print(result)
    
    asyncio.run(main())