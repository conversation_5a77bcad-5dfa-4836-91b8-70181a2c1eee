import chainlit as cl
from rag.database_rag import MySQLDatabaseRAG
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

# 创建数据RAG对象
# rag = SQLiteDatabaseRAG()
_yq_instance = None

def get_yq_instance():
    global _yq_instance
    if _yq_instance is None:
        _yq_instance = MySQLDatabaseRAG()
    return _yq_instance


async def is_sql_valid(sql):
    yq = get_yq_instance()
    return yq.is_sql_valid(sql)

@cl.step(language="sql", name="sql_generator", show_input="text")
async def generate_sql(human_query: str, **kwargs):
    current_step = cl.context.current_step
    current_step.input = human_query
    yq = get_yq_instance()
    sql = yq.generate_sql(human_query, allow_llm_to_see_data=True, **kwargs)
    current_step.output = sql
    return sql


@cl.step(name="sql_generator", show_input="sql")
async def execute_query(sql):
    current_step = cl.context.current_step
    current_step.input = sql
    yq = get_yq_instance()
    df = yq.run_sql(sql)
    current_step.output = df.to_markdown(index=False)
    return df

@cl.step(name="sql_executor", show_input="sql")
async def execute_query_for_api(sql):
    current_step = cl.context.current_step
    current_step.input = sql
    yq = get_yq_instance()
    df = yq.run_sql(sql)
    current_step.output = df.to_json(orient="records")
    return df

@cl.step(name="agent_analyzer", language="python")
async def plot(human_query, sql, df):
    current_step = cl.context.current_step
    yq = get_yq_instance()
    plotly_code = yq.generate_plotly_code(question=human_query, sql=sql, df_metadata=df)
    fig = yq.get_plotly_figure(plotly_code=plotly_code, df=df)

    current_step.output = plotly_code
    return fig