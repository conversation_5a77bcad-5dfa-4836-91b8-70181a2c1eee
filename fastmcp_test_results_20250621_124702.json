{"timestamp": "2025-06-21T12:47:02.837851", "client_type": "FastMCP Client", "server_capabilities": {"tools": ["rag_upload_file_by_doc_id", "rag_get_rag_collections", "rag_delete_rag_collection", "rag_query_rag_documents", "rag_get_embedding_status", "rag_get_all_embedding_tasks", "rag_delete_rag_document", "rag_rag_stream_query", "rag_get_embedding_text", "text2sql_get_all_database_connections", "text2sql_get_database_connection", "text2sql_get_database_schema", "text2sql_discover_and_save_schema", "text2sql_get_schema_metadata", "text2sql_publish_schema", "text2sql_update_table_metadata", "text2sql_update_column_metadata", "text2sql_get_graph_visualization_data", "text2sql_get_value_mappings", "text2sql_create_value_mapping", "kg_get_knowledge_graph_statistics", "kg_search_knowledge_entities", "kg_get_entity_details", "kg_search_entity_relationships", "kg_get_document_knowledge_graph", "kg_create_knowledge_graph_from_text", "stats_get_database_statistics_overview", "stats_get_knowledge_statistics_overview", "stats_get_text2sql_analytics_overview", "stats_get_dashboard_overview_stats", "stats_get_chart_distribution_data", "stats_get_monthly_trend_data", "stats_get_database_type_distribution", "stats_get_field_analysis_stats", "stats_get_data_type_distribution", "stats_get_table_size_distribution", "stats_get_comprehensive_database_stats", "stats_get_trend_analysis", "stats_get_system_performance_metrics", "stats_get_usage_analytics", "cache_clear_cache", "cache_get_cache_status", "cache_get_cache_keys_pattern", "cache_trigger_cache_warmup", "cache_get_warmup_status", "cache_quick_cache_warmup", "cache_clear_cache_by_pattern", "cache_list_cache_keys", "cache_get_cache_performance", "cache_test_cache_operations", "cache_cache_health_check", "train_create_training_data", "train_start_model_training", "train_get_training_status", "train_stop_training", "train_evaluate_model", "train_list_trained_models", "train_delete_model", "train_export_model", "analytics_analyze_sql_query", "analytics_get_usage_statistics"], "resources": ["get_version", "get_server_info", "get_system_health"], "prompts": ["help_prompt", "deployment_guide_prompt", "rag_rag_usage_prompt", "rag_rag_best_practices_prompt", "rag_rag_troubleshooting_prompt", "rag_rag_usage_examples_prompt", "rag_multimodal_rag_prompt", "text2sql_text2sql_usage_prompt", "text2sql_text2sql_best_practices_prompt", "text2sql_text2sql_query_patterns_prompt", "text2sql_text2sql_troubleshooting_prompt", "text2sql_schema_management_prompt", "kg_knowledge_graph_usage_prompt", "kg_knowledge_extraction_prompt", "kg_graph_reasoning_prompt", "kg_graph_applications_prompt", "stats_statistics_usage_prompt", "stats_data_analysis_best_practices_prompt", "stats_dashboard_design_prompt", "cache_cache_usage_prompt", "cache_cache_best_practices_prompt", "cache_cache_performance_tuning_prompt", "train_training_assistant_prompt", "train_model_training_best_practices_prompt", "train_training_troubleshooting_prompt", "analytics_analytics_assistant_prompt", "analytics_sql_quality_analysis_prompt", "analytics_performance_optimization_prompt", "analytics_usage_analytics_prompt"]}, "test_results": {"rag_collections": {"success": true, "data": ["yq_1933785761556066306", "yq_1934208084859731970", "yq_1916111815235076097", "kg_text2sql_doc", "kg_entities_yq_1933759216628133890", "kg_contextual_entities_yq_1933759216628133890", "yq_1929782615774670849", "yq_1933759216628133890", "yq_1934201827335651329", "yq_1929798024435392513", "yq_1932053061870579713", "default", "kg_relations_yq_1933759216628133890"]}, "kg_statistics": {"status": "success", "statistics": {"total_entities": 0, "total_relationships": 0, "entity_types": {}, "relationship_types": {}}}, "text2sql_usage": "# Text2SQL系统使用指南\n\n本Text2SQL系统基于重构后的服务层架构，提供以下核心功能：\n\n## 主要功能\n\n### 1. 数据库连接管理\n- `get_all_database_connections`: 获取所有数据库连接\n- `get_database_connection`: 获取指定数据库连接信息\n- 支持多种数据库类型（MySQL、PostgreSQL、SQLite等）\n\n### 2. 数据库模式管理\n- `get_database_schema`: 获取数据库模式信息\n- `discover_and_save_schema`: 自动发现并保存数据库模式\n- `get_schema_metadata`: 获取模式元数据\n- `publish_schema`: 发布模式配置\n\n### 3. 元数据管理\n- `update_table_metadata`: 更新表元数据\n- `update_column_metadata`: 更新列元数据\n- `get_value_mappings`: 获取值映射配置\n- `create_value_mapping`: 创建值映射\n\n\n### 4. 可视化支持\n- `get_graph_visualization_data`: 获取数据库关系图可视化数据\n- 支持表关系和依赖关系的图形化展示\n\n## 架构优势\n- 复用服务层业务逻辑，避免重复实现\n- 基于多智能体架构的智能SQL生成\n- 统一的错误处理和日志记录\n- 更好的代码维护性和扩展性\n", "training_troubleshooting": "\n    Text2SQL训练常见问题及解决方案：\n    \n    **训练不收敛问题：**\n    问题：损失函数不下降或震荡\n    解决：调整学习率、检查数据质量、改进模型架构\n    \n    问题：验证集性能不提升\n    解决：增加正则化、扩充训练数据、调整训练策略\n    \n    **过拟合问题：**\n    问题：训练集准确率高但验证集低\n    解决：增加Dropout、使用数据增强、早停训练\n    \n    问题：模型在新数据上表现差\n    解决：增加数据多样性、使用迁移学习、调整模型复杂度\n    \n    **资源限制问题：**\n    问题：显存不足\n    解决：减少批大小、使用梯度累积、模型并行\n    \n    问题：训练时间过长\n    解决：使用混合精度训练、模型压缩、分布式训练\n    \n    **数据质量问题：**\n    问题：标注错误率高\n    解决：建立质量控制流程、多人交叉验证、自动检查工具\n    \n    问题：数据分布不均\n    解决：数据重采样、损失函数权重调整、生成合成数据\n    \n    **部署问题：**\n    问题：推理速度慢\n    解决：模型量化、知识蒸馏、推理优化\n    \n    问题：服务稳定性差\n    解决：负载均衡、异常处理、监控告警\n    "}, "summary": {"success_count": 4, "total_count": 4, "success_rate": 1.0}}