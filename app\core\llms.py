from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import settings


def _setup_model_client():
    """设置模型客户端"""
    model_config = {
        "model": settings.configuration.llm_model_name,
        "api_key": settings.configuration.llm_api_key,
        "base_url": settings.configuration.llm_api_base,
        "request_timeout": 120,
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "structured_output": True,
            "multiple_system_messages": True  # 支持多个系统消息
        },
    }
    return OpenAIChatCompletionClient(**model_config)

model_client = _setup_model_client()