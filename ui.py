import json
import os
import tempfile
from typing import Optional, List, Dict
import asyncio

from llama_index.embeddings.huggingface import HuggingFaceEmbedding

import chainlit as cl
import requests
from chainlit.input_widget import Switch, Select
from chainlit.types import ThreadDict
from llama_index.core import Settings
from llama_index.core.agent import FunctionCallingAgent
from llama_index.core.base.llms.types import ChatMessage
from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.chat_engine.types import ChatMode
from llama_index.core.tools import FunctionTool

from rag.base_rag import RAG
from rag.multimodal_rag import MultiModalRAG
from config import settings
import utils.ui_utils as cl_utils
# 配置日志
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 导入国际化配置
from i18n import get_translation


def knowledge_starters(locale: str = "zh_CN"):
    t = get_translation(locale)["knowledge_starters"]
    starters = [
        cl.Starter(
            label=t["hazard_label"],
            message=t["hazard_message"],
            icon="/public/starter/document.svg",
        ),
        cl.Starter(
            label=t["voucher_label"],
            message=t["voucher_message"],
            icon="/public/starter/knowledge.svg",
        ),
        cl.Starter(
            label=t["performance_label"],
            message=t["performance_message"],
            icon="/public/starter/robot.svg",
        ),
        cl.Starter(
            label=t["llm_label"],
            message=t["llm_message"],
            icon="/public/starter/message.svg",
        )
    ]
    return starters


def database_starters(locale: str = "zh_CN"):
    t = get_translation(locale)["database_starters"]
    starters = [
        cl.Starter(
            label=t["users_label"],
            message=t["users_message"],
            icon="/public/starter/search.svg",
        ),
        cl.Starter(
            label=t["devices_label"],
            message=t["devices_message"],
            icon="/public/starter/send.svg",
        ),
        cl.Starter(
            label=t["repair_label"],
            message=t["repair_message"],
            icon="/public/starter/settings.svg",
        ),
        cl.Starter(
            label=t["scrap_label"],
            message=t["scrap_message"],
            icon="/public/starter/user.svg",
        )
    ]
    return starters


def process_starters(locale: str = "zh_CN"):
    t = get_translation(locale)["process_starters"]
    starters = [
        cl.Starter(
            label=t["daily_label"],
            message=t["daily_message"],
            icon="/public/starter/robot.svg",
        ),
        cl.Starter(
            label=t["training_label"],
            message=t["training_message"],
            icon="/public/starter/document.svg",
        )
    ]
    return starters


@cl.on_chat_start
async def start():
    # 从用户 metadata 中获取 locale 参数
    user = cl.user_session.get("user")
    locale = user.metadata.get("locale", "zh_CN") if user and user.metadata else "zh_CN"
    
    # 将 locale 存储到用户会话中，以便后续使用
    cl.user_session.set("locale", locale)
    
    t = get_translation(locale)
    
    identifier = user.identifier
    collections = await cl_utils.list_collections(identifier)
    await cl.ChatSettings(
        [
            # Switch(id="multimodal", initial=True, label="多模态RAG", tooltip="多模态RAG", description="多模态RAG" ),
            Select(
                id="collection_name",
                label=t["settings"]["collection_label"],
                initial_value="yq_1916111815235076097",#为了测试方便，先设定默认值为训练库collection
                items=collections
            ),
        ]
    ).send()

@cl.set_chat_profiles
async def chat_profile(current_user: cl.User):
    # 从用户 metadata 中获取 locale 参数
    locale = current_user.metadata.get("locale", "zh_CN") if current_user and current_user.metadata else "zh_CN"
    t = get_translation(locale)["chat_profiles"]
    
    profiles = [
        cl.ChatProfile(
            name=t["knowledge_name"],
            markdown_description=t["knowledge_desc"],
            icon=f"/public/profile/document_search.svg",
            starters=knowledge_starters(locale),
        ),
        cl.ChatProfile(
            name=t["database_name"],
            markdown_description=t["database_desc"],
            icon=f"/public/profile/vector_embedding.svg",
            starters=database_starters(locale),
        ),
        cl.ChatProfile(
            name=t["process_name"],
            markdown_description=t["process_desc"],
            icon=f"/public/profile/knowledge_base.svg",
            starters=process_starters(locale)
        ),
        # cl.ChatProfile(
        #     name="联网搜索",
        #     markdown_description=f"联网搜索",
        #     icon=f"/public/profile/document_search.svg",
        #     starters=[]
        # ),
        # cl.ChatProfile(
        #     name="MCP服务",
        #     markdown_description=f"MCP服务",
        #     icon=f"/public/profile/knowledge_base.sv",
        #     starters=[]
        # )
    ]
    return profiles


@cl.set_starters
async def set_starters():
    # 尝试从用户会话中获取 locale，如果获取不到则使用默认值
    try:
        locale = cl.user_session.get("locale", "zh_CN")
    except:
        # 如果用户会话尚未建立，使用默认 locale
        locale = "zh_CN"
    return knowledge_starters(locale)


@cl.url_param_auth_callback
async def url_param_auth_callback(query_params: Dict[str, str]) -> Optional[cl.User]:
    mode = query_params.get("mode")
    locale = query_params.get("locale", "zh_CN")
    
    # 注意：不能在这里使用 cl.user_session.set()，因为上下文还未建立
    # 将 locale 存储在用户的 metadata 中，稍后在 on_chat_start 中使用
    
    if mode == "standalone":
        return cl.User(
            identifier="admin",
            display_name="admin",
            metadata={"role": "admin", "locale": locale}
        )
    # 获取URL参数
    # user_id = query_params.get("user_id")
    token = query_params.get("token")
    username = await cl_utils.get_username_from_token(token)
    # api_key = query_params.get("api_key")
    # print(user_id,token,api_key)
    # 验证参数
    # if user_id == "admin" and api_key == "secret123":
    if username:
        return cl.User(
            identifier=username,
            display_name=username,
            metadata={"role": "admin", "locale": locale}
        )
    return None

# @cl.password_auth_callback
# def auth_callback(username: str, password: str) -> Optional[cl.User]:
#     # 发送 GET 请求,可以考虑在三方系统增加一个简单的登陆认证
#     # response = requests.get('http://36.137.215.254:8981/jeecg-boot/sys/loginToken/getToken')
#     # result = response.json()
#     # print(result)
#     # token = result['result']['token']
#     # print(token)
#     if (username, password) == ("admin", "123456"):
#         return cl.User(identifier="admin",
#                        metadata={"role": "admin", "provider": "credentials"})
#     else:
#         return None
# @cl.on_window_message
# async def window_message(message: Dict):
#     if message.get("type")=="YQ_CLIENT":
#         logger.info(f"收到前端消息: {message}")
#         #前端的消息格式为{type: 'YQ_CLIENT',data: {username: 'admin'},timestamp: 1640995200000}
#         await cl.send_window_message({"type": 'CHAINLIT_SERVER',"msg":"收到前端消息"})
#         cl.user_session.set("username", message.get("data").get("username"))

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    chat_engine = SimpleChatEngine.from_defaults()
    for message in thread.get("steps", []):
        if message["type"] == "user_message":
            chat_engine.chat_history.append(ChatMessage(content=message["output"], role="user"))
        elif message["type"] == "assistant_message":
            chat_engine.chat_history.append(ChatMessage(content=message["output"], role="assistant"))


@cl.on_settings_update
async def setup_settings(settings):
    cl.user_session.set("settings", settings)


@cl.on_message
async def main(message: cl.Message):
    """优化后的主消息处理函数 - 立即响应用户"""
    # 获取语言设置
    locale = cl.user_session.get("locale", "zh_CN")
    t = get_translation(locale)["status_messages"]
    
    # 立即创建消息并发送初始反馈
    msg = cl.Message(content="", author="Assistant")

    # 立即显示处理状态，让用户知道系统在工作
    await msg.stream_token(t["thinking"])

    chat_profiles = get_translation(locale)["chat_profiles"]
    chat_mode = cl.user_session.get("chat_profile", chat_profiles["knowledge_name"])
    history = cl.user_session.get("history", [])

    try:
        # 使用翻译后的模式名称进行匹配
        if chat_mode == chat_profiles["database_name"]:
            await handle_database_chat(message, msg, history, locale)
        elif chat_mode == chat_profiles["process_name"]:
            await handle_process_chat(message, msg, history, locale)
        elif chat_mode == chat_profiles["knowledge_name"]:
            await handle_knowledge_chat(message, msg, history, locale)
        elif chat_mode == "联网搜索" or chat_mode == "Web Search":
            await handle_web_search(message, msg, locale)
        elif chat_mode == "MCP服务" or chat_mode == "MCP Service":
            await handle_mcp_service(msg, locale)
    except Exception as e:
        logger.error(f"处理消息时发生错误: {e}")
        await msg.stream_token(f"\n❌ 处理过程中发生错误: {str(e)}")
        await msg.send()


async def handle_database_chat(message: cl.Message, msg: cl.Message, history: List, locale: str = "zh_CN"):
    """优化的数据库对话处理"""
    try:
        import utils.vanna_utils as vanna_utils
        t = get_translation(locale)["status_messages"]

        # 清空思考状态，显示具体进度
        msg.content = ""
        await msg.stream_token(t["generating_sql"], is_sequence=True)

        sql = await vanna_utils.generate_sql(message.content, history=history)

        await msg.stream_token(f"\n{t['validating_sql']}", is_sequence=False)
        is_valid = await vanna_utils.is_sql_valid(sql)

        if is_valid:
            await msg.stream_token(f"\n{t['executing_query']}", is_sequence=False)
            df = await vanna_utils.execute_query(sql)

            # 展示结果
            await msg.stream_token(f"\n{t['query_complete']}", is_sequence=False)
            await msg.send()

            # 分别发送表格和图表
            table_msg = cl.Message(content=df.to_markdown(index=False), author="Assistant")
            await table_msg.send()

            fig = await vanna_utils.plot(human_query=message.content, sql=sql, df=df)
            elements = [cl.Plotly(name="chart", figure=fig, display="inline")]
            chart_msg = cl.Message(content=t["chart_content"], elements=elements, author="Assistant")
            await chart_msg.send()

            # 更新历史记录
            history.extend([
                {"content": sql, "role": "assistant"},
                {"content": message.content, "role": "user"}
            ])
            cl.user_session.set("history", history)
        else:
            await msg.stream_token(f"\n{t['sql_error']}", is_sequence=False)
            await msg.send()

    except Exception as e:
        await msg.stream_token(f"\n{t['database_error']}: {str(e)}", is_sequence=False)
        await msg.send()


async def handle_process_chat(message: cl.Message, msg: cl.Message, history: List, locale: str = "zh_CN"):
    """优化的全流程对话处理"""
    try:
        import utils.vanna_utils as vanna_utils
        t = get_translation(locale)["status_messages"]
        
        msg.content = ""
        await msg.stream_token(t["generating_sql"], is_sequence=True)

        sql = await vanna_utils.generate_sql(message.content, history=history)

        await msg.stream_token(f"\n{t['validating_sql']}", is_sequence=False)
        is_valid = await vanna_utils.is_sql_valid(sql)

        if is_valid:
            await msg.stream_token(f"\n{t['executing_query']}", is_sequence=False)
            df = await vanna_utils.execute_query(sql)

            await msg.stream_token(f"\n{t['processing_result']}", is_sequence=False)
            json_data = json.loads(df.to_json(orient='records', force_ascii=False))

            if json_data:
                await msg.stream_token(f"\n{t['getting_report']}", is_sequence=False)
                url = json_data[0]["report_url"]
                excelConfigId = json_data[0]["report_param"]
                token = await cl_utils.getToken()

                headers = {
                    'x-access-token': token,
                    'Content-Type': 'application/json'
                }
                data = {
                    "excelConfigId": excelConfigId,
                    "queryParam": {
                        "pageNo": "1",
                        "pageSize": 10,
                        "currentPageNo": "1",
                        "currentPageSize": 10
                    }
                }

                # 使用异步请求避免阻塞
                response = await asyncio.create_task(
                    asyncio.to_thread(requests.post, url, headers=headers, json=data)
                )

                if response.status_code == 200 and response.text.find("success") == -1:
                    await msg.stream_token(f"\n{t['generating_pdf']}", is_sequence=False)
                    await msg.send()

                    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                        temp_file.write(response.content)
                        temp_file_path = temp_file.name

                    file_viewer = cl.Pdf(name="downloaded_file.pdf", display="inline", path=temp_file_path, page=1)
                    await cl.Message(content=t["report_content"], elements=[file_viewer]).send()
                else:
                    await msg.stream_token(f"\n{t['data_fetch_failed']}", is_sequence=False)
                    await msg.send()
            else:
                await msg.stream_token(f"\n{t['incomplete_result']}", is_sequence=False)
                await msg.send()

            # 更新历史记录
            history.extend([
                {"content": sql, "role": "assistant"},
                {"content": message.content, "role": "user"}
            ])
            cl.user_session.set("history", history)
        else:
            await msg.stream_token(f"\n{t['sql_error']}", is_sequence=False)
            await msg.send()
    except Exception as e:
        await msg.stream_token(f"\n⚠️ 全流程处理失败: {str(e)}", is_sequence=False)
        await msg.send()


async def handle_knowledge_chat(message: cl.Message, msg: cl.Message, history: List, locale: str = "zh_CN"):
    """优化的知识库对话处理"""
    try:
        t = get_translation(locale)["status_messages"]
        
        # 检查上传的文件
        files = []
        for element in message.elements:
            if isinstance(element, cl.File) or isinstance(element, cl.Image):
                files.append(element.path)
                cl.user_session.set("is_local_chat", True)

        chat_settings = cl.user_session.get("settings")
        if chat_settings is None:
            chat_settings = {"collection_name": "yq_1916111815235076097"}

        collection_name = chat_settings.get("collection_name", "yq_1916111815235076097")
        is_local_chat = cl.user_session.get("is_local_chat", False)

        if is_local_chat:
            #本地聊天使用默认的嵌入模型
            selected_embed_model = cl_utils.get_embed_model_by_name()
            msg.content = ""
            await msg.stream_token(t["processing_files"], is_sequence=True)
            await handle_local_chat(files, msg, selected_embed_model, locale)
        else:
            use_kg, model_name = await cl_utils.check_knowledge_use_kg(collection_name)
            # 根据model_name选择对应的嵌入模型
            selected_embed_model = cl_utils.get_embed_model_by_name(model_name)
            logger.info(f"对话使用嵌入模型: {model_name}")
            # 并行检查知识图谱功能
            msg.content = ""
            await msg.stream_token(t["checking_config"], is_sequence=True)

            if use_kg:
                #考虑到嵌入数据库的设计，如果使用了知识图谱，则只能使用默认的嵌入模型
                await handle_knowledge_graph_chat(message, msg, collection_name, selected_embed_model, locale)
                return
            else:
                await handle_vector_chat(message, msg, collection_name, selected_embed_model, locale)

        # 获取聊天引擎并处理消息
        chat_engine = cl.user_session.get("chat_engine")
        if chat_engine is None:
            await msg.stream_token(f"\n{t['system_error']}", is_sequence=False)
            await msg.send()
            return

        # 开始生成回答
        await msg.stream_token(f"\n{t['generating_answer']}\n", is_sequence=False)

        # 异步调用聊天引擎
        res = await chat_engine.astream_chat(message.content)

        # 重置内容准备流式输出
        msg.content = ""
        tokens_received = False

        # 流式输出响应
        async for token in res.async_response_gen():
            if token != "Empty Response":
                await msg.stream_token(token, is_sequence=not tokens_received)
                tokens_received = True

        if not tokens_received:
            await msg.stream_token(t["no_response"], is_sequence=True)

        # 处理图片和来源
        await process_sources_and_images(msg, res, is_local_chat, locale)
        await msg.send()

    except Exception as e:
        logger.error(f"知识库对话处理错误: {e}")
        await msg.stream_token(f"\n⚠️ 知识库查询失败: {str(e)}", is_sequence=False)
        await msg.send()


async def handle_local_chat(files: List, msg: cl.Message, selected_embed_model: HuggingFaceEmbedding, locale: str = "zh_CN"):
    """处理本地文件对话"""
    t = get_translation(locale)["status_messages"]
    identifier = cl.user_session.get("user").identifier
    persist_dir = f"./storage/{identifier}"

    if len(files) > 0:
        await msg.stream_token(f"\n{t['processing_upload']}", is_sequence=False)
        rag = MultiModalRAG(files=files, use_kg=False, embed_model=selected_embed_model)
        index = await rag.create_index_local(persist_dir=persist_dir)
        await msg.stream_token(f"\n{t['file_complete']}", is_sequence=False)
    else:
        await msg.stream_token(f"\n{t['loading_personal']}", is_sequence=False)
        index = await RAG.load_index_local(persist_dir=persist_dir, embed_model=selected_embed_model)

    chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT, similarity_top_k=3)
    cl.user_session.set("chat_engine", chat_engine)


async def handle_knowledge_graph_chat(message: cl.Message, msg: cl.Message, collection_name: str, selected_embed_model: HuggingFaceEmbedding, locale: str = "zh_CN"):
    """处理知识图谱对话"""
    t = get_translation(locale)["status_messages"]
    
    msg.content = ""
    await msg.stream_token(t["searching_kg"], is_sequence=True)

    prompt, source_texts, source_images = await RAG.load_index_with_graph(
        collection_name=collection_name,
        query=message.content,
        embed_model=selected_embed_model
    )

    await msg.stream_token(f"\n{t['generating_answer']}", is_sequence=False)

    llm = Settings.llm
    response = await llm.astream_complete(prompt)

    # 准备数据来源
    source_names = []
    for idx, source_item in enumerate(source_texts):
        if source_item["type"] == "entity":
            source_name = t["entity_info"]
            source_names.append(source_name)
            msg.elements.append(
                cl.Text(content=source_item["text"], name=source_name, display="side")
            )
        elif source_item["type"] == "chunk":
            source_name = f"{t['source_prefix']}{source_item['index']}"
            source_names.append(source_name)
            msg.elements.append(
                cl.Text(content=source_item["text"], name=source_name, display="side")
            )

    for idx, source_item in enumerate(source_images):
        source_name = f"{t['img_source_prefix']}{idx+1}"
        source_names.append(source_name)
        msg.elements.append(
            cl.Text(content=source_item["content"], name=source_name, display="side")
        )
        msg.elements.append(
            cl.Image(url=source_item["url"], name=source_item["name"], display="inline")
        )

    # 重置内容并流式输出
    msg.content = ""
    async for token in response:
        await msg.stream_token(token.text, is_sequence=True)

    # 添加数据来源引用
    if len(source_names) > 0:
        await msg.stream_token(f"\n\n {t['data_sources']}: {', '.join(source_names)}")

    await msg.send()


async def handle_vector_chat(message: cl.Message, msg: cl.Message, collection_name: str, selected_embed_model: HuggingFaceEmbedding, locale: str = "zh_CN"):
    """处理向量数据库对话"""
    t = get_translation(locale)["status_messages"]
    
    await msg.stream_token(f"\n{t['searching_vector']}", is_sequence=False)

    # 根据语言选择系统提示词
    system_prompt = get_translation(locale)["system_prompts"]["vector_chat"]

    index = await RAG.load_index(collection_name=collection_name, embed_model=selected_embed_model)
    chat_engine = index.as_chat_engine(
        chat_mode=ChatMode.CONTEXT,
        similarity_top_k=5,
        system_prompt=system_prompt,
        vector_store_query_mode="hybrid",
    )
    cl.user_session.set("chat_engine", chat_engine)


async def handle_web_search(message: cl.Message, msg: cl.Message, locale: str = "zh_CN"):
    """处理联网搜索"""
    t = get_translation(locale)["status_messages"]
    
    msg.content = ""
    await msg.stream_token(t["searching_web"], is_sequence=True)

    # 根据语言选择系统提示词
    prompts = get_translation(locale)["system_prompts"]
    system_prompt = prompts["web_search"]
    tool_description = prompts["web_search_tool_desc"]

    tool = FunctionTool.from_defaults(fn=cl_utils.websearchtool, description=tool_description)
    agent = FunctionCallingAgent.from_tools(
        [tool],
        system_prompt=system_prompt,
        verbose=True,
        max_function_calls=1
    )

    response = agent.chat(message.content)

    # 重置内容并流式输出
    msg.content = ""
    for token in response.sources[0].content:
        await msg.stream_token(token, is_sequence=True)

    await msg.send()


async def handle_mcp_service(msg: cl.Message, locale: str = "zh_CN"):
    """处理MCP服务"""
    t = get_translation(locale)["status_messages"]
    
    msg.content = ""
    await msg.stream_token(t["mcp_developing"], is_sequence=True)
    await msg.send()


async def process_sources_and_images(msg: cl.Message, res, is_local_chat: bool, locale: str = "zh_CN"):
    """处理数据来源和图片"""
    t = get_translation(locale)["status_messages"]
    
    # 显示图片
    for source_node in res.source_nodes:
        if "type" in source_node.metadata and source_node.metadata["type"] == "image":
            if not is_local_chat:
                msg.elements.append(
                    cl.Image(
                        url=source_node.metadata["image"],
                        name=source_node.metadata["source"],
                        display="inline"
                    )
                )

    # 显示数据来源
    if not isinstance(res.response, SimpleChatEngine):
        source_names = []
        for idx, node_with_score in enumerate(res.source_nodes, 1):
            node = node_with_score.node
            source_name = f"{t['source_prefix']}{idx}"
            source_names.append(source_name)
            msg.elements.append(
                cl.Text(content=node.get_text(), name=source_name, display="side")
            )

        if len(source_names) > 0:
            await msg.stream_token(f"\n\n {t['data_sources']}: {', '.join(source_names)}")