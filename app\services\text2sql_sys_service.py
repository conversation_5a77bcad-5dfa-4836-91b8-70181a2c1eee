"""
Text2SQL 系统管理服务层
提供数据源管理、模式发现、表和列管理、关系管理、值映射等业务逻辑
"""
import json
import traceback
from typing import Any, List, Dict, Optional

from app.schemas import (
    Text2sqlDatasourceResponse, Text2sqlSchematableResponse, Text2sqlSchemacolumnResponse,
    Text2sqlSchemarelationshipDetailed, Text2sqlSchematableWithRelationships, 
    Text2sqlSchematableUpdate, Text2sqlSchemacolumnUpdate, Text2sqlValuemappingResponse,
    Text2sqlValuemappingCreate, Text2sqlValuemappingUpdate
)
from app.services.schema_service import sync_schema_to_graph_db, discover_schema, save_discovered_schema
from app.services.db_service import test_db_connection

# 导入数据源操作工具函数
from dao.text2sqlsys_utils import (
    get_all_datasources, get_datasource_by_id, get_schematable_by_connection_id, 
    get_schemacolumn_by_table_id, get_schemarelationship_by_source_table_id, 
    get_schemarelationship_by_target_table_id, get_schemacolumn_by_id,
    get_schematable_by_id, get_schemarelationship_by_connection_id, 
    get_schematable_by_name, update_schematable, create_schematable, 
    get_schemacolumn_by_name, update_schemacolumn, create_schemacolumn,
    get_schemarelationship_by_column_ids, update_schemarelationship, 
    create_schemarelationship, delete_schemarelationship, 
    get_valuemapping_by_column_id, get_valuemapping, 
    get_valuemapping_by_column_id_and_term, create_valuemapping, 
    get_valuemapping_by_id, update_valuemapping, delete_valuemapping,
    delete_connection_all_data
)
from utils.kg_utils import KnowledgeGraphManager
from utils.logger import get_logger

logger = get_logger()


class Text2SQLSysService:
    """Text2SQL 系统管理服务类"""
    
    def __init__(self):
        """初始化服务"""
        pass
    
    async def get_all_connections(self) -> Dict[str, Any]:
        """获取所有数据库连接"""
        try:
            datasources = await get_all_datasources()
            return {
                "success": True,
                "data": [Text2sqlDatasourceResponse.model_validate(ds) for ds in datasources]
            }
        except Exception as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            return {"success": False, "message": f"获取数据库连接失败: {str(e)}"}
    
    async def get_connection_by_id(self, connection_id: str) -> Dict[str, Any]:
        """根据connection_id获取数据库连接"""
        try:
            datasource = await get_datasource_by_id(connection_id)
            
            if not datasource:
                return {"success": False, "message": f"数据库连接未找到: {connection_id}", "status_code": 404}
            
            return {
                "success": True,
                "data": Text2sqlDatasourceResponse.model_validate(datasource)
            }
        except Exception as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            return {"success": False, "message": f"获取数据库连接失败: {str(e)}"}
    
    async def discover_and_save_schema(self, connection_id: str) -> Dict[str, Any]:
        """发现并保存数据库结构"""
        try:
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": "Connection not found", "status_code": 404}

            # Test the connection first
            await test_db_connection(connection)

            # Discover schema
            schema_info = await discover_schema(connection)

            # Save discovered schema
            tables_data, relationships_data = await save_discovered_schema(connection, schema_info)

            return {
                "success": True,
                "data": {
                    "status": "success",
                    "message": f"Successfully discovered and saved schema for {connection.name}",
                    "tables": tables_data,
                    "relationships": relationships_data
                }
            }
        except Exception as e:
            logger.error(f"Error discovering and saving schema: {str(e)}")
            return {"success": False, "message": f"Error discovering and saving schema: {str(e)}"}
    
    async def get_schema_metadata(self, connection_id: str) -> Dict[str, Any]:
        """获取某个数据源的表结构"""
        try:
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": "Connection not found", "status_code": 404}

            # Get tables for this connection
            tables = await get_schematable_by_connection_id(connection_id)

            # Prepare the response with tables, columns and relationships
            result = []
            for table in tables:
                # Get columns for this table
                columns = await get_schemacolumn_by_table_id(table.id)

                # Get relationships for this table (both source and target)
                source_relationships = await get_schemarelationship_by_source_table_id(table.id)
                target_relationships = await get_schemarelationship_by_target_table_id(table.id)

                # Convert relationships to detailed format
                detailed_relationships = []

                for rel in source_relationships:
                    source_column = await get_schemacolumn_by_id(rel.source_column_id)
                    target_column = await get_schemacolumn_by_id(rel.target_column_id)
                    target_table = await get_schematable_by_id(rel.target_table_id)

                    if source_column and target_column and target_table:
                        detailed_rel = Text2sqlSchemarelationshipDetailed(
                            id=rel.id,
                            connection_id=rel.connection_id,
                            source_table_id=rel.source_table_id,
                            source_column_id=rel.source_column_id,
                            target_table_id=rel.target_table_id,
                            target_column_id=rel.target_column_id,
                            relationship_type=rel.relationship_type,
                            description=rel.description,
                            created_at=rel.created_at,
                            updated_at=rel.updated_at,
                            source_table_name=table.table_name,
                            source_column_name=source_column.column_name,
                            target_table_name=target_table.table_name,
                            target_column_name=target_column.column_name
                        )
                        detailed_relationships.append(detailed_rel)

                for rel in target_relationships:
                    source_column = await get_schemacolumn_by_id(rel.source_column_id)
                    target_column = await get_schemacolumn_by_id(rel.target_column_id)
                    source_table = await get_schematable_by_id(rel.source_table_id)

                    if source_column and target_column and source_table:
                        detailed_rel = Text2sqlSchemarelationshipDetailed(
                            id=rel.id,
                            connection_id=rel.connection_id,
                            source_table_id=rel.source_table_id,
                            source_column_id=rel.source_column_id,
                            target_table_id=rel.target_table_id,
                            target_column_id=rel.target_column_id,
                            relationship_type=rel.relationship_type,
                            description=rel.description,
                            created_at=rel.created_at,
                            updated_at=rel.updated_at,
                            source_table_name=source_table.table_name,
                            source_column_name=source_column.column_name,
                            target_table_name=table.table_name,
                            target_column_name=target_column.column_name
                        )
                        detailed_relationships.append(detailed_rel)

                # Convert SQLAlchemy model objects to Pydantic model objects
                pydantic_columns = [
                    Text2sqlSchemacolumnResponse(
                        id=column.id,
                        table_id=column.table_id,
                        column_name=column.column_name,
                        data_type=column.data_type,
                        description=column.description,
                        is_primary_key=column.is_primary_key,
                        is_foreign_key=column.is_foreign_key,
                        created_at=column.created_at,
                        updated_at=column.updated_at
                    ) for column in columns
                ]

                # Create a SchemaTableWithRelationships object
                table_with_columns = Text2sqlSchematableWithRelationships(
                    id=table.id,
                    connection_id=table.connection_id,
                    table_name=table.table_name,
                    description=table.description,
                    ui_metadata=table.ui_metadata,
                    created_at=table.created_at,
                    updated_at=table.updated_at,
                    columns=pydantic_columns,
                    relationships=detailed_relationships
                )
                result.append(table_with_columns)

            return {"success": True, "data": result}
        except Exception as e:
            logger.error(f"Error retrieving schema metadata: {str(e)}")
            return {"success": False, "message": f"Error retrieving schema metadata: {str(e)}"}
    
    async def get_saved_schema(self, connection_id: str) -> Dict[str, Any]:
        """获取保存的表结构和UI元数据"""
        try:
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": "Connection not found", "status_code": 404}

            # Get tables for this connection
            tables = await get_schematable_by_connection_id(connection_id)

            # Prepare tables data with UI metadata
            tables_data = []
            for table in tables:
                # Get columns for this table
                columns = await get_schemacolumn_by_table_id(table.id)

                # Convert columns to simple dict format
                columns_data = [
                    {
                        "id": column.id,
                        "column_name": column.column_name,
                        "data_type": column.data_type,
                        "description": column.description,
                        "is_primary_key": column.is_primary_key,
                        "is_foreign_key": column.is_foreign_key
                    } for column in columns
                ]

                # Add table with UI metadata
                tables_data.append({
                    "id": table.id,
                    "table_name": table.table_name,
                    "description": table.description,
                    "ui_metadata": table.ui_metadata,
                    "columns": columns_data
                })

            # Get all relationships for this connection
            relationships = await get_schemarelationship_by_connection_id(connection_id)

            # Convert relationships to simple dict format
            relationships_data = []
            for rel in relationships:
                source_table = await get_schematable_by_id(rel.source_table_id)
                target_table = await get_schematable_by_id(rel.target_table_id)
                source_column = await get_schemacolumn_by_id(rel.source_column_id)
                target_column = await get_schemacolumn_by_id(rel.target_column_id)

                if source_table and target_table and source_column and target_column:
                    relationships_data.append({
                        "id": rel.id,
                        "source_table": source_table.table_name,
                        "source_table_id": source_table.id,
                        "source_column": source_column.column_name,
                        "source_column_id": source_column.id,
                        "target_table": target_table.table_name,
                        "target_table_id": target_table.id,
                        "target_column": target_column.column_name,
                        "target_column_id": target_column.id,
                        "relationship_type": rel.relationship_type,
                        "description": rel.description
                    })

            return {
                "success": True,
                "data": {
                    "tables": tables_data,
                    "relationships": relationships_data
                }
            }
        except Exception as e:
            logger.error(f"Error retrieving saved schema: {str(e)}")
            return {"success": False, "message": f"Error retrieving saved schema: {str(e)}"}
    
    async def publish_schema(self, connection_id: str, schema_data: Dict[str, Any]) -> Dict[str, Any]:
        """发布模式元数据到MySQL和图数据库"""
        try:
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": "Connection not found", "status_code": 404}

            # Save to MySQL
            tables_data = schema_data.get("tables", [])
            relationships_data = schema_data.get("relationships", [])

            # Process tables and columns
            for table_data in tables_data:
                table_obj = await get_schematable_by_name(connection_id, table_data["table_name"])

                if table_obj:
                    # Update existing table
                    table_obj = await update_schematable(table_obj.id, {
                        "description": table_data.get("description"),
                        "ui_metadata": table_data.get("ui_metadata")
                    })
                else:
                    # Create new table
                    table_obj = await create_schematable({
                        "connection_id": connection_id,
                        "table_name": table_data["table_name"],
                        "description": table_data.get("description"),
                        "ui_metadata": table_data.get("ui_metadata")
                    })
                    
                # Process columns
                for column_data in table_data.get("columns", []):
                    column_obj = await get_schemacolumn_by_name(table_obj.id, column_data["column_name"])

                    if column_obj:
                        # Update existing column
                        await update_schemacolumn(column_obj.id, {
                            "description": column_data.get("description"),
                            "is_primary_key": column_data.get("is_primary_key"),
                            "is_foreign_key": column_data.get("is_foreign_key")
                        })
                    else:
                        # Create new column
                        await create_schemacolumn({
                            "table_id": table_obj.id,
                            "column_name": column_data["column_name"],
                            "data_type": column_data["data_type"],
                            "description": column_data.get("description"),
                            "is_primary_key": column_data.get("is_primary_key", False),
                            "is_foreign_key": column_data.get("is_foreign_key", False)
                        })

            # Get all existing relationships for this connection
            existing_relationships = await get_schemarelationship_by_connection_id(connection_id)

            # Track which relationships are still valid
            processed_relationship_ids = set()

            # Process relationships from the frontend
            for rel_data in relationships_data:
                # Find source and target tables
                source_table = await get_schematable_by_name(connection_id, rel_data["source_table"])
                target_table = await get_schematable_by_name(connection_id, rel_data["target_table"])

                if not source_table or not target_table:
                    continue

                # Find source and target columns
                source_column = await get_schemacolumn_by_name(source_table.id, rel_data["source_column"])
                target_column = await get_schemacolumn_by_name(target_table.id, rel_data["target_column"])

                if not source_column or not target_column:
                    continue

                # Check if relationship exists
                rel_obj = await get_schemarelationship_by_column_ids(source_column.id, target_column.id)

                if rel_obj:
                    # Update existing relationship
                    await update_schemarelationship(rel_obj.id, {
                        "relationship_type": rel_data.get("relationship_type"),
                        "description": rel_data.get("description")
                    })
                    # Mark this relationship as processed
                    processed_relationship_ids.add(rel_obj.id)
                else:
                    # Create new relationship
                    new_rel = await create_schemarelationship({
                        "connection_id": connection_id,
                        "source_table_id": source_table.id,
                        "source_column_id": source_column.id,
                        "target_table_id": target_table.id,
                        "target_column_id": target_column.id,
                        "relationship_type": rel_data.get("relationship_type"),
                        "description": rel_data.get("description")
                    })
                    # Mark this new relationship as processed
                    processed_relationship_ids.add(new_rel.id)

            # Delete relationships that are no longer in the frontend
            for rel in existing_relationships:
                if rel.id not in processed_relationship_ids:
                    # This relationship was not in the frontend data, so delete it
                    await delete_schemarelationship(rel.id)

            # Sync to Graph DB
            await sync_schema_to_graph_db(connection)

            return {"success": True, "message": "Schema published successfully"}
        except Exception as e:
            error_trace = traceback.format_exc()
            logger.error(f"Error publishing schema: {str(e)}\n{error_trace}")
            return {"success": False, "message": f"Error publishing schema: {str(e)}"}
    
    async def update_table(self, table_id: int, table_in: Text2sqlSchematableUpdate) -> Dict[str, Any]:
        """更新表信息"""
        try:
            table = await get_schematable_by_id(table_id)
            if not table:
                return {"success": False, "message": "Table not found", "status_code": 404}

            # 使用 model_dump() 获取字典数据，并排除 None 值
            update_data = table_in.model_dump(exclude_unset=True)
            updated_table = await update_schematable(table.id, update_data)
            return {"success": True, "data": updated_table}
        except Exception as e:
            logger.error(f"Error updating table: {str(e)}")
            return {"success": False, "message": f"Error updating table: {str(e)}"}
    
    async def update_column(self, column_id: int, column_in: Text2sqlSchemacolumnUpdate) -> Dict[str, Any]:
        """更新列信息"""
        try:
            column = await get_schemacolumn_by_id(column_id)
            if not column:
                return {"success": False, "message": "Column not found", "status_code": 404}

            # 使用 model_dump() 获取字典数据，并排除 None 值
            update_data = column_in.model_dump(exclude_unset=True)
            updated_column = await update_schemacolumn(column.id, update_data)
            return {"success": True, "data": updated_column}
        except Exception as e:
            logger.error(f"Error updating column: {str(e)}")
            return {"success": False, "message": f"Error updating column: {str(e)}"}
    
    async def get_relationship_tips(self) -> Dict[str, Any]:
        """获取关系类型的提示信息"""
        try:
            relationship_tips = {
                "1-to-1": {
                    "title": "一对一关系 (1:1)",
                    "description": "每个源记录对应一个目标记录，反之亦然。例如：一个人只能有一个身份证号，一个身份证号只能属于一个人。",
                    "example": "Person.id → IDCard.person_id",
                    "when_to_use": "当两个实体之间存在唯一对应关系时使用。"
                },
                "1-to-N": {
                    "title": "一对多关系 (1:N)",
                    "description": "一个源记录可以对应多个目标记录，但每个目标记录只对应一个源记录。例如：一个部门有多个员工，但每个员工只属于一个部门。",
                    "example": "Department.id → Employee.department_id",
                    "when_to_use": "当父表中的一条记录对应子表中的多条记录时使用。"
                },
                "N-to-1": {
                    "title": "多对一关系 (N:1)",
                    "description": "多个源记录对应同一个目标记录，但每个源记录只对应一个目标记录。例如：多个订单可能属于同一个客户，但每个订单只属于一个客户。",
                    "example": "Order.customer_id → Customer.id",
                    "when_to_use": "当子表中的多条记录引用父表中的同一条记录时使用。"
                },
                "N-to-M": {
                    "title": "多对多关系 (N:M)",
                    "description": "一个源记录可以对应多个目标记录，一个目标记录也可以对应多个源记录。例如：一个学生可以选修多门课程，一门课程也可以被多个学生选修。",
                    "example": "通过关联表实现：Student ↔ StudentCourse ↔ Course",
                    "when_to_use": "当两个实体之间存在多对多的关联关系时使用，通常需要通过关联表实现。"
                }
            }
            return {"success": True, "data": relationship_tips}
        except Exception as e:
            logger.error(f"获取关系类型提示信息失败: {str(e)}")
            return {"success": False, "message": f"获取关系类型提示信息失败: {str(e)}"}
    
    async def get_graph_data(self, connection_id: str) -> Dict[str, Any]:
        """获取知识图谱可视化数据"""
        try:
            # Check if connection exists
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": "Connection not found", "status_code": 404}

            kg_manager = KnowledgeGraphManager.get_instance()
            driver = await kg_manager.neo4j_driver
            
            with driver.session(database=kg_manager.neo4j_db) as session:
                # 查询指定connection_id的所有表节点
                nodes_query = """
                MATCH (t:Table)
                WHERE t.connection_id = $connection_id
                RETURN t.id as id, 
                       t.name as name, 
                       t.description as description,
                       t.columns_count as columns_count,
                       t.columns_info as columns_info,
                       t.table_type as table_type
                """
                
                nodes_result = session.run(nodes_query, connection_id=connection_id)
                
                # 构建节点数据
                nodes = []
                for record in nodes_result:
                    node_data = {
                        "id": record["id"],
                        "name": record["name"],
                        "description": record["description"] or "",
                        "columns_count": record["columns_count"] or 0,
                        "table_type": record["table_type"] or "table",
                        "type": "table"  # 前端识别的节点类型
                    }
                    
                    # 解析列信息
                    try:
                        columns_info = json.loads(record["columns_info"] or "[]")
                        node_data["columns"] = columns_info
                    except (json.JSONDecodeError, TypeError):
                        node_data["columns"] = []
                    
                    nodes.append(node_data)
                
                # 查询指定connection_id的所有表关系
                edges_query = """
                MATCH (source:Table)-[r:REFERENCES]->(target:Table)
                WHERE r.connection_id = $connection_id
                RETURN source.id as source_id,
                       target.id as target_id,
                       r.relationship_type as relationship_type,
                       r.description as description,
                       r.source_table as source_table,
                       r.source_column as source_column,
                       r.target_table as target_table,
                       r.target_column as target_column
                """
                
                edges_result = session.run(edges_query, connection_id=connection_id)
                
                # 构建边数据
                edges = []
                for record in edges_result:
                    edge_data = {
                        "id": f"{record['source_id']}-{record['target_id']}",
                        "source": record["source_id"],
                        "target": record["target_id"],
                        "relationship_type": record["relationship_type"] or "REFERENCES",
                        "description": record["description"] or "",
                        "source_table": record["source_table"],
                        "source_column": record["source_column"],
                        "target_table": record["target_table"],
                        "target_column": record["target_column"],
                        "type": "relationship"  # 前端识别的边类型
                    }
                    edges.append(edge_data)
                
                # 构建返回数据
                graph_data = {
                    "nodes": nodes,
                    "edges": edges,
                    "connection_info": {
                        "id": connection.id,
                        "name": connection.name,
                        "db_type": connection.db_type,
                        "description": f"数据库连接: {connection.name}"
                    },
                    "statistics": {
                        "total_tables": len(nodes),
                        "total_relationships": len(edges),
                        "total_columns": sum(node.get("columns_count", 0) for node in nodes)
                    }
                }
                
                return {"success": True, "data": graph_data}
                
        except Exception as e:
            error_trace = traceback.format_exc()
            logger.error(f"获取知识图谱数据失败: {str(e)}\n{error_trace}")
            return {"success": False, "message": f"获取知识图谱数据失败: {str(e)}"}
    
    # Value Mapping 相关方法
    async def read_value_mappings(self, column_id: Optional[int] = None, skip: int = 0, limit: int = 100) -> Dict[str, Any]:
        """获取值映射列表"""
        try:
            if column_id:
                mappings = await get_valuemapping_by_column_id(column_id, skip, limit)
            else:
                mappings = await get_valuemapping(skip, limit)
            return {"success": True, "data": mappings}
        except Exception as e:
            logger.error(f"获取值映射失败: {str(e)}")
            return {"success": False, "message": f"获取值映射失败: {str(e)}"}
    
    async def create_value_mapping(self, mapping_in: Text2sqlValuemappingCreate) -> Dict[str, Any]:
        """创建值映射"""
        try:
            # Check if column exists
            column = await get_schemacolumn_by_id(mapping_in.column_id)
            if not column:
                return {"success": False, "message": "Column not found", "status_code": 404}

            # Check if mapping already exists
            existing = await get_valuemapping_by_column_id_and_term(mapping_in.column_id, mapping_in.nl_term)
            if existing:
                return {"success": False, "message": "Mapping already exists for this term", "status_code": 400}

            # Convert Pydantic object to dict
            mapping_data = mapping_in.model_dump()
            mapping = await create_valuemapping(mapping_data)
            return {"success": True, "data": mapping}
        except Exception as e:
            logger.error(f"创建值映射失败: {str(e)}")
            return {"success": False, "message": f"创建值映射失败: {str(e)}"}
    
    async def read_value_mapping(self, mapping_id: int) -> Dict[str, Any]:
        """根据ID获取值映射"""
        try:
            mapping = await get_valuemapping_by_id(mapping_id)
            if not mapping:
                return {"success": False, "message": "Value mapping not found", "status_code": 404}
            return {"success": True, "data": mapping}
        except Exception as e:
            logger.error(f"获取值映射失败: {str(e)}")
            return {"success": False, "message": f"获取值映射失败: {str(e)}"}
    
    async def update_value_mapping(self, mapping_id: int, mapping_in: Text2sqlValuemappingUpdate) -> Dict[str, Any]:
        """更新值映射"""
        try:
            mapping = await get_valuemapping_by_id(mapping_id)
            if not mapping:
                return {"success": False, "message": "Value mapping not found", "status_code": 404}

            # Check if new term would create a duplicate
            if mapping_in.nl_term and mapping_in.nl_term != mapping.nl_term:
                existing = await get_valuemapping_by_column_id_and_term(
                    column_id=mapping.column_id,
                    nl_term=mapping_in.nl_term
                )
                if existing:
                    return {"success": False, "message": "Mapping already exists for this term", "status_code": 400}

            # Convert Pydantic object to dict
            update_data = mapping_in.model_dump(exclude_unset=True)
            updated_mapping = await update_valuemapping(mapping_id, update_data)
            return {"success": True, "data": updated_mapping}
        except Exception as e:
            logger.error(f"更新值映射失败: {str(e)}")
            return {"success": False, "message": f"更新值映射失败: {str(e)}"}
    
    async def delete_value_mapping(self, mapping_id: int) -> Dict[str, Any]:
        """删除值映射"""
        try:
            mapping = await delete_valuemapping(mapping_id)
            if not mapping:
                return {"success": False, "message": "Value mapping not found", "status_code": 404}
            return {"success": True, "data": mapping}
        except Exception as e:
            logger.error(f"删除值映射失败: {str(e)}")
            return {"success": False, "message": f"删除值映射失败: {str(e)}"}
    
    async def delete_connection_all_data_endpoint(self, request_body: Dict[str, Any]) -> Dict[str, Any]:
        """删除指定连接的所有相关数据，包括知识图谱和向量库数据"""
        try:
            # 从请求体中获取connection_id
            connection_id = request_body.get("connection_id")
            if not connection_id:
                return {"success": False, "message": "缺少必需参数: connection_id", "status_code": 400}
            
            # 验证连接是否存在
            connection = await get_datasource_by_id(connection_id)
            if not connection:
                return {"success": False, "message": f"数据库连接未找到: {connection_id}", "status_code": 404}
            
            overall_result = {
                "connection_id": connection_id,
                "connection_name": connection.name,
                "mysql_deletion": {},
                "neo4j_deletion": {},
                "vector_deletion": {},
                "success": False,
                "errors": []
            }
            
            logger.info(f"开始删除连接 '{connection.name}' ({connection_id}) 的所有相关数据")
            
            # 步骤1: 删除MySQL数据库中的数据
            logger.info("步骤1: 删除MySQL数据库中的schema数据...")
            mysql_result = await delete_connection_all_data(connection_id)
            overall_result["mysql_deletion"] = mysql_result
            
            if not mysql_result["success"]:
                overall_result["errors"].extend(mysql_result.get("errors", []))
                logger.error("MySQL数据删除失败")
            else:
                logger.info(f"MySQL数据删除成功: 删除了 {mysql_result['deleted_tables']} 个表, {mysql_result['deleted_columns']} 个列, {mysql_result['deleted_relationships']} 个关系, {mysql_result['deleted_value_mappings']} 个值映射")
            
            # 步骤2: 删除Neo4j知识图谱中的数据
            logger.info("步骤2: 删除Neo4j知识图谱中的数据...")
            try:
                kg_manager = KnowledgeGraphManager.get_instance()
                driver = await kg_manager.neo4j_driver
                
                with driver.session(database=kg_manager.neo4j_db) as session:
                    # 先查询有多少个节点要删除
                    count_query = """
                    MATCH (t:Table)
                    WHERE t.connection_id = $connection_id
                    RETURN count(t) as nodes_count
                    """
                    
                    count_result = session.run(count_query, {"connection_id": connection_id}).single()
                    deleted_nodes = count_result["nodes_count"] if count_result else 0
                    
                    # 然后删除所有相关的Table节点和关系
                    if deleted_nodes > 0:
                        delete_query = """
                        MATCH (t:Table)
                        WHERE t.connection_id = $connection_id
                        DETACH DELETE t
                        """
                        session.run(delete_query, {"connection_id": connection_id})
                    
                    overall_result["neo4j_deletion"] = {
                        "deleted_table_nodes": deleted_nodes,
                        "success": True
                    }
                    logger.info(f"Neo4j数据删除成功: 删除了 {deleted_nodes} 个表节点及其所有关系")
                    
            except Exception as e:
                error_msg = f"删除Neo4j知识图谱数据时出错: {str(e)}"
                overall_result["errors"].append(error_msg)
                overall_result["neo4j_deletion"] = {
                    "success": False,
                    "error": error_msg
                }
                logger.error(error_msg)
            
            # 步骤3: 删除向量库中的数据
            logger.info("步骤3: 删除向量库中的数据...")
            try:
                from rag.entity_vectorizer import EntityVectorizer
                vectorizer = EntityVectorizer()
                
                vector_success = await vectorizer.clear_text2sql_connection_data(connection_id)
                overall_result["vector_deletion"] = {
                    "success": vector_success
                }
                
                if vector_success:
                    logger.info("向量库数据删除成功")
                else:
                    error_msg = "向量库数据删除失败"
                    overall_result["errors"].append(error_msg)
                    logger.error(error_msg)
                    
            except Exception as e:
                error_msg = f"删除向量库数据时出错: {str(e)}"
                overall_result["errors"].append(error_msg)
                overall_result["vector_deletion"] = {
                    "success": False,
                    "error": error_msg
                }
                logger.error(error_msg)
            
            # 判断整体操作是否成功
            mysql_success = mysql_result.get("success", False)
            neo4j_success = overall_result["neo4j_deletion"].get("success", False)
            vector_success = overall_result["vector_deletion"].get("success", False)
            
            overall_result["success"] = mysql_success and neo4j_success and vector_success
            
            if overall_result["success"]:
                logger.info(f"连接 '{connection.name}' ({connection_id}) 的所有相关数据删除完成")
                return {"success": True, "message": "删除成功", "data": overall_result}
            else:
                logger.warning(f"连接 '{connection.name}' ({connection_id}) 的数据删除部分成功，请查看详细信息")
                return {"success": False, "message": "部分删除失败", "data": overall_result}
            
        except Exception as e:
            error_msg = f"删除连接数据时发生未知错误: {str(e)}"
            logger.error(error_msg)
            traceback.print_exc()
            return {"success": False, "message": f"删除连接数据失败: {str(e)}"}


# 创建全局服务实例
text2sql_sys_service = Text2SQLSysService() 