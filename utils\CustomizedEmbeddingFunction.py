from FlagEmbedding import BGEM3FlagModel
from typing import List
from llama_index.vector_stores.milvus.utils import BaseSparseEmbeddingFunction
import torch
import gc
import time
import threading

from config import settings
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

class CustomizedEmbeddingFunction(BaseSparseEmbeddingFunction):
    # 单例实例缓存
    _instance = None
    _initialized = False
    _last_used_time = None
    _cleanup_timer = None
    _auto_cleanup_enabled = True
    _cleanup_delay = 300  # 5分钟后自动清理显存（单位：秒）
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(CustomizedEmbeddingFunction, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 确保只初始化一次
        if not self._initialized:
            self.model = None
            self._load_model()
            CustomizedEmbeddingFunction._initialized = True
            CustomizedEmbeddingFunction._last_used_time = time.time()
    
    def _load_model(self):
        """加载模型到显存"""
        if self.model is None:
            logger.info("正在加载 BGEM3FlagModel 到显存...")
            self.model = BGEM3FlagModel(settings.configuration.sparse_embedding_function,
                                        # cache_dir=settings.configuration.model_cache_folder,
                                        use_fp16=False,
                                        devices="cuda:0"
                                        )
            logger.info("BGEM3FlagModel 加载完成")
    
    def _unload_model(self):
        """从显存中释放模型"""
        if self.model is not None:
            logger.info("正在释放 BGEM3FlagModel 显存...")
            del self.model
            self.model = None
            # 强制垃圾回收
            gc.collect()
            # 清空CUDA缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            logger.info("BGEM3FlagModel 显存已释放")
    
    def _update_last_used_time(self):
        """更新最后使用时间并重置清理计时器"""
        CustomizedEmbeddingFunction._last_used_time = time.time()
        
        # 取消之前的清理计时器
        if CustomizedEmbeddingFunction._cleanup_timer is not None:
            CustomizedEmbeddingFunction._cleanup_timer.cancel()
        
        # 如果启用了自动清理，设置新的清理计时器
        if CustomizedEmbeddingFunction._auto_cleanup_enabled:
            CustomizedEmbeddingFunction._cleanup_timer = threading.Timer(
                CustomizedEmbeddingFunction._cleanup_delay, 
                self._auto_cleanup
            )
            CustomizedEmbeddingFunction._cleanup_timer.start()
    
    def _auto_cleanup(self):
        """自动清理显存"""
        current_time = time.time()
        time_since_last_use = current_time - CustomizedEmbeddingFunction._last_used_time
        
        # 如果超过清理延迟时间且启用了自动清理，则释放显存
        if (time_since_last_use >= CustomizedEmbeddingFunction._cleanup_delay and 
            CustomizedEmbeddingFunction._auto_cleanup_enabled):
            logger.info(f"BGEM3FlagModel 已 {time_since_last_use:.1f} 秒未使用，自动释放显存")
            self._unload_model()

    def encode_queries(self, queries: List[str]):
        # 确保模型已加载
        if self.model is None:
            self._load_model()
        
        # 更新使用时间
        self._update_last_used_time()
        
        outputs = self.model.encode(
            queries,
            return_dense=False,
            return_sparse=True,
            return_colbert_vecs=False,
        )["lexical_weights"]
        return [self._to_standard_dict(output) for output in outputs]

    def encode_documents(self, documents: List[str]):
        # 确保模型已加载
        if self.model is None:
            self._load_model()
        
        # 更新使用时间
        self._update_last_used_time()
        
        outputs = self.model.encode(
            documents,
            return_dense=False,
            return_sparse=True,
            return_colbert_vecs=False,
        )["lexical_weights"]
        return [self._to_standard_dict(output) for output in outputs]

    def _to_standard_dict(self, raw_output):
        result = {}
        for k in raw_output:
            result[int(k)] = raw_output[k]
        return result
    
    @classmethod
    def force_cleanup(cls):
        """强制释放显存"""
        if cls._instance is not None:
            cls._instance._unload_model()
    
    @classmethod
    def set_auto_cleanup(cls, enabled: bool, delay_seconds: int = 300):
        """设置自动清理配置
        
        Args:
            enabled: 是否启用自动清理
            delay_seconds: 清理延迟时间（秒）
        """
        cls._auto_cleanup_enabled = enabled
        cls._cleanup_delay = delay_seconds
        logger.info(f"自动清理设置: {'启用' if enabled else '禁用'}, 延迟时间: {delay_seconds} 秒")
    
    @classmethod
    def get_memory_status(cls):
        """获取显存使用状态"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0) / 1024**3  # GB
            reserved = torch.cuda.memory_reserved(0) / 1024**3   # GB
            return {
                "model_loaded": cls._instance.model is not None if cls._instance else False,
                "cuda_allocated_gb": allocated,
                "cuda_reserved_gb": reserved,
                "last_used": cls._last_used_time,
                "auto_cleanup_enabled": cls._auto_cleanup_enabled
            }
        return {"cuda_available": False}