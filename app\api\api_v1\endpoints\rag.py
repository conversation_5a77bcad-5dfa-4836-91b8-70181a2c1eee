from fastapi import UploadFile, Form, APIRouter, BackgroundTasks, Query, Request, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Dict, Optional, Set
import asyncio
import uuid
import json
import time

from app.services.rag_service import rag_service
from app.services.rag_chat_service import rag_chat_service
from app.schemas.rag import (
    RAGSessionInfo,
    MilvusEntity,
    UploadFileByDocIdEntity,
    EmbeddingStatusEntity,
    CreateCollectionEntity,
    KnowledgeGraphChatEntity,
    HistoryCleanupEntity
)
from utils.r import R
from utils.logger import get_logger
import utils.ui_utils as cl_utils
from rag.base_rag import RAG
from utils.sse_chat_utils import (
    process_knowledge_graph_chat_query,
)
from utils.file_types import (
    validate_upload_files,
    validate_file_paths,
    get_supported_file_types_description
)

# 获取logger实例
logger = get_logger()

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)


# 会话信息数据结构已移至 app/schemas/rag/session.py


# RAG会话管理字典
rag_sessions: Dict[str, RAGSessionInfo] = {}
rag_user_sessions: Dict[str, Set[str]] = {}


# 请求实体类已移至 app/schemas/rag/ 目录下


# RAG会话管理函数
def create_rag_session(user_id: str, session_id: str = None) -> RAGSessionInfo:
    """创建新的RAG会话"""
    if not session_id:
        session_id = str(uuid.uuid4())

    # 创建会话信息
    session_info = RAGSessionInfo(session_id, user_id)
    rag_sessions[session_id] = session_info

    # 更新用户会话映射
    if user_id not in rag_user_sessions:
        rag_user_sessions[user_id] = set()
    rag_user_sessions[user_id].add(session_id)

    logger.info(f"创建新RAG会话: session_id={session_id}, user_id={user_id}")
    return session_info


def get_rag_session(session_id: str) -> Optional[RAGSessionInfo]:
    """获取RAG会话信息"""
    session_info = rag_sessions.get(session_id)
    if session_info:
        session_info.last_activity = time.time()
    return session_info


def cleanup_rag_session(session_id: str):
    """清理RAG会话"""
    session_info = rag_sessions.get(session_id)
    if not session_info:
        return

    user_id = session_info.user_id

    # 取消任务
    if session_info.task and not session_info.task.done():
        session_info.task.cancel()

    # 从映射中移除
    if user_id in rag_user_sessions:
        rag_user_sessions[user_id].discard(session_id)
        if not rag_user_sessions[user_id]:
            del rag_user_sessions[user_id]

    # 删除会话
    del rag_sessions[session_id]
    logger.info(f"清理RAG会话: session_id={session_id}, user_id={user_id}")


def cleanup_expired_rag_sessions():
    """清理过期的RAG会话"""
    current_time = time.time()
    expired_sessions = []

    for session_id, session_info in rag_sessions.items():
        if current_time - session_info.last_activity > 3600:  # 1小时超时
            expired_sessions.append(session_id)

    for session_id in expired_sessions:
        cleanup_rag_session(session_id)

    if expired_sessions:
        logger.info(f"清理了 {len(expired_sessions)} 个过期的RAG会话")


def is_rag_session_connected(session_id: str) -> bool:
    """检查RAG会话连接是否有效"""
    session_info = get_rag_session(session_id)
    return session_info is not None and session_info.is_connected


async def safe_send_rag_message(session_id: str, message: dict) -> bool:
    """安全地发送SSE消息到指定RAG会话"""
    try:
        session_info = get_rag_session(session_id)
        if not session_info or not session_info.is_connected:
            logger.debug(f"RAG会话 {session_id} 连接无效，跳过消息发送")
            return False

        # 记录消息发送信息
        message_type = message.get("type", "unknown")
        message_content = message.get("content", "")
        content_length = len(message_content) if message_content else 0

        # 检查队列状态
        queue_size = session_info.event_queue.qsize()

        logger.debug(
            f"发送消息到RAG会话 {session_id} - 类型: {message_type}, 长度: {content_length}, 队列大小: {queue_size}")

        # 如果队列太大，可能存在堵塞问题
        if queue_size > 10:
            logger.warning(f"RAG会话 {session_id} 的消息队列过大 ({queue_size})，可能存在堵塞问题")

        await session_info.event_queue.put(message)

        # 记录成功发送
        logger.debug(f"消息已成功加入队列: {message_type}")
        return True

    except Exception as e:
        logger.warning(f"发送SSE消息到RAG会话 {session_id} 失败: {str(e)}")
        session_info = rag_sessions.get(session_id)
        if session_info:
            session_info.is_connected = False
        return False


def format_sse_message(data: dict, event: str = "message") -> str:
    """格式化SSE消息"""
    json_data = json.dumps(data, ensure_ascii=False)
    return f"event: {event}\ndata: {json_data}\n\n"

async def cleanup_rag_session_resources(session_id: str):
    """清理RAG会话资源的后台任务"""
    try:
        session_info = get_rag_session(session_id)
        if not session_info:
            return

        # 等待查询任务完成或被取消
        if session_info.task:
            try:
                await session_info.task
            except asyncio.CancelledError:
                logger.info(f"RAG会话 {session_id} 的查询任务已被取消")
            except Exception as e:
                logger.warning(f"RAG会话 {session_id} 的查询任务执行出错: {str(e)}")

        # 等待一段时间后清理资源（给SSE连接时间关闭）
        await asyncio.sleep(30)

        # 检查会话是否仍然活跃
        session_info = get_rag_session(session_id)
        if session_info and not session_info.is_connected:
            # 如果连接已关闭且没有活动，清理会话
            cleanup_rag_session(session_id)

        logger.info(f"RAG会话 {session_id} 的资源清理完成")

    except Exception as e:
        logger.error(f"清理RAG会话 {session_id} 资源时出错: {str(e)}")



# 业务逻辑已移至服务层

@router.post("/uploadfilesbydocid",
             summary="根据文档ID上传文件",
             description=f"根据指定的文档ID上传文件进行向量化处理，默认使用多模态模式，支持知识图谱增强模式。{get_supported_file_types_description()}")
async def create_upload_files_by_docid(request: UploadFileByDocIdEntity, background_tasks: BackgroundTasks):
    """根据文档ID上传文件进行嵌入"""
    try:
        result = await rag_service.upload_files_by_docid(
            collection_name=request.collection_name,
            use_kg=request.use_kg,
            doc_id=request.docId,
            embedding_model=request.embedding_model,
            background_task_func=background_tasks.add_task
        )

        if result["success"]:
            return R.ok(result["message"])
        else:
            return R.error(result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {e}")
        return R.error(f"上传文件失败: {str(e)}")


@router.post("/uploadfiles",
             summary="上传文件进行向量化",
             description=f"上传多个文件进行向量化处理，默认使用多模态模式，支持知识图谱集成。{get_supported_file_types_description()}")
async def create_upload_files(files: list[UploadFile], background_tasks: BackgroundTasks,
                              collection_name: str = Form(default="default"), docId: str = Form(default="")):
    """上传文件进行嵌入"""
    try:
        # 验证文件类型
        validate_upload_files(files)
        
        result = await rag_service.upload_files(
            files=files,
            collection_name=collection_name,
            doc_id=docId,
            background_task_func=background_tasks.add_task
        )

        if result["success"]:
            return R.ok(result["message"])
        else:
            return R.error(result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {e}")
        return R.error(f"上传文件失败: {str(e)}")


@router.post("/embedding_status",
             summary="获取文档嵌入状态",
             description="查询指定文档的向量化处理状态，包括进度、完成状态和错误信息")
async def get_embedding_status(request: EmbeddingStatusEntity):
    """获取文档嵌入状态"""
    try:
        result = await rag_service.get_embedding_status(request.docId)

        if result["success"]:
            return R.ok(result["data"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"获取嵌入状态失败: {e}")
        return R.error(f"获取嵌入状态失败: {str(e)}")


@router.get("/all_embedding_tasks",
            summary="获取所有嵌入任务",
            description="获取当前系统中所有文档嵌入任务的状态列表，包括运行中、已完成和失败的任务")
async def get_all_embedding_tasks():
    """获取所有嵌入任务状态"""
    try:
        result = await rag_service.get_all_embedding_tasks()
        return R.ok(result["data"])
    except Exception as e:
        logger.error(f"获取所有嵌入任务失败: {e}")
        return R.error(f"获取所有嵌入任务失败: {str(e)}")


@router.delete("/clear_embedding_task/{doc_id}",
               summary="清除嵌入任务",
               description="清除指定文档ID的嵌入任务记录，释放相关资源")
async def clear_embedding_task(doc_id: str):
    """清除指定文档的嵌入任务"""
    try:
        result = await rag_service.clear_embedding_task(doc_id)

        if result["success"]:
            return R.ok(result["message"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"清除嵌入任务失败: {e}")
        return R.error(f"清除嵌入任务失败: {str(e)}")


@router.get("/clear_completed_tasks",
            summary="清除已完成任务",
            description="清除所有已完成的嵌入任务记录，保持任务列表清洁")
async def clear_completed_tasks():
    """清除所有已完成的嵌入任务"""
    try:
        result = await rag_service.clear_completed_tasks()
        return R.ok(result["message"])
    except Exception as e:
        logger.error(f"清除已完成任务失败: {e}")
        return R.error(f"清除已完成任务失败: {str(e)}")


@router.get("/getcollections",
            summary="获取向量集合列表",
            description="获取当前用户可访问的知识库集合列表及其基本信息")
async def list_collections(request: Request):
    """获取用户可访问的知识库集合"""
    try:
        identifier = request.query_params.get("identifier", "admin")
        
        result = await rag_service.list_collections(identifier)

        if result["success"]:
            return R.ok(data=result["data"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"获取集合列表失败: {e}")
        return R.error(f"获取集合列表失败: {str(e)}")


@router.post("/deletecollection",
             summary="删除向量集合",
             description="删除指定的向量数据库集合及其所有数据，此操作不可逆")
async def delete_collection(milvus_entity: MilvusEntity):
    """删除向量集合"""
    try:
        result = await rag_service.delete_collection(milvus_entity.collection_name)

        if result["success"]:
            return R.ok(result["message"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"删除集合失败: {e}")
        return R.error(f"删除集合失败: {str(e)}")


@router.post("/createcollection",
             summary="创建向量集合",
             description="创建新的向量数据库集合，用于存储文档向量数据")
async def create_collection(request: CreateCollectionEntity):
    """创建向量集合"""
    try:
        result = await rag_service.create_collection(
            collection_name=request.collection_name,
            embedding_model=request.embedding_model
        )

        if result["success"]:
            return R.ok(result["message"], result.get("data"))
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"创建集合失败: {e}")
        return R.error(f"创建集合失败: {str(e)}")


@router.post("/querydocuments",
             summary="查询集合文档",
             description="查询指定向量集合中的所有文档列表及其元数据信息")
async def query_documents(milvus_entity: MilvusEntity):
    """查询向量集合中的文档"""
    try:
        result = await rag_service.query_documents(
            collection_name=milvus_entity.collection_name,
            file_name=milvus_entity.file_name
        )

        if result["success"]:
            return R.ok(result["data"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"查询文档失败: {e}")
        return R.error(f"查询文档失败: {str(e)}")


@router.post("/deletedocument",
             summary="删除文档",
             description="从向量集合中删除指定的文档及其向量数据")
async def delete_document(milvus_entity: MilvusEntity):
    """删除文档"""
    try:
        result = await rag_service.delete_document(
            collection_name=milvus_entity.collection_name,
            file_name=milvus_entity.file_name
        )

        if result["success"]:
            return R.ok(result["message"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        return R.error(f"删除文档失败: {str(e)}")


@router.get("/qatest",
            summary="问答测试",
            description="使用默认集合进行问答测试，快速验证RAG系统的基本功能")
async def qatest(question: str):
    """问答测试接口"""
    try:
        result = await rag_service.qa_test(question)

        if result["success"]:
            return R.ok(result["data"])
        else:
            return R.error(result["message"])

    except Exception as e:
        logger.error(f"QA测试失败: {e}")
        return R.error(f"QA测试失败: {str(e)}")


@router.get("/getEmebddingText",
            summary="获取文本向量",
            description="获取指定文本的向量表示，用于调试和分析")
async def getEmebddingText(query: str):
    """获取文本向量"""
    try:
        result_embedding = await rag_service.get_embedding_text(query)
        return result_embedding
    except Exception as e:
        logger.error(f"获取文本嵌入失败: {e}")
        return R.error(f"获取文本嵌入失败: {str(e)}")





@router.get("/stream/{session_id}",
            summary="获取RAG流式响应",
            description="通过SSE获取RAG查询任务的实时响应流，包括检索结果和生成内容")
async def rag_sse_stream(session_id: str, request: Request):
    """RAG SSE流式响应"""
    logger.info(f"RAG会话 {session_id} 建立SSE连接")

    # 从查询参数中获取user_id
    user_id = request.query_params.get("user_id")

    session_info = get_rag_session(session_id)
    if not session_info:
        # 如果会话不存在，检查是否提供了user_id
        if not user_id:
            raise HTTPException(
                status_code=400,
                detail=f"RAG会话 {session_id} 不存在，请提供 user_id 参数来创建新会话"
            )

        logger.info(f"RAG会话 {session_id} 不存在，为用户 {user_id} 创建新会话")
        session_info = create_rag_session(user_id, session_id)
    else:
        # 如果会话存在但提供了不同的user_id，验证是否匹配
        if user_id and session_info.user_id != user_id:
            raise HTTPException(
                status_code=403,
                detail=f"RAG会话 {session_id} 属于用户 {session_info.user_id}，不能被用户 {user_id} 访问"
            )

    # 标记连接为活动状态
    session_info.is_connected = True

    async def event_generator():
        try:
            # 发送连接建立消息
            welcome_msg = {
                "type": "message",
                "content": "RAG SSE连接已建立，可以开始查询",
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            }
            yield format_sse_message(welcome_msg)

            # 持续监听事件队列
            while True:
                try:
                    # 检查客户端是否断开连接
                    if await request.is_disconnected():
                        logger.info(f"RAG会话 {session_id} 的SSE客户端已断开连接")
                        break

                    # 检查会话状态
                    if not is_rag_session_connected(session_id):
                        logger.info(f"RAG会话 {session_id} 的SSE连接已标记为关闭")
                        break

                    # 从队列获取事件，设置超时避免无限等待
                    try:
                        message = await asyncio.wait_for(
                            session_info.event_queue.get(),
                            timeout=1.0
                        )

                        # 记录消息发送信息
                        message_type = message.get("type", "unknown")
                        message_content = message.get("content", "")
                        content_length = len(message_content) if message_content else 0

                        logger.debug(f"SSE发送消息 [{message_type}]: 长度={content_length}")

                        # 发送消息
                        formatted_message = format_sse_message(message)
                        yield formatted_message

                        # 对于token类型的消息，添加额外的换行符强制刷新
                        if message_type == "token":
                            yield "\n"

                    except asyncio.TimeoutError:
                        # 发送心跳消息保持连接
                        heartbeat_msg = format_sse_message({"type": "heartbeat"}, "heartbeat")
                        yield heartbeat_msg
                        continue

                except asyncio.CancelledError:
                    logger.info(f"RAG会话 {session_id} 的SSE事件生成器被取消")
                    break
                except Exception as e:
                    logger.error(f"RAG会话 {session_id} 的SSE事件生成器出错: {str(e)}")
                    error_msg = {
                        "type": "error",
                        "content": f"SSE连接出错: {str(e)}",
                        "sessionId": session_id,
                        "userId": session_info.user_id
                    }
                    yield format_sse_message(error_msg)
                    break

        except Exception as e:
            logger.error(f"RAG会话 {session_id} 的SSE连接处理出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 标记连接为关闭状态
            session_info.is_connected = False
            logger.info(f"RAG会话 {session_id} 的SSE连接已关闭")

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no",  # 禁用nginx缓冲
            "Transfer-Encoding": "chunked"  # 确保分块传输
        }
    )







@router.delete("/rag_session/{session_id}",
               summary="取消RAG会话",
               description="取消指定的RAG查询会话，停止处理并清理相关资源")
async def cancel_rag_session(session_id: str):
    """取消RAG会话"""
    try:
        session_info = get_rag_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"RAG会话 {session_id} 不存在")

        # 取消查询任务
        if session_info.task and not session_info.task.done():
            logger.info(f"取消RAG会话 {session_id} 的查询任务")
            session_info.task.cancel()

        # 标记连接为关闭状态
        session_info.is_connected = False

        # 发送取消消息
        await session_info.event_queue.put({
            "type": "cancelled",
            "content": "RAG查询已被取消",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        logger.info(f"RAG会话 {session_id} 已取消")

        return {
            "status": "success",
            "message": "RAG会话已取消"
        }

    except Exception as e:
        logger.error(f"取消RAG会话 {session_id} 时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消RAG会话失败: {str(e)}")


@router.get("/rag_session/{session_id}/status",
            summary="获取RAG会话状态",
            description="获取指定RAG会话的详细状态信息，包括查询进度和连接状态")
async def get_rag_session_status(session_id: str):
    """获取RAG会话状态"""
    try:
        session_info = get_rag_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"RAG会话 {session_id} 不存在")

        status_data = {
            "session_id": session_id,
            "user_id": session_info.user_id,
            "is_connected": session_info.is_connected,
            "has_active_task": session_info.task and not session_info.task.done(),
            "created_at": session_info.created_at,
            "last_activity": session_info.last_activity,
            "current_query": session_info.current_query
        }

        return {
            "status": "success",
            "data": status_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取RAG会话状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取RAG会话状态失败: {str(e)}")


# 知识图谱对话请求实体类已移至 app/schemas/rag/query.py


@router.post("/knowledge_graph_chat_stream",
             summary="智能对话流式查询",
             description=f"启动智能对话流式查询任务，根据配置自动选择知识图谱模式或向量模式，支持历史对话持久化。{get_supported_file_types_description()}")
async def knowledge_graph_chat_stream(request: KnowledgeGraphChatEntity, background_tasks: BackgroundTasks):
    """智能对话流式查询接口，支持知识图谱和向量两种模式，带历史对话持久化"""
    try:
        # 验证必需参数
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="问题内容不能为空")

        # 验证文件类型
        if request.files:
            validate_file_paths(request.files)

        # 生成会话ID
        session_id = request.session_id or str(uuid.uuid4())
        user_id = request.user_id or "anonymous"

        # 创建或获取内存会话信息
        session_info = get_rag_session(session_id)
        if not session_info:
            session_info = create_rag_session(user_id, session_id)

        # 创建或获取数据库会话
        db_session = None
        try:
            db_session = await rag_chat_service.create_or_get_session(
                session_id=session_id,
                user_id=user_id,
                collection_name=request.collection_name,
                session_title=request.question
            )
            logger.info(f"数据库会话创建/获取成功: session_id={session_id}")
        except Exception as e:
            logger.error(f"创建数据库会话失败: {str(e)}")
            # 数据库会话创建失败不阻止功能继续

        # 从数据库加载历史对话
        if db_session:
            try:
                history_messages = await rag_chat_service.get_session_history(
                    session_id=session_id,
                    limit=10  # 最多加载最近10条历史
                )
                
                # 更新内存会话历史
                session_info.clear_history()
                for msg in history_messages:
                    session_info.add_to_history(msg["role"], msg["content"])
                
                logger.info(f"从数据库加载历史对话: session_id={session_id}, 历史数量={len(history_messages)}")
            except Exception as e:
                logger.error(f"加载历史对话失败: {str(e)}")

        # 如果请求中包含对话历史，覆盖数据库历史
        if request.chat_history:
            session_info.clear_history()
            for msg in request.chat_history:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role and content:
                    session_info.add_to_history(role, content)
            logger.info(f"使用请求中的对话历史: session_id={session_id}, 历史数量={len(request.chat_history)}")

        # 更新会话信息
        session_info.current_query = request.question
        session_info.last_activity = time.time()
        session_info.is_connected = True

        # 启动后台处理任务
        task = asyncio.create_task(process_knowledge_graph_chat_query(
            question=request.question,
            collection_name=request.collection_name,
            session_id=session_id,
            files=request.files,  # 传递文件路径列表
            get_rag_session_func=get_rag_session,
            safe_send_rag_message_func=safe_send_rag_message,
            rag_chat_service=rag_chat_service,
            cl_utils=cl_utils,
            RAG=RAG,
            logger=logger
        ))

        # 保存任务引用
        session_info.task = task

        # 启动资源清理任务
        background_tasks.add_task(cleanup_rag_session_resources, session_id)

        logger.info(f"智能对话流式查询任务已启动: session_id={session_id}, question='{request.question}'")

        return {
            "status": "success",
            "message": "智能对话流式查询任务已启动",
            "session_id": session_id,
            "user_id": user_id,
            "stream_url": f"/rag/stream/{session_id}",
            "chat_history_length": len(session_info.chat_history),
            "database_session": db_session is not None,
            "file_count": len(request.files) if request.files else 0,
            "has_files": bool(request.files)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动智能对话流式查询任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动智能对话流式查询任务失败: {str(e)}")


# 知识图谱对话处理函数已迁移至 utils/rag_utils.py


# ==================== 历史对话管理接口 ====================

# 历史对话请求实体类已移至 app/schemas/rag/session.py


@router.get("/chat_sessions/user/{user_id}",
           summary="获取用户会话列表",
           description="获取指定用户的所有对话会话列表，包括会话基本信息和最后消息")
async def get_user_chat_sessions(user_id: str, limit: int = Query(20), offset: int = Query(0)):
    """获取用户的对话会话列表"""
    try:
        result = await rag_chat_service.get_user_sessions(
            user_id=user_id,
            limit=limit,
            offset=offset
        )
        
        return R.ok(data=result)
    except Exception as e:
        logger.error(f"获取用户会话列表失败: {str(e)}")
        return R.error(f"获取用户会话列表失败: {str(e)}")


@router.get("/chat_sessions/search",
           summary="搜索对话历史",
           description="在对话历史中搜索包含特定关键词的消息")
async def search_chat_history(
    user_id: str = Query(..., description="用户ID"),
    keyword: str = Query(..., description="搜索关键词"),
    limit: int = Query(20, description="返回结果数量限制"),
    offset: int = Query(0, description="分页偏移量")
):
    """搜索对话历史"""
    try:
        result = await rag_chat_service.search_history(
            user_id=user_id,
            keyword=keyword,
            limit=limit,
            offset=offset
        )
        
        return R.ok(data=result)
    except Exception as e:
        logger.error(f"搜索对话历史失败: {str(e)}")
        return R.error(f"搜索对话历史失败: {str(e)}")


@router.get("/chat_sessions/stats/user/{user_id}",
           summary="获取用户对话统计",
           description="获取指定用户的对话统计信息，包括会话数量、消息数量等")
async def get_user_chat_stats(user_id: str):
    """获取用户对话统计"""
    try:
        result = await rag_chat_service.get_user_stats(user_id)
        
        return R.ok(data=result)
    except Exception as e:
        logger.error(f"获取用户对话统计失败: {str(e)}")
        return R.error(f"获取用户对话统计失败: {str(e)}")


@router.get("/chat_sessions/{session_id}",
           summary="获取会话详情",
           description="获取指定会话的详细信息，包括会话配置和统计信息")
async def get_chat_session_info(session_id: str):
    """获取会话详情"""
    try:
        result = await rag_chat_service.get_session_info(session_id)
        
        if result:
            return R.ok(data=result)
        else:
            return R.error("会话不存在")
    except Exception as e:
        logger.error(f"获取会话详情失败: {str(e)}")
        return R.error(f"获取会话详情失败: {str(e)}")


@router.get("/chat_sessions/{session_id}/history",
           summary="获取会话历史记录",
           description="获取指定会话的对话历史记录，支持分页和消息过滤")
async def get_chat_session_history(session_id: str, limit: int = Query(50), offset: int = Query(0)):
    """获取会话历史记录"""
    try:
        result = await rag_chat_service.get_session_history(
            session_id=session_id,
            limit=limit,
            offset=offset
        )
        
        return R.ok(data=result)
    except Exception as e:
        logger.error(f"获取会话历史记录失败: {str(e)}")
        return R.error(f"获取会话历史记录失败: {str(e)}")


@router.delete("/chat_sessions/{session_id}",
              summary="删除会话",
              description="删除指定的对话会话及其所有历史记录")
async def delete_chat_session(session_id: str):
    """删除会话"""
    try:
        result = await rag_chat_service.delete_session(session_id)
        
        if result:
            return R.ok("会话删除成功")
        else:
            return R.error("会话不存在或删除失败")
    except Exception as e:
        logger.error(f"删除会话失败: {str(e)}")
        return R.error(f"删除会话失败: {str(e)}")


@router.post("/chat_sessions/cleanup",
            summary="清理历史数据",
            description="批量清理过期的历史对话数据，支持按用户和时间范围筛选")
async def cleanup_chat_history(request: HistoryCleanupEntity):
    """清理历史数据"""
    try:
        result = await rag_chat_service.cleanup_old_history(
            user_id=request.user_id,
            days_before=request.days_before
        )
        
        return R.ok(f"清理完成，删除了 {result} 条历史记录")
    except Exception as e:
        logger.error(f"清理历史数据失败: {str(e)}")
        return R.error(f"清理历史数据失败: {str(e)}")


@router.put("/chat_sessions/{session_id}/title",
           summary="更新会话标题",
           description="更新指定会话的标题")
async def update_session_title(session_id: str, title: str = Query(..., description="新的会话标题")):
    """更新会话标题"""
    try:
        result = await rag_chat_service.update_session_title(session_id, title)
        
        if result:
            return R.ok("会话标题更新成功")
        else:
            return R.error("会话不存在或更新失败")
    except Exception as e:
        logger.error(f"更新会话标题失败: {str(e)}")
        return R.error(f"更新会话标题失败: {str(e)}")


@router.post("/chat_sessions/{session_id}/export",
            summary="导出会话记录",
            description="导出指定会话的完整对话记录为结构化数据")
async def export_chat_session(session_id: str):
    """导出会话记录"""
    try:
        result = await rag_chat_service.export_session(session_id)
        
        if result:
            return R.ok(data=result)
        else:
            return R.error("会话不存在或导出失败")
    except Exception as e:
        logger.error(f"导出会话记录失败: {str(e)}")
        return R.error(f"导出会话记录失败: {str(e)}")


@router.post("/chat_sessions/{session_id}/end",
             summary="结束会话",
             description="将指定会话的状态设置为结束状态")
async def end_chat_session(session_id: str):
    """结束会话"""
    try:
        result = await rag_chat_service.end_session(session_id)

        if result:
            return R.ok("会话已结束")
        else:
            return R.error("会话不存在或结束失败")
    except Exception as e:
        logger.error(f"结束会话失败: {str(e)}")
        return R.error(f"结束会话失败: {str(e)}")


# ==================== 本地文件聊天处理函数已迁移至 utils/rag_utils.py ====================