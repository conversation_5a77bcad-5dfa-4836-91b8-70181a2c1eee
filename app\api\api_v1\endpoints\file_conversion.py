from fastapi import UploadFile, File, APIRouter, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import Optional
import os
import tempfile
import asyncio

from utils.rag_utils import (
    safe_convert_doc_to_docx,
    safe_convert_ppt_to_pptx,
    safe_convert_office_to_pdf
)
from persistent.minio_storage_client import get_minio_client
from utils.logger import get_logger
from utils.conversion_pool import get_conversion_executor

# 获取logger实例
logger = get_logger()

# 使用统一的LibreOffice转换线程池（确保不冲突）
conversion_executor = get_conversion_executor()

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)


async def _upload_to_minio_and_cleanup(file_path: str, object_key: str, mime_type: str) -> Optional[str]:
    """
    将文件上传到Minio并清理本地临时文件
    
    Args:
        file_path: 本地文件路径
        object_key: Minio对象键
        mime_type: MIME类型
        
    Returns:
        上传成功后的URL，失败返回None
    """
    try:
        minio_client = get_minio_client()
        
        # 读取文件并上传
        with open(file_path, "rb") as f:
            result = await minio_client.upload_file(
                object_key=object_key,
                data=f.read(),
                mime=mime_type
            )
        
        url = result.get("url", "")
        if url:
            logger.info(f"文件上传成功: {object_key}")
            return url
        else:
            logger.error(f"文件上传失败: {object_key}")
            return None
            
    except Exception as e:
        logger.error(f"上传文件时出错: {e}")
        return None
    finally:
        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"已清理临时文件: {os.path.basename(file_path)}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {os.path.basename(file_path)}, 错误: {e}")


def _sync_convert_doc_to_docx(input_path: str) -> Optional[str]:
    """同步执行DOC转DOCX转换"""
    return safe_convert_doc_to_docx(input_path)


def _sync_convert_ppt_to_pptx(input_path: str) -> Optional[str]:
    """同步执行PPT转PPTX转换"""
    return safe_convert_ppt_to_pptx(input_path)


def _sync_convert_office_to_pdf(input_path: str) -> Optional[str]:
    """同步执行Office转PDF转换"""
    return safe_convert_office_to_pdf(input_path)


@router.post("/convert/doc-to-docx", summary="DOC转DOCX")
async def convert_doc_to_docx(
    file_url: str = Form(..., description="要转换的DOC文件的Minio URL"),
    target_folder: str = Form(default="converted_docs", description="目标文件夹名称")
):
    """
    将DOC文件转换为DOCX格式并上传到Minio
    
    Args:
        file_url: DOC文件的Minio URL
        target_folder: Minio中的目标文件夹
        
    Returns:
        转换后的DOCX文件URL
    """
    temp_input_file = None
    temp_output_file = None
    
    try:
        # 从URL获取文件名
        from urllib.parse import urlparse, unquote
        parsed_url = urlparse(file_url)
        filename = os.path.basename(unquote(parsed_url.path))
        
        if not filename.lower().endswith('.doc'):
            raise HTTPException(status_code=400, detail="只支持.doc文件格式")
        
        # 下载文件到临时目录
        import requests
        response = requests.get(file_url)
        response.raise_for_status()
        
        # 创建临时输入文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=".doc") as f:
            f.write(response.content)
            temp_input_file = f.name
        
        logger.info(f"开始转换DOC文件: {filename}")
        
        # 使用线程池执行转换（避免阻塞）
        loop = asyncio.get_event_loop()
        temp_output_file = await loop.run_in_executor(
            conversion_executor, 
            _sync_convert_doc_to_docx, 
            temp_input_file
        )
        
        if temp_output_file is None:
            raise HTTPException(
                status_code=500, 
                detail="DOC转DOCX失败，请检查LibreOffice是否正常安装"
            )
        
        # 构建Minio对象键
        base_name = os.path.splitext(filename)[0]
        object_key = f"{target_folder}/{base_name}.docx"
        
        # 上传到Minio并清理
        url = await _upload_to_minio_and_cleanup(
            temp_output_file, 
            object_key, 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        
        if url is None:
            raise HTTPException(status_code=500, detail="文件上传到Minio失败")
        
        logger.success(f"DOC转DOCX转换完成: {filename}")
        
        return JSONResponse(content={
            "status": "success",
            "message": "DOC转DOCX转换成功",
            "original_filename": filename,
            "converted_filename": f"{base_name}.docx",
            "url": url,
            "object_key": object_key
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"DOC转DOCX过程中出错: {e}")
        raise HTTPException(status_code=500, detail=f"转换过程中出错: {str(e)}")
    finally:
        # 清理临时输入文件
        if temp_input_file and os.path.exists(temp_input_file):
            try:
                os.remove(temp_input_file)
            except Exception as e:
                logger.warning(f"清理临时输入文件失败: {e}")


@router.post("/convert/ppt-to-pptx", summary="PPT转PPTX", deprecated=True)
async def convert_ppt_to_pptx(
    file_url: str = Form(..., description="要转换的PPT文件的Minio URL"),
    target_folder: str = Form(default="converted_presentations", description="目标文件夹名称")
):
    """
    将PPT文件转换为PPTX格式并上传到Minio
    
    ⚠️ 注意：此接口已过时，推荐使用 /convert/powerpoint-to-pdf 接口直接转换为PDF
    
    Args:
        file_url: PPT文件的Minio URL
        target_folder: Minio中的目标文件夹
        
    Returns:
        转换后的PPTX文件URL
    """
    temp_input_file = None
    temp_output_file = None
    
    try:
        # 从URL获取文件名
        from urllib.parse import urlparse, unquote
        parsed_url = urlparse(file_url)
        filename = os.path.basename(unquote(parsed_url.path))
        
        if not filename.lower().endswith('.ppt'):
            raise HTTPException(status_code=400, detail="只支持.ppt文件格式")
        
        # 下载文件到临时目录
        import requests
        response = requests.get(file_url)
        response.raise_for_status()
        
        # 创建临时输入文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=".ppt") as f:
            f.write(response.content)
            temp_input_file = f.name
        
        logger.info(f"开始转换PPT文件: {filename}")
        logger.warning("PPT转PPTX接口已过时，推荐直接转换为PDF以获得更好的兼容性")
        
        # 使用线程池执行转换（避免阻塞）
        loop = asyncio.get_event_loop()
        temp_output_file = await loop.run_in_executor(
            conversion_executor, 
            _sync_convert_ppt_to_pptx, 
            temp_input_file
        )
        
        if temp_output_file is None:
            raise HTTPException(
                status_code=500, 
                detail="PPT转PPTX失败，请检查LibreOffice是否正常安装，或考虑使用 /convert/powerpoint-to-pdf 接口"
            )
        
        # 构建Minio对象键
        base_name = os.path.splitext(filename)[0]
        object_key = f"{target_folder}/{base_name}.pptx"
        
        # 上传到Minio并清理
        url = await _upload_to_minio_and_cleanup(
            temp_output_file, 
            object_key, 
            "application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        
        if url is None:
            raise HTTPException(status_code=500, detail="文件上传到Minio失败")
        
        logger.success(f"PPT转PPTX转换完成: {filename}")
        
        return JSONResponse(content={
            "status": "success",
            "message": "PPT转PPTX转换成功（推荐使用PDF转换获得更好兼容性）",
            "original_filename": filename,
            "converted_filename": f"{base_name}.pptx",
            "url": url,
            "object_key": object_key,
            "recommendation": "建议使用 /convert/powerpoint-to-pdf 接口直接转换为PDF"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PPT转PPTX过程中出错: {e}")
        raise HTTPException(status_code=500, detail=f"转换过程中出错: {str(e)}")
    finally:
        # 清理临时输入文件
        if temp_input_file and os.path.exists(temp_input_file):
            try:
                os.remove(temp_input_file)
            except Exception as e:
                logger.warning(f"清理临时输入文件失败: {e}")


@router.post("/convert/powerpoint-to-pdf", summary="PowerPoint文件直接转PDF")
async def convert_powerpoint_to_pdf(
    file_url: str = Form(..., description="要转换的PowerPoint文件的Minio URL（支持PPT和PPTX）"),
    target_folder: str = Form(default="converted_pdfs", description="目标文件夹名称")
):
    """
    将PowerPoint文件（PPT或PPTX）直接转换为PDF格式并上传到Minio
    
    这是推荐的PowerPoint处理方式，统一了PPT和PPTX的处理逻辑。
    相比先转换为PPTX再处理，直接转PDF具有更好的兼容性和处理效率。
    
    Args:
        file_url: PowerPoint文件的Minio URL（支持.ppt和.pptx格式）
        target_folder: Minio中的目标文件夹
        
    Returns:
        转换后的PDF文件URL
    """
    temp_input_file = None
    temp_output_file = None
    
    try:
        # 从URL获取文件名
        from urllib.parse import urlparse, unquote
        parsed_url = urlparse(file_url)
        filename = os.path.basename(unquote(parsed_url.path))
        file_ext = os.path.splitext(filename.lower())[1]
        
        if file_ext not in ['.ppt', '.pptx']:
            raise HTTPException(status_code=400, detail="只支持.ppt和.pptx文件格式")
        
        # 下载文件到临时目录
        import requests
        response = requests.get(file_url)
        response.raise_for_status()
        
        # 创建临时输入文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as f:
            f.write(response.content)
            temp_input_file = f.name
        
        logger.info(f"开始统一PowerPoint转PDF: {filename}")
        
        # 直接转换为PDF（统一处理逻辑）
        loop = asyncio.get_event_loop()
        temp_output_file = await loop.run_in_executor(
            conversion_executor, 
            _sync_convert_office_to_pdf, 
            temp_input_file
        )
        
        if temp_output_file is None:
            raise HTTPException(
                status_code=500, 
                detail="PowerPoint转PDF失败，请检查LibreOffice是否正常安装"
            )
        
        # 构建Minio对象键，添加PowerPoint转换标识
        base_name = os.path.splitext(filename)[0]
        object_key = f"{target_folder}/{base_name}_from_powerpoint.pdf"
        
        # 上传到Minio并清理
        url = await _upload_to_minio_and_cleanup(
            temp_output_file, 
            object_key, 
            "application/pdf"
        )
        
        if url is None:
            raise HTTPException(status_code=500, detail="文件上传到Minio失败")
        
        logger.success(f"PowerPoint转PDF转换完成: {filename}")
        
        return JSONResponse(content={
            "status": "success",
            "message": "PowerPoint转PDF转换成功",
            "original_filename": filename,
            "original_format": file_ext.upper(),
            "converted_filename": f"{base_name}_from_powerpoint.pdf",
            "url": url,
            "object_key": object_key,
            "processing_method": "unified_pdf_conversion",
            "note": "使用统一PDF转换，兼容PPT和PPTX格式",
            "powerpoint_origin": True  # 添加标识字段
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PowerPoint转PDF过程中出错: {e}")
        raise HTTPException(status_code=500, detail=f"转换过程中出错: {str(e)}")
    finally:
        # 清理临时输入文件
        if temp_input_file and os.path.exists(temp_input_file):
            try:
                os.remove(temp_input_file)
            except Exception as e:
                logger.warning(f"清理临时输入文件失败: {e}")


@router.post("/convert/office-to-pdf", summary="Office文件转PDF")
async def convert_office_to_pdf(
    file_url: str = Form(..., description="要转换的Office文件的Minio URL"),
    target_folder: str = Form(default="converted_pdfs", description="目标文件夹名称")
):
    """
    将Office文件（DOC、XLS、PPT、DOCX、XLSX、PPTX等）转换为PDF格式并上传到Minio
    
    Args:
        file_url: Office文件的Minio URL
        target_folder: Minio中的目标文件夹
        
    Returns:
        转换后的PDF文件URL
    """
    temp_input_file = None
    temp_output_file = None
    
    try:
        # 从URL获取文件名
        from urllib.parse import urlparse, unquote
        parsed_url = urlparse(file_url)
        filename = os.path.basename(unquote(parsed_url.path))
        file_ext = os.path.splitext(filename.lower())[1]
        
        allowed_extensions = ['.doc', '.xls', '.ppt', '.docx', '.xlsx', '.pptx']
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式，支持的格式: {', '.join(allowed_extensions)}"
            )
        
        # 下载文件到临时目录
        import requests
        response = requests.get(file_url)
        response.raise_for_status()
        
        # 创建临时输入文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as f:
            f.write(response.content)
            temp_input_file = f.name
        
        logger.info(f"开始转换Office文件: {filename}")
        
        # 使用线程池执行转换（避免阻塞）
        loop = asyncio.get_event_loop()
        temp_output_file = await loop.run_in_executor(
            conversion_executor, 
            _sync_convert_office_to_pdf, 
            temp_input_file
        )
        
        if temp_output_file is None:
            raise HTTPException(
                status_code=500, 
                detail="Office转PDF失败，请检查LibreOffice是否正常安装"
            )
        
        # 构建Minio对象键
        base_name = os.path.splitext(filename)[0]
        object_key = f"{target_folder}/{base_name}.pdf"
        
        # 上传到Minio并清理
        url = await _upload_to_minio_and_cleanup(
            temp_output_file, 
            object_key, 
            "application/pdf"
        )
        
        if url is None:
            raise HTTPException(status_code=500, detail="文件上传到Minio失败")
        
        logger.success(f"Office转PDF转换完成: {filename}")
        
        return JSONResponse(content={
            "status": "success",
            "message": "Office转PDF转换成功",
            "original_filename": filename,
            "converted_filename": f"{base_name}.pdf",
            "url": url,
            "object_key": object_key
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Office转PDF过程中出错: {e}")
        raise HTTPException(status_code=500, detail=f"转换过程中出错: {str(e)}")
    finally:
        # 清理临时输入文件
        if temp_input_file and os.path.exists(temp_input_file):
            try:
                os.remove(temp_input_file)
            except Exception as e:
                logger.warning(f"清理临时输入文件失败: {e}")


@router.get("/convert/status", summary="获取转换服务状态")
async def get_conversion_status():
    """
    获取文件转换服务的状态信息
    
    Returns:
        服务状态信息
    """
    try:
        # 检查LibreOffice是否可用
        import subprocess
        result = subprocess.run(['libreoffice', '--version'], 
                               capture_output=True, text=True, timeout=10)
        libreoffice_available = result.returncode == 0
        libreoffice_version = result.stdout.strip() if libreoffice_available else "未安装"
    except Exception:
        libreoffice_available = False
        libreoffice_version = "未安装或不可访问"
    
    return JSONResponse(content={
        "status": "running",
        "libreoffice_available": libreoffice_available,
        "libreoffice_version": libreoffice_version,
        "supported_conversions": [
            "DOC -> DOCX",
            "PPT/PPTX -> PDF (推荐，统一处理)",
            "PPT -> PPTX (已过时)",
            "Office文件 -> PDF"
        ],
        "recommended_apis": {
            "powerpoint_conversion": "/convert/powerpoint-to-pdf",
            "doc_conversion": "/convert/doc-to-docx",
            "general_office_conversion": "/convert/office-to-pdf"
        },
        "deprecated_apis": {
            "ppt_to_pptx": "/convert/ppt-to-pptx"
        },
        "processing_improvements": {
            "unified_powerpoint_processing": "PPT和PPTX现在都直接转换为PDF，提供更好的兼容性",
            "eliminates_intermediate_conversion": "不再需要PPT->PPTX->PDF的中间转换步骤",
            "better_text_extraction": "从PDF中提取文本，确保格式一致性"
        },
        "max_workers": conversion_executor._max_workers,
        "thread_name_prefix": conversion_executor._thread_name_prefix,
        "executor_type": "shared_conversion_pool",
        "thread_safety": "Unified single-threaded LibreOffice executor shared across all modules"
    }) 