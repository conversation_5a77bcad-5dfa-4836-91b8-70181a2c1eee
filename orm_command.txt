uv pip install sqlacodegen

sqlacodegen mysql+pymysql://root:qh2008@localhost:3308/jeecg-boot-ai --outfile=AiragKnowledge.py --tables=airag_knowledge,courses

sqlacodegen mysql+pymysql://root:qh2008@localhost:3308/jeecg-boot-ai --outfile=Text2sqlSys.py --tables=text2sql_sys_datasource,text2sql_sys_schemacolumn,text2sql_sys_schemarelationship,text2sql_sys_schematable,text2sql_sys_valuemapping

sqlacodegen mysql+pymysql://root:qh2008@localhost:3308/jeecg-boot-ai --outfile=Sys.py --tables=sys_user,sys_dict,sys_dict_item

sqlacodegen mysql+pymysql://root:qh2008@localhost:3308/jeecg-boot-ai --outfile=AiragModel.py --tables=airag_model