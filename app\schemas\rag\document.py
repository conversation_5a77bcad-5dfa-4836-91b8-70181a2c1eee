from pydantic import BaseModel
from typing import Optional


class MilvusEntity(BaseModel):
    """Milvus向量数据库实体"""
    collection_name: str = "default"
    file_name: str = None


class UploadFileByDocIdEntity(BaseModel):
    """根据文档ID上传文件实体"""
    collection_name: str = "default"
    use_kg: bool
    docId: str
    embedding_model: str


class EmbeddingStatusEntity(BaseModel):
    """嵌入状态查询实体"""
    docId: str


class CreateCollectionEntity(BaseModel):
    """创建向量集合实体"""
    collection_name: str
    embedding_model: str = "local" 