import re
import json
from typing import List, Dict, Any, Tuple, Optional
from neo4j import GraphDatabase
from llama_index.core import Settings
import asyncio
from functools import lru_cache

from config import settings
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

class KnowledgeGraphManager:
    """
    知识图谱管理工具类，负责实体和关系的提取、存储和查询
    """
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """单例模式获取KnowledgeGraphManager实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self, config: Optional[Dict[str, str]] = None):
        # Neo4j连接配置
        self.config = config or {}
        self.neo4j_uri = self.config.get("NEO4J_URI") or settings.configuration.kg_neo4j_uri
        self.neo4j_user = self.config.get("NEO4J_USERNAME") or settings.configuration.kg_neo4j_user
        self.neo4j_password = self.config.get("NEO4J_PASSWORD") or settings.configuration.kg_neo4j_password
        self.neo4j_db = self.config.get("NEO4J_DATABASE") or settings.configuration.kg_neo4j_database
        self._neo4j_driver = None
        self._connection_lock = asyncio.Lock()
        self._connection_count = 0
        self._max_connections = int(self.config.get("NEO4J_MAX_CONNECTIONS") or settings.configuration.kg_neo4j_max_connections)
        self._database_initialized = False  # 添加标志来跟踪数据库是否已初始化
        
    @property
    async def neo4j_driver(self):
        """懒加载Neo4j驱动，只在需要时创建连接，使用异步锁保证线程安全"""
        async with self._connection_lock:
            if self._neo4j_driver is None:
                try:
                    # 创建驱动实例
                    self._neo4j_driver = GraphDatabase.driver(
                        self.neo4j_uri, 
                        auth=(self.neo4j_user, self.neo4j_password),
                        max_connection_lifetime=300,  # 连接最大生命周期（秒）
                        max_connection_pool_size=self._max_connections  # 连接池大小
                    )
                    
                    # 测试连接
                    try:
                        # 尝试连接到指定数据库
                        with self._neo4j_driver.session(database=self.neo4j_db) as session:
                            session.run("RETURN 1")
                        logger.success(f"成功连接到Neo4j数据库: {self.neo4j_uri}, 数据库: {self.neo4j_db}")
                    except Exception as db_error:
                        if "DatabaseNotFound" in str(db_error):
                            # 如果指定数据库不存在，尝试连接到默认数据库
                            logger.warning(f"数据库 '{self.neo4j_db}' 不存在，尝试连接到默认数据库 'neo4j'")
                            self.neo4j_db = "neo4j"
                            with self._neo4j_driver.session(database=self.neo4j_db) as session:
                                session.run("RETURN 1")
                            logger.success(f"成功连接到Neo4j默认数据库 'neo4j'")
                        else:
                            # 其他错误
                            raise db_error
                except Exception as e:
                    logger.error(f"Neo4j连接失败: {e}")
                    self._neo4j_driver = None
                    raise
            return self._neo4j_driver
        
    async def close(self):
        """关闭Neo4j连接"""
        async with self._connection_lock:
            if self._neo4j_driver is not None:
                self._neo4j_driver.close()
                self._neo4j_driver = None
                logger.info("Neo4j连接已关闭")
    
    @lru_cache(maxsize=100)
    def entity_relation_extraction_prompt(self, text_length: int) -> str:
        """生成用于实体和关系提取的提示模板，根据文本长度优化提示"""
        # 根据文本长度调整提取深度
        depth = "详细" if text_length < 1000 else "关键"
        
        return f"""你是一个专业的知识图谱构建专家，请从以下文本中准确提取{depth}的实体和它们之间的直接关系。

## 重要说明：特别关注文档结构
**务必识别文档的层级结构，包括标题、章节、分组等结构化元素，这些对于建立正确的知识关系至关重要。**

## 实体提取规则

### 优先提取的实体类型（按重要性排序）：
1. **文档结构实体**：标题、章节、分组、分类（如"核心功能"、"技术优势"、"核心价值"等）
2. **产品/系统**：具体的产品名称、系统名称、平台名称
3. **核心概念**：重要的业务概念、价值主张、核心理念
4. **技术概念**：技术术语、方法论、技术栈
5. **业务概念**：业务流程、商业模式、服务内容
6. **组织机构**：公司、部门、团队、机构名称
7. **功能特性**：产品功能、系统特性、能力描述
8. **指标度量**：性能指标、业务指标、量化标准

### 实体识别标准：
- **必须识别标题和章节**：文本中的"###"、"####"、"**"包围的标题内容
- **识别分组结构**：如"核心功能"、"技术优势"等概念分组
- **优先识别完整的名词短语**，避免过度拆分
- **重视上下文中的重要概念**，即使没有明确定义
- **保持实体名称的完整性和准确性**
- **区分具体实体和抽象概念**
- **特别注意带有编号的列表项**（如"1. xxx"、"- xxx"）

## 关系提取规则

### 关系类型定义（新增层级关系）：
- **包含关系**：A包含B、A由B组成、A下属于B
- **归属关系**：A归属于B、A属于B章节
- **层级关系**：A是B的子项、A在B之下
- **实现关系**：A实现B、A提供B
- **服务关系**：A服务于B、A支持B
- **属性关系**：A具有属性B、A的特征是B
- **应用关系**：A应用于B、A用于B
- **关联关系**：A与B相关、A连接B
- **依赖关系**：A依赖于B、A基于B
- **目标关系**：A目标是B、A旨在B

### 关系识别原则：
- **优先建立层级关系**：章节与子项之间的关系
- **识别分组关系**：同一标题下的内容应关联到该标题
- **只提取文本中明确表达或强烈暗示的关系**
- **关系必须在文本中有明确的语义支撑**
- **避免过度推理，不要创造文本中不存在的关系**
- **建立结构化的层级关系网络**

## 结构化提取策略

### 标题识别规则：
- 以"#"、"##"、"###"、"####"开头的行
- 以"**"包围的粗体文本
- 明显的章节标题（如"产品定位"、"核心功能"、"技术优势"等）

### 层级关系建立：
- 标题下的具体内容应建立"归属关系"
- 编号列表项应建立"包含关系"
- 相同层级的内容应通过上级标题关联

## 质量控制要求

1. **实体命名**：使用文本中的原始表述，保持术语的专业性
2. **关系准确性**：确保每个关系都有文本依据，不要臆测
3. **完整性检查**：重要的核心概念和所有标题不应被遗漏
4. **一致性要求**：相同概念使用相同的实体名称
5. **结构完整性**：确保文档的层级结构得到正确表达

## 提取示例
对于文本结构："### 核心价值 1. **降本增效** 2. **低成本AI应用**"
应提取：
- 实体："核心价值"(文档结构实体)、"降本增效"(核心概念)、"低成本AI应用"(核心概念)
- 关系："降本增效"归属于"核心价值"、"低成本AI应用"归属于"核心价值"

## 输出格式要求

请严格按照以下JSON格式输出，确保格式正确：

{{
  "entities": [
    {{"name": "实体名称", "type": "实体类型"}}
  ],
  "relations": [
    {{
      "source": "源实体名称",
      "target": "目标实体名称", 
      "relation": "关系类型",
      "description": "关系的具体描述"
    }}
  ]
}}

## 格式规范：
- 使用双引号包围所有字符串
- 实体名称必须与relations中使用的名称完全一致
- 如果没有找到实体或关系，返回空数组
- 不要添加注释或额外文本

## 处理文本：
{{text_placeholder}}
"""

    async def extract_entities_and_relations(self, text: str) -> Dict[str, List[Dict[str, str]]]:
        """使用LlamaIndex中的LLM提取实体和关系"""
        try:
            if text is None or not isinstance(text, str):
                logger.warning(f"无效的文本输入: {type(text)}")
                return {"entities": [], "relations": []}
                
            # 文本太长时进行截断
            max_text_length = 8000
            if len(text) > max_text_length:
                text = text[:max_text_length] + "..."
                
            # 使用LlamaIndex全局配置的LLM
            llm = Settings.llm
            prompt_template = self.entity_relation_extraction_prompt(len(text))
            # 直接将文本替换到模板中，使用双花括号避免与f-string冲突
            logger.info("正在替换模板中的text_placeholder...")
            prompt = prompt_template.replace("{{text_placeholder}}", text)
            
            # 检查替换是否成功
            if "{text_placeholder}" in prompt:
                logger.warning("警告: 模板替换失败，text_placeholder仍存在于提示中")
                # 尝试不同的替换方法
                prompt = prompt_template.replace("{text_placeholder}", text)
                if "text_placeholder" in prompt:
                    logger.warning("警告: 第二次替换也失败，使用硬编码的方式替换")
                    # 最后的尝试：使用正则表达式替换
                    prompt = re.sub(r'text_placeholder', text, prompt)
            
            # 记录最终的提示开头，用于调试
            prompt_head = prompt[:500] if len(prompt) > 500 else prompt
            # print(f"提示开头部分(500字符): {prompt_head}...")
            response = await llm.acomplete(prompt)
            # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
            filter_response_text = re.sub(r'<think>.*?</think>', '', response.text, flags=re.DOTALL)
            # 打印完整响应用于调试
            logger.info(f"LLM实体提取响应(前500字符): {filter_response_text[:500]}...")
            
            # 解析响应中的JSON数据
            json_str = re.search(r'\{.*\}', filter_response_text, re.DOTALL)
            if not json_str:
                logger.warning("未能从LLM响应中提取到JSON数据，尝试使用更宽松的模式")
                # 尝试使用更宽松的正则表达式
                json_str = re.search(r'({[\s\S]*})', filter_response_text)
                if not json_str:
                    logger.warning("仍然无法提取JSON数据，返回空结果")
                    return {"entities": [], "relations": []}
            
            # 获取匹配的JSON字符串
            json_content = json_str.group(0)
            # 打印提取的JSON字符串
            logger.info(f"提取的JSON字符串(前200字符): {json_content[:200]}...")
            
            try:
                # 尝试直接解析
                result = json.loads(json_content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}，尝试修复格式问题")
                
                # 尝试各种修复方法
                try:
                    # 1. 替换单引号为双引号
                    fixed_json = json_content.replace("'", "\"")
                    result = json.loads(fixed_json)
                except json.JSONDecodeError:
                    try:
                        # 2. 去除可能的JavaScript注释
                        fixed_json = re.sub(r'//.*?\n', '\n', json_content)
                        fixed_json = re.sub(r'/\*.*?\*/', '', fixed_json, flags=re.DOTALL)
                        result = json.loads(fixed_json)
                    except json.JSONDecodeError:
                        try:
                            # 3. 尝试将JSON属性名加上双引号
                            # 匹配没有双引号的属性名
                            fixed_json = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_content)
                            result = json.loads(fixed_json)
                        except json.JSONDecodeError:
                            try:
                                # 4. 尝试更严格的手动解析
                                # 提取实体部分
                                entities_match = re.search(r'"entities"\s*:\s*\[(.*?)\]', json_content, re.DOTALL)
                                entities_str = entities_match.group(1) if entities_match else ""
                                
                                # 提取关系部分
                                relations_match = re.search(r'"relations"\s*:\s*\[(.*?)\]', json_content, re.DOTALL)
                                relations_str = relations_match.group(1) if relations_match else ""
                                
                                # 手动构建JSON
                                manual_json = '{"entities":[' + entities_str + '], "relations":[' + relations_str + ']}'
                                result = json.loads(manual_json)
                            except (json.JSONDecodeError, AttributeError):
                                logger.warning("所有JSON修复尝试都失败，返回空结果")
                                return {"entities": [], "relations": []}
            
            # 确保结果包含必要的键
            if "entities" not in result:
                result["entities"] = []
            if "relations" not in result:
                result["relations"] = []
            
            # 验证实体和关系数据
            validated_entities = []
            for entity in result["entities"]:
                if isinstance(entity, dict) and "name" in entity and "type" in entity:
                    validated_entities.append(entity)
                
            validated_relations = []
            for relation in result["relations"]:
                if (isinstance(relation, dict) and
                    "source" in relation and "target" in relation and
                    "relation" in relation and "description" in relation):
                    validated_relations.append(relation)
                
            return {
                "entities": validated_entities,
                "relations": validated_relations
            }
        except Exception as e:
            logger.error(f"实体和关系提取过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return {"entities": [], "relations": []}

    async def save_to_knowledge_graph(self, doc_id: str, entities: List[Dict[str, str]],
                                    relations: List[Dict[str, str]], source_metadata: Dict[str, Any],
                                    max_retries: int = 3, collection_name="default"):
        """将提取的实体和关系保存到Neo4j知识图谱"""
        if not entities:
            logger.warning(f"文档 {doc_id} 没有提取到实体，跳过知识图谱保存")
            return
            
        retry_count = 0
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                # 创建批量操作，减少数据库交互次数
                with driver.session(database=self.neo4j_db) as session:
                    # 1. 创建或更新文档节点
                    session.run(
                        """
                        MERGE (d:Document {id: $doc_id,collection_name:$collection_name})
                        SET d.title = $title,
                            d.type = $type,
                            d.source = $source,
                            d.updated_at = datetime()
                        """,
                        doc_id=source_metadata.get("file_id", "未知文档"),
                        title=source_metadata.get("file_name", "未知文档"),
                        type=source_metadata.get("type", "text"),
                        source=doc_id,
                        collection_name = collection_name
                    )
                    
                    # 2. 批量创建实体节点
                    if entities:
                        # 准备批量操作的参数
                        entity_params = []
                        for entity in entities:
                            # 为实体生成唯一ID (实体名称+类型+集合名称)，确保在不同集合中的相同实体有不同ID
                            entity_id = f"{entity['name']}_{entity['type']}:{collection_name}".lower().replace(" ", "_")
                            # 获取source_id字段，如果存在的话
                            source_id = entity.get("source_id", "")
                            source_doc = entity.get("source_doc", doc_id)
                            
                            entity_params.append({
                                "entity_id": entity_id,
                                "name": entity["name"],
                                "type": entity["type"],
                                "source_id": source_id,
                                "source_doc": source_doc,
                                "collection_name": collection_name
                            })
                        
                        # 批量创建实体节点并与文档建立关联
                        batch_size = 50  # 每批处理的实体数量
                        for i in range(0, len(entity_params), batch_size):
                            batch = entity_params[i:i+batch_size]
                            # 使用UNWIND进行批量操作
                            session.run(
                                """
                                UNWIND $entities AS entity
                                MERGE (e:Entity {id: entity.entity_id,collection_name:entity.collection_name})
                                SET e.name = entity.name,
                                    e.type = entity.type,
                                    e.updated_at = datetime()
                                WITH e, entity
                                // 设置或更新source_id字段
                                FOREACH(ignoreMe IN CASE WHEN entity.source_id <> '' THEN [1] ELSE [] END |
                                    SET e.source_id = CASE
                                        WHEN e.source_id IS NULL THEN entity.source_id
                                        // 如果已有source_id，则不重复添加相同值
                                        WHEN e.source_id CONTAINS entity.source_id THEN e.source_id
                                        // 添加新值，用|分隔
                                        ELSE e.source_id + '|' + entity.source_id
                                    END
                                )
                                WITH e, entity
                                MATCH (d:Document {source: entity.source_doc})
                                MERGE (e)-[r:APPEARS_IN]->(d)
                                ON CREATE SET r.created_at = datetime()
                                SET r.updated_at = datetime(),
                                    r.source_id = entity.source_id,
                                    r.collection_name = entity.collection_name
                                """,
                                entities=batch
                            )
                    
                    # 3. 批量创建关系
                    if relations:
                        # 准备批量操作的参数
                        relation_params = []
                        for relation in relations:
                            source_id = f"{relation['source']}_{next((e['type'] for e in entities if e['name'] == relation['source']), 'unknown')}:{collection_name}".lower().replace(" ", "_")
                            target_id = f"{relation['target']}_{next((e['type'] for e in entities if e['name'] == relation['target']), 'unknown')}:{collection_name}".lower().replace(" ", "_")
                            # 获取source_id字段，如果存在的话
                            chunk_source_id = relation.get("source_id", "")
                            
                            relation_params.append({
                                "source_id": source_id,
                                "target_id": target_id,
                                "relation_type": relation["relation"],
                                "description": relation["description"],
                                "source_doc": relation["source_doc"],
                                "chunk_source_id": chunk_source_id,
                                "collection_name": collection_name
                            })
                        
                        # 批量创建关系
                        batch_size = 50  # 每批处理的关系数量
                        for i in range(0, len(relation_params), batch_size):
                            batch = relation_params[i:i+batch_size]
                            # 使用UNWIND进行批量操作
                            session.run(
                                """
                                UNWIND $relations AS rel
                                MATCH (source:Entity {id: rel.source_id,collection_name: rel.collection_name})
                                MATCH (target:Entity {id: rel.target_id,collection_name: rel.collection_name})
                                MERGE (source)-[r:RELATED_TO {type: rel.relation_type}]->(target)
                                SET r.description = rel.description,
                                    r.updated_at = datetime(),
                                    r.source_doc = rel.source_doc,
                                    r.collection_name = rel.collection_name,
                                    r.created_at = CASE WHEN r.created_at IS NULL THEN datetime() ELSE r.created_at END
                                // 设置或更新source_id字段，与实体逻辑保持一致
                                FOREACH(ignoreMe IN CASE WHEN rel.chunk_source_id <> '' THEN [1] ELSE [] END |
                                    SET r.source_id = CASE
                                        WHEN r.source_id IS NULL THEN rel.chunk_source_id
                                        // 如果已有source_id，则不重复添加相同值
                                        WHEN r.source_id CONTAINS rel.chunk_source_id THEN r.source_id
                                        // 添加新值，用|分隔
                                        ELSE r.source_id + '|' + rel.chunk_source_id
                                    END
                                )
                                WITH source, target, r, rel
                                MATCH (d:Document {source: rel.source_doc})
                                MERGE (source)-[:APPEARS_IN]->(d)
                                MERGE (target)-[:APPEARS_IN]->(d)
                                """,
                                relations=batch
                            )
                
                logger.success(f"成功将{len(entities)}个实体和{len(relations)}个关系保存到知识图谱 (文档ID: {doc_id})")
                return
                
            except Exception as e:
                retry_count += 1
                logger.error(f"保存知识图谱时出错 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"保存知识图谱失败，已达到最大重试次数: {doc_id}")
                    raise
    
    async def query_knowledge_graph(self, entity_name: Optional[str] = None, entity_type: Optional[str] = None, 
                                  relation_type: Optional[str] = None, max_nodes: int = 50) -> Dict[str, Any]:
        """查询知识图谱中的实体和关系"""
        cache_key = f"query_{entity_name}_{entity_type}_{relation_type}_{max_nodes}"
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                with driver.session(database=self.neo4j_db) as session:
                    query_parts = []
                    params = {}
                    
                    # 构建基础查询
                    if entity_name:
                        query_parts.append("WHERE toLower(e.name) CONTAINS toLower($entity_name)")
                        params["entity_name"] = entity_name
                    
                    if entity_type:
                        if query_parts:
                            query_parts.append("AND e.type = $entity_type")
                        else:
                            query_parts.append("WHERE e.type = $entity_type")
                        params["entity_type"] = entity_type
                    
                    # 限制返回的节点数量
                    params["max_nodes"] = max_nodes
                    
                    base_query = f"""
                    MATCH (e:Entity)
                    {' '.join(query_parts)}
                    WITH e LIMIT $max_nodes
                    """
                    
                    # 如果指定了关系类型，则查询特定关系
                    if relation_type:
                        # 只查询所有出向关系，避免重复
                        query = f"""
                        {base_query}
                        OPTIONAL MATCH path=(e)-[r:RELATED_TO {{type: $relation_type}}]->(related)
                        RETURN e, r, related
                        """
                        params["relation_type"] = relation_type
                    else:
                        # 只查询所有出向关系，避免重复
                        query = f"""
                        {base_query}
                        OPTIONAL MATCH path=(e)-[r:RELATED_TO]->(related)
                        RETURN e, r, related
                        """
                    
                    try:
                        result = session.run(query, params)
                        
                        # 构建知识图谱数据
                        nodes = {}
                        links = []
                        processed_relations = set()  # 用于记录已处理的关系ID，避免重复
                        
                        for record in result:
                            # 处理源实体
                            entity = record["e"]
                            if entity["id"] not in nodes:
                                nodes[entity["id"]] = {
                                    "id": entity["id"],
                                    "name": entity["name"],
                                    "type": entity["type"]
                                }
                            
                            # 处理关系和目标实体
                            if record["r"] and record["related"]:
                                relation = record["r"]
                                related = record["related"]
                                
                                # 生成关系的唯一标识（源ID+目标ID+关系类型）
                                # 注意：这里假设ID1_ID2_TYPE和ID2_ID1_TYPE是同一关系的两个方向
                                relation_ids = sorted([entity["id"], related["id"]])
                                relation_key = f"{relation_ids[0]}_{relation_ids[1]}_{relation['type']}"
                                
                                # 如果这个关系已经处理过，则跳过
                                if relation_key in processed_relations:
                                    continue
                                
                                processed_relations.add(relation_key)
                                
                                # 添加目标实体
                                if related["id"] not in nodes:
                                    nodes[related["id"]] = {
                                        "id": related["id"],
                                        "name": related["name"],
                                        "type": related["type"]
                                    }
                                
                                # 添加关系（保持原方向）
                                links.append({
                                    "source": entity["id"],
                                    "target": related["id"],
                                    "type": relation["type"],
                                    "description": relation.get("description", ""),
                                    "direction": "outgoing"
                                })
                        
                        result = {
                            "nodes": list(nodes.values()),
                            "links": links
                        }
                        
                        return result
                    
                    except Exception as e:
                        logger.error(f"查询过程中出错: {e}")
                        raise
                
            except Exception as e:
                retry_count += 1
                logger.error(f"查询知识图谱时出错 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"查询知识图谱失败，已达到最大重试次数")
                    return {"nodes": [], "links": []}
    
    async def batch_process_documents(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量处理多个文档的实体和关系提取与存储
        
        参数:
            documents: 包含文档ID、文本内容和元数据的列表
            
        返回:
            处理结果统计
        """
        if not documents:
            logger.warning("没有文档需要处理")
            return {
                "total": 0,
                "success": 0,
                "failure": 0,
                "entities_count": 0,
                "relations_count": 0
            }
            
        results = {
            "total": len(documents),
            "success": 0,
            "failure": 0,
            "entities_count": 0,
            "relations_count": 0,
            "errors": []  # 记录错误信息
        }
        
        # 创建有效文档列表
        valid_docs = []
        for idx, doc in enumerate(documents):
            if not isinstance(doc, dict):
                error_msg = f"文档格式错误，不是字典: {type(doc)}"
                logger.error(error_msg)
                results["failure"] += 1
                results["errors"].append({
                    "doc_idx": idx,
                    "doc_id": "unknown",
                    "error": error_msg
                })
                continue
                
            if "text" not in doc or "id" not in doc or "metadata" not in doc:
                doc_id = doc.get("id", f"未知ID_{idx}")
                error_msg = f"文档缺少必要字段(text/id/metadata)，跳过处理: {doc_id}"
                logger.error(error_msg)
                results["failure"] += 1
                results["errors"].append({
                    "doc_idx": idx,
                    "doc_id": doc_id,
                    "error": error_msg
                })
                continue
            
            if doc.get("text") is None or not isinstance(doc["text"], str) or len(doc["text"].strip()) == 0:
                doc_id = doc.get("id", f"未知ID_{idx}")
                error_msg = f"文档 {doc_id} 的文本为空或无效，跳过处理"
                logger.error(error_msg)
                results["failure"] += 1
                results["errors"].append({
                    "doc_idx": idx,
                    "doc_id": doc_id,
                    "error": error_msg
                })
                continue
                
            # 添加到有效文档列表
            valid_docs.append((idx, doc))
                
        # 处理有效文档
        logger.info(f"开始批量处理 {len(valid_docs)} 个有效文档")
        
        # 创建所有协程任务（不立即执行）
        task_coroutines = []
        task_metadata = []  # 存储任务的元数据信息
        
        for idx, doc in valid_docs:
            # 创建协程对象，但不立即执行
            coroutine = self._process_single_document(doc["id"], doc["text"], doc["metadata"])
            task_coroutines.append(coroutine)
            task_metadata.append((idx, doc["id"]))
            
        # 使用 asyncio.gather 并发执行所有任务
        if task_coroutines:
            try:
                # 并发执行所有任务
                logger.info(f"并发执行 {len(task_coroutines)} 个文档处理任务")
                task_results = await asyncio.gather(*task_coroutines, return_exceptions=True)
                
                # 处理所有任务的结果
                for i, (result, (idx, doc_id)) in enumerate(zip(task_results, task_metadata)):
                    try:
                        # 检查是否是异常结果
                        if isinstance(result, Exception):
                            error_msg = f"处理文档 {doc_id} 时出现异常: {result}"
                            logger.error(error_msg)
                            results["failure"] += 1
                            results["errors"].append({
                                "doc_idx": idx,
                                "doc_id": doc_id,
                                "error": error_msg
                            })
                            continue
                            
                        # 处理正常结果
                        if isinstance(result, dict):
                            if result.get("status") == "success":
                                results["success"] += 1
                                results["entities_count"] += result.get("entities_count", 0)
                                results["relations_count"] += result.get("relations_count", 0)
                            elif result.get("status") == "no_entities":
                                results["success"] += 1  # 无实体也算成功处理
                            else:
                                results["failure"] += 1
                                results["errors"].append({
                                    "doc_idx": idx,
                                    "doc_id": doc_id,
                                    "error": result.get("error", "处理失败，无详细错误信息")
                                })
                        else:
                            error_msg = f"文档 {doc_id} 处理结果格式错误: {result}"
                            logger.error(error_msg)
                            results["failure"] += 1
                            results["errors"].append({
                                "doc_idx": idx,
                                "doc_id": doc_id,
                                "error": error_msg
                            })
                    except Exception as e:
                        error_msg = f"处理文档 {doc_id} 的结果时出现未捕获的异常: {e}"
                        logger.error(error_msg)
                        import traceback
                        traceback.print_exc()
                        results["failure"] += 1
                        results["errors"].append({
                            "doc_idx": idx,
                            "doc_id": doc_id,
                            "error": error_msg
                        })
                        
            except Exception as e:
                error_msg = f"并发执行任务时出现异常: {e}"
                logger.error(error_msg)
                import traceback
                traceback.print_exc()
                # 将所有文档标记为失败
                for idx, doc_id in task_metadata:
                    results["failure"] += 1
                    results["errors"].append({
                        "doc_idx": idx,
                        "doc_id": doc_id,
                        "error": error_msg
                    })
                    
        # 移除详细错误记录，避免返回过多数据
        error_count = len(results["errors"])
        if error_count > 0:
            logger.error(f"处理过程中遇到 {error_count} 个错误")
            
        # 只返回总结信息，不返回详细错误
        summary_results = {k: v for k, v in results.items() if k != "errors"}
                    
        return summary_results
        
    async def _process_single_document(self, doc_id: str, text: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个文档的实体和关系提取与存储"""
        try:
            if text is None or not isinstance(text, str):
                logger.warning(f"文档 {doc_id} 的文本为空或无效")
                return {
                    "status": "error",
                    "doc_id": doc_id,
                    "error": "文本为空或无效",
                    "entities_count": 0,
                    "relations_count": 0
                }
            
            logger.info(f"开始处理文档: {doc_id}")
                
            # 提取实体和关系
            try:
                extraction_result = await self.extract_entities_and_relations(text)
            except Exception as e:
                logger.error(f"从文档 {doc_id} 提取实体和关系时出错: {e}")
                return {
                    "status": "error",
                    "doc_id": doc_id,
                    "error": f"实体提取失败: {str(e)}",
                    "entities_count": 0,
                    "relations_count": 0
                }
            
            # 只有在有实体时才保存到知识图谱
            if extraction_result["entities"]:
                try:
                    # 获取chunk_id，如果metadata中没有，就使用doc_id作为chunk_id
                    chunk_id = metadata.get("chunk_id", doc_id)
                    collection_name = metadata.get("collection_name", "default")
                    # 建立实体和关系与源文本块的关联
                    enhanced_entities, enhanced_relations = await self.connect_entities_to_chunks(
                        entities=extraction_result["entities"],
                        relations=extraction_result["relations"],
                        chunk_id=chunk_id,
                        doc_id=doc_id,
                        collection_name=collection_name
                    )
                    
                    # 保存关联后的实体和关系到知识图谱
                    await self.save_to_knowledge_graph(
                        doc_id=doc_id,
                        entities=enhanced_entities,
                        relations=enhanced_relations,
                        source_metadata=metadata,
                        collection_name=collection_name
                    )
                    
                    return {
                        "status": "success",
                        "doc_id": doc_id,
                        "entities_count": len(enhanced_entities),
                        "relations_count": len(enhanced_relations)
                    }
                except Exception as e:
                    logger.error(f"保存文档 {doc_id} 到知识图谱时出错: {e}")
                    return {
                        "status": "error",
                        "doc_id": doc_id,
                        "error": f"知识图谱保存失败: {str(e)}",
                        "entities_count": len(extraction_result["entities"]),
                        "relations_count": len(extraction_result["relations"])
                    }
            else:
                logger.warning(f"文档 {doc_id} 没有提取到实体")
                return {
                    "status": "no_entities",
                    "doc_id": doc_id,
                    "entities_count": 0,
                    "relations_count": 0
                }
        except Exception as e:
            logger.error(f"处理文档 {doc_id} 时出错: {e}")
            import traceback
            traceback.print_exc()
            return {
                "status": "error",
                "doc_id": doc_id,
                "error": str(e),
                "entities_count": 0,
                "relations_count": 0
            }
    
    async def query_kg_by_entity(self, entity_name: str = None, entity_type: str = None) -> Dict[str, Any]:
        """
        根据实体名称或类型查询知识图谱
        
        参数:
            entity_name: 实体名称
            entity_type: 实体类型
            
        返回:
            包含节点和关系的图谱数据
        """
        return await self.query_knowledge_graph(entity_name=entity_name, entity_type=entity_type)
    
    async def query_kg_by_relation(self, relation_type: str) -> Dict[str, Any]:
        """
        根据关系类型查询知识图谱
        
        参数:
            relation_type: 关系类型
            
        返回:
            包含节点和关系的图谱数据
        """
        return await self.query_knowledge_graph(relation_type=relation_type)
    
    async def get_kg_statistics(self) -> Dict[str, Any]:
        """
        获取知识图谱统计信息
        
        返回:
            包含实体和关系统计信息的字典
        """
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                with driver.session(database=self.neo4j_db) as session:
                    # 检查Neo4j是否安装了APOC插件
                    has_apoc = True
                    try:
                        session.run("CALL apoc.help('map')")
                    except Exception:
                        has_apoc = False
                        logger.warning("警告: Neo4j未安装APOC插件，将使用替代查询")
                    
                    # 获取实体统计信息
                    entity_stats_query = """
                    MATCH (e:Entity)
                    RETURN 
                        count(e) as total_entities,
                        collect(distinct e.type) as entity_types
                    """
                    
                    # 获取关系统计信息
                    relation_stats_query = """
                    MATCH ()-[r:RELATED_TO]->()
                    RETURN 
                        count(r) as total_relations,
                        collect(distinct r.type) as relation_types
                    """
                    
                    # 获取文档统计信息
                    document_stats_query = """
                    MATCH (d:Document)
                    RETURN 
                        count(d) as total_documents,
                        collect(distinct d.type) as document_types
                    """
                    
                    # 执行查询
                    entity_stats = session.run(entity_stats_query).single()
                    relation_stats = session.run(relation_stats_query).single()
                    document_stats = session.run(document_stats_query).single()
                    
                    # 分别查询类型计数
                    # 计算实体类型计数
                    entity_type_counts_query = """
                    MATCH (e:Entity) 
                    RETURN e.type as type, count(e) as count
                    """
                    entity_type_counts = {record["type"]: record["count"] for record in session.run(entity_type_counts_query)}
                    
                    # 计算关系类型计数
                    relation_type_counts_query = """
                    MATCH ()-[r:RELATED_TO]->() 
                    RETURN r.type as type, count(r) as count
                    """
                    relation_type_counts = {record["type"]: record["count"] for record in session.run(relation_type_counts_query)}
                    
                    # 计算文档类型计数
                    document_type_counts_query = """
                    MATCH (d:Document) 
                    RETURN d.type as type, count(d) as count
                    """
                    document_type_counts = {record["type"]: record["count"] for record in session.run(document_type_counts_query)}
                    
                    # 构建结果
                    return {
                        "entities": {
                            "total": entity_stats["total_entities"],
                            "types": entity_stats["entity_types"],
                            "type_counts": entity_type_counts
                        },
                        "relations": {
                            "total": relation_stats["total_relations"],
                            "types": relation_stats["relation_types"],
                            "type_counts": relation_type_counts
                        },
                        "documents": {
                            "total": document_stats["total_documents"],
                            "types": document_stats["document_types"],
                            "type_counts": document_type_counts
                        }
                    }
            except Exception as e:
                retry_count += 1
                logger.error(f"获取知识图谱统计信息时出错 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"获取知识图谱统计信息失败，已达到最大重试次数")
                    return {
                        "entities": {"total": 0, "types": [], "type_counts": {}},
                        "relations": {"total": 0, "types": [], "type_counts": {}},
                        "documents": {"total": 0, "types": [], "type_counts": {}}
                    }
    
    async def get_document_entities(self, doc_id: str) -> Dict[str, Any]:
        """
        获取指定文档中的所有实体和关系
        
        参数:
            doc_id: 文档ID
            
        返回:
            包含文档相关实体和关系的图谱数据
        """
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                with driver.session(database=self.neo4j_db) as session:
                    query = """
                    MATCH (d:Document {id: $doc_id})
                    MATCH (e:Entity)-[:APPEARS_IN]->(d)
                    OPTIONAL MATCH (e)-[r:RELATED_TO]->(related)
                    WHERE exists((related)-[:APPEARS_IN]->(d))
                    RETURN e, r, related
                    """
                    
                    result = session.run(query, {"doc_id": doc_id})
                    
                    # 构建知识图谱数据
                    nodes = {}
                    links = []
                    for record in result:
                        # 处理源实体
                        entity = record["e"]
                        if entity["id"] not in nodes:
                            nodes[entity["id"]] = {
                                "id": entity["id"],
                                "name": entity["name"],
                                "type": entity["type"],
                                "source_id": entity["source_id"],
                                "collection_name": entity["collection_name"]
                            }
                        
                        # 处理关系和目标实体
                        if record["r"] and record["related"]:
                            relation = record["r"]
                            related = record["related"]
                            
                            # 添加目标实体
                            if related["id"] not in nodes:
                                nodes[related["id"]] = {
                                    "id": related["id"],
                                    "name": related["name"],
                                    "type": related["type"],
                                    "source_id": related["source_id"],
                                    "collection_name": related["collection_name"]
                                }
                            
                            # 添加关系
                            links.append({
                                "source": entity["id"],
                                "source_type": entity["type"],
                                "target": related["id"],
                                "target_type": related["type"],
                                "type": relation["type"],
                                "description": relation.get("description", ""),
                                "source_id": entity["source_id"],
                                "collection_name": entity["collection_name"]
                            })
                    
                    return {
                        "doc_id": doc_id,
                        "nodes": list(nodes.values()),
                        "links": links
                    }
                    
            except Exception as e:
                retry_count += 1
                logger.error(f"获取文档实体时出错 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"获取文档实体失败，已达到最大重试次数: {doc_id}")
                    return {"doc_id": doc_id, "nodes": [], "links": []}
    
    async def search_entities(self, 
                            query: str, 
                            entity_types: List[str] = None, 
                            limit: int = 20,collection_name="default") -> List[Dict[str, Any]]:
        """
        搜索实体
        
        参数:
            query: 搜索关键词
            entity_types: 要搜索的实体类型列表
            limit: 返回结果数量限制
            
        返回:
            匹配的实体列表
        """
        try:
            driver = await self.neo4j_driver
            
            with driver.session(database=self.neo4j_db) as session:
                params = {"query": f"(?i).*{query}.*", "limit": limit,"collection_name": collection_name}
                
                if entity_types and len(entity_types) > 0:
                    # 按指定类型搜索
                    cypher = """
                    MATCH (e:Entity)
                    WHERE e.name =~ $query AND e.type IN $types AND e.collection_name = $collection_name
                    RETURN e.id as id, e.name as name, e.type as type
                    LIMIT $limit
                    """
                    params["types"] = entity_types
                else:
                    # 搜索所有类型
                    cypher = """
                    MATCH (e:Entity)
                    WHERE e.name =~ $query AND e.collection_name = $collection_name
                    RETURN e.id as id, e.name as name, e.type as type
                    LIMIT $limit
                    """
                
                result = session.run(cypher, params)
                entities = [dict(record) for record in result]
                return entities
                
        except Exception as e:
            logger.error(f"搜索实体时出错: {e}")
            return []
    
    async def get_entity_details(self, entity_id: str) -> Dict[str, Any]:
        """
        获取实体详细信息，包括相关文档和关系
        
        参数:
            entity_id: 实体ID
            
        返回:
            实体详细信息
        """
        try:
            driver = await self.neo4j_driver
            
            with driver.session(database=self.neo4j_db) as session:
                # 查询实体基本信息，包括source_id
                entity_query = """
                MATCH (e:Entity {id: $entity_id})
                RETURN e.id as id, e.name as name, e.type as type, e.source_id as source_id
                """
                
                entity_result = session.run(entity_query, {"entity_id": entity_id}).single()
                if not entity_result:
                    return {"error": "实体不存在"}
                
                entity = dict(entity_result)
                
                # 查询相关文档
                docs_query = """
                MATCH (e:Entity {id: $entity_id})-[r:APPEARS_IN]->(d:Document)
                RETURN d.id as id, d.title as title, d.type as type, r.source_id as chunk_id
                LIMIT 10
                """
                
                docs_result = session.run(docs_query, {"entity_id": entity_id})
                documents = [dict(record) for record in docs_result]
                
                # 查询出向关系
                out_relations_query = """
                MATCH (e:Entity {id: $entity_id})-[r:RELATED_TO]->(target:Entity)
                RETURN target.id as id, target.name as name, target.type as type, 
                       r.type as relation_type, r.description as description, r.source_id as source_id
                LIMIT 20
                """
                
                out_relations_result = session.run(out_relations_query, {"entity_id": entity_id})
                out_relations = [dict(record) for record in out_relations_result]
                
                # 查询入向关系
                in_relations_query = """
                MATCH (source:Entity)-[r:RELATED_TO]->(e:Entity {id: $entity_id})
                RETURN source.id as id, source.name as name, source.type as type, 
                       r.type as relation_type, r.description as description, r.source_id as source_id
                LIMIT 20
                """
                
                in_relations_result = session.run(in_relations_query, {"entity_id": entity_id})
                in_relations = [dict(record) for record in in_relations_result]
                
                return {
                    "entity": entity,
                    "documents": documents,
                    "outgoing_relations": out_relations,
                    "incoming_relations": in_relations,
                    "source_id": entity.get("source_id", "")  # 直接返回source_id字段
                }
                
        except Exception as e:
            logger.error(f"获取实体详细信息时出错: {e}")
            return {"error": f"获取实体详细信息时出错: {e}"}

    async def get_entity(self, entity_id: str) -> Dict[str, Any]:
        """
        获取实体信息

        参数:
            entity_id: 实体ID

        返回:
            实体信息
        """
        try:
            driver = await self.neo4j_driver

            with driver.session(database=self.neo4j_db) as session:
                # 查询实体基本信息，包括source_id
                entity_query = """
                MATCH (e:Entity {id: $entity_id})
                RETURN e.id as id, e.name as name, e.type as type, e.source_id as source_id
                """

                entity_result = session.run(entity_query, {"entity_id": entity_id}).single()
                if not entity_result:
                    return {"error": "实体不存在"}

                entity = dict(entity_result)

                return {
                    "entity": entity,
                    "source_id": entity.get("source_id", "")  # 直接返回source_id字段
                }

        except Exception as e:
            logger.error(f"获取实体详细信息时出错: {e}")
            return {"error": f"获取实体详细信息时出错: {e}"}

    async def setup_database(self):
        """
        设置数据库索引和约束，提高查询性能
        """
        # 如果数据库已初始化，直接返回
        if self._database_initialized:
            logger.info("数据库已经初始化，跳过重复初始化")
            return True
            
        try:
            driver = await self.neo4j_driver
            
            with driver.session(database=self.neo4j_db) as session:
                # 检查Neo4j版本
                version_query = "CALL dbms.components() YIELD name, versions RETURN versions[0] as version"
                version_result = session.run(version_query).single()
                neo4j_version = version_result["version"] if version_result else "unknown"
                logger.info(f"Neo4j版本: {neo4j_version}")
                
                # 检查数据库是否存在
                try:
                    db_exists_query = "SHOW DATABASES YIELD name WHERE name = $db_name RETURN count(*) > 0 as exists"
                    db_exists_result = session.run(db_exists_query, {"db_name": self.neo4j_db}).single()
                    if db_exists_result and not db_exists_result["exists"]:
                        logger.warning(f"数据库 {self.neo4j_db} 不存在，尝试使用系统默认数据库 'neo4j'")
                        self.neo4j_db = "neo4j"
                except Exception:
                    # 如果SHOW DATABASES命令不可用（Neo4j版本问题），继续使用默认设置
                    logger.error("无法检查数据库列表，将使用默认数据库设置继续")
                
                # 为实体ID和collection_name组合创建复合唯一约束
                try:
                    session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (e:Entity) REQUIRE (e.id, e.collection_name) IS UNIQUE")
                except Exception as e:
                    logger.error(f"创建实体复合唯一约束时出错: {e}")
                    # 尝试旧版本Neo4j语法或单独约束
                    try:
                        # 先删除可能存在的旧约束
                        try:
                            session.run("DROP CONSTRAINT ON (e:Entity) ASSERT e.id IS UNIQUE")
                        except:
                            pass
                        # 创建复合约束（旧语法）
                        session.run("CREATE CONSTRAINT ON (e:Entity) ASSERT (e.id, e.collection_name) IS UNIQUE")
                    except Exception as e2:
                        logger.error(f"使用旧语法创建实体复合约束时也出错: {e2}")
                        # 如果复合约束不支持，至少保证collection_name内的唯一性
                        logger.warning("将使用collection_name作为实体ID的一部分来确保唯一性")
                
                # 为文档ID创建唯一约束
                try:
                    session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE")
                except Exception as e:
                    logger.error(f"创建文档ID约束时出错: {e}")
                    # 尝试旧版本Neo4j语法
                    try:
                        session.run("CREATE CONSTRAINT ON (d:Document) ASSERT d.id IS UNIQUE")
                    except Exception as e2:
                        logger.error(f"使用旧语法创建文档ID约束时也出错: {e2}")
                
                # 为实体名称创建索引
                try:
                    session.run("CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.name)")
                except Exception as e:
                    logger.error(f"创建实体名称索引时出错: {e}")
                    # 尝试旧版本Neo4j语法
                    try:
                        session.run("CREATE INDEX ON :Entity(name)")
                    except Exception as e2:
                        logger.error(f"使用旧语法创建实体名称索引时也出错: {e2}")
                
                # 为实体类型创建索引
                try:
                    session.run("CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.type)")
                except Exception as e:
                    logger.error(f"创建实体类型索引时出错: {e}")
                    # 尝试旧版本Neo4j语法
                    try:
                        session.run("CREATE INDEX ON :Entity(type)")
                    except Exception as e2:
                        logger.error(f"使用旧语法创建实体类型索引时也出错: {e2}")
                
                # 为关系类型创建索引
                try:
                    session.run("CREATE INDEX IF NOT EXISTS FOR ()-[r:RELATED_TO]-() ON (r.type)")
                except Exception as e:
                    logger.error(f"创建关系类型索引时出错: {e}")
                    # Neo4j 4.x版本可能不支持关系索引，忽略这个错误
                
                # 为文档类型创建索引
                try:
                    session.run("CREATE INDEX IF NOT EXISTS FOR (d:Document) ON (d.type)")
                except Exception as e:
                    logger.error(f"创建文档类型索引时出错: {e}")
                    # 尝试旧版本Neo4j语法
                    try:
                        session.run("CREATE INDEX ON :Document(type)")
                    except Exception as e2:
                        logger.error(f"使用旧语法创建文档类型索引时也出错: {e2}")
                
                logger.success("数据库索引和约束设置完成")
                # 设置初始化标志为True
                self._database_initialized = True
                return True
        except Exception as e:
            logger.error(f"设置数据库索引和约束时出错: {e}")
            return False 

    # 添加一个用于直接提取实体和关系的方法，方便测试
    @classmethod
    async def extract_from_text(cls, text: str) -> Dict[str, Any]:
        """
        从文本中直接提取实体和关系，用于测试
        
        参数:
            text: 要提取实体和关系的文本
            
        返回:
            包含实体和关系的字典
        """
        # 初始化示例
        instance = cls.get_instance()
        
        # 尝试连接Neo4j数据库，但不抛出连接错误
        try:
            driver = await instance.neo4j_driver
        except Exception as e:
            logger.warning(f"Neo4j连接警告: {e}")
            logger.warning("这只是一个警告，将继续提取实体和关系但不保存到数据库")
        
        # 提取实体和关系
        result = await instance.extract_entities_and_relations(text)
        
        # 打印提取结果
        entities_count = len(result.get("entities", []))
        relations_count = len(result.get("relations", []))
        logger.info(f"从文本中提取了 {entities_count} 个实体和 {relations_count} 个关系")
        
        # 如果有实体，打印一些示例
        if entities_count > 0:
            logger.info("\n实体示例:")
            for i, entity in enumerate(result.get("entities", [])[:3]):
                logger.info(f"{i+1}. {entity.get('name')} (类型: {entity.get('type')})")
                
        # 如果有关系，打印一些示例
        if relations_count > 0:
            logger.info("\n关系示例:")
            for i, relation in enumerate(result.get("relations", [])[:3]):
                logger.info(f"{i+1}. {relation.get('source')} --[{relation.get('relation')}]--> {relation.get('target')}")
                logger.info(f"   描述: {relation.get('description')}")
        
        return result

    async def connect_entities_to_chunks(self, entities: List[Dict[str, str]], 
                                        relations: List[Dict[str, str]],
                                        chunk_id: str, doc_id: str, collection_name="default") -> Tuple[List[Dict[str, str]], List[Dict[str, str]]]:
        """
        将实体和关系与源文本块关联
        
        参数:
            entities: 提取的实体列表
            relations: 提取的关系列表
            chunk_id: 文本块ID
            doc_id: 文档ID
            
        返回:
            增强后的实体和关系列表，包含source_id字段
        """
        # 深拷贝实体和关系列表，避免修改原始数据
        import copy
        enhanced_entities = copy.deepcopy(entities)
        enhanced_relations = copy.deepcopy(relations)
        
        # 为每个实体添加source_id
        for entity in enhanced_entities:
            # 设置source_id字段，指向来源文本块
            entity["source_id"] = chunk_id
            # 设置source_doc字段，指向文档
            entity["source_doc"] = doc_id

            entity["collection_name"] = collection_name
            
        # 为每个关系添加source_id
        for relation in enhanced_relations:
            # 设置source_id字段，指向来源文本块
            relation["source_id"] = chunk_id
            # 设置source_doc字段，指向文档
            relation["source_doc"] = doc_id

            relation["collection_name"] = collection_name
            
        logger.info(f"为 {len(enhanced_entities)} 个实体和 {len(enhanced_relations)} 个关系添加了来源信息: 块ID={chunk_id}, 文档ID={doc_id}")
        
        return enhanced_entities, enhanced_relations

    async def delete_by_collection_name(self, collection_name: str) -> Dict[str, Any]:
        """
        根据collection_name删除所有相关的节点和关系
        
        参数:
            collection_name: 集合名称
            
        返回:
            删除操作的结果统计信息
        """
        if not collection_name:
            logger.error("集合名称不能为空")
            return {"error": "集合名称不能为空", "deleted_nodes": 0, "deleted_relations": 0}
            
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                with driver.session(database=self.neo4j_db) as session:
                    # 先删除关系
                    delete_relations_query = """
                    MATCH ()-[r:RELATED_TO]->()
                    WHERE r.collection_name = $collection_name
                    WITH count(r) AS relations_count
                    CALL {
                        MATCH ()-[r:RELATED_TO]->()
                        WHERE r.collection_name = $collection_name
                        DELETE r
                    }
                    RETURN relations_count
                    """
                    
                    relations_result = session.run(delete_relations_query, {"collection_name": collection_name}).single()
                    deleted_relations = relations_result["relations_count"] if relations_result else 0
                    
                    # 删除APPEARS_IN关系
                    delete_appears_in_query = """
                    MATCH ()-[r:APPEARS_IN]->()
                    WHERE r.collection_name = $collection_name
                    WITH count(r) AS appears_in_count
                    CALL {
                        MATCH ()-[r:APPEARS_IN]->()
                        WHERE r.collection_name = $collection_name
                        DELETE r
                    }
                    RETURN appears_in_count
                    """
                    
                    appears_in_result = session.run(delete_appears_in_query, {"collection_name": collection_name}).single()
                    deleted_appears_in = appears_in_result["appears_in_count"] if appears_in_result else 0
                    
                    # 再删除实体节点
                    delete_entities_query = """
                    MATCH (e:Entity)
                    WHERE e.collection_name = $collection_name
                    WITH count(e) AS entities_count
                    CALL {
                        MATCH (e:Entity)
                        WHERE e.collection_name = $collection_name
                        DELETE e
                    }
                    RETURN entities_count
                    """
                    
                    entities_result = session.run(delete_entities_query, {"collection_name": collection_name}).single()
                    deleted_entities = entities_result["entities_count"] if entities_result else 0
                    
                    # 最后删除文档节点
                    delete_documents_query = """
                    MATCH (d:Document)
                    WHERE d.collection_name = $collection_name
                    WITH count(d) AS documents_count
                    CALL {
                        MATCH (d:Document)
                        WHERE d.collection_name = $collection_name
                        DELETE d
                    }
                    RETURN documents_count
                    """
                    
                    documents_result = session.run(delete_documents_query, {"collection_name": collection_name}).single()
                    deleted_documents = documents_result["documents_count"] if documents_result else 0
                    
                    total_deleted_nodes = deleted_entities + deleted_documents
                    total_deleted_relationships = deleted_relations + deleted_appears_in
                    
                    logger.success(f"已删除集合 '{collection_name}' 的所有数据: {total_deleted_nodes} 个节点, {total_deleted_relationships} 个关系")
                    
                    return {
                        "collection_name": collection_name,
                        "deleted_entities": deleted_entities,
                        "deleted_documents": deleted_documents, 
                        "deleted_relations": deleted_relations,
                        "deleted_appears_in": deleted_appears_in,
                        "total_deleted_nodes": total_deleted_nodes,
                        "total_deleted_relationships": total_deleted_relationships
                    }
                    
            except Exception as e:
                retry_count += 1
                logger.error(f"删除集合数据时出错 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"删除集合数据失败，已达到最大重试次数: {collection_name}")
                    return {
                        "error": f"删除集合数据失败: {str(e)}",
                        "collection_name": collection_name,
                        "deleted_nodes": 0,
                        "deleted_relations": 0
                    }

    async def delete_by_document_chunks(self, file_name: str, chunk_ids: List[str], collection_name: str = "default") -> Dict[str, Any]:
        """
        根据文档名称和分块ID删除或更新图谱数据
        
        处理逻辑：
        1. 对于每个节点，检查其source_id字段是否包含被删除的文本块ID
           - 如果节点的所有源文本块都被删除，则将该节点进行删除
           - 如果节点还有其他源文本块，则更新source_id字段，移除已删除的文本块ID
        2. 系统检查每个关系的source_id字段
           - 如果关系的所有源文本块都被删除，则将该关系进行删除
           - 否则更新source_id字段，保留有效的文本块ID
        3. 对于document节点，直接用file_name匹配title删除
        
        参数:
            file_name: 文档名称
            chunk_ids: 要删除的分块ID列表
            collection_name: 集合名称
            
        返回:
            删除和更新操作的统计信息
        """
        if not chunk_ids:
            logger.warning(f"没有提供分块ID，无法删除图谱数据")
            return {
                "status": "error",
                "message": "没有提供分块ID",
                "deleted_entities": 0,
                "updated_entities": 0,
                "deleted_relations": 0,
                "updated_relations": 0,
                "deleted_documents": 0
            }
            
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                driver = await self.neo4j_driver
                
                with driver.session(database=self.neo4j_db) as session:
                    # 1. 删除文档节点
                    delete_doc_query = """
                    MATCH (d:Document)
                    WHERE d.title = $file_name AND d.collection_name = $collection_name
                    WITH count(d) as documents_count
                    CALL {
                        MATCH (d:Document)
                        WHERE d.title = $file_name AND d.collection_name = $collection_name
                        DETACH DELETE d
                    }
                    RETURN documents_count
                    """
                    
                    doc_result = session.run(delete_doc_query, {
                        "file_name": file_name,
                        "collection_name": collection_name
                    }).single()
                    deleted_documents = doc_result["documents_count"] if doc_result else 0
                    
                    # 2. 处理实体节点
                    # 2.1 查找需要更新的实体（有部分source_id需要保留）
                    entity_update_query = """
                    MATCH (e:Entity)
                    WHERE e.collection_name = $collection_name AND 
                          ANY(chunk_id IN $chunk_ids WHERE chunk_id IN split(e.source_id, '|'))
                    WITH e, [x IN split(e.source_id, '|') WHERE NOT x IN $chunk_ids] as remaining_ids
                    WHERE size(remaining_ids) > 0
                    RETURN e.id as id, e.source_id as source_id, remaining_ids
                    """
                    
                    entities_to_update = list(session.run(entity_update_query, {
                        "collection_name": collection_name,
                        "chunk_ids": chunk_ids
                    }))
                    
                    # 2.2 对每个实体进行source_id更新
                    updated_entities = 0
                    for entity in entities_to_update:
                        entity_id = entity["id"]
                        new_source_id = "|".join(entity["remaining_ids"])
                        
                        # 更新实体
                        update_entity_query = """
                        MATCH (e:Entity {id: $entity_id, collection_name: $collection_name})
                        SET e.source_id = $new_source_id
                        """
                        
                        session.run(update_entity_query, {
                            "entity_id": entity_id,
                            "collection_name": collection_name,
                            "new_source_id": new_source_id
                        })
                        updated_entities += 1
                    
                    # 2.3 删除需要完全删除的实体（所有source_id都在被删除的chunk_ids中）
                    delete_entities_query = """
                    MATCH (e:Entity)
                    WHERE e.collection_name = $collection_name AND
                          ALL(x IN split(e.source_id, '|') WHERE x IN $chunk_ids)
                    WITH count(e) as entities_count
                    CALL {
                        MATCH (e:Entity)
                        WHERE e.collection_name = $collection_name AND
                              ALL(x IN split(e.source_id, '|') WHERE x IN $chunk_ids)
                        DETACH DELETE e
                    }
                    RETURN entities_count
                    """
                    
                    entities_result = session.run(delete_entities_query, {
                        "collection_name": collection_name,
                        "chunk_ids": chunk_ids
                    }).single()
                    deleted_entities = entities_result["entities_count"] if entities_result else 0
                    
                    # 3. 处理关系
                    # 3.1 查找需要更新的关系（有部分source_id需要保留）
                    relation_update_query = """
                    MATCH ()-[r:RELATED_TO]->()
                    WHERE r.collection_name = $collection_name AND 
                          ANY(chunk_id IN $chunk_ids WHERE chunk_id IN split(r.source_id, '|'))
                    WITH r, [x IN split(r.source_id, '|') WHERE NOT x IN $chunk_ids] as remaining_ids
                    WHERE size(remaining_ids) > 0
                    RETURN id(r) as id, r.source_id as source_id, remaining_ids
                    """
                    
                    relations_to_update = list(session.run(relation_update_query, {
                        "collection_name": collection_name,
                        "chunk_ids": chunk_ids
                    }))
                    
                    # 3.2 对每个关系进行source_id更新
                    updated_relations = 0
                    for relation in relations_to_update:
                        relation_id = relation["id"]
                        new_source_id = "|".join(relation["remaining_ids"])
                        
                        # 更新关系
                        update_relation_query = """
                        MATCH ()-[r:RELATED_TO]->()
                        WHERE id(r) = $relation_id AND r.collection_name = $collection_name
                        SET r.source_id = $new_source_id
                        """
                        
                        session.run(update_relation_query, {
                            "relation_id": relation_id,
                            "collection_name": collection_name,
                            "new_source_id": new_source_id
                        })
                        updated_relations += 1
                    
                    # 3.3 删除需要完全删除的关系（所有source_id都在被删除的chunk_ids中）
                    delete_relations_query = """
                    MATCH ()-[r:RELATED_TO]->()
                    WHERE r.collection_name = $collection_name AND
                          ALL(x IN split(r.source_id, '|') WHERE x IN $chunk_ids)
                    WITH count(r) as relations_count
                    CALL {
                        MATCH ()-[r:RELATED_TO]->()
                        WHERE r.collection_name = $collection_name AND
                              ALL(x IN split(r.source_id, '|') WHERE x IN $chunk_ids)
                        DELETE r
                    }
                    RETURN relations_count
                    """
                    
                    relations_result = session.run(delete_relations_query, {
                        "collection_name": collection_name,
                        "chunk_ids": chunk_ids
                    }).single()
                    deleted_relations = relations_result["relations_count"] if relations_result else 0
                    
                    # 删除APPEARS_IN关系
                    delete_appears_in_query = """
                    MATCH ()-[r:APPEARS_IN]->()
                    WHERE r.collection_name = $collection_name AND
                          ANY(chunk_id IN $chunk_ids WHERE chunk_id IN split(r.source_id, '|'))
                    WITH count(r) as appears_in_count
                    CALL {
                        MATCH ()-[r:APPEARS_IN]->()
                        WHERE r.collection_name = $collection_name AND
                              ANY(chunk_id IN $chunk_ids WHERE chunk_id IN split(r.source_id, '|'))
                        DELETE r
                    }
                    RETURN appears_in_count
                    """
                    
                    appears_in_result = session.run(delete_appears_in_query, {
                        "collection_name": collection_name,
                        "chunk_ids": chunk_ids
                    }).single()
                    deleted_appears_in = appears_in_result["appears_in_count"] if appears_in_result else 0
                    
                    logger.info(f"已根据文件名 '{file_name}' 和分块ID处理图谱数据:")
                    logger.info(f"- 删除了 {deleted_documents} 个文档节点")
                    logger.info(f"- 删除了 {deleted_entities} 个实体节点，更新了 {updated_entities} 个实体节点")
                    logger.info(f"- 删除了 {deleted_relations} 个关系和 {deleted_appears_in} 个APPEARS_IN关系，更新了 {updated_relations} 个关系")
                    
                    return {
                        "status": "success",
                        "file_name": file_name,
                        "collection_name": collection_name,
                        "chunk_ids_count": len(chunk_ids),
                        "deleted_documents": deleted_documents,
                        "deleted_entities": deleted_entities,
                        "updated_entities": updated_entities,
                        "deleted_relations": deleted_relations,
                        "updated_relations": updated_relations,
                        "deleted_appears_in": deleted_appears_in
                    }
                    
            except Exception as e:
                retry_count += 1
                logger.error(f"处理图谱数据时出错 (尝试 {retry_count}/{max_retries}): {e}")
                import traceback
                traceback.print_exc()
                
                if retry_count < max_retries:
                    # 指数退避重试策略
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"处理图谱数据失败，已达到最大重试次数: {file_name}")
                    return {
                        "status": "error",
                        "message": f"处理图谱数据失败: {str(e)}",
                        "file_name": file_name,
                        "deleted_entities": 0,
                        "updated_entities": 0,
                        "deleted_relations": 0,
                        "updated_relations": 0,
                        "deleted_documents": 0
                    }