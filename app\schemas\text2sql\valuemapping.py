from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class Text2sqlValuemappingBase(BaseModel):
    """值映射基础模型"""
    column_id: int = Field(..., description="列ID")
    nl_term: str = Field(..., description="自然语言术语", max_length=255)
    db_value: str = Field(..., description="数据库值", max_length=255)


class Text2sqlValuemappingCreate(Text2sqlValuemappingBase):
    """创建值映射请求模型"""
    pass


class Text2sqlValuemappingUpdate(BaseModel):
    """更新值映射请求模型"""
    nl_term: Optional[str] = Field(None, description="自然语言术语", max_length=255)
    db_value: Optional[str] = Field(None, description="数据库值", max_length=255)


class Text2sqlValuemappingResponse(Text2sqlValuemappingBase):
    """值映射响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="映射ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class Text2sqlValuemappingFilter(BaseModel):
    """值映射查询过滤器"""
    column_id: Optional[int] = Field(None, description="列ID")
    nl_term: Optional[str] = Field(None, description="自然语言术语") 