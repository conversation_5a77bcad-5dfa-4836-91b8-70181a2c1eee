import json
import time
from typing import List, Optional, Awaitable, Callable, Dict, Tuple, Any

from autogen_core import SingleThreadedAgentRuntime, DefaultTopicId, TopicId, \
    ClosureContext, CancellationToken, TypeSubscription
from autogen_core import ClosureAgent

from app.schemas import (
    Text2SQLResponse, ResponseMessage, QueryMessage
)

from app.services.db_service import get_db_engine
from dao.text2sqlsys_utils import get_datasource_by_id

# 从agents模块导入所有智能体和相关类
from app.services.agents import (
    SchemaRetrieverAgent,
    QueryAnalyzerAgent,
    SqlGeneratorAgent,
    SqlExplainerAgent,
    SqlExecutorAgent,
    VisualizationRecommenderAgent,
    Text2SQLException,
    DatabaseConnectionException,
    schema_retriever_topic_type,
    stream_output_topic_type,
    DB_TYPE, query_analyzer_topic_type, sql_generator_topic_type, sql_explainer_topic_type, sql_executor_topic_type,
    visualization_recommender_topic_type
)

# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()


class StreamResponseCollector:
    """流式响应收集器，用于收集智能体产生的流式输出"""

    def __init__(self):
        """初始化流式响应收集器"""
        self.callback: Optional[Callable[[ClosureContext, ResponseMessage, Any], Awaitable[None]]] = None
        self.user_input: Optional[Callable[[str, CancellationToken], Awaitable[str]]] = None
        self.message_buffers: Dict[str, str] = {}  # 用于缓存各智能体的消息
        self.last_flush_time: Dict[str, float] = {}  # 记录最后一次刷新缓冲区的时间
        self.buffer_flush_interval: float = 0.3  # 缓冲区刷新间隔（秒）
        self._is_closed: bool = False  # 标记收集器是否已关闭

    def set_callback(self, callback: Callable[[ClosureContext, ResponseMessage, Any], Awaitable[None]]) -> None:
        """设置回调函数

        Args:
            callback: 用于处理响应消息的异步回调函数
        """
        if not callable(callback):
            raise ValueError("回调函数必须是可调用对象")
        self.callback = callback

    def set_user_input(self, user_input: Callable[[str, CancellationToken], Awaitable[str]]) -> None:
        """设置用户输入函数

        Args:
            user_input: 用于获取用户输入的异步函数
        """
        if user_input is not None and not callable(user_input):
            raise ValueError("用户输入函数必须是可调用对象或None")
        self.user_input = user_input

    async def buffer_message(self, ctx: ClosureContext, source: str, content: str,
                          is_final: bool = False, result: Dict[str, Any] = None) -> None:
        """缓冲消息并在达到一定条件时发送

        Args:
            ctx: 闭包上下文
            source: 消息来源
            content: 消息内容
            is_final: 是否最终消息
            result: 可选的结果数据
        """
        if self._is_closed:
            logger.warning("StreamResponseCollector已关闭，忽略消息")
            return
            
        if not self.callback:
            logger.warning("未设置回调函数，无法发送消息")
            return

        try:
            current_time = time.time()

            # 如果是最终消息或结果不为空，直接发送，不经过缓冲
            if is_final or result:
                # 先发送缓冲区中的消息
                if source in self.message_buffers and self.message_buffers[source]:
                    await self._safe_callback(ctx, ResponseMessage(
                        source=source,
                        content=self.message_buffers[source]
                    ))
                    self.message_buffers[source] = ""

                # 再发送当前消息
                await self._safe_callback(ctx, ResponseMessage(
                    source=source,
                    content=content,
                    is_final=is_final,
                    result=result
                ))
                return

            # 累积消息到缓冲区
            if source not in self.message_buffers:
                self.message_buffers[source] = ""
                self.last_flush_time[source] = current_time

            self.message_buffers[source] += content

            # 检查是否需要刷新缓冲区
            if current_time - self.last_flush_time.get(source, 0) >= self.buffer_flush_interval:
                await self._safe_callback(ctx, ResponseMessage(
                    source=source,
                    content=self.message_buffers[source]
                ))
                self.message_buffers[source] = ""
                self.last_flush_time[source] = current_time
                
        except Exception as e:
            logger.error(f"缓冲消息时发生错误: {str(e)}")

    async def _safe_callback(self, ctx: ClosureContext, message: ResponseMessage) -> None:
        """安全地调用回调函数
        
        Args:
            ctx: 闭包上下文
            message: 响应消息
        """
        try:
            await self.callback(ctx, message, None)
        except Exception as e:
            logger.error(f"回调函数执行失败: {str(e)}")

    async def flush_all_buffers(self, ctx: ClosureContext = None) -> None:
        """刷新所有消息缓冲区

        Args:
            ctx: 可选的闭包上下文
        """
        if not self.callback or self._is_closed:
            return

        try:
            for source, content in self.message_buffers.items():
                if content.strip():  # 只发送非空内容
                    await self._safe_callback(ctx, ResponseMessage(
                        source=source,
                        content=content
                    ))

            # 清空所有缓冲区
            self.message_buffers.clear()
            self.last_flush_time.clear()
        except Exception as e:
            logger.error(f"刷新缓冲区时发生错误: {str(e)}")

    def close(self) -> None:
        """关闭收集器"""
        self._is_closed = True
        self.message_buffers.clear()
        self.last_flush_time.clear()
        logger.info("StreamResponseCollector已关闭")


class Text2SQLService:
    """Text2SQL服务类，处理自然语言到SQL转换的全流程"""
    
    def __init__(self):
        """初始化Text2SQL服务"""
        self.db_type = DB_TYPE
        self.db_engine = None
        self._runtime = None

    async def _get_connection_info(self, connection_id: str):
        """根据连接ID获取数据库连接信息和类型

        Args:
            connection_id: 数据库连接ID

        Returns:
            DBConnection: 数据库连接信息
            
        Raises:
            DatabaseConnectionException: 当获取连接信息失败时
        """
        if not connection_id:
            raise DatabaseConnectionException("数据库连接ID不能为空")
            
        try:
            connection_info = await get_datasource_by_id(connection_id)
            if not connection_info:
                raise DatabaseConnectionException(f"未找到连接ID为 {connection_id} 的数据库连接")
            return connection_info
        except Exception as e:
            logger.error(f"获取数据库连接信息时出错: {str(e)}")
            raise DatabaseConnectionException(f"获取数据库连接信息失败: {str(e)}")

    async def _setup_database_engine(self, connection_id: str) -> None:
        """设置数据库引擎和类型
        
        Args:
            connection_id: 数据库连接ID
            
        Raises:
            DatabaseConnectionException: 当设置数据库引擎失败时
        """
        try:
            # 获取数据库连接信息
            connection_info = await self._get_connection_info(connection_id)
            logger.info(f"[连接ID: {connection_id}] 数据库类型: {connection_info.db_type}")
            # 设置数据库引擎
            self.db_engine = await get_db_engine(connection_info)
            if not self.db_engine:
                raise DatabaseConnectionException(f"无法为连接ID {connection_id} 创建数据库引擎")
            # 设置数据库类型
            if connection_info.db_type == "4":
                self.db_type = "MySQL"
            elif connection_info.db_type == "6":
                self.db_type = "PostgreSQL"
            elif connection_info.db_type == "10":
                self.db_type = "SQLite"
            else:
                logger.warning(f"未知的数据库类型: {connection_info.db_type}，使用默认类型 MySQL")
                self.db_type = "MySQL"

            logger.info(f"数据库引擎设置完成，类型: {self.db_type}")
            
        except DatabaseConnectionException:
            # 重新抛出自定义异常
            raise
        except Exception as e:
            logger.error(f"设置数据库引擎时出错: {str(e)}")
            raise DatabaseConnectionException(f"设置数据库引擎失败: {str(e)}")

    async def _register_agents(self, runtime: SingleThreadedAgentRuntime, collector: StreamResponseCollector) -> None:
        """注册所有智能体

        Args:
            runtime: 运行时实例
            collector: 流式响应收集器

        Raises:
            Text2SQLException: 当注册智能体失败时
        """
        try:
            logger.info("开始注册智能体...")

            # 注册各个智能体
            await SchemaRetrieverAgent.register(
                runtime,
                schema_retriever_topic_type,
                lambda: SchemaRetrieverAgent(db_type=self.db_type)
            )

            await QueryAnalyzerAgent.register(
                runtime,
                query_analyzer_topic_type,
                lambda: QueryAnalyzerAgent(db_type=self.db_type, input_func=collector.user_input)
            )

            await SqlGeneratorAgent.register(
                runtime,
                sql_generator_topic_type,
                lambda: SqlGeneratorAgent(db_type=self.db_type)
            )

            await SqlExplainerAgent.register(
                runtime,
                sql_explainer_topic_type,
                lambda: SqlExplainerAgent(db_type=self.db_type)
            )

            await SqlExecutorAgent.register(
                runtime,
                sql_executor_topic_type,
                lambda: SqlExecutorAgent(db_type=self.db_type, db_engine=self.db_engine)
            )

            await VisualizationRecommenderAgent.register(
                runtime,
                visualization_recommender_topic_type,
                lambda: VisualizationRecommenderAgent(db_type=self.db_type)
            )

            # 注册流式响应收集器
            await ClosureAgent.register_closure(
                runtime,
                "stream_collector_agent",
                collector.callback,
                subscriptions=lambda: [
                    TypeSubscription(
                        topic_type=stream_output_topic_type,
                        agent_type="stream_collector_agent"
                    )
                ],
            )

            logger.info("所有智能体注册完成")

        except Exception as e:
            logger.error(f"注册智能体时出错: {str(e)}")
            raise Text2SQLException(f"注册智能体失败: {str(e)}")

    async def _cleanup_runtime(self) -> None:
        """清理运行时资源"""
        try:
            if self._runtime:
                # 检查运行时是否还在运行
                if hasattr(self._runtime, '_running') and self._runtime._running:
                    await self._runtime.stop()
                    logger.info("运行时已停止")
                else:
                    logger.info("运行时已经停止，无需重复停止")
                self._runtime = None
                logger.info("运行时资源已清理")
        except Exception as e:
            logger.error(f"清理运行时时出错: {str(e)}")
            # 即使出错也要清理引用
            self._runtime = None

    async def process_query(self, query: str, connection_id: str = None, collector: StreamResponseCollector = None):
        """处理自然语言查询，转换为SQL并执行
        
        Args:
            query: 用户的自然语言查询
            connection_id: 数据库连接ID
            collector: 流式响应收集器
            
        Returns:
            Text2SQLResponse: 处理结果
            
        Raises:
            Text2SQLException: 当处理过程中出现错误时
        """
        if not query or not query.strip():
            raise Text2SQLException("查询内容不能为空")
            
        if not collector:
            raise Text2SQLException("流式响应收集器不能为空")

        try:
            # 设置数据库引擎（如果提供了连接ID）
            if connection_id:
                await self._setup_database_engine_async(connection_id)

            # 创建运行时
            runtime = SingleThreadedAgentRuntime()
            self._runtime = runtime

            # 注册智能体
            await self._register_agents(runtime, collector)

            # 启动运行时
            runtime.start()
            logger.info("运行时启动成功")

            # 发送初始查询消息给表结构检索智能体
            await self._runtime.publish_message(
                QueryMessage(query=query, connection_id=connection_id),
                topic_id=DefaultTopicId(type=schema_retriever_topic_type)
            )
            logger.info("初始查询消息已发送")

            # 等待处理完成
            await runtime.stop_when_idle()
            
            logger.info("查询处理完成")
            
        except Exception as e:
            logger.error(f"处理查询时出错: {str(e)}")
            raise Text2SQLException(f"处理查询失败: {str(e)}")
        finally:
            # 清理资源
            await self._cleanup_runtime()
            if collector:
                await collector.flush_all_buffers()

    async def _setup_database_engine_async(self, connection_id: str) -> None:
        """异步设置数据库引擎
        
        Args:
            connection_id: 数据库连接ID
        """
        # 直接调用异步的数据库设置操作
        await self._setup_database_engine(connection_id)