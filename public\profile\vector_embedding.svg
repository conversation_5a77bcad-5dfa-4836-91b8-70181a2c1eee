<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.08"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.08"/>
    </linearGradient>
    
    <!-- 文档渐变 -->
    <linearGradient id="doc" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1d4ed8"/>
    </linearGradient>
    
    <!-- 向量渐变 -->
    <linearGradient id="vector" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4"/>
      <stop offset="100%" style="stop-color:#0891b2"/>
    </linearGradient>
    
    <!-- 神经网络渐变 -->
    <radialGradient id="neural" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8b5cf6"/>
      <stop offset="100%" style="stop-color:#7c3aed"/>
    </radialGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow2" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="1" dy="2" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.15"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 强发光效果 -->
    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 - 添加脉冲动画 -->
  <circle cx="128" cy="128" r="120" fill="url(#bg2)" stroke="#e2e8f0" stroke-width="2">
    <animate attributeName="r" values="120;125;120" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2;4;2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 左侧文档 - 添加浮动和发光效果 -->
  <g transform="translate(30, 60)" filter="url(#shadow2)">
    <animateTransform attributeName="transform" type="translate" values="30,60; 30,55; 30,60" dur="3s" repeatCount="indefinite"/>
    
    <rect x="0" y="0" width="70" height="90" rx="6" fill="url(#doc)" opacity="0.9">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="4" y="4" width="62" height="82" rx="4" fill="white" opacity="0.95"/>
    
    <!-- 文档内容线条 - 添加逐行显示动画 -->
    <g stroke="#6b7280" stroke-width="1.5" opacity="0.7">
      <line x1="8" y1="15" x2="50" y2="15">
        <animate attributeName="x2" values="8;50;8;50" dur="6s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="25" x2="58" y2="25">
        <animate attributeName="x2" values="8;58;8;58" dur="6.5s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="35" x2="45" y2="35">
        <animate attributeName="x2" values="8;45;8;45" dur="7s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="45" x2="55" y2="45">
        <animate attributeName="x2" values="8;55;8;55" dur="7.5s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="55" x2="40" y2="55">
        <animate attributeName="x2" values="8;40;8;40" dur="8s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="65" x2="52" y2="65">
        <animate attributeName="x2" values="8;52;8;52" dur="8.5s" repeatCount="indefinite"/>
      </line>
      <line x1="8" y1="75" x2="35" y2="75">
        <animate attributeName="x2" values="8;35;8;35" dur="9s" repeatCount="indefinite"/>
      </line>
    </g>
  </g>
  
  <!-- 转换箭头 - 增强动画效果 -->
  <g transform="translate(110, 100)">
    <animateTransform attributeName="transform" type="translate" values="110,100; 115,100; 110,100" dur="2s" repeatCount="indefinite"/>
    
    <defs>
      <marker id="arrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
        <polygon points="0 0, 12 4, 0 8" fill="#f59e0b"/>
      </marker>
    </defs>
    <path d="M0 5 L30 5" stroke="#f59e0b" stroke-width="3" marker-end="url(#arrow)" filter="url(#strongGlow)">
      <animate attributeName="stroke-width" values="3;5;3" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="stroke-dasharray" values="0 35;15 20;0 35" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <!-- 魔法粒子效果 - 增强动画 -->
    <circle cx="8" cy="2" r="1.5" fill="#fbbf24" opacity="0.8">
      <animate attributeName="cy" values="2;-2;2;6;2" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="r" values="1.5;2.5;1.5" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="8" r="1" fill="#f59e0b" opacity="0.6">
      <animate attributeName="cy" values="8;4;8;12;8" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="r" values="1;2;1" dur="1.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="22" cy="1" r="1.2" fill="#fbbf24" opacity="0.9">
      <animate attributeName="cy" values="1;-3;1;5;1" dur="1.8s" repeatCount="indefinite"/>
      <animate attributeName="r" values="1.2;2.2;1.2" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.9;1;0.9" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 右侧向量立方体 - 添加3D旋转效果 -->
  <g transform="translate(160, 50)" filter="url(#shadow2)">
    <animateTransform attributeName="transform" type="translate" values="160,50; 160,45; 160,50" dur="4s" repeatCount="indefinite"/>
    
    <!-- 3D立方体效果 - 添加旋转透视 -->
    <g>
      <animateTransform attributeName="transform" type="skewX" values="0;3;0;-3;0" dur="8s" repeatCount="indefinite"/>
      
      <path d="M0 40 L0 100 L60 100 L60 40 Z" fill="url(#vector)" opacity="0.7">
        <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
      </path>
      <path d="M0 40 L20 20 L80 20 L60 40 Z" fill="url(#vector)" opacity="0.9">
        <animate attributeName="opacity" values="0.9;1;0.9" dur="3.5s" repeatCount="indefinite"/>
      </path>
      <path d="M60 40 L80 20 L80 80 L60 100 Z" fill="url(#vector)" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 向量数据点 - 添加脉冲和连接动画 -->
    <g fill="url(#neural)" opacity="0.8">
      <circle cx="15" cy="60" r="3">
        <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="30" cy="70" r="3">
        <animate attributeName="r" values="3;5;3" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="45" cy="60" r="3">
        <animate attributeName="r" values="3;5;3" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="85" r="3">
        <animate attributeName="r" values="3;5;3" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="45" cy="85" r="3">
        <animate attributeName="r" values="3;5;3" dur="2.1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2.1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="30" cy="50" r="2.5">
        <animate attributeName="r" values="2.5;4.5;2.5" dur="1.7s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1.7s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 向量连接线 - 添加流动效果 -->
      <g stroke="url(#neural)" stroke-width="1.5" fill="none" opacity="0.6">
        <line x1="15" y1="60" x2="30" y2="70">
          <animate attributeName="stroke-dasharray" values="0 20;10 10;0 20" dur="3s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </line>
        <line x1="30" y1="70" x2="45" y2="60">
          <animate attributeName="stroke-dasharray" values="0 20;10 10;0 20" dur="3.2s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
        </line>
        <line x1="15" y1="60" x2="15" y2="85">
          <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="2.8s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
        </line>
        <line x1="45" y1="60" x2="45" y2="85">
          <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="3.1s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.1s" repeatCount="indefinite"/>
        </line>
        <line x1="30" y1="50" x2="30" y2="70">
          <animate attributeName="stroke-dasharray" values="0 20;10 10;0 20" dur="2.5s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
        </line>
        <line x1="15" y1="85" x2="45" y2="85">
          <animate attributeName="stroke-dasharray" values="0 30;15 15;0 30" dur="3.5s" repeatCount="indefinite"/>
          <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
        </line>
      </g>
    </g>
  </g>
   
  <!-- 神经网络图标 - 添加脉冲连接动画 -->
  <g transform="translate(115, 160)">
    <animateTransform attributeName="transform" type="translate" values="115,160; 115,165; 115,160" dur="5s" repeatCount="indefinite"/>
    
    <!-- 神经元层 - 添加脉冲效果 -->
    <g fill="url(#neural)" opacity="0.7">
      <circle cx="0" cy="0" r="4">
        <animate attributeName="r" values="4;6;4" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="20" r="4">
        <animate attributeName="r" values="4;6;4" dur="3.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3.2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="5" r="4">
        <animate attributeName="r" values="4;6;4" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="15" r="4">
        <animate attributeName="r" values="4;6;4" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;1;0.7" dur="3.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 神经连接 - 添加信号传递动画 -->
    <g stroke="url(#neural)" stroke-width="1.5" opacity="0.5" fill="none">
      <line x1="4" y1="0" x2="21" y2="5">
        <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="4" y1="0" x2="21" y2="15">
        <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2.3s" repeatCount="indefinite"/>
      </line>
      <line x1="4" y1="20" x2="21" y2="5">
        <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="1.8s" repeatCount="indefinite"/>
      </line>
      <line x1="4" y1="20" x2="21" y2="15">
        <animate attributeName="stroke-dasharray" values="0 25;12 13;0 25" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/>
      </line>
    </g>
  </g>
   
  <!-- 数据流动效果 - 增强动画 -->
  <g transform="translate(50, 190)">
    <circle cx="0" cy="0" r="3" fill="#f59e0b" opacity="0.3" filter="url(#glow)">
      <animate attributeName="cx" values="0;150;0" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="r" values="3;6;3" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="0" r="2.5" fill="#3b82f6" opacity="0.4" filter="url(#glow)">
      <animate attributeName="cx" values="20;170;20" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="r" values="2.5;5.5;2.5" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.9;0.4" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="40" cy="0" r="2" fill="#06b6d4" opacity="0.5" filter="url(#glow)">
      <animate attributeName="cx" values="40;190;40" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="r" values="2;5;2" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 添加旋转的装饰环 -->
  <g transform="translate(128, 128)" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" values="0 128 128;360 128 128" dur="20s" repeatCount="indefinite"/>
    <circle cx="0" cy="0" r="100" fill="none" stroke="#667eea" stroke-width="1" stroke-dasharray="5 10">
      <animate attributeName="stroke-opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg> 