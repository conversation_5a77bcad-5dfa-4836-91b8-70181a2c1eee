import asyncio
from typing import List, Dict, Any, Optional, Literal
from llama_index.core import QueryBundle
from llama_index.core.indices.base import BaseIndex
from llama_index.core.retrievers import VectorIndexRetriever

from .multimodal_rag import MultiModalRAG
from .local_mode_retriever import LocalModeRetriever
from utils.kg_utils import KnowledgeGraphManager

class EnhancedKGRetriever:
    """
    增强版知识图谱检索器，集成多种检索模式
    包括：
    - 向量检索（naive）：基于语义相似度的检索
    - 知识图谱增强（kg_enhance）：基于实体和关系的增强检索
    - 局部模式（local）：以实体为中心的检索
    - 混合模式（hybrid）：结合向量检索和局部模式
    """
    
    def __init__(self, 
                 index: Optional[BaseIndex] = None, 
                 files: List[str] = None,
                 top_k: int = 5):
        """
        初始化增强版检索器
        
        参数:
            index: LlamaIndex检索索引，如果为None则使用默认索引
            files: 文件路径列表，用于创建MultiModalRAG实例
            top_k: 检索返回的最大结果数量
        """
        self.index = index
        self.files = files or []
        self.top_k = top_k
        
        # 初始化多模态RAG
        if self.files:
            self.rag = MultiModalRAG(self.files)
        else:
            self.rag = None
            
        # 初始化局部模式检索器
        self.local_mode_retriever = LocalModeRetriever(top_k=self.top_k)
        
        # 获取知识图谱管理器实例
        self.kg_manager = KnowledgeGraphManager.get_instance()
        
    async def _ensure_initialized(self):
        """确保资源已初始化"""
        # 确保知识图谱数据库已初始化
        if self.rag:
            await self.rag._ensure_db_initialized()
        
    async def vector_retrieve(self, query: str) -> List[Dict[str, Any]]:
        """
        执行向量检索
        
        参数:
            query: 查询文本
            
        返回:
            List[Dict]: 向量检索结果列表
        """
        retrieval_results = []
        
        if self.index:
            # 使用提供的索引进行检索
            retriever = VectorIndexRetriever(
                index=self.index,
                similarity_top_k=self.top_k
            )
            query_bundle = QueryBundle(query_str=query)
            nodes = retriever.retrieve(query_bundle)
            
            # 转换检索结果格式
            for node in nodes:
                retrieval_results.append({
                    "text": node.node.text,
                    "metadata": node.node.metadata,
                    "score": node.score if hasattr(node, 'score') else None,
                    "id": node.node.id_ if hasattr(node.node, 'id_') else None
                })
                
        return retrieval_results
    
    async def kg_enhance_retrieve(self, query: str, retrieval_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        执行知识图谱增强检索
        
        参数:
            query: 查询文本
            retrieval_results: 向量检索结果
            
        返回:
            Dict: 知识图谱增强结果
        """
        if not self.rag:
            return {
                "original_results": retrieval_results,
                "knowledge_graph": {"nodes": [], "links": []},
                "entity_details": []
            }
            
        return await self.rag.enhance_with_knowledge_graph(query, retrieval_results)
    
    async def local_mode_retrieve(self, query: str) -> Dict[str, Any]:
        """
        执行局部模式检索
        
        参数:
            query: 查询文本
            
        返回:
            Dict: 局部模式检索结果
        """
        return await self.local_mode_retriever.retrieve(query)
    
    async def hybrid_retrieve(self, query: str) -> Dict[str, Any]:
        """
        执行混合模式检索，结合向量检索和局部模式
        
        参数:
            query: 查询文本
            
        返回:
            Dict: 混合模式检索结果
        """
        # 并行执行向量检索和局部模式检索
        vector_results, local_results = await asyncio.gather(
            self.vector_retrieve(query),
            self.local_mode_retrieve(query)
        )
        
        # 使用局部模式增强向量检索结果
        enhanced_results = await self.local_mode_retriever.enhance_retrieval_results(
            query, 
            vector_results
        )
        
        return enhanced_results
    
    async def retrieve(self, 
                      query: str, 
                      mode: Literal["naive", "kg_enhance", "local", "hybrid"] = "hybrid",
                      include_text: bool = True) -> Dict[str, Any]:
        """
        执行检索，支持多种检索模式
        
        参数:
            query: 查询文本
            mode: 检索模式
                - naive: 只使用向量检索
                - kg_enhance: 使用知识图谱增强向量检索结果
                - local: 只使用局部模式检索
                - hybrid: 结合向量检索和局部模式检索（默认）
            include_text: 是否包含原始文本内容，设为False可减少返回数据量
            
        返回:
            Dict: 检索结果
        """
        await self._ensure_initialized()
        
        if mode == "naive":
            # 只使用向量检索
            retrieval_results = await self.vector_retrieve(query)
            return {"results": retrieval_results}
            
        elif mode == "kg_enhance":
            # 知识图谱增强
            retrieval_results = await self.vector_retrieve(query)
            enhanced_results = await self.kg_enhance_retrieve(query, retrieval_results)
            return enhanced_results
            
        elif mode == "local":
            # 只使用局部模式
            local_results = await self.local_mode_retrieve(query)
            return local_results
            
        elif mode == "hybrid":
            # 混合模式
            hybrid_results = await self.hybrid_retrieve(query)
            return hybrid_results
            
        else:
            raise ValueError(f"不支持的检索模式: {mode}")
            
    async def format_results_for_llm(self, results: Dict[str, Any]) -> str:
        """
        将检索结果格式化为LLM可用的上下文
        
        参数:
            results: 检索结果
            
        返回:
            str: 格式化后的上下文文本
        """
        # 提取实体信息
        entities_text = ""
        if "entities" in results and results["entities"]:
            entities = results["entities"]
            entities_text = "实体信息:\n"
            for i, entity in enumerate(entities[:5], 1):  # 只取前5个实体
                entity_type = entity.get("type", "未知类型")
                entity_name = entity.get("name", "未知实体")
                entity_text = f"{i}. {entity_name} (类型: {entity_type})\n"
                entities_text += entity_text
                
        # 提取关系信息
        relations_text = ""
        if "relations" in results and results["relations"]:
            relations = results["relations"]
            relations_text = "\n关系信息:\n"
            for i, relation in enumerate(relations[:5], 1):  # 只取前5个关系
                source = relation.get("source", "未知来源")
                relation_type = relation.get("relation", "未知关系")
                target = relation.get("target", "未知目标")
                description = relation.get("description", "")
                relation_text = f"{i}. {source} --[{relation_type}]--> {target}"
                if description:
                    relation_text += f" ({description})"
                relation_text += "\n"
                relations_text += relation_text
                
        # 提取文本块信息
        text_units_text = ""
        if "text_units" in results and results["text_units"]:
            text_units = results["text_units"]
            text_units_text = "\n文本块:\n"
            for i, unit in enumerate(text_units[:3], 1):  # 只取前3个文本块
                text_units_text += f"{i}. {unit.get('title', '未知标题')}\n"
                
        # 提取向量检索结果
        vector_text = ""
        if "original_results" in results and results["original_results"]:
            vector_results = results["original_results"]
            vector_text = "\n向量检索结果:\n"
            for i, result in enumerate(vector_results[:3], 1):  # 只取前3个结果
                text = result.get("text", "")
                if len(text) > 200:
                    text = text[:200] + "..."
                vector_text += f"{i}. {text}\n\n"
                
        # 组合所有信息
        context = f"{entities_text}\n{relations_text}\n{text_units_text}\n{vector_text}".strip()
        return context
    
    async def close(self):
        """关闭资源"""
        tasks = []
        if self.rag:
            tasks.append(self.rag.close())
        tasks.append(self.local_mode_retriever.close())
        await asyncio.gather(*tasks) 