from typing import List, Optional, Dict, Any

from fastapi import  Request, Response, Query, Body, APIRouter
from fastapi.responses import JSONResponse
import pandas as pd
import json
import traceback
from typing import Optional, List
from fastapi import Request, Query, Response
from rag.database_rag import MySQLDatabaseRAG
from utils.vanna_utils import get_yq_instance
from app.services.question_history_service import QuestionHistoryService

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)

@router.get("/generate_questions",
           summary="生成示例问题",
           description="基于数据库结构自动生成可询问的示例问题列表，帮助用户了解系统能力")
async def generate_questions():
    yq = get_yq_instance()
    return {"type": "question_list", "questions": yq.generate_questions(), "header": "这里是一些您可以问的问题:"}

@router.get("/generate_sql",
           summary="生成SQL查询",
           description="根据自然语言问题生成对应的SQL查询语句，并保存到问题历史记录")
async def generate_sql(question: Optional[str] = None, session_id: Optional[str] = None):
    if question is None:
        return JSONResponse(content={"type": "error", "error": "No question provided"})

    yq = get_yq_instance()
    sql = yq.generate_sql(question=question)
    
    # 保存到数据库进行持久化
    try:
        history_id = await QuestionHistoryService.save_question_history(
            question=question,
            sql=sql,
            session_id=session_id,
            execution_status="generated"
        )
        return {"type": "sql", "id": history_id, "text": sql}
    except Exception as e:
        # 如果持久化失败，返回错误
        return JSONResponse(content={"type": "error", "error": f"保存问题历史失败: {str(e)}"})

@router.get("/run_sql",
           summary="执行SQL查询",
           description="执行指定问题历史记录中的SQL查询，返回查询结果数据")
async def run_sql(request: Request, history_id: str = Query(..., description="问题历史记录ID")):
    try:
        # 从数据库获取SQL
        question_data = await QuestionHistoryService.get_question_by_id(history_id)
        if not question_data or not question_data.get("sql"):
            return JSONResponse(content={"type": "error", "error": "未找到对应的SQL语句"})
        
        sql = question_data["sql"]
        yq = get_yq_instance()
        df = yq.run_sql(sql=sql)

        # 更新数据库中的执行状态和查询结果
        try:
            # 将查询结果转换为JSON字符串保存
            query_results_json = json.dumps(json.loads(df.head(100).to_json(orient='records')), ensure_ascii=False)
            
            await QuestionHistoryService.update_question_history(
                history_id=history_id,
                execution_status="success",
                query_results=query_results_json,
                results_count=len(df)
            )
        except Exception as e:
            print(f"更新问题历史状态失败: {str(e)}")

        return {
            "type": "df",
            "id": history_id,
            "df": json.loads(df.head(10).to_json(orient='records')),
        }
    except Exception as e:
        # 更新数据库中的执行状态为失败
        try:
            await QuestionHistoryService.update_question_history(
                history_id=history_id,
                execution_status="failed"
            )
        except Exception as update_e:
            print(f"更新问题历史状态失败: {str(update_e)}")
        
        return JSONResponse(content={"type": "error", "error": str(e)})

@router.get("/download_csv",
           summary="下载CSV文件",
           description="将指定问题历史记录的查询结果导出为CSV文件进行下载")
async def download_csv(request: Request, history_id: str = Query(..., description="问题历史记录ID")):
    try:
        # 从数据库获取查询结果
        question_data = await QuestionHistoryService.get_question_by_id(history_id)
        if not question_data or not question_data.get("query_results"):
            return JSONResponse(content={"type": "error", "error": "未找到查询结果数据"})
        
        # 解析查询结果JSON
        query_results = json.loads(question_data["query_results"])
        df = pd.DataFrame(query_results)
        csv = df.to_csv(index=False)

        headers = {
            "Content-Disposition": f"attachment; filename={history_id}.csv"
        }

        return Response(content=csv, media_type="text/csv", headers=headers)
    except Exception as e:
        return JSONResponse(content={"type": "error", "error": f"生成CSV失败: {str(e)}"})

@router.get("/generate_plotly_figure",
           summary="生成可视化图表",
           description="根据问题和查询结果生成Plotly可视化图表")
async def generate_plotly_figure(request: Request, history_id: str = Query(..., description="问题历史记录ID")):
    try:
        # 从数据库获取数据
        question_data = await QuestionHistoryService.get_question_by_id(history_id)
        if not question_data:
            return JSONResponse(content={"type": "error", "error": "未找到问题历史记录"})
        
        question = question_data["question"]
        sql = question_data["sql"]
        query_results = question_data.get("query_results")
        
        if not query_results:
            return JSONResponse(content={"type": "error", "error": "未找到查询结果数据，请先执行SQL"})
        
        # 重构DataFrame
        df = pd.DataFrame(json.loads(query_results))
        
        yq = get_yq_instance()
        code = yq.generate_plotly_code(question=question, sql=sql,
                                      df_metadata=f"Running df.dtypes gives:\n {df.dtypes}")
        fig = yq.get_plotly_figure(plotly_code=code, df=df, dark_mode=False)
        fig_json = fig.to_json()

        # 保存可视化数据到数据库
        try:
            await QuestionHistoryService.update_question_history(
                history_id=history_id,
                plotly_figure=fig_json
            )
        except Exception as e:
            print(f"保存可视化数据失败: {str(e)}")

        return {
            "type": "plotly_figure",
            "id": history_id,
            "fig": fig_json,
        }
    except Exception as e:
        # 打印堆栈跟踪
        traceback.print_exc()
        return JSONResponse(content={"type": "error", "error": str(e)})

@router.get("/get_training_data",
           summary="获取训练数据",
           description="获取系统中的训练数据集，包括问题-SQL对和DDL信息")
async def get_training_data():
    yq = get_yq_instance()
    df = yq.get_training_data()

    return {
        "type": "df",
        "id": "training_data",
        "df": json.loads(df.head(25).to_json(orient='records')),
    }

@router.post("/remove_training_data",
            summary="删除训练数据",
            description="删除指定的训练数据记录")
async def remove_training_data(data: Dict[str, Any] = Body(...)):
    id = data.get('id')

    if id is None:
        return JSONResponse(content={"type": "error", "error": "No id provided"})

    yq = get_yq_instance()
    if yq.remove_training_data(id=id):
        return {"success": True}
    else:
        return JSONResponse(content={"type": "error", "error": "Couldn't remove training data"})

@router.post("/train",
            summary="添加训练数据",
            description="向系统添加新的训练数据，包括问题、SQL、DDL和文档信息")
async def add_training_data(data: Dict[str, Any] = Body(...)):
    question = data.get('question')
    sql = data.get('sql')
    ddl = data.get('ddl')
    documentation = data.get('documentation')

    try:
        yq = get_yq_instance()
        id = yq.train(question=question, sql=sql, ddl=ddl, documentation=documentation)
        return {"id": id}
    except Exception as e:
        print("TRAINING ERROR", e)
        return JSONResponse(content={"type": "error", "error": str(e)})

@router.get("/generate_followup_questions",
           summary="生成后续问题",
           description="基于当前问题和查询结果生成相关的后续问题建议")
async def generate_followup_questions(request: Request, history_id: str = Query(..., description="问题历史记录ID")):
    try:
        # 从数据库获取数据
        question_data = await QuestionHistoryService.get_question_by_id(history_id)
        if not question_data:
            return JSONResponse(content={"type": "error", "error": "未找到问题历史记录"})
        
        question = question_data["question"]
        sql = question_data["sql"]
        query_results = question_data.get("query_results")
        
        if not query_results:
            return JSONResponse(content={"type": "error", "error": "未找到查询结果数据，请先执行SQL"})
        
        # 重构DataFrame
        df = pd.DataFrame(json.loads(query_results))
        
        yq = get_yq_instance()
        followup_questions = yq.generate_followup_questions(question=question, sql=sql, df=df)

        # 保存后续问题到数据库
        try:
            followup_questions_json = json.dumps(followup_questions, ensure_ascii=False)
            await QuestionHistoryService.update_question_history(
                history_id=history_id,
                followup_questions=followup_questions_json
            )
        except Exception as e:
            print(f"保存后续问题失败: {str(e)}")

        return {
            "type": "question_list",
            "id": history_id,
            "questions": followup_questions,
            "header": "Here are some followup questions you can ask:"
        }
    except Exception as e:
        return JSONResponse(content={"type": "error", "error": f"生成后续问题失败: {str(e)}"})

@router.get("/load_question",
           summary="加载问题",
           description="加载指定的问题历史记录")
async def load_question(request: Request, history_id: str = Query(..., description="问题历史记录ID")):
    """加载问题历史数据（重定向到load_question_from_db）"""
    return await load_question_from_db(history_id)

@router.get("/load_question_from_db/{history_id}",
           summary="从数据库加载问题",
           description="从数据库中加载指定的问题历史记录详情")
async def load_question_from_db(history_id: str):
    """从数据库加载完整的问题历史数据"""
    try:
        question_data = await QuestionHistoryService.get_question_by_id(history_id)
        if not question_data:
            return JSONResponse(
                status_code=404,
                content={"type": "error", "error": "问题历史记录不存在"}
            )
        
        # 解析JSON数据
        response_data = {
            "type": "question_cache_db",
            "id": question_data["id"],
            "question": question_data["question"],
            "sql": question_data["sql"],
            "execution_status": question_data["execution_status"],
            "results_count": question_data["results_count"]
        }
        
        # 解析查询结果
        if question_data["query_results"]:
            try:
                response_data["df"] = json.loads(question_data["query_results"])
            except:
                response_data["df"] = None
        
        # 解析可视化数据
        if question_data["plotly_figure"]:
            try:
                response_data["fig"] = question_data["plotly_figure"]
            except:
                response_data["fig"] = None
        
        # 解析后续问题
        if question_data["followup_questions"]:
            try:
                response_data["followup_questions"] = json.loads(question_data["followup_questions"])
            except:
                response_data["followup_questions"] = []
        
        response_data["created_at"] = question_data["created_at"]
        response_data["updated_at"] = question_data["updated_at"]
        
        return response_data
        
    except Exception as e:
        return JSONResponse(
            content={"type": "error", "error": f"加载问题历史失败: {str(e)}"}
        )

@router.get("/get_question_history",
           summary="获取问题历史",
           description="获取问题历史记录列表，支持分页和会话过滤")
async def get_question_history(
    limit: int = Query(50, description="返回数量限制"), 
    session_id: Optional[str] = Query(None, description="会话ID过滤")
):
    try:
        # 从数据库获取持久化的问题历史
        db_questions = await QuestionHistoryService.get_question_history(
            limit=limit, 
            session_id=session_id
        )
        
        return {
            "type": "question_history", 
            "questions": db_questions,
            "source": "database"
        }
        
    except Exception as e:
        return JSONResponse(content={"type": "error", "error": f"获取问题历史失败: {str(e)}"})

@router.get("/auto_train_by_information_schema",
           summary="自动训练",
           description="基于数据库信息模式自动生成训练数据")
async def dbtrain():
    yq = get_yq_instance()
    await yq.create_index()
    return {"result":"success"}

@router.get("/get_question_detail/{history_id}",
           summary="获取问题详情",
           description="获取指定问题历史记录的详细信息")
async def get_question_detail(history_id: str):
    """根据ID获取问题历史详情"""
    try:
        question_detail = await QuestionHistoryService.get_question_by_id(history_id)
        if question_detail:
            return {
                "type": "question_detail",
                "data": question_detail
            }
        else:
            return JSONResponse(
                status_code=404, 
                content={"type": "error", "error": "问题历史记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            content={"type": "error", "error": f"获取问题详情失败: {str(e)}"}
        )

@router.delete("/delete_question_history/{history_id}",
              summary="删除问题历史",
              description="删除指定的问题历史记录")
async def delete_question_history(history_id: str):
    """删除问题历史记录"""
    try:
        success = await QuestionHistoryService.delete_question_history(history_id)
        if success:
            return {"type": "success", "message": "问题历史记录已删除"}
        else:
            return JSONResponse(
                status_code=404,
                content={"type": "error", "error": "问题历史记录不存在"}
            )
    except Exception as e:
        return JSONResponse(
            content={"type": "error", "error": f"删除问题历史失败: {str(e)}"}
        )

@router.post("/clear_question_history",
            summary="清空问题历史",
            description="清空指定会话的所有问题历史记录")
async def clear_question_history(data: Dict[str, Any] = Body(...)):
    """批量清理问题历史"""
    session_id = data.get('session_id')
    try:
        # TODO: 实现批量删除数据库中的问题历史
        # 这里可以添加批量删除逻辑，根据session_id或时间范围删除
        if session_id:
            # 如果指定了session_id，删除特定会话的历史
            # 暂时不实现，避免误删数据
            return {"type": "success", "message": f"会话 {session_id} 的问题历史清理功能待实现"}
        else:
            # 清理所有历史（危险操作，暂时不实现）
            return {"type": "success", "message": "全量清理功能待实现，请使用具体的删除接口"}
        
    except Exception as e:
        return JSONResponse(
            content={"type": "error", "error": f"清理问题历史失败: {str(e)}"}
        )
