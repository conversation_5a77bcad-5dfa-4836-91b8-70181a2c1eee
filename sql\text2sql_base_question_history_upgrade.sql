-- 为现有的问题历史表添加新字段
-- 如果表已存在，执行此脚本来升级表结构

-- 添加查询结果字段
ALTER TABLE `text2sql_base_question_history` 
ADD COLUMN `query_results` longtext DEFAULT NULL COMMENT '查询结果JSON数据' AFTER `execution_status`;

-- 添加查询结果行数字段
ALTER TABLE `text2sql_base_question_history` 
ADD COLUMN `results_count` int(11) DEFAULT NULL COMMENT '查询结果行数' AFTER `query_results`;

-- 添加Plotly图表数据字段
ALTER TABLE `text2sql_base_question_history` 
ADD COLUMN `plotly_figure` longtext DEFAULT NULL COMMENT 'Plotly图表JSON数据' AFTER `results_count`;

-- 添加后续问题字段
ALTER TABLE `text2sql_base_question_history` 
ADD COLUMN `followup_questions` text DEFAULT NULL COMMENT '后续问题JSON数据' AFTER `plotly_figure`; 