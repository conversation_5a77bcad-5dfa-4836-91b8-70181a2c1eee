import sqlalchemy
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine
from sqlalchemy import text
from typing import Dict, Any, List
from dao.models.Text2sqlSys import Text2sqlDatasource

from utils.encode_utils import SecurityUtil
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

async def get_db_engine(connection: Text2sqlDatasource) -> AsyncEngine:
    """
    Create an async SQLAlchemy engine for the given database connection.
    """
    try:
        encoded_password = connection.db_password
        actual_password = SecurityUtil.jiemi(encoded_password)
        
        #MySQL5.7+ 数据库
        if connection.db_type in ("4", "1"):
            conn_str = (
                f"mysql+aiomysql://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to async MySQL database: {connection.name}")
            return create_async_engine(
                conn_str,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        #postgresql 数据库
        elif connection.db_type == "6":
            conn_str = (
                f"postgresql+asyncpg://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to async PostgreSQL database: {connection.name}")
            return create_async_engine(
                conn_str,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        #Oracle 数据库
        elif connection.db_type == "2":
            conn_str = (
                f"oracle+oracledb://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to async Oracle database: {connection.name}")
            return create_async_engine(
                conn_str,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        #SQLServer 数据库
        elif connection.db_type == "3":
            conn_str = (
                f"mssql+aioodbc://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
                f"?driver=ODBC+Driver+17+for+SQL+Server"
            )
            logger.info(f"Connecting to async SQLServer database: {connection.name}")
            return create_async_engine(
                conn_str,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        #SQLite 数据库
        elif connection.db_type == "10":
            # For SQLite, use aiosqlite for async
            conn_str = f"sqlite+aiosqlite:///{connection.db_name}"
            logger.info(f"Connecting to async SQLite database: {connection.name}")
            return create_async_engine(
                conn_str,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        else:
            raise ValueError(f"Unsupported database type for async: {connection.db_type}")
    except Exception as e:
        logger.error(f"Error creating async database engine: {str(e)}")
        raise


def get_sync_db_engine(connection: Text2sqlDatasource):
    """
    Create a sync SQLAlchemy engine for the given database connection (for backward compatibility).
    """
    try:
        encoded_password = connection.db_password
        actual_password = SecurityUtil.jiemi(encoded_password)
        #MySQL5.7+ 数据库
        if connection.db_type in ("4", "1"):
            conn_str = (
                f"mysql+pymysql://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to MySQL database: {connection.name}")
            return create_engine(conn_str)
        #postgresql 数据库
        elif connection.db_type == "6":
            conn_str = (
                f"postgresql://{connection.db_username}:"
                f"{encoded_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to PostgreSQL database: {connection.name}")
            return create_engine(conn_str)
        #Oracle 数据库
        elif connection.db_type == "2":
            conn_str = (
                f"oracle+oracledb://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
            )
            logger.info(f"Connecting to Oracle database: {connection.name}")
            return create_engine(conn_str)
        #SQLServer 数据库
        elif connection.db_type == "3":
            conn_str = (
                f"mssql+pyodbc://{connection.db_username}:"
                f"{actual_password}@"
                f"{connection.db_host}:{connection.db_port}/{connection.db_name}"
                f"?driver=ODBC+Driver+17+for+SQL+Server"
            )
            logger.info(f"Connecting to SQLServer database: {connection.name}")
            return create_engine(conn_str)
        #SQLite 数据库
        elif connection.db_type == "10":
            # For SQLite, the database_name is treated as the file path
            conn_str = f"sqlite:///{connection.db_name}"
            logger.info(f"Connecting to SQLite database: {connection.name}")
            return create_engine(conn_str)

        else:
            raise ValueError(f"Unsupported database type: {connection.db_type}")
    except Exception as e:
        logger.error(f"Error creating database engine: {str(e)}")
        raise


async def test_db_connection(connection: Text2sqlDatasource) -> bool:
    """
    Test if an async database connection is valid.
    """
    try:
        logger.debug(f"Testing async connection to {connection.db_url}")
        engine = await get_db_engine(connection)
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()
            logger.success(f"Async connection test successful: {row}")
        await engine.dispose()
        return True
    except Exception as e:
        error_msg = f"Async connection test failed: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def test_sync_db_connection(connection: Text2sqlDatasource) -> bool:
    """
    Test if a sync database connection is valid (for backward compatibility).
    """
    try:
        logger.debug(f"Testing connection to {connection.db_url}")
        engine = get_sync_db_engine(connection)
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1"))
            logger.success(f"Connection test successful: {result.fetchone()}")
        return True
    except Exception as e:
        error_msg = f"Connection test failed: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


async def execute_query(connection: Text2sqlDatasource, query: str) -> List[Dict[str, Any]]:
    """
    Execute an async SQL query on the target database and return the results.
    """
    try:
        engine = await get_db_engine(connection)
        async with engine.begin() as conn:
            result = await conn.execute(text(query))
            columns = result.keys()
            rows = result.fetchall()
            data = [dict(zip(columns, row)) for row in rows]
        await engine.dispose()
        return data
    except Exception as e:
        raise Exception(f"Async query execution failed: {str(e)}")


def execute_sync_query(connection: Text2sqlDatasource, query: str) -> List[Dict[str, Any]]:
    """
    Execute a sync SQL query on the target database and return the results (for backward compatibility).
    """
    try:
        engine = get_sync_db_engine(connection)
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text(query))
            columns = result.keys()
            return [dict(zip(columns, row)) for row in result.fetchall()]
    except Exception as e:
        raise Exception(f"Query execution failed: {str(e)}")
