import asyncio
from typing import Dict, Any, List, Optional

from fastmcp import FastMC<PERSON>, Context
from pydantic import BaseModel

# 导入服务层
from app.services.database_statistics_service import DatabaseStatisticsService
from app.services.knowledge_statistics_service import StatisticsService as KnowledgeStatisticsService
from app.services.text2sql_analytics_service import Text2SQLAnalyticsService
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建统计MCP服务器实例
stats_server = FastMCP("Statistics Server")

# 创建服务实例
database_stats_service = DatabaseStatisticsService()
knowledge_stats_service = KnowledgeStatisticsService()
text2sql_analytics_service = Text2SQLAnalyticsService()

# ============================================================================
# 统计相关工具
# ============================================================================

@stats_server.tool
async def get_database_statistics_overview(ctx: Context) -> Dict[str, Any]:
    """
    获取数据库统计概览（对应FastAPI接口 /database/overview）
    
    Returns:
        数据库统计概览
    """
    try:
        await ctx.info("正在获取数据库统计概览")
        
        # 复用服务层的业务逻辑
        overview = await database_stats_service.get_database_overview_data()
        
        await ctx.info("数据库统计概览获取完成")
        return {
            "status": "success",
            "overview": overview
        }
        
    except Exception as e:
        error_msg = f"获取数据库统计概览时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_knowledge_statistics_overview(ctx: Context) -> Dict[str, Any]:
    """
    获取知识库统计概览（对应FastAPI接口 /knowledge/overview）
    
    Returns:
        知识库统计概览
    """
    try:
        await ctx.info("正在获取知识库统计概览")
        
        # 复用服务层的业务逻辑
        overview = await knowledge_stats_service.get_statistics_overview_data()
        
        await ctx.info("知识库统计概览获取完成")
        return {
            "status": "success",
            "overview": overview
        }
        
    except Exception as e:
        error_msg = f"获取知识库统计概览时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_text2sql_analytics_overview(ctx: Context) -> Dict[str, Any]:
    """
    获取Text2SQL分析概览（对应FastAPI接口 /text2sql/analytics）
    
    Returns:
        Text2SQL分析概览
    """
    try:
        await ctx.info("正在获取Text2SQL分析概览")
        
        # 复用服务层的业务逻辑
        overview = await text2sql_analytics_service.get_performance_overview()
        
        await ctx.info("Text2SQL分析概览获取完成")
        return {
            "status": "success",
            "overview": overview.dict() if hasattr(overview, 'dict') else str(overview)
        }
        
    except Exception as e:
        error_msg = f"获取Text2SQL分析概览时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_dashboard_overview_stats(ctx: Context) -> Dict[str, Any]:
    """
    获取仪表板概览统计（对应FastAPI接口 /dashboard/overview）
    
    Returns:
        仪表板概览数据
    """
    try:
        await ctx.info("正在获取仪表板概览统计")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_dashboard_overview_data()
        
        await ctx.info("仪表板概览统计获取完成")
        return {
            "status": "success",
            "overview": {
                "database_count": data["database_count"],
                "total_records": data["total_records"],
                "relationship_count": data["relationship_count"],
                "datasource_count": data["datasource_count"],
                "avg_fields_coverage": data["avg_fields_coverage"]
            }
        }
        
    except Exception as e:
        error_msg = f"获取仪表板概览统计时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_chart_distribution_data(ctx: Context) -> Dict[str, Any]:
    """
    获取图表分布数据（对应FastAPI接口 /charts/distribution）
    
    Returns:
        图表分布数据
    """
    try:
        await ctx.info("正在获取图表分布数据")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_chart_distribution_data()
        
        await ctx.info("图表分布数据获取完成")
        return {
            "status": "success",
            **data
        }
        
    except Exception as e:
        error_msg = f"获取图表分布数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_monthly_trend_data(ctx: Context) -> Dict[str, Any]:
    """
    获取月度趋势数据（对应FastAPI接口 /trends/monthly）
    
    Returns:
        月度趋势数据
    """
    try:
        await ctx.info("正在获取月度趋势数据")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_monthly_trend_chart_data()
        
        await ctx.info("月度趋势数据获取完成")
        return {
            "status": "success",
            **data
        }
        
    except Exception as e:
        error_msg = f"获取月度趋势数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_database_type_distribution(ctx: Context) -> Dict[str, Any]:
    """
    获取数据库类型分布（对应FastAPI接口 /database/types）
    
    Returns:
        数据库类型分布数据
    """
    try:
        await ctx.info("正在获取数据库类型分布")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_database_type_chart_data()
        
        await ctx.info("数据库类型分布获取完成")
        return {
            "status": "success",
            **data
        }
        
    except Exception as e:
        error_msg = f"获取数据库类型分布时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_field_analysis_stats(ctx: Context) -> Dict[str, Any]:
    """
    获取字段分析统计（对应FastAPI接口 /fields/analysis）
    
    Returns:
        字段分析统计数据
    """
    try:
        await ctx.info("正在获取字段分析统计")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_field_analysis_data()
        
        await ctx.info("字段分析统计获取完成")
        return {
            "status": "success",
            **data
        }
        
    except Exception as e:
        error_msg = f"获取字段分析统计时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_data_type_distribution(ctx: Context) -> Dict[str, Any]:
    """
    获取数据类型分布（对应FastAPI接口 /datatypes/distribution）
    
    Returns:
        数据类型分布数据
    """
    try:
        await ctx.info("正在获取数据类型分布")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_data_type_distribution_data()
        
        await ctx.info("数据类型分布获取完成")
        return {
            "status": "success",
            "distribution": data
        }
        
    except Exception as e:
        error_msg = f"获取数据类型分布时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_table_size_distribution(ctx: Context) -> Dict[str, Any]:
    """
    获取表大小分布（对应FastAPI接口 /tables/sizes）
    
    Returns:
        表大小分布数据
    """
    try:
        await ctx.info("正在获取表大小分布")
        
        # 复用服务层的业务逻辑
        data = await database_stats_service.get_table_size_distribution_data()
        
        await ctx.info("表大小分布获取完成")
        return {
            "status": "success",
            "distribution": data
        }
        
    except Exception as e:
        error_msg = f"获取表大小分布时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_comprehensive_database_stats(ctx: Context) -> Dict[str, Any]:
    """
    获取综合数据库统计（对应FastAPI接口 /database/comprehensive）
    
    Returns:
        综合数据库统计数据
    """
    try:
        await ctx.info("正在获取综合数据库统计")
        
        # 并行获取多个统计数据
        overview_task = database_stats_service.get_database_overview_data()
        distribution_task = database_stats_service.get_database_type_chart_data()
        field_analysis_task = database_stats_service.get_field_analysis_data()
        
        overview, distribution, field_analysis = await asyncio.gather(
            overview_task, distribution_task, field_analysis_task
        )
        
        await ctx.info("综合数据库统计获取完成")
        return {
            "status": "success",
            "comprehensive_stats": {
                "overview": overview,
                "type_distribution": distribution,
                "field_analysis": field_analysis
            }
        }
        
    except Exception as e:
        error_msg = f"获取综合数据库统计时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_trend_analysis(days: int = 30, ctx: Context = None) -> Dict[str, Any]:
    """
    获取趋势分析（对应FastAPI接口 /trends/analysis）
    
    Args:
        days: 分析天数
        
    Returns:
        趋势分析数据
    """
    try:
        await ctx.info(f"正在获取最近 {days} 天的趋势分析")
        
        # 并行获取数据库和知识库趋势
        db_trend_task = database_stats_service.get_database_trend_analysis_data(days)
        knowledge_trend_task = knowledge_stats_service.get_weekly_trend_data()
        
        db_trend, knowledge_trend = await asyncio.gather(
            db_trend_task, knowledge_trend_task
        )
        
        await ctx.info("趋势分析获取完成")
        return {
            "status": "success",
            "trend_analysis": {
                "database_trend": db_trend,
                "knowledge_trend": knowledge_trend,
                "period_days": days
            }
        }
        
    except Exception as e:
        error_msg = f"获取趋势分析时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_system_performance_metrics(ctx: Context) -> Dict[str, Any]:
    """
    获取系统性能指标
    
    Returns:
        系统性能指标
    """
    try:
        await ctx.info("正在获取系统性能指标")
        
        # 获取知识库性能指标
        performance_metrics = await knowledge_stats_service.get_performance_metrics()
        
        # 扩展系统级指标
        metrics = {
            "response_time": performance_metrics.get("avg_response_time_seconds", 0),
            "completion_rate": performance_metrics.get("completion_rate", 0),
            "total_threads": performance_metrics.get("total_threads", 0),
            "completed_threads": performance_metrics.get("completed_threads", 0),
            "period_days": performance_metrics.get("period_days", 30)
        }
        
        await ctx.info("系统性能指标获取完成")
        return {
            "status": "success",
            "metrics": metrics
        }
        
    except Exception as e:
        error_msg = f"获取系统性能指标时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@stats_server.tool
async def get_usage_analytics(start_date: Optional[str] = None, end_date: Optional[str] = None, ctx: Context = None) -> Dict[str, Any]:
    """
    获取使用情况分析
    
    Args:
        start_date: 开始日期（可选）
        end_date: 结束日期（可选）
        
    Returns:
        使用情况分析数据
    """
    try:
        await ctx.info(f"正在获取使用情况分析 (从 {start_date} 到 {end_date})")
        
        # 获取用户参与度和使用模式
        engagement_stats = await knowledge_stats_service.get_user_engagement_stats()
        usage_patterns = await knowledge_stats_service.get_usage_patterns()
        
        analytics = {
            "user_engagement": engagement_stats,
            "usage_patterns": usage_patterns,
            "start_date": start_date,
            "end_date": end_date
        }
        
        await ctx.info("使用情况分析获取完成")
        return {
            "status": "success",
            "analytics": analytics
        }
        
    except Exception as e:
        error_msg = f"获取使用情况分析时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

# ============================================================================
# 统计相关提示模板
# ============================================================================

@stats_server.prompt
def statistics_usage_prompt() -> str:
    """统计系统使用指南"""
    return """# 统计系统使用指南

本统计系统基于重构后的服务层架构，提供以下核心功能：

## 主要功能

### 1. 数据库统计
- `get_database_statistics_overview`: 获取数据库统计概览
- `get_dashboard_overview_stats`: 获取仪表板概览统计
- `get_database_type_distribution`: 获取数据库类型分布
- `get_field_analysis_stats`: 获取字段分析统计
- `get_comprehensive_database_stats`: 获取综合数据库统计

### 2. 知识库统计
- `get_knowledge_statistics_overview`: 获取知识库统计概览
- 包含对话统计、用户参与度、满意度指标等

### 3. Text2SQL分析
- `get_text2sql_analytics_overview`: 获取Text2SQL分析概览
- 查询性能、转换准确率等指标

### 4. 趋势分析
- `get_monthly_trend_data`: 获取月度趋势数据
- `get_trend_analysis`: 获取综合趋势分析
- `get_chart_distribution_data`: 获取图表分布数据

### 5. 性能监控
- `get_system_performance_metrics`: 获取系统性能指标
- `get_usage_analytics`: 获取使用情况分析

## 架构优势
- 复用服务层业务逻辑，避免重复实现
- 支持并行数据获取，提高统计效率
- 统一的错误处理和日志记录
- 更好的代码维护性和扩展性

## 数据维度
1. **时间维度**: 日、周、月趋势分析
2. **用户维度**: 活跃用户、新用户、参与度统计
3. **内容维度**: 数据库、知识库、查询统计
4. **性能维度**: 响应时间、完成率、满意度
"""

@stats_server.prompt
def data_analysis_best_practices_prompt() -> str:
    """数据分析最佳实践"""
    return """# 数据分析最佳实践

## 统计数据收集最佳实践
1. **数据质量保证**
   - 确保数据来源的准确性和一致性
   - 定期清理异常和重复数据
   - 建立数据验证和校准机制

2. **时间范围选择**
   - 根据业务需求选择合适的统计周期
   - 考虑季节性和周期性因素
   - 保持历史数据的连续性

## 指标设计原则
1. **关键性能指标(KPI)**
   - 用户活跃度：日活、月活、留存率
   - 系统性能：响应时间、错误率、吞吐量
   - 业务效果：转换率、满意度、使用率

2. **指标层次结构**
   - 战略指标：整体业务目标相关
   - 战术指标：具体功能模块效果
   - 操作指标：日常运营监控

## 可视化建议
1. **图表选择**
   - 趋势数据：折线图、面积图
   - 分布数据：饼图、柱状图
   - 对比数据：条形图、雷达图
   - 关系数据：散点图、热力图

2. **仪表板设计**
   - 突出重要指标和异常情况
   - 保持界面简洁和易读性
   - 支持钻取和筛选功能
   - 提供实时更新和历史对比

## 分析方法
1. **趋势分析**
   - 识别长期发展趋势
   - 发现周期性变化模式
   - 预测未来发展方向

2. **对比分析**
   - 同比、环比分析
   - 不同群体对比
   - 功能效果对比

3. **相关性分析**
   - 寻找影响因素
   - 验证假设和推论
   - 优化策略制定

## 数据驱动决策
1. **基于数据的洞察**
   - 深入分析数据背后的原因
   - 结合业务场景理解数据含义
   - 形成可执行的改进建议

2. **持续监控和优化**
   - 建立数据监控体系
   - 定期评估指标有效性
   - 根据业务变化调整分析维度
"""

@stats_server.prompt
def dashboard_design_prompt() -> str:
    """仪表板设计指南"""
    return """# 仪表板设计指南

## 设计原则

### 1. 用户导向设计
**了解用户需求**:
- 明确目标用户群体（管理者、运营人员、技术人员）
- 识别关键决策场景和信息需求
- 设计符合用户习惯的交互方式

**信息层次化**:
- 核心指标突出显示
- 支持从概览到详细的钻取
- 合理安排信息密度和布局

### 2. 视觉设计规范
**布局结构**:
- 采用网格系统保持一致性
- 重要信息放在视觉焦点位置
- 保留适当的留白空间

**色彩运用**:
- 使用一致的配色方案
- 区分数据类型和状态
- 考虑色盲用户的可访问性

### 3. 交互体验优化
**响应式设计**:
- 适配不同屏幕尺寸
- 优化移动端显示效果
- 保证加载速度和性能

**操作便捷性**:
- 提供筛选和搜索功能
- 支持数据导出和分享
- 实现个性化定制

## 关键模块设计

### 1. 概览仪表板
- **系统状态总览**: 健康度、性能指标
- **核心业务指标**: KPI趋势、完成情况
- **异常告警**: 突出显示需要关注的问题
- **快速入口**: 常用功能和详细报告链接

### 2. 详细分析页面
- **多维度分析**: 时间、地域、用户群体等
- **对比分析**: 历史对比、目标对比
- **深度洞察**: 原因分析、影响评估
- **预测模型**: 趋势预测、预警机制

### 3. 运营监控面板
- **实时监控**: 当前状态、实时数据流
- **性能指标**: 系统性能、业务性能
- **告警管理**: 告警历史、处理状态
- **资源使用**: 系统资源、成本分析

## 技术实现建议

### 1. 数据更新策略
- **实时数据**: WebSocket、SSE推送
- **定时更新**: 根据数据特性设置刷新频率
- **增量更新**: 只更新变化部分，提高效率
- **缓存策略**: 合理使用缓存减少数据库压力

### 2. 性能优化
- **数据聚合**: 预计算常用指标
- **分页加载**: 大数据集分批显示
- **懒加载**: 按需加载详细数据
- **CDN加速**: 静态资源优化

### 3. 可扩展性设计
- **模块化架构**: 便于功能扩展和维护
- **配置化**: 支持灵活的布局和指标配置
- **插件机制**: 支持第三方组件集成
- **API标准化**: 便于数据源集成和扩展

## 质量保证

### 1. 数据准确性
- 建立数据质量监控机制
- 实现数据校验和审计功能
- 提供数据来源和计算逻辑说明

### 2. 用户体验测试
- 进行可用性测试
- 收集用户反馈和建议
- 持续优化界面和交互

### 3. 安全性考虑
- 实现权限控制和数据脱敏
- 确保数据传输和存储安全
- 定期进行安全审计
""" 