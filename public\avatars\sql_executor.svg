<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <radialGradient id="bgSql" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.05"/>
    </radialGradient>
    
    <!-- 数据库渐变 -->
    <linearGradient id="dbGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669"/>
      <stop offset="100%" style="stop-color:#047857"/>
    </linearGradient>
    
    <!-- 执行按钮渐变 -->
    <radialGradient id="playGrad" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1e40af"/>
    </radialGradient>
    
    <!-- 结果框渐变 -->
    <linearGradient id="resultGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadowSql" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="3" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glowSql" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgSql)" stroke="#e2e8f0" stroke-width="2"/>
  
  <!-- 数据库图标 -->
  <g transform="translate(60, 40)" filter="url(#shadowSql)">
    <!-- 数据库顶部 -->
    <ellipse cx="40" cy="25" rx="35" ry="15" fill="url(#dbGrad)" opacity="0.9"/>
    <ellipse cx="40" cy="22" rx="35" ry="15" fill="url(#dbGrad)"/>
    
    <!-- 数据库主体 -->
    <rect x="5" y="22" width="70" height="50" fill="url(#dbGrad)" opacity="0.8"/>
    <ellipse cx="40" cy="72" rx="35" ry="15" fill="url(#dbGrad)"/>
    
    <!-- 数据库层次效果 -->
    <ellipse cx="40" cy="45" rx="32" ry="12" fill="none" stroke="#047857" stroke-width="1.5" opacity="0.6"/>
    <ellipse cx="40" cy="60" rx="32" ry="12" fill="none" stroke="#047857" stroke-width="1.5" opacity="0.6"/>
    
    <!-- 数据表示 -->
    <g fill="white" opacity="0.8">
      <rect x="15" y="35" width="20" height="2" rx="1"/>
      <rect x="15" y="40" width="25" height="2" rx="1"/>
      <rect x="45" y="35" width="18" height="2" rx="1"/>
      <rect x="45" y="40" width="22" height="2" rx="1"/>
    </g>
  </g>
  
  <!-- 执行按钮 -->
  <g transform="translate(45, 140)" filter="url(#shadowSql)">
    <circle cx="25" cy="25" r="22" fill="url(#playGrad)" filter="url(#glowSql)"/>
    <circle cx="25" cy="25" r="18" fill="url(#playGrad)" opacity="0.9"/>
    
    <!-- 播放三角形 -->
    <path d="M18 18 L18 32 L32 25 Z" fill="white" opacity="0.95"/>
    
    <!-- 按钮光泽 -->
    <ellipse cx="20" cy="20" rx="8" ry="6" fill="white" opacity="0.3"/>
  </g>
  
  <!-- 执行箭头流 -->
  <g transform="translate(90, 165)">
    <defs>
      <marker id="execArrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
        <polygon points="0 0, 12 4, 0 8" fill="#f59e0b"/>
      </marker>
    </defs>
    
    <!-- 动态箭头 -->
    <path d="M0 0 Q20 -15 40 -25 Q60 -35 80 -45" 
          stroke="#f59e0b" stroke-width="3" fill="none" 
          marker-end="url(#execArrow)" opacity="0.8">
      <animate attributeName="stroke-dasharray" values="0 100;50 50;0 100" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <!-- 数据流粒子 -->
    <circle cx="20" cy="-10" r="2" fill="#fbbf24" opacity="0.6">
      <animateMotion dur="2s" repeatCount="indefinite">
        <mpath href="#flowPath"/>
      </animateMotion>
    </circle>
    <path id="flowPath" d="M0 0 Q20 -15 40 -25 Q60 -35 80 -45" opacity="0"/>
  </g>
  
  <!-- 结果显示区域 -->
  <g transform="translate(150, 90)" filter="url(#shadowSql)">
    <!-- 结果框 -->
    <rect x="0" y="0" width="70" height="60" rx="8" fill="url(#resultGrad)" opacity="0.9"/>
    <rect x="3" y="3" width="64" height="54" rx="6" fill="white" opacity="0.95"/>
    
    <!-- 表头 -->
    <rect x="6" y="6" width="58" height="12" rx="3" fill="url(#resultGrad)" opacity="0.6"/>
    
    <!-- 数据行 -->
    <g fill="#059669" opacity="0.7">
      <rect x="8" y="22" width="15" height="2" rx="1"/>
      <rect x="26" y="22" width="20" height="2" rx="1"/>
      <rect x="49" y="22" width="12" height="2" rx="1"/>
      
      <rect x="8" y="28" width="18" height="2" rx="1"/>
      <rect x="26" y="28" width="15" height="2" rx="1"/>
      <rect x="49" y="28" width="10" height="2" rx="1"/>
      
      <rect x="8" y="34" width="12" height="2" rx="1"/>
      <rect x="26" y="34" width="22" height="2" rx="1"/>
      <rect x="49" y="34" width="8" height="2" rx="1"/>
    </g>
    
    <!-- 成功指示器 -->
    <circle cx="60" cy="50" r="4" fill="#10b981" opacity="0.8">
      <animate attributeName="r" values="4;6;4" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
     <!-- 性能指示器 -->
   <g transform="translate(180, 165)">
     <rect x="0" y="10" width="3" height="15" fill="#10b981" opacity="0.8"/>
     <rect x="5" y="5" width="3" height="20" fill="#fbbf24" opacity="0.7"/>
     <rect x="10" y="0" width="3" height="25" fill="#3b82f6" opacity="0.9"/>
   </g>
</svg> 