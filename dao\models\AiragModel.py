from typing import Optional

from sqlalchemy import DateTime, String
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
import datetime

class Base(DeclarativeBase):
    pass


class AiragModel(Base):
    __tablename__ = 'airag_model'

    id: Mapped[str] = mapped_column(String(36, 'utf8mb4_unicode_ci'), primary_key=True)
    create_by: Mapped[Optional[str]] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='创建日期')
    update_by: Mapped[Optional[str]] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新日期')
    sys_org_code: Mapped[Optional[str]] = mapped_column(String(64, 'utf8mb4_unicode_ci'), comment='所属部门')
    tenant_id: Mapped[Optional[str]] = mapped_column(String(32, 'utf8mb4_unicode_ci'), comment='租户id')
    name: Mapped[Optional[str]] = mapped_column(String(100, 'utf8mb4_unicode_ci'), comment='名称')
    provider: Mapped[Optional[str]] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='供应者')
    model_name: Mapped[Optional[str]] = mapped_column(String(100, 'utf8mb4_unicode_ci'), comment='模型名称')
    credential: Mapped[Optional[str]] = mapped_column(String(500, 'utf8mb4_unicode_ci'), comment='凭证信息')
    base_url: Mapped[Optional[str]] = mapped_column(String(500, 'utf8mb4_unicode_ci'), comment='API域名')
    model_type: Mapped[Optional[str]] = mapped_column(String(32, 'utf8mb4_unicode_ci'), comment='模型类型')
    model_params: Mapped[Optional[str]] = mapped_column(String(500, 'utf8mb4_unicode_ci'), comment='模型参数')
