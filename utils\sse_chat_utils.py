import os
import asyncio
import time
from typing import List, Optional, Dict
from llama_index.core import Settings
from llama_index.core.chat_engine.types import ChatMode
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

# ==================== 从rag.py分离的处理方法 ====================

async def process_knowledge_graph_chat_query(
        question: str,
        collection_name: str,
        session_id: str,
        files: Optional[List[str]] = None,
        get_rag_session_func=None,
        safe_send_rag_message_func=None,
        rag_chat_service=None,
        cl_utils=None,
        RAG=None,
        logger=None
):
    """处理智能对话查询任务（知识图谱模式或向量模式）"""
    session_info = get_rag_session_func(session_id)
    if not session_info:
        logger.error(f"智能对话会话 {session_id} 不存在")
        return

    try:
        # 获取当前会话的对话历史
        chat_history = session_info.get_history()

        # 添加当前问题到对话历史
        session_info.add_to_history("user", question)

        # 保存用户问题到数据库
        try:
            # 准备metadata，包括文件信息
            metadata = {
                "collection_name": collection_name,
                "has_files": bool(files and len(files) > 0),
                "file_count": len(files) if files else 0
            }

            # 如果有文件信息，解析并保存到metadata中
            if files and len(files) > 0:
                user_images = []
                user_files = []

                for file_path in files:
                    # # 从文件路径获取文件名
                    # file_name = file_path.split('/')[-1] if '/' in file_path else file_path
                    #
                    # # 判断文件类型
                    # ext = file_name.lower().split('.')[-1] if '.' in file_name else ''

                    file_name = os.path.basename(file_path)  # 获取原始文件名
                    ext = os.path.splitext(file_name.lower())[1]

                    if ext in ['jpg', 'jpeg', 'png']:
                        # 图片文件
                        user_images.append({
                            "url": file_path,
                            "name": file_name,
                            "alt": file_name
                        })
                    else:
                        # 其他文件
                        user_files.append({
                            "url": file_path,
                            "name": file_name,
                            "type": ext
                        })

                # 将文件信息添加到metadata
                if user_images:
                    metadata["user_images"] = user_images
                if user_files:
                    metadata["user_files"] = user_files
            await rag_chat_service.add_message(
                session_id=session_id,
                role="user",
                content=question,
                metadata=metadata
            )
            logger.info(f"用户问题已保存到数据库: session_id={session_id}, 文件数量={len(files) if files else 0}")
        except Exception as e:
            logger.error(f"保存用户问题到数据库失败: {str(e)}")
            # 数据库保存失败不影响功能继续

        # 发送开始处理消息
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🚀 开始智能对话查询...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 检查是否需要进入本地聊天模式
        # 1. 如果这次上传了文件，进入本地模式
        # 2. 如果会话已经是本地模式，继续使用本地模式
        if (files and len(files) > 0) or session_info.is_local_chat:
            # 本地聊天使用默认的嵌入模型
            selected_embed_model = cl_utils.get_embed_model_by_name()

            if files and len(files) > 0:
                # 这次上传了新文件
                await safe_send_rag_message_func(session_id, {
                    "type": "status",
                    "content": "📁 检测到上传文件，进入本地聊天模式...",
                    "sessionId": session_id,
                    "userId": session_info.user_id,
                    "timestamp": time.time()
                })
                # 标记为本地聊天模式
                session_info.is_local_chat = True
                # 处理新上传的文件
                await handle_local_file_chat(
                    files, question, session_id, selected_embed_model,
                    get_rag_session_func, safe_send_rag_message_func,
                    rag_chat_service, logger
                )
            else:
                # 继续使用本地模式（之前上传过文件）
                await safe_send_rag_message_func(session_id, {
                    "type": "status",
                    "content": "📂 继续使用本地文档聊天模式...",
                    "sessionId": session_id,
                    "userId": session_info.user_id,
                    "timestamp": time.time()
                })
                # 处理本地聊天（不上传新文件）
                await handle_local_existing_chat(
                    question, session_id, selected_embed_model,
                    get_rag_session_func, safe_send_rag_message_func,
                    rag_chat_service, logger
                )
            return

        # 检查配置
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🔍 正在检查配置...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 获取嵌入模型
        use_kg, model_name = await cl_utils.check_knowledge_use_kg(collection_name)
        selected_embed_model = cl_utils.get_embed_model_by_name(model_name)

        # 根据use_kg判断使用知识图谱还是普通向量搜索
        if use_kg:
            # 使用知识图谱加载索引
            prompt, source_texts, source_images = await RAG.load_index_with_graph(
                collection_name=collection_name,
                query=question,
                embed_model=selected_embed_model
            )

            # 发送检索结果
            await safe_send_rag_message_func(session_id, {
                "type": "sources_retrieved",
                "content": f"📚 检索到 {len(source_texts)} 个文本来源和 {len(source_images)} 个图像来源",
                "source_count": len(source_texts) + len(source_images),
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })

            # 生成回答
            await safe_send_rag_message_func(session_id, {
                "type": "status",
                "content": "🤖 正在生成回答...",
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })

            # 使用LLM生成回答
            llm = Settings.llm
            response = await llm.astream_complete(prompt)

            # 流式输出回答
            full_response = ""
            sent_length = 0  # 已发送的内容长度
            token_count = 0
            
            async for token in response:
                if token.delta != "Empty Response":
                    token_count += 1
                    token_length = len(token.delta)
                    
                    # 如果单个token太长，进行分割
                    if token_length > 20:
                        logger.warning(f"检测到大块token (长度={token_length})，进行分割处理")
                        words = token.delta.split()
                        current_chunk = ""
                        
                        for word in words:
                            if len(current_chunk + " " + word) > 15:
                                if current_chunk:
                                    full_response += current_chunk
                                    await safe_send_rag_message_func(session_id, {
                                        "type": "token",
                                        "content": current_chunk,
                                        "sessionId": session_id,
                                        "userId": session_info.user_id,
                                        "timestamp": time.time()
                                    })
                                    await asyncio.sleep(0.05)
                                    current_chunk = ""
                            current_chunk += (" " if current_chunk else "") + word
                        
                        # 发送剩余部分
                        if current_chunk:
                            full_response += current_chunk
                            await safe_send_rag_message_func(session_id, {
                                "type": "token",
                                "content": current_chunk,
                                "sessionId": session_id,
                                "userId": session_info.user_id,
                                "timestamp": time.time()
                            })
                            await asyncio.sleep(0.05)
                    else:
                        # 正常大小的token，直接发送
                        full_response += token.delta
                        await safe_send_rag_message_func(session_id, {
                            "type": "token",
                            "content": token.delta,
                            "sessionId": session_id,
                            "userId": session_info.user_id,
                            "timestamp": time.time()
                        })
                        # 增加延迟以确保流式效果
                        await asyncio.sleep(0.02)

                    # 检查是否被取消
                    if asyncio.current_task().cancelled():
                        logger.info(f"知识图谱流式输出被取消，已处理{token_count}个token")
                        return

            # 在知识图谱模式下，流式输出完成后添加延迟
            await asyncio.sleep(0.1)

            # 发送数据来源信息
            sources_info = []
            for idx, source_item in enumerate(source_texts):
                if source_item["type"] == "entity":
                    sources_info.append({
                        "type": "entity",
                        "content": source_item["text"],
                        "name": f"实体信息 {idx + 1}"
                    })
                elif source_item["type"] == "chunk":
                    sources_info.append({
                        "type": "chunk",
                        "content": source_item["text"],
                        "name": f"知识片段 {source_item['index']}"
                    })

            for idx, source_item in enumerate(source_images):
                sources_info.append({
                    "type": "image",
                    "url": source_item["url"],
                    "name": source_item["name"],
                    "content": source_item["content"]
                })

            # 发送数据来源
            await safe_send_rag_message_func(session_id, {
                "type": "sources",
                "content": "📖 数据来源信息",
                "sources": sources_info,
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })
            # 发送数据来源后添加延迟
            await asyncio.sleep(0.05)
        else:
            # 使用普通向量搜索
            await safe_send_rag_message_func(session_id, {
                "type": "status",
                "content": "🔍 正在检索向量数据库...",
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })

            # 系统提示词
            system_prompt = """你是一个专业的智能助手，能够根据提供的文档内容回答用户的问题。
请基于检索到的相关文档内容，为用户提供准确、有用的回答。
如果文档内容不足以回答问题，请诚实告知用户。"""

            # 创建聊天引擎
            index = await RAG.load_index(collection_name=collection_name, embed_model=selected_embed_model)
            chat_engine = index.as_chat_engine(
                chat_mode=ChatMode.CONTEXT,
                similarity_top_k=5,
                system_prompt=system_prompt,
                vector_store_query_mode="hybrid",
            )

            # 流式聊天
            streaming_response = await chat_engine.astream_chat(question)

            # 流式输出响应
            full_response = ""
            sources_info = []
            token_count = 0

            async for token in streaming_response.async_response_gen():
                if token != "Empty Response":
                    token_count += 1
                    token_length = len(token)

                    # 如果单个token太长，进行分割
                    if token_length > 20:
                        logger.warning(f"检测到大块token (长度={token_length})，进行分割处理")
                        words = token.split()
                        current_chunk = ""

                        for word in words:
                            if len(current_chunk + " " + word) > 15:
                                if current_chunk:
                                    full_response += current_chunk
                                    await safe_send_rag_message_func(session_id, {
                                        "type": "token",
                                        "content": current_chunk,
                                        "sessionId": session_id,
                                        "userId": session_info.user_id,
                                        "timestamp": time.time()
                                    })
                                    current_chunk = word
                                else:
                                    current_chunk = word
                            else:
                                current_chunk += " " + word if current_chunk else word

                        if current_chunk:
                            full_response += current_chunk
                            await safe_send_rag_message_func(session_id, {
                                "type": "token",
                                "content": current_chunk,
                                "sessionId": session_id,
                                "userId": session_info.user_id,
                                "timestamp": time.time()
                            })

                        # 分割处理后的延迟
                        await asyncio.sleep(0.05)
                    else:
                        # 正常token处理
                        full_response += token
                        await safe_send_rag_message_func(session_id, {
                            "type": "token",
                            "content": token,
                            "sessionId": session_id,
                            "userId": session_info.user_id,
                            "timestamp": time.time()
                        })

                    # 控制发送频率
                    if token_count % 5 == 0:
                        await asyncio.sleep(0.01)  # 每5个token暂停10ms

            # 在发送数据来源之前添加适当延迟，确保流式输出完成
            await asyncio.sleep(0.1)

            # 处理数据来源
            if hasattr(streaming_response, 'source_nodes'):
                for idx, node_with_score in enumerate(streaming_response.source_nodes, 1):
                    node = node_with_score.node

                    # 检查是否是图片
                    if "type" in node.metadata and node.metadata["type"] == "image":
                        sources_info.append({
                            "type": "image",
                            "url": node.metadata["image"],
                            "name": node.metadata.get("caption", node.metadata.get("file_name", "未知文件")),
                            "content": node.text,
                        })
                    else:
                        sources_info.append({
                            "type": "text",
                            "content": node.get_text(),
                            "name": f"文档片段 {idx}"
                        })

            # 发送数据来源
            if sources_info:
                await safe_send_rag_message_func(session_id, {
                    "type": "sources",
                    "content": "📖 数据来源信息",
                    "sources": sources_info,
                    "sessionId": session_id,
                    "userId": session_info.user_id,
                    "timestamp": time.time()
                })
                # 发送数据来源后添加延迟
                await asyncio.sleep(0.05)

        # 更新历史记录
        session_info.add_to_history("assistant", full_response)

        # 保存AI回答到数据库
        try:
            # 准备source_info数据
            final_source_info = None
            if 'sources_info' in locals() and sources_info:
                final_source_info = {
                    "sources": sources_info,
                    "source_count": len(sources_info),
                    "mode": "knowledge_graph" if use_kg else "vector_search"
                }

            await rag_chat_service.add_message(
                session_id=session_id,
                role="assistant",
                content=full_response,
                metadata={
                    "collection_name": collection_name,
                    "token_count": token_count if 'token_count' in locals() else 0,
                    "response_length": len(full_response),
                    "mode": "knowledge_graph" if use_kg else "vector_search"
                },
                source_info=final_source_info
            )
            logger.info(
                f"AI回答已保存到数据库: session_id={session_id}, 回答长度={len(full_response)}, 源信息数量={len(sources_info) if sources_info else 0}")
        except Exception as e:
            logger.error(f"保存AI回答到数据库失败: {str(e)}")
            # 数据库保存失败不影响功能继续

        # 在发送完成消息之前添加延迟，确保数据来源信息先显示
        await asyncio.sleep(0.1)

        # 记录流式输出完成统计
        response_length = len(full_response)
        logger.info(f"智能对话会话 {session_id} - 流式输出完成统计:")
        logger.info(f"  - 总响应长度: {response_length} 字符")
        if 'token_count' in locals():
            logger.info(f"  - 总Token数量: {token_count}")
            if token_count > 0:
                avg_token_length = response_length / token_count
                logger.info(f"  - 平均Token长度: {avg_token_length:.2f} 字符")

        # 发送完成消息
        await safe_send_rag_message_func(session_id, {
            "type": "complete",
            "content": "✅ 智能对话查询完成",
            "answer": full_response,
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 记录完成信息
        logger.info(f"智能对话会话 {session_id} 的查询处理完成")
        logger.info(f"流式输出统计 - 总响应长度: {len(full_response)} 字符")
        if 'token_count' in locals():
            logger.info(f"流式输出统计 - 总Token数量: {token_count}")

    except asyncio.CancelledError:
        logger.info(f"智能对话会话 {session_id} 的查询处理被取消")
        raise
    except Exception as e:
        logger.error(f"智能对话会话 {session_id} 的查询处理错误: {str(e)}")
        await safe_send_rag_message_func(session_id, {
            "type": "error",
            "content": f"❌ 智能对话查询出错: {str(e)}",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })


async def handle_local_existing_chat(
        question: str,
        session_id: str,
        selected_embed_model: HuggingFaceEmbedding,
        get_rag_session_func=None,
        safe_send_rag_message_func=None,
        rag_chat_service=None,
        logger=None
):
    """处理已存在的本地文档聊天（无新文件上传）"""
    from rag.base_rag import RAG

    session_info = get_rag_session_func(session_id)
    if not session_info:
        logger.error(f"会话 {session_id} 不存在")
        return

    try:
        # 创建用户专属的存储目录
        user_id = session_info.user_id
        persist_dir = f"./storage/{user_id}"

        # 发送加载本地索引状态
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🔄 正在加载个人文档索引...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 加载已存在的本地索引
        index = await RAG.load_index_local(persist_dir=persist_dir, embed_model=selected_embed_model)

        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "✅ 本地文档索引加载完成",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 创建聊天引擎
        chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT, similarity_top_k=3)

        # 生成回答
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🤖 正在生成回答...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 流式聊天
        streaming_response = await chat_engine.astream_chat(question)

        # 流式输出响应
        full_response = ""
        sources_info = []
        token_count = 0

        async for token in streaming_response.async_response_gen():
            if token != "Empty Response":
                token_count += 1
                token_length = len(token)

                # 如果单个token太长，进行分割
                if token_length > 20:
                    logger.warning(f"检测到大块token (长度={token_length})，进行分割处理")
                    words = token.split()
                    current_chunk = ""

                    for word in words:
                        if len(current_chunk + " " + word) > 15:
                            if current_chunk:
                                full_response += current_chunk
                                await safe_send_rag_message_func(session_id, {
                                    "type": "token",
                                    "content": current_chunk,
                                    "sessionId": session_id,
                                    "userId": session_info.user_id,
                                    "timestamp": time.time()
                                })
                                current_chunk = word
                            else:
                                current_chunk = word
                        else:
                            current_chunk += " " + word if current_chunk else word

                    if current_chunk:
                        full_response += current_chunk
                        await safe_send_rag_message_func(session_id, {
                            "type": "token",
                            "content": current_chunk,
                            "sessionId": session_id,
                            "userId": session_info.user_id,
                            "timestamp": time.time()
                        })

                    # 分割处理后的延迟
                    await asyncio.sleep(0.05)
                else:
                    # 正常token处理
                    full_response += token
                    await safe_send_rag_message_func(session_id, {
                        "type": "token",
                        "content": token,
                        "sessionId": session_id,
                        "userId": session_info.user_id,
                        "timestamp": time.time()
                    })

                # 控制发送频率
                if token_count % 5 == 0:
                    await asyncio.sleep(0.01)  # 每5个token暂停10ms

        # 处理数据来源
        if hasattr(streaming_response, 'source_nodes'):
            for idx, node_with_score in enumerate(streaming_response.source_nodes, 1):
                node = node_with_score.node

                # 检查是否是图片
                if "type" in node.metadata and node.metadata["type"] == "image":
                    sources_info.append({
                        "type": "image",
                        "url": node.metadata["image"],
                        "name": node.metadata.get("caption", node.metadata.get("file_name", "未知文件")),
                        "content": node.text,
                    })
                else:
                    sources_info.append({
                        "type": "text",
                        "content": node.get_text(),
                        "name": f"文档片段 {idx}"
                    })

        # 发送数据来源
        if sources_info:
            await asyncio.sleep(0.1)
            await safe_send_rag_message_func(session_id, {
                "type": "sources",
                "content": "📖 数据来源信息",
                "sources": sources_info,
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })
            await asyncio.sleep(0.05)

        # 更新历史记录
        session_info.add_to_history("assistant", full_response)

        # 保存AI回答到数据库
        try:
            # 准备source_info数据
            final_source_info = None
            if sources_info:
                final_source_info = {
                    "sources": sources_info,
                    "source_count": len(sources_info),
                    "mode": "local_existing_chat"
                }

            await rag_chat_service.add_message(
                session_id=session_id,
                role="assistant",
                content=full_response,
                metadata={
                    "mode": "local_existing_chat",
                    "token_count": token_count,
                    "response_length": len(full_response)
                },
                source_info=final_source_info
            )
            logger.info(f"本地文档聊天AI回答已保存到数据库: session_id={session_id}, 回答长度={len(full_response)}")
        except Exception as e:
            logger.error(f"保存本地文档聊天AI回答到数据库失败: {str(e)}")

        # 发送完成消息
        await asyncio.sleep(0.1)
        await safe_send_rag_message_func(session_id, {
            "type": "complete",
            "content": "✅ 本地文档聊天查询完成",
            "answer": full_response,
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        logger.info(f"本地文档聊天会话 {session_id} 的查询处理完成")
        logger.info(f"流式输出统计 - 总响应长度: {len(full_response)} 字符, 总Token数量: {token_count}")

    except asyncio.CancelledError:
        logger.info(f"本地文档聊天会话 {session_id} 的查询处理被取消")
        raise
    except Exception as e:
        logger.error(f"本地文档聊天会话 {session_id} 的查询处理错误: {str(e)}")
        await safe_send_rag_message_func(session_id, {
            "type": "error",
            "content": f"❌ 本地文档聊天查询出错: {str(e)}",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })


async def handle_local_file_chat(
        files: List[str],
        question: str,
        session_id: str,
        selected_embed_model: HuggingFaceEmbedding,
        get_rag_session_func=None,
        safe_send_rag_message_func=None,
        rag_chat_service=None,
        logger=None
):
    """处理本地文件聊天模式"""
    from rag.multimodal_rag import MultiModalRAG

    session_info = get_rag_session_func(session_id)
    if not session_info:
        logger.error(f"会话 {session_id} 不存在")
        return

    try:
        # 发送处理文件状态
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": f"📂 正在处理 {len(files)} 个文件...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 创建用户专属的存储目录
        user_id = session_info.user_id
        persist_dir = f"./storage/{user_id}"

        # 创建多模态RAG实例并建立索引
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🔧 正在建立文档索引...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        rag = MultiModalRAG(files=files, use_kg=False, embed_model=selected_embed_model)
        index = await rag.create_index_local(persist_dir=persist_dir)

        # 标记为本地聊天模式
        session_info.is_local_chat = True

        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "✅ 文档索引建立完成",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 创建聊天引擎
        chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT, similarity_top_k=3)

        # 生成回答
        await safe_send_rag_message_func(session_id, {
            "type": "status",
            "content": "🤖 正在生成回答...",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        # 流式聊天
        streaming_response = await chat_engine.astream_chat(question)

        # 流式输出响应
        full_response = ""
        sources_info = []
        token_count = 0

        async for token in streaming_response.async_response_gen():
            if token != "Empty Response":
                token_count += 1
                token_length = len(token)

                # 如果单个token太长，进行分割
                if token_length > 20:
                    logger.warning(f"检测到大块token (长度={token_length})，进行分割处理")
                    words = token.split()
                    current_chunk = ""

                    for word in words:
                        if len(current_chunk + " " + word) > 15:
                            if current_chunk:
                                full_response += current_chunk
                                await safe_send_rag_message_func(session_id, {
                                    "type": "token",
                                    "content": current_chunk,
                                    "sessionId": session_id,
                                    "userId": session_info.user_id,
                                    "timestamp": time.time()
                                })
                                current_chunk = word
                            else:
                                current_chunk = word
                        else:
                            current_chunk += " " + word if current_chunk else word

                    if current_chunk:
                        full_response += current_chunk
                        await safe_send_rag_message_func(session_id, {
                            "type": "token",
                            "content": current_chunk,
                            "sessionId": session_id,
                            "userId": session_info.user_id,
                            "timestamp": time.time()
                        })

                    # 分割处理后的延迟
                    await asyncio.sleep(0.05)
                else:
                    # 正常token处理
                    full_response += token
                    await safe_send_rag_message_func(session_id, {
                        "type": "token",
                        "content": token,
                        "sessionId": session_id,
                        "userId": session_info.user_id,
                        "timestamp": time.time()
                    })

                # 控制发送频率
                if token_count % 5 == 0:
                    await asyncio.sleep(0.01)  # 每5个token暂停10ms

        # 处理数据来源
        if hasattr(streaming_response, 'source_nodes'):
            for idx, node_with_score in enumerate(streaming_response.source_nodes, 1):
                node = node_with_score.node

                # 检查是否是图片
                if "type" in node.metadata and node.metadata["type"] == "image":
                    sources_info.append({
                        "type": "image",
                        "url": node.metadata["image"],
                        "name": node.metadata.get("caption", node.metadata.get("file_name", "未知文件")),
                        "content": node.text,
                    })
                else:
                    sources_info.append({
                        "type": "text",
                        "content": node.get_text(),
                        "name": f"文档片段 {idx}"
                    })

        # 发送数据来源
        if sources_info:
            await asyncio.sleep(0.1)
            await safe_send_rag_message_func(session_id, {
                "type": "sources",
                "content": "📖 数据来源信息",
                "sources": sources_info,
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            })
            await asyncio.sleep(0.05)

        # 更新历史记录
        session_info.add_to_history("assistant", full_response)

        # 保存AI回答到数据库
        try:
            # 准备source_info数据
            final_source_info = None
            if sources_info:
                final_source_info = {
                    "sources": sources_info,
                    "source_count": len(sources_info),
                    "mode": "local_file_chat"
                }

            await rag_chat_service.add_message(
                session_id=session_id,
                role="assistant",
                content=full_response,
                metadata={
                    "mode": "local_file_chat",
                    "file_count": len(files),
                    "token_count": token_count,
                    "response_length": len(full_response)
                },
                source_info=final_source_info
            )
            logger.info(f"本地文件聊天AI回答已保存到数据库: session_id={session_id}, 回答长度={len(full_response)}")
        except Exception as e:
            logger.error(f"保存本地文件聊天AI回答到数据库失败: {str(e)}")

        # 发送完成消息
        await asyncio.sleep(0.1)
        await safe_send_rag_message_func(session_id, {
            "type": "complete",
            "content": "✅ 本地文件聊天查询完成",
            "answer": full_response,
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })

        logger.info(f"本地文件聊天会话 {session_id} 的查询处理完成")
        logger.info(f"流式输出统计 - 总响应长度: {len(full_response)} 字符, 总Token数量: {token_count}")

    except asyncio.CancelledError:
        logger.info(f"本地文件聊天会话 {session_id} 的查询处理被取消")
        raise
    except Exception as e:
        logger.error(f"本地文件聊天会话 {session_id} 的查询处理错误: {str(e)}")
        await safe_send_rag_message_func(session_id, {
            "type": "error",
            "content": f"❌ 本地文件聊天查询出错: {str(e)}",
            "sessionId": session_id,
            "userId": session_info.user_id,
            "timestamp": time.time()
        })