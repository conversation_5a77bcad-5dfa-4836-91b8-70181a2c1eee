<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <radialGradient id="bgAnalyzer" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.05"/>
    </radialGradient>
    
    <!-- 大脑渐变 -->
    <linearGradient id="brainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6"/>
      <stop offset="100%" style="stop-color:#7c3aed"/>
    </linearGradient>
    
    <!-- 数据渐变 -->
    <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1e40af"/>
    </linearGradient>
    
    <!-- 分析渐变 -->
    <linearGradient id="analyzeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    
    <!-- 洞察渐变 -->
    <radialGradient id="insightGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f59e0b"/>
      <stop offset="100%" style="stop-color:#d97706"/>
    </radialGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadowAnalyzer" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="3" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glowAnalyzer" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgAnalyzer)" stroke="#e2e8f0" stroke-width="2"/>
  
  <!-- 中央大脑图标 -->
  <g transform="translate(70, 40)" filter="url(#shadowAnalyzer)">
    <!-- 大脑主体轮廓 -->
    <path d="M20 40 Q0 20 30 15 Q58 10 86 15 Q116 20 96 40 Q110 65 90 70 Q58 80 30 70 Q10 65 20 40 Z" 
          fill="url(#brainGrad)" opacity="0.9" filter="url(#glowAnalyzer)"/>
    
    <!-- 大脑纹理 -->
    <path d="M25 35 Q35 25 45 35 Q55 30 65 40" 
          stroke="url(#brainGrad)" stroke-width="2" fill="none" opacity="0.7"/>
    <path d="M30 50 Q40 45 50 50 Q60 48 70 55" 
          stroke="url(#brainGrad)" stroke-width="2" fill="none" opacity="0.7"/>
    <path d="M35 25 Q45 20 55 25" 
          stroke="url(#brainGrad)" stroke-width="1.5" fill="none" opacity="0.6"/>
    
    <!-- 神经网络连接点 -->
    <circle cx="35" cy="30" r="3" fill="url(#insightGrad)" opacity="0.8"/>
    <circle cx="55" cy="35" r="3" fill="url(#insightGrad)" opacity="0.8"/>
    <circle cx="75" cy="30" r="3" fill="url(#insightGrad)" opacity="0.8"/>
    <circle cx="45" cy="50" r="3" fill="url(#insightGrad)" opacity="0.8"/>
    <circle cx="65" cy="55" r="3" fill="url(#insightGrad)" opacity="0.8"/>
    
    <!-- 思维连接线 -->
    <g stroke="url(#insightGrad)" stroke-width="1.5" opacity="0.6" fill="none">
      <line x1="35" y1="30" x2="55" y2="35"/>
      <line x1="55" y1="35" x2="75" y2="30"/>
      <line x1="35" y1="30" x2="45" y2="50"/>
      <line x1="55" y1="35" x2="65" y2="55"/>
      <line x1="45" y1="50" x2="65" y2="55"/>
    </g>
  </g>
  
  <!-- 左侧数据图表 -->
  <g transform="translate(40, 120)" filter="url(#shadowAnalyzer)">
    <!-- 柱状图 -->
    <rect x="0" y="25" width="8" height="20" rx="4" fill="url(#dataGrad)" opacity="0.8"/>
    <rect x="12" y="15" width="8" height="30" rx="4" fill="url(#dataGrad)" opacity="0.9"/>
    <rect x="24" y="20" width="8" height="25" rx="4" fill="url(#dataGrad)"/>
    <rect x="36" y="10" width="8" height="35" rx="4" fill="url(#dataGrad)" opacity="0.8"/>
    <rect x="48" y="18" width="8" height="27" rx="4" fill="url(#dataGrad)" opacity="0.9"/>
    
    <!-- 数据点动画 -->
    <circle cx="4" cy="55" r="2" fill="url(#insightGrad)" opacity="0.7">
      <animate attributeName="r" values="2;4;2" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="16" cy="55" r="2" fill="url(#insightGrad)" opacity="0.8">
      <animate attributeName="r" values="2;4;2" dur="2.3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2.3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 右侧饼图 -->
  <g transform="translate(160, 110)" filter="url(#shadowAnalyzer)">
    <circle cx="25" cy="25" r="20" fill="none" stroke="url(#analyzeGrad)" stroke-width="12" 
            stroke-dasharray="40 38" transform="rotate(-90 25 25)" opacity="0.9"/>
    <circle cx="25" cy="25" r="20" fill="none" stroke="url(#dataGrad)" stroke-width="12" 
            stroke-dasharray="25 53" stroke-dashoffset="-40" transform="rotate(-90 25 25)" opacity="0.8"/>
    <circle cx="25" cy="25" r="20" fill="none" stroke="url(#insightGrad)" stroke-width="12" 
            stroke-dasharray="13 65" stroke-dashoffset="-65" transform="rotate(-90 25 25)" opacity="0.7"/>
    
    <!-- 中心指示器 -->
    <circle cx="25" cy="25" r="4" fill="url(#brainGrad)" opacity="0.8"/>
  </g>
  
  <!-- 底部趋势线 -->
  <g transform="translate(70, 180)" filter="url(#shadowAnalyzer)">
    <!-- 趋势线背景 -->
    <rect x="0" y="0" width="80" height="40" rx="8" fill="white" opacity="0.1"/>
    
    <!-- 趋势曲线 -->
    <path d="M5 30 Q15 20 25 25 Q35 15 45 18 Q55 12 65 15 Q75 8 85 10" 
          stroke="url(#analyzeGrad)" stroke-width="3" fill="none" opacity="0.9" filter="url(#glowAnalyzer)"/>
    
    <!-- 趋势点 -->
    <circle cx="15" cy="22" r="3" fill="url(#analyzeGrad)" opacity="0.8"/>
    <circle cx="35" cy="15" r="3" fill="url(#analyzeGrad)" opacity="0.9"/>
    <circle cx="55" cy="12" r="3" fill="url(#analyzeGrad)"/>
    <circle cx="75" cy="8" r="3" fill="url(#analyzeGrad)" opacity="0.8"/>
    
    <!-- 上升趋势指示器 -->
    <path d="M75 15 L80 10 L85 15" stroke="url(#analyzeGrad)" stroke-width="2" 
          fill="url(#analyzeGrad)" opacity="0.7"/>
  </g>
  
  <!-- 连接线动画 -->
  <g stroke="url(#brainGrad)" stroke-width="2" opacity="0.4" fill="none">
    <path d="M128 90 Q120 110 90 130" stroke-dasharray="3,3">
      <animate attributeName="stroke-dashoffset" values="0;6;0" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M128 90 Q140 110 170 130" stroke-dasharray="3,3">
      <animate attributeName="stroke-dashoffset" values="0;6;0" dur="2.2s" repeatCount="indefinite"/>
    </path>
    <path d="M128 90 Q128 140 128 180" stroke-dasharray="3,3">
      <animate attributeName="stroke-dashoffset" values="0;6;0" dur="1.8s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 洞察光环 -->
  <g transform="translate(128, 60)">
    <circle cx="0" cy="0" r="8" fill="none" stroke="url(#insightGrad)" stroke-width="2" opacity="0.6">
      <animate attributeName="r" values="8;15;8" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="12" fill="none" stroke="url(#insightGrad)" stroke-width="1" opacity="0.4">
      <animate attributeName="r" values="12;20;12" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.05;0.4" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 智能粒子效果 -->
  <g>
    <circle cx="50" cy="80" r="1.5" fill="url(#insightGrad)" opacity="0.6">
      <animateMotion dur="4s" repeatCount="indefinite">
        <mpath href="#particlePath1"/>
      </animateMotion>
    </circle>
    <circle cx="200" cy="90" r="1" fill="url(#dataGrad)" opacity="0.7">
      <animateMotion dur="5s" repeatCount="indefinite">
        <mpath href="#particlePath2"/>
      </animateMotion>
    </circle>
    <path id="particlePath1" d="M50 80 Q128 60 200 90 Q128 120 50 80" opacity="0"/>
    <path id="particlePath2" d="M200 90 Q128 70 50 100 Q128 130 200 90" opacity="0"/>
  </g>
</svg> 