from pydantic import BaseModel
from typing import List, Dict, Optional, Set
import asyncio
import time
import uuid
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()


class RAGSessionInfo:
    """RAG会话信息"""

    def __init__(self, session_id: str, user_id: str):
        self.session_id = session_id
        self.user_id = user_id
        self.is_connected = True
        self.created_at = time.time()
        self.last_activity = time.time()
        self.current_query = None
        self.task = None
        self.event_queue = asyncio.Queue()
        self.chat_history = []  # 对话历史
        self.max_history_pairs = 5  # 最多保留5轮对话（10条消息）
        self.is_local_chat = False  # 是否为本地聊天模式

    def add_to_history(self, role: str, content: str):
        """添加对话记录到历史，自动限制长度"""
        self.chat_history.append({
            "role": role,
            "content": content
        })

        # 保持最近5轮对话（每轮包含用户问题和AI回答，共10条消息）
        max_messages = self.max_history_pairs * 2
        if len(self.chat_history) > max_messages:
            # 删除最旧的消息，保持在限制内
            self.chat_history = self.chat_history[-max_messages:]
            logger.info(f"会话 {self.session_id} 历史记录已截断，保留最近 {self.max_history_pairs} 轮对话")

        self.last_activity = time.time()

    def get_history(self) -> List[Dict[str, str]]:
        """获取对话历史"""
        return self.chat_history.copy()

    def clear_history(self):
        """清空对话历史"""
        self.chat_history.clear()
        self.last_activity = time.time()


class ChatHistoryQueryEntity(BaseModel):
    """历史对话查询请求实体"""
    session_id: str
    limit: Optional[int] = 50
    offset: Optional[int] = 0


class UserSessionsQueryEntity(BaseModel):
    """用户会话查询请求实体"""
    user_id: str
    limit: Optional[int] = 20
    offset: Optional[int] = 0


class SessionDeleteEntity(BaseModel):
    """会话删除请求实体"""
    session_id: str


class HistoryCleanupEntity(BaseModel):
    """历史数据清理请求实体"""
    user_id: Optional[str] = None
    days_before: Optional[int] = 30  # 删除30天前的历史记录 