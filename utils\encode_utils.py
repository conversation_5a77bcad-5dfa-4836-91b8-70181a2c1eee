"""
@Description: 密码加解密工具类
@author: Python版本实现
@date: 基于Java SecurityUtil转换
"""

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii
from typing import List, Dict, Any
import json
import datetime
import numpy as np
import pandas as pd
from decimal import Decimal
import uuid


class SecurityUtil:
    """AES加解密工具类，与Java hutool库保持一致"""
    
    # 加密key
    KEY = "YUQING202508!!!!"
    
    @staticmethod
    def _get_key():
        """
        获取密钥字节数组，与Java hutool库保持一致
        Java中直接使用 key.getBytes() 方法
        """
        # 直接使用UTF-8编码，不做额外处理，与Java的key.getBytes()保持一致
        key_bytes = SecurityUtil.KEY.encode('utf-8')
        return key_bytes
    
    @staticmethod
    def jiami(content):
        """
        AES加密，与Java hutool的SymmetricCrypto保持一致
        使用AES/ECB/PKCS5Padding模式（在Python中PKCS5和PKCS7是等价的）
        @param content: 要加密的字符串
        @return: 加密后的十六进制字符串
        """
        try:
            # 将字符串转换为字节
            content_bytes = content.encode('utf-8')
            
            # 创建AES加密器，使用ECB模式
            key = SecurityUtil._get_key()
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 对内容进行填充（PKCS7，等同于Java的PKCS5）
            padded_content = pad(content_bytes, AES.block_size)
            
            # 加密
            encrypted_bytes = cipher.encrypt(padded_content)
            
            # 转换为十六进制字符串（小写）
            encrypted_hex = binascii.hexlify(encrypted_bytes).decode('utf-8')
            
            return encrypted_hex
            
        except Exception as e:
            raise Exception(f"加密失败: {str(e)}")
    
    @staticmethod
    def jiemi(encrypted_hex_str):
        """
        AES解密，与Java hutool的SymmetricCrypto保持一致
        @param encrypted_hex_str: 加密后的十六进制字符串
        @return: 解密后的原始字符串
        """
        try:
            # 将十六进制字符串转换为字节
            encrypted_bytes = binascii.unhexlify(encrypted_hex_str)
            
            # 创建AES解密器，使用ECB模式
            key = SecurityUtil._get_key()
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 解密
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted_bytes = unpad(decrypted_padded, AES.block_size)
            
            # 转换为字符串
            decrypted_str = decrypted_bytes.decode('utf-8')
            
            return decrypted_str
            
        except Exception as e:
            raise Exception(f"解密失败: {str(e)}")


# 测试函数
def test_encryption():
    """测试加解密功能"""
    try:
        content = "qh2008"
        print(f"原始内容: {content}")
        
        # 加密
        encrypted = SecurityUtil.jiami(content)
        print(f"python加密结果: {encrypted}")
        
        # 解密
        decrypted = SecurityUtil.jiemi(encrypted)
        print(f"python解密结果: {decrypted}")
        
        # 验证
        if content == decrypted:
            print("✅ 加解密测试成功!")
        else:
            print("❌ 加解密测试失败!")
    except Exception as e:
        print(f"测试出错: {str(e)}")


if __name__ == "__main__":
    test_encryption()

class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，用于处理datetime、Decimal、UUID、Pydantic模型等对象"""
    
    def default(self, obj):
        # 处理Pydantic模型
        if hasattr(obj, 'model_dump'):  # Pydantic v2
            return obj.model_dump()
        elif hasattr(obj, 'dict'):  # Pydantic v1
            return obj.dict()
        elif isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif isinstance(obj, datetime.date):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(DateTimeEncoder, self).default(obj)

def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化，处理datetime等特殊对象
    
    Args:
        obj: 要序列化的对象
        **kwargs: 传递给json.dumps的其他参数
        
    Returns:
        str: JSON字符串
    """
    kwargs.setdefault('cls', DateTimeEncoder)
    kwargs.setdefault('ensure_ascii', False)
    return json.dumps(obj, **kwargs)

def safe_json_loads(json_str: str, **kwargs) -> Any:
    """安全的JSON反序列化
    
    Args:
        json_str: JSON字符串
        **kwargs: 传递给json.loads的其他参数
        
    Returns:
        Any: 反序列化后的对象
    """
    return json.loads(json_str, **kwargs)

def serialize_for_db(obj: Any) -> Any:
    """为数据库存储准备对象，处理不可序列化的类型
    
    Args:
        obj: 要序列化的对象
        
    Returns:
        Any: 可序列化的对象
    """
    # 处理Pydantic模型
    if hasattr(obj, 'model_dump'):  # Pydantic v2
        return obj.model_dump()
    elif hasattr(obj, 'dict'):  # Pydantic v1
        return obj.dict()
    elif isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, datetime.time):
        return obj.isoformat()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, uuid.UUID):
        return str(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: serialize_for_db(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_db(v) for v in obj]
    elif isinstance(obj, tuple):
        return tuple(serialize_for_db(v) for v in obj)
    else:
        return obj

def clean_dict_for_json(data) -> Any:
    """清理数据中不可JSON序列化的对象
    
    Args:
        data: 要清理的数据（可以是字典、列表或其他类型）
        
    Returns:
        Any: 清理后的数据
    """
    return serialize_for_db(data)
