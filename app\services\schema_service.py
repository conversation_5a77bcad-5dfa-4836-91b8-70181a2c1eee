from typing import List, Dict, Any, Tuple

from llama_index.core import StorageContext, VectorStoreIndex
from llama_index.core.schema import TextNode
from llama_index.vector_stores.milvus import MilvusVectorStore
from sqlalchemy import inspect

from app.services.db_service import get_db_engine, get_sync_db_engine
from config import settings
from utils.schema_utils import determine_relationship_type

from dao.models.Text2sqlSys import Text2sqlDatasource
from dao.text2sqlsys_utils import get_schematable_by_name, create_schematable, get_schemacolumn_by_name, \
    update_schemacolumn, create_schemacolumn, get_schemarelationship_by_column_ids, update_schemarelationship, \
    create_schemarelationship
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

async def discover_schema(connection: Text2sqlDatasource) -> List[Dict[str, Any]]:
    """
    Discover schema from a database connection.
    """
    try:
        logger.info(f"Discovering schema for {connection.name} ({connection.db_type} at {connection.db_host}:{connection.db_port}/{connection.db_name})")
        # 使用同步引擎进行schema检查，因为inspect函数不支持异步引擎
        engine = get_sync_db_engine(connection)
        inspector = inspect(engine)

        # Choose the appropriate discovery method based on database type
        if connection.db_type == "4":
            return discover_mysql_schema(inspector)
        elif connection.db_type == "6":
            return discover_postgresql_schema(inspector)
        elif connection.db_type == "10":
            return discover_sqlite_schema(inspector)
        else:
            # Default discovery method
            return discover_generic_schema(inspector)
    except Exception as e:
        error_msg = f"Schema discovery failed: {str(e)}"
        logger.error(error_msg)
        import traceback
        traceback.print_exc()
        raise Exception(error_msg)


def discover_generic_schema(inspector) -> List[Dict[str, Any]]:
    """
    Generic schema discovery that works with most database types.
    """
    schema_info = []

    # Get all tables and views
    tables = inspector.get_table_names()
    try:
        views = inspector.get_view_names()
        tables.extend(views)
    except Exception as view_error:
        print(f"Warning: Could not get views: {str(view_error)}")
        views = []

    print(f"Found {len(tables)} tables/views: {', '.join(tables)}")

    for table_name in tables:
        print(f"Processing table/view: {table_name}")
        table_info = {
            "table_name": table_name,
            "columns": [],
            "is_view": table_name in views,
            "unique_constraints": [],  # 添加唯一约束信息
            "indexes": []  # 添加索引信息
        }

        # Get columns for each table
        try:
            columns = inspector.get_columns(table_name)
            print(f"Found {len(columns)} columns in {table_name}")

            for column in columns:
                column_info = {
                    "column_name": column["name"],
                    "data_type": str(column["type"]),
                    "is_primary_key": False,
                    "is_foreign_key": False,
                    "is_nullable": column.get("nullable", True),
                    "is_unique": False  # 添加唯一标记
                }
                table_info["columns"].append(column_info)

            # Mark primary keys
            try:
                pk_constraint = inspector.get_pk_constraint(table_name)
                pks = pk_constraint["constrained_columns"] if pk_constraint else []
                print(f"Primary keys for {table_name}: {pks}")
                for pk in pks:
                    for column in table_info["columns"]:
                        if column["column_name"] == pk:
                            column["is_primary_key"] = True
            except Exception as pk_error:
                # Some databases might not support primary key inspection
                print(f"Warning: Could not get primary keys for {table_name}: {str(pk_error)}")
                # Try to identify primary keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name == 'id' or col_name.endswith('_id') or col_name == f"{table_name.lower()}_id":
                        if 'int' in column["data_type"].lower() or 'serial' in column["data_type"].lower():
                            print(f"Identified potential primary key by naming convention: {column['column_name']}")
                            column["is_primary_key"] = True

            # Mark foreign keys
            try:
                fks = inspector.get_foreign_keys(table_name)
                print(f"Foreign keys for {table_name}: {len(fks)}")
                for fk in fks:
                    print(f"  FK: {fk}")
                    # 处理复合外键
                    for i, constrained_column in enumerate(fk["constrained_columns"]):
                        for column in table_info["columns"]:
                            if column["column_name"] == constrained_column:
                                column["is_foreign_key"] = True
                                # 确保引用列索引有效
                                referred_column_idx = min(i, len(fk["referred_columns"]) - 1) if fk["referred_columns"] else 0
                                referred_column = fk["referred_columns"][referred_column_idx] if fk["referred_columns"] else "id"
                                column["references"] = {
                                    "table": fk["referred_table"],
                                    "column": referred_column,
                                    "constraint_name": fk.get("name", ""),  # 保存约束名称
                                    "is_part_of_composite_key": len(fk["constrained_columns"]) > 1  # 标记是否为复合键的一部分
                                }
            except Exception as fk_error:
                # Some databases might not support foreign key inspection
                print(f"Warning: Could not get foreign keys for {table_name}: {str(fk_error)}")
                # Try to identify foreign keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()

                    # 如果列名以_id结尾且不是主键，可能是外键
                    if col_name.endswith('_id') and not column["is_primary_key"]:
                        # 提取可能的表名
                        potential_table = col_name[:-3]  # 移除 '_id' 后缀

                        # 检查这个表是否存在
                        table_exists = potential_table in [t.lower() for t in tables]

                        # 如果表存在，标记为外键
                        if table_exists:
                            print(f"Identified potential foreign key by naming convention: {column['column_name']} -> {potential_table}")
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": next(t for t in tables if t.lower() == potential_table),
                                "column": "id",  # 假设主键是 'id'
                                "constraint_name": f"fk_{table_name}_{column['column_name']}_inferred",  # 生成一个约束名
                                "is_part_of_composite_key": False,  # 默认不是复合键的一部分
                                "is_inferred": True  # 标记为推断的外键
                            }

                    # 如果列名与其他表名相同，也可能是外键
                    elif not column["is_primary_key"] and not column["is_foreign_key"]:
                        for table_name_to_check in tables:
                            if col_name == table_name_to_check.lower() or col_name == f"{table_name_to_check.lower()}id":
                                print(f"Identified potential foreign key by table name match: {column['column_name']} -> {table_name_to_check}")
                                column["is_foreign_key"] = True
                                column["references"] = {
                                    "table": table_name_to_check,
                                    "column": "id",  # 假设主键是 'id'
                                    "constraint_name": f"fk_{table_name}_{column['column_name']}_inferred",  # 生成一个约束名
                                    "is_part_of_composite_key": False,  # 默认不是复合键的一部分
                                    "is_inferred": True  # 标记为推断的外键
                                }
                                break

            # 获取唯一约束
            try:
                unique_constraints = inspector.get_unique_constraints(table_name)
                print(f"Unique constraints for {table_name}: {len(unique_constraints)}")
                for uc in unique_constraints:
                    print(f"  UC: {uc}")
                    table_info["unique_constraints"].append(uc)

                    # 标记列为唯一
                    for column in table_info["columns"]:
                        if column["column_name"] in uc.get("column_names", []):
                            column["is_unique"] = True
            except Exception as uc_error:
                print(f"Warning: Could not get unique constraints for {table_name}: {str(uc_error)}")

            # 获取索引
            try:
                indexes = inspector.get_indexes(table_name)
                print(f"Indexes for {table_name}: {len(indexes)}")
                for idx in indexes:
                    print(f"  Index: {idx}")
                    table_info["indexes"].append(idx)

                    # 标记列为唯一（如果索引是唯一的）
                    if idx.get("unique", False):
                        for column in table_info["columns"]:
                            if column["column_name"] in idx.get("column_names", []):
                                column["is_unique"] = True
            except Exception as idx_error:
                print(f"Warning: Could not get indexes for {table_name}: {str(idx_error)}")

        except Exception as column_error:
            print(f"Warning: Could not process columns for {table_name}: {str(column_error)}")
            continue

        schema_info.append(table_info)

    print(f"Schema discovery completed successfully. Found {len(schema_info)} tables/views.")
    return schema_info


def discover_mysql_schema(inspector) -> List[Dict[str, Any]]:
    """
    MySQL-specific schema discovery.
    """
    logger.info("Using MySQL-specific schema discovery")
    schema_info = []

    # Get all tables and views
    tables = inspector.get_table_names()
    try:
        views = inspector.get_view_names()
        tables.extend(views)
    except Exception as view_error:
        logger.error(f"Warning: Could not get views: {str(view_error)}")
        views = []

    logger.info(f"Found {len(tables)} tables/views: {', '.join(tables)}")

    for table_name in tables:
        logger.info(f"Processing table/view: {table_name}")
        comment_dict = inspector.get_table_comment(table_name)
        comment = comment_dict.get("text","")
        if comment == "":
            comment = table_name
        table_info = {
            "table_name": table_name,
            "columns": [],
            "is_view": table_name in views,
            "comment":comment
        }

        # Get columns for each table
        try:
            columns = inspector.get_columns(table_name)
            logger.info(f"Found {len(columns)} columns in {table_name}")

            for column in columns:
                column_info = {
                    "column_name": column["name"],
                    "data_type": str(column["type"]),
                    "is_primary_key": False,
                    "is_foreign_key": False,
                    "is_nullable": column.get("nullable", True),
                    "comment": column.get("comment", "")
                }
                table_info["columns"].append(column_info)

            # Mark primary keys - MySQL has reliable PK detection
            try:
                pk_constraint = inspector.get_pk_constraint(table_name)
                pks = pk_constraint["constrained_columns"] if pk_constraint else []
                logger.info(f"Primary keys for {table_name}: {pks}")
                for pk in pks:
                    for column in table_info["columns"]:
                        if column["column_name"] == pk:
                            column["is_primary_key"] = True
            except Exception as pk_error:
                logger.error(f"Warning: Could not get primary keys for {table_name}: {str(pk_error)}")
                # Try to identify primary keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name == 'id' or col_name.endswith('_id') or col_name == f"{table_name.lower()}_id":
                        if 'int' in column["data_type"].lower():
                            logger.error(f"Identified potential primary key by naming convention: {column['column_name']}")
                            column["is_primary_key"] = True

            # Mark foreign keys - MySQL has reliable FK detection through INFORMATION_SCHEMA
            try:
                fks = inspector.get_foreign_keys(table_name)
                logger.info(f"Foreign keys for {table_name}: {len(fks)}")
                for fk in fks:
                    logger.info(f"  FK: {fk}")
                    for column in table_info["columns"]:
                        if column["column_name"] in fk["constrained_columns"]:
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": fk["referred_table"],
                                "column": fk["referred_columns"][0]
                            }
            except Exception as fk_error:
                logger.error(f"Warning: Could not get foreign keys for {table_name}: {str(fk_error)}")
                # Try to identify foreign keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name.endswith('_id') and not column["is_primary_key"]:
                        # Extract potential table name from column name
                        potential_table = col_name[:-3]  # Remove '_id' suffix
                        # Check if this table exists
                        if potential_table in [t.lower() for t in tables]:
                            logger.error(f"Identified potential foreign key by naming convention: {column['column_name']} -> {potential_table}")
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": next(t for t in tables if t.lower() == potential_table),
                                "column": "id"  # Assume the primary key is 'id'
                            }
        except Exception as column_error:
            logger.error(f"Warning: Could not process columns for {table_name}: {str(column_error)}")
            continue

        schema_info.append(table_info)

    return schema_info


def discover_postgresql_schema(inspector) -> List[Dict[str, Any]]:
    """
    PostgreSQL-specific schema discovery.
    """
    logger.info("Using PostgreSQL-specific schema discovery")
    schema_info = []

    # Get all tables and views
    tables = inspector.get_table_names()
    try:
        views = inspector.get_view_names()
        tables.extend(views)
    except Exception as view_error:
        logger.warning(f"Warning: Could not get views: {str(view_error)}")
        views = []

    logger.info(f"Found {len(tables)} tables/views: {', '.join(tables)}")

    for table_name in tables:
        logger.info(f"Processing table/view: {table_name}")
        table_info = {
            "table_name": table_name,
            "columns": [],
            "is_view": table_name in views
        }

        # Get columns for each table
        try:
            columns = inspector.get_columns(table_name)
            logger.info(f"Found {len(columns)} columns in {table_name}")

            for column in columns:
                column_info = {
                    "column_name": column["name"],
                    "data_type": str(column["type"]),
                    "is_primary_key": False,
                    "is_foreign_key": False,
                    "is_nullable": column.get("nullable", True)
                }
                table_info["columns"].append(column_info)

            # Mark primary keys - PostgreSQL has reliable PK detection
            try:
                pks = inspector.get_pk_constraint(table_name)["constrained_columns"]
                logger.info(f"Primary keys for {table_name}: {pks}")
                for pk in pks:
                    for column in table_info["columns"]:
                        if column["column_name"] == pk:
                            column["is_primary_key"] = True
            except Exception as pk_error:
                logger.warning(f"Warning: Could not get primary keys for {table_name}: {str(pk_error)}")
                # Try to identify primary keys by naming convention and data type
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name == 'id' or col_name.endswith('_id') or col_name == f"{table_name.lower()}_id":
                        if 'int' in column["data_type"].lower() or 'serial' in column["data_type"].lower():
                            logger.error(f"Identified potential primary key by naming convention: {column['column_name']}")
                            column["is_primary_key"] = True

            # Mark foreign keys - PostgreSQL has reliable FK detection
            try:
                fks = inspector.get_foreign_keys(table_name)
                logger.info(f"Foreign keys for {table_name}: {len(fks)}")
                for fk in fks:
                    logger.info(f"  FK: {fk}")
                    for column in table_info["columns"]:
                        if column["column_name"] in fk["constrained_columns"]:
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": fk["referred_table"],
                                "column": fk["referred_columns"][0]
                            }
            except Exception as fk_error:
                logger.error(f"Warning: Could not get foreign keys for {table_name}: {str(fk_error)}")
                # Try to identify foreign keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name.endswith('_id') and not column["is_primary_key"]:
                        # Extract potential table name from column name
                        potential_table = col_name[:-3]  # Remove '_id' suffix
                        # Check if this table exists
                        if potential_table in [t.lower() for t in tables]:
                            logger.error(f"Identified potential foreign key by naming convention: {column['column_name']} -> {potential_table}")
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": next(t for t in tables if t.lower() == potential_table),
                                "column": "id"  # Assume the primary key is 'id'
                            }
        except Exception as column_error:
            logger.error(f"Warning: Could not process columns for {table_name}: {str(column_error)}")
            continue

        schema_info.append(table_info)

    return schema_info


def discover_sqlite_schema(inspector) -> List[Dict[str, Any]]:
    """
    SQLite-specific schema discovery.
    """
    logger.info("Using SQLite-specific schema discovery")
    schema_info = []

    # Get all tables and views
    tables = inspector.get_table_names()
    try:
        views = inspector.get_view_names()
        tables.extend(views)
    except Exception as view_error:
        logger.error(f"Warning: Could not get views: {str(view_error)}")
        views = []

    logger.info(f"Found {len(tables)} tables/views: {', '.join(tables)}")

    for table_name in tables:
        logger.info(f"Processing table/view: {table_name}")
        table_info = {
            "table_name": table_name,
            "columns": [],
            "is_view": table_name in views
        }

        # Get columns for each table
        try:
            columns = inspector.get_columns(table_name)
            logger.info(f"Found {len(columns)} columns in {table_name}")

            for column in columns:
                column_info = {
                    "column_name": column["name"],
                    "data_type": str(column["type"]),
                    "is_primary_key": column.get("primary_key", False),  # SQLite provides primary_key info directly
                    "is_foreign_key": False,
                    "is_nullable": column.get("nullable", True)
                }
                table_info["columns"].append(column_info)

            # SQLite doesn't always expose primary key info through the column attributes
            # so we also check through the inspector
            try:
                pks = inspector.get_pk_constraint(table_name)["constrained_columns"]
                logger.info(f"Primary keys for {table_name}: {pks}")
                for pk in pks:
                    for column in table_info["columns"]:
                        if column["column_name"] == pk:
                            column["is_primary_key"] = True
            except Exception as pk_error:
                logger.error(f"Warning: Could not get primary keys for {table_name}: {str(pk_error)}")
                # Already tried to identify primary keys from column attributes

            # Mark foreign keys - SQLite has basic FK support
            try:
                fks = inspector.get_foreign_keys(table_name)
                logger.info(f"Foreign keys for {table_name}: {len(fks)}")
                for fk in fks:
                    logger.info(f"  FK: {fk}")
                    for column in table_info["columns"]:
                        if column["column_name"] in fk["constrained_columns"]:
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": fk["referred_table"],
                                "column": fk["referred_columns"][0]
                            }
            except Exception as fk_error:
                logger.error(f"Warning: Could not get foreign keys for {table_name}: {str(fk_error)}")
                # Try to identify foreign keys by naming convention
                for column in table_info["columns"]:
                    col_name = column["column_name"].lower()
                    if col_name.endswith('_id') and not column["is_primary_key"]:
                        # Extract potential table name from column name
                        potential_table = col_name[:-3]  # Remove '_id' suffix
                        # Check if this table exists
                        if potential_table in [t.lower() for t in tables]:
                            logger.error(f"Identified potential foreign key by naming convention: {column['column_name']} -> {potential_table}")
                            column["is_foreign_key"] = True
                            column["references"] = {
                                "table": next(t for t in tables if t.lower() == potential_table),
                                "column": "id"  # Assume the primary key is 'id'
                            }
        except Exception as column_error:
            logger.error(f"Warning: Could not process columns for {table_name}: {str(column_error)}")
            continue

        schema_info.append(table_info)

    return schema_info


async def save_discovered_schema(connection: Text2sqlDatasource, schema_info: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Save discovered schema to the database and detect relationships.
    Returns a tuple of (tables_data, relationships_data) for frontend display.
    """
    logger.info(f"Saving discovered schema for connection {connection.name}")

    # 创建数据库检查器，用于获取更详细的表结构信息
    # 使用同步引擎进行schema检查，因为inspect函数不支持异步引擎
    engine = get_sync_db_engine(connection)
    inspector = inspect(engine)

    # Track created tables and relationships
    tables_data = []
    relationships_data = []

    # Process each table
    for table_info in schema_info:
        table_name = table_info["table_name"]
        table_comment = table_info["comment"]
        logger.info(f"Processing table: {table_name}")

        # Create or update the table
        existing_table = await get_schematable_by_name(connection.id, table_name)

        if existing_table:
            logger.info(f"Table {table_name} already exists, updating...")
            table_obj = existing_table
        else:
            logger.info(f"Creating new table: {table_name}")
            table_obj = await create_schematable({
                "connection_id":connection.id,
                "table_name":table_name,
                "description":f"{table_comment}",
                "ui_metadata":{"position": {"x": 0, "y": 0},"is_on_canvas": True}
            })
        # Add to tables_data for frontend
        tables_data.append({
            "id": table_obj.id,
            "table_name": table_obj.table_name,
            "description": table_obj.description,
            "ui_metadata": table_obj.ui_metadata
        })

        # Process columns for this table
        # 对列进行排序：主键 > 外键 > 其他列
        sorted_columns = sorted(table_info["columns"], key=lambda col: (
            not col.get("is_primary_key", False),  # 主键排在前面（False < True）
            not col.get("is_foreign_key", False),  # 外键排在第二位
            col.get("column_name", "")  # 其他列按名称排序
        ))
        
        for column_info in sorted_columns:
            column_name = column_info["column_name"]
            column_comment = column_info["comment"]

            # Check if column already exists
            existing_column = await get_schemacolumn_by_name(table_obj.id,column_name)

            if existing_column:
                logger.info(f"Column {column_name} already exists, updating...")
                column_obj = await update_schemacolumn(existing_column.id, {
                    "data_type": column_info["data_type"],
                    "is_primary_key": column_info["is_primary_key"],
                    "is_foreign_key": column_info["is_foreign_key"],
                    "is_unique": column_info.get("is_unique", False)  # 添加唯一标记
                })
            else:
                logger.info(f"Creating new column: {column_name}")
                column_obj = await create_schemacolumn({
                    "table_id":table_obj.id,
                    "column_name":column_name,
                    "data_type":column_info["data_type"],
                    "description":f"{column_comment}",
                    "is_primary_key":column_info["is_primary_key"],
                    "is_foreign_key":column_info["is_foreign_key"],
                    "is_unique":column_info.get("is_unique", False)  # 添加唯一标记
                })

    # Process relationships after all tables and columns are created
    for table_info in schema_info:
        for column_info in table_info["columns"]:
            if column_info.get("is_foreign_key") and column_info.get("references"):
                source_table_name = table_info["table_name"]
                source_column_name = column_info["column_name"]
                target_table_name = column_info["references"]["table"]
                target_column_name = column_info["references"]["column"]

                logger.info(f"Processing relationship: {source_table_name}.{source_column_name} -> {target_table_name}.{target_column_name}")

                # Get source and target tables
                source_table = await get_schematable_by_name(connection.id,source_table_name)
                target_table = await get_schematable_by_name(connection.id,target_table_name)

                if not source_table or not target_table:
                    logger.warning(f"Warning: Could not find tables for relationship")
                    continue

                # Get source and target columns
                source_column = await get_schemacolumn_by_name(source_table.id,source_column_name)
                target_column = await get_schemacolumn_by_name(target_table.id, target_column_name)

                if not source_column or not target_column:
                    logger.warning(f"Warning: Could not find columns for relationship")
                    continue

                # Check if relationship already exists
                existing_rel = await get_schemarelationship_by_column_ids(source_column.id,target_column.id)

                # 使用优化后的关系类型判断逻辑
                logger.debug(f"\n[DEBUG] 分析关系: {source_table_name}.{source_column_name} -> {target_table_name}.{target_column_name}")
                logger.debug(f"[DEBUG] 源列是主键: {source_column.is_primary_key}")
                logger.debug(f"[DEBUG] 目标列是主键: {target_column.is_primary_key}")

                # 使用 schema_utils 中的函数确定关系类型
                try:
                    relationship_type = determine_relationship_type(
                        inspector=inspector,
                        source_table=source_table_name,
                        source_column=source_column_name,
                        target_table=target_table_name,
                        target_column=target_column_name,
                        schema_info=schema_info
                    )
                    logger.debug(f"[DEBUG] 确定的关系类型: {relationship_type}")
                except Exception as e:
                    logger.error(f"[WARNING] 确定关系类型时出错: {str(e)}")
                    # 回退到基本逻辑
                    relationship_type = "1-to-N"  # 默认为一对多



                logger.info(f"[DEBUG] 最终确定的关系类型: {relationship_type}")

                if existing_rel:
                    logger.info(f"Relationship already exists, updating...")
                    rel_obj = await update_schemarelationship(existing_rel.id, {
                        "relationship_type": relationship_type,
                        "description": f"{source_table_name}.{source_column_name} -> {target_table_name}.{target_column_name}"
                    })
                else:
                    logger.info(f"Creating new relationship")
                    rel_obj = await create_schemarelationship({
                        "connection_id":connection.id,
                        "source_table_id":source_table.id,
                        "source_column_id":source_column.id,
                        "target_table_id":target_table.id,
                        "target_column_id":target_column.id,
                        "relationship_type":relationship_type,
                        "description":f" {source_table_name}.{source_column_name} -> {target_table_name}.{target_column_name}"
                    })
                # Add to relationships_data for frontend
                relationships_data.append({
                    "id": rel_obj.id,
                    "source_table": source_table.table_name,
                    "source_table_id": source_table.id,
                    "source_column": source_column.column_name,
                    "source_column_id": source_column.id,
                    "target_table": target_table.table_name,
                    "target_table_id": target_table.id,
                    "target_column": target_column.column_name,
                    "target_column_id": target_column.id,
                    "relationship_type": rel_obj.relationship_type,
                    "description": rel_obj.description
                })

    # Sync to graph database
    # try:
    #     await sync_schema_to_graph_db(connection)
    # except Exception as e:
    #     logger.error(f"Warning: Failed to sync to graph database: {str(e)}")

    return tables_data, relationships_data


async def sync_schema_to_graph_db(connection: Text2sqlDatasource):
    """
    将某个数据源中的表以及表之间的关系同步到知识图谱中
    将表信息作为节点，表与表之间的关系作为边
    
    修改逻辑：每次调用时先清除对应连接下的所有图谱数据和嵌入向量，然后再做新增
    这样可以应付数据建模中表的增删改操作
    
    1. 清除指定connection_id的所有图谱数据
    2. 清除指定connection_id的所有向量嵌入数据
    3. 将表信息作为节点新增到知识图谱，节点采用Table作为标签，具有id，connection_id，name，description属性
    4. 将表与表之间的关系作为边插入知识图谱，边采用REFERENCES作为标签，具有connection_id,relationship_type，description属性
    5. 完成知识图谱的同步后再将表信息嵌入到向量数据库中,集合名称采用text2sql_doc,元数据携带connection.id
    """
    import json
    
    try:
        logger.info(f"开始同步数据源 '{connection.name}' 到知识图谱和向量数据库")
        
        # 导入知识图谱管理器
        from utils.kg_utils import KnowledgeGraphManager
        # 获取知识图谱管理器实例
        kg_manager = KnowledgeGraphManager.get_instance()
        
        # 获取Neo4j驱动
        driver = await kg_manager.neo4j_driver
        
        # 步骤1: 清除指定connection_id的所有图谱数据
        logger.info(f"正在清除连接 '{connection.id}' 的所有图谱数据...")
        with driver.session(database=kg_manager.neo4j_db) as session:
            # 使用DETACH DELETE一次性删除所有表节点及其关系
            delete_result = session.run(
                """
                MATCH (t:Table)
                WHERE t.connection_id = $connection_id
                DETACH DELETE t
                RETURN count(t) as deleted_nodes
                """,
                connection_id=connection.id
            )
            deleted_nodes = delete_result.single()["deleted_nodes"]
            logger.info(f"已删除 {deleted_nodes} 个表节点及其所有关系")
        
        # 步骤2: 清除指定connection_id的所有向量嵌入数据
        logger.info(f"正在清除连接 '{connection.id}' 的所有向量嵌入数据...")
        from rag.entity_vectorizer import EntityVectorizer
        
        # 创建向量化处理器
        vectorizer = EntityVectorizer()
        
        # 使用专门的方法清除text2sql_doc集合中对应connection_id的数据
        clear_success = await vectorizer.clear_text2sql_connection_data(connection.id)
        if not clear_success:
            logger.warning(f"清除连接 '{connection.id}' 的向量数据时出现错误，将继续执行后续步骤")
        
        # 步骤3: 获取当前数据源的所有表、列和关系信息
        logger.info("正在批量获取所有表、列和关系信息...")
        from dao.text2sqlsys_utils import get_schema_data_by_connection_id
        
        schema_data = await get_schema_data_by_connection_id(connection.id)
        
        tables = schema_data["tables"]
        relationships = schema_data["relationships"]
        table_id_to_obj = schema_data["table_id_to_obj"]
        column_id_to_obj = schema_data["column_id_to_obj"]
        table_id_to_columns = schema_data["table_id_to_columns"]
        
        logger.info(f"已获取 {len(tables)} 个表、{len(schema_data['columns'])} 个列和 {len(relationships)} 个关系的信息")
        
        # 步骤4: 新增表节点到知识图谱
        logger.info("开始新增表节点到知识图谱...")
        
        with driver.session(database=kg_manager.neo4j_db) as session:
            # 批量创建表节点
            table_params = []
            for table in tables:
                # 从缓存中获取表的列信息
                columns = table_id_to_columns.get(table.id, [])
                columns_info = []
                for column in columns:
                    columns_info.append({
                        "name": column.column_name,
                        "type": column.data_type,
                        "is_primary_key": column.is_primary_key,
                        "is_foreign_key": column.is_foreign_key,
                        "description": column.description or ""
                    })
                
                # 将columns_info转换为JSON字符串，避免Neo4j类型错误
                columns_info_json = json.dumps(columns_info, ensure_ascii=False)
                
                table_params.append({
                    "table_id": f"table_{connection.id}_{table.id}",
                    "connection_id": connection.id,
                    "name": table.table_name,
                    "description": table.description or "",
                    "columns_count": len(columns),
                    "columns_info": columns_info_json,
                    "table_type": "table"
                })
            
            # 使用CREATE批量创建表节点（因为已经清除了旧数据，所以使用CREATE而不是MERGE）
            if table_params:
                session.run(
                    """
                    UNWIND $tables AS table
                    CREATE (t:Table {
                        id: table.table_id,
                        connection_id: table.connection_id,
                        name: table.name,
                        description: table.description,
                        columns_count: table.columns_count,
                        columns_info: table.columns_info,
                        table_type: table.table_type,
                        created_at: datetime(),
                        updated_at: datetime()
                    })
                    """,
                    tables=table_params
                )
                logger.info(f"已新增 {len(table_params)} 个表节点到知识图谱")
        
        # 步骤5: 新增表关系到知识图谱
        logger.info("开始新增表关系到知识图谱...")
        with driver.session(database=kg_manager.neo4j_db) as session:
            # 批量创建关系
            relationship_params = []
            for rel in relationships:
                # 从缓存中获取源表和目标表信息
                source_table = table_id_to_obj.get(rel.source_table_id)
                target_table = table_id_to_obj.get(rel.target_table_id)
                source_column = column_id_to_obj.get(rel.source_column_id)
                target_column = column_id_to_obj.get(rel.target_column_id)
                
                if source_table and target_table and source_column and target_column:
                    relationship_params.append({
                        "source_table_id": f"table_{connection.id}_{source_table.id}",
                        "target_table_id": f"table_{connection.id}_{target_table.id}",
                        "connection_id": connection.id,
                        "relationship_type": rel.relationship_type or "REFERENCES",
                        "description": f"{source_table.table_name}.{source_column.column_name} -> {target_table.table_name}.{target_column.column_name}",
                        "source_table": source_table.table_name,
                        "source_column": source_column.column_name,
                        "target_table": target_table.table_name,
                        "target_column": target_column.column_name
                    })
                else:
                    logger.warning(f"跳过关系 {rel.id}：无法找到对应的表或列信息")
            
            # 使用CREATE批量创建关系（因为已经清除了旧数据，所以使用CREATE而不是MERGE）
            if relationship_params:
                session.run(
                    """
                    UNWIND $relationships AS rel
                    MATCH (source:Table {id: rel.source_table_id})
                    MATCH (target:Table {id: rel.target_table_id})
                    CREATE (source)-[r:REFERENCES {
                        connection_id: rel.connection_id,
                        relationship_type: rel.relationship_type,
                        description: rel.description,
                        source_table: rel.source_table,
                        source_column: rel.source_column,
                        target_table: rel.target_table,
                        target_column: rel.target_column,
                        created_at: datetime(),
                        updated_at: datetime()
                    }]->(target)
                    """,
                    relationships=relationship_params
                )
                logger.info(f"已新增 {len(relationship_params)} 个表关系到知识图谱")
        
        # 步骤6: 将表信息嵌入到向量数据库
        logger.info("开始新增表信息到向量数据库...")
        
        try:
            # 获取集合名称
            collection_name = f"{vectorizer.milvus_collection_prefix}text2sql_doc"
            
            # 重新创建或加载向量存储
            vector_store = MilvusVectorStore(
                uri=vectorizer.milvus_uri,
                collection_name=collection_name,
                dim=int(settings.configuration.embedding_model_dim_bge_large),
                overwrite=False
            )
            
            # 检查集合是否已存在
            from pymilvus import connections, Collection
            connections.connect(uri=vectorizer.milvus_uri)
            
            try:
                collection = Collection(collection_name)
                collection_exists = True
            except Exception:
                collection_exists = False
            
            # 获取嵌入模型
            embed_model = settings.local_bge_large_embed_model()
            
            if not collection_exists or Collection(collection_name).is_empty:
                # 如果集合不存在或为空，创建索引
                storage_context = StorageContext.from_defaults(vector_store=vector_store)
                # 创建一个初始化节点
                init_node = TextNode(
                    text="初始化text2sql文档索引",
                    metadata={"node_type": "text2sql_table", "connection_id": connection.id},
                    id_=f"init_text2sql_{connection.id}"
                )
                index = VectorStoreIndex([init_node], storage_context=storage_context, embed_model=embed_model, show_progress=True)
            else:
                # 如果集合已存在，直接加载索引
                storage_context = StorageContext.from_defaults(vector_store=vector_store)
                index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=embed_model)
            
            # 为每个表创建文本节点
            table_nodes = []
            for table in tables:
                # 从缓存中获取表的列信息
                columns = table_id_to_columns.get(table.id, [])
                
                # 构建表的详细描述
                table_text = f"数据库表: {table.table_name}\n"
                table_text += f"描述: {table.description or '无描述'}\n"
                table_text += f"数据源: {connection.name}\n"
                table_text += f"列数量: {len(columns)}\n\n"
                
                # 添加列信息
                table_text += "列信息:\n"
                for column in columns:
                    column_desc = f"- {column.column_name} ({column.data_type})"
                    if column.is_primary_key:
                        column_desc += " [主键]"
                    if column.is_foreign_key:
                        column_desc += " [外键]"
                    if column.description:
                        column_desc += f" - {column.description}"
                    table_text += column_desc + "\n"
                
                # 添加关系信息（优化：只查找与当前表相关的关系）
                table_relationships = [rel for rel in relationships if rel.source_table_id == table.id or rel.target_table_id == table.id]
                if table_relationships:
                    table_text += "\n关系信息:\n"
                    for rel in table_relationships[:5]:  # 限制显示前5个关系
                        # 从缓存中获取关系信息
                        source_table = table_id_to_obj.get(rel.source_table_id)
                        target_table = table_id_to_obj.get(rel.target_table_id)
                        source_column = column_id_to_obj.get(rel.source_column_id)
                        target_column = column_id_to_obj.get(rel.target_column_id)
                        
                        if source_table and target_table and source_column and target_column:
                            rel_desc = f"- {source_table.table_name}.{source_column.column_name} -> {target_table.table_name}.{target_column.column_name} ({rel.relationship_type})"
                            table_text += rel_desc + "\n"
                
                # 创建文本节点
                metadata = {
                    "table_id": table.id,
                    "table_name": table.table_name,
                    "connection_id": connection.id,
                    "connection_name": connection.name,
                    "node_type": "text2sql_table",
                    "columns_count": len(columns),
                    "relationships_count": len(table_relationships)
                }
                
                node = TextNode(
                    text=table_text,
                    metadata=metadata,
                    id_=f"text2sql_table_{connection.id}_{table.id}"
                )
                table_nodes.append(node)
            
            # 将节点添加到索引
            if table_nodes:
                index.insert_nodes(table_nodes)
                logger.info(f"已将 {len(table_nodes)} 个表信息嵌入到向量数据库 (集合: {collection_name})")
            
        except Exception as e:
            logger.error(f"向量数据库同步失败: {e}")
            import traceback
            traceback.print_exc()
        
        logger.success(f"成功将数据源 '{connection.name}' 的表结构同步到知识图谱和向量数据库")
        return True
        
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"Graph DB sync failed: {str(e)}\n{error_trace}")
        raise Exception(f"Graph DB sync failed: {str(e)}")
