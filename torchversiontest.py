# from docling.document_converter import DocumentConverter
#
# source = "documents/公司法.pdf"  # document per local path or URL
# converter = DocumentConverter()
# result = converter.convert(source)
# print(result.document.export_to_markdown())  # output: "## Docling Technical Report[...]"
import torch
print(torch.cuda.is_available())
print("CUDA Version:", torch.version.cuda)
print("device num:", torch.cuda.device_count())
print("device name:", torch.cuda.get_device_name(0))
print("device index:", torch.cuda.current_device())