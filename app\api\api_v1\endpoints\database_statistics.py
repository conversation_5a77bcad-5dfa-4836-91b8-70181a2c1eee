from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from dao.DatabaseEngine import DatabaseEngine
from dao.models.Text2sqlSys import Text2sqlDatasource, Text2sqlSchematable, Text2sqlSchemacolumn, Text2sqlSchemarelationship
# 导入缓存相关模块
from app.core.cache import cache_statistics, CacheConfig, invalidate_cache, clear_all_statistics_cache
# 导入数据库统计服务
from app.services.database_statistics_service import DatabaseStatisticsService

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)

# ==================== 依赖注入函数 ====================

def get_database_statistics_service() -> DatabaseStatisticsService:
    """
    获取数据库统计服务实例
    使用依赖注入模式，避免重复创建service实例
    """
    return DatabaseStatisticsService()

# ==================== 数据模型 ====================

class DatabaseOverviewStats(BaseModel):
    """数据库概览统计模型"""
    total_tables: int  # 数据库表统计
    total_fields: int  # 字段总数
    total_relationships: int  # 表关系
    total_datasources: int  # 数据源

class DatabaseDistributionStats(BaseModel):
    """数据库分布统计"""
    dimension: str  # 维度名称（如数据源名称、数据库类型等）
    count: int  # 数量
    percentage: float  # 占比

class FieldAnalysisStats(BaseModel):
    """字段分析统计"""
    avg_fields_per_table: float  # 平均每表字段数
    max_fields_table: str  # 字段最多的表名
    max_fields_count: int  # 最多字段数
    min_fields_table: str  # 字段最少的表名  
    min_fields_count: int  # 最少字段数
    primary_key_coverage: float  # 主键覆盖率
    foreign_key_coverage: float  # 外键覆盖率

class DataTypeDistribution(BaseModel):
    """数据类型分布"""
    data_type: str
    count: int
    percentage: float

class TableSizeDistribution(BaseModel):
    """表规模分布"""
    size_range: str  # 如 "1-10字段", "11-20字段" 等
    table_count: int
    percentage: float

class ChartData(BaseModel):
    """图表数据模型"""
    label: str  # 标签（如数据源名称、月份等）
    value: int  # 数值

class DashboardStats(BaseModel):
    """仪表板总览统计"""
    database_count: int  # 数据库表统计
    total_records: int   # 学段总数
    relationship_count: int  # 表关系
    datasource_count: int   # 数据源
    avg_fields_coverage: float  # 平均覆盖字段数

# ==================== 工具函数 ====================

async def get_db_session() -> AsyncSession:
    """获取异步数据库会话"""
    return await DatabaseEngine.get_session()

# ==================== 统计接口 ====================

@router.get("/dashboard-overview", response_model=DashboardStats)
@cache_statistics(cache_type="overview")
async def get_dashboard_overview(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取仪表板概览统计（对应前端图片中的卡片数据）
    """
    try:
        data = await service.get_dashboard_overview_data()
        
        return DashboardStats(
            database_count=data["database_count"],
            total_records=data["total_records"],
            relationship_count=data["relationship_count"],
            datasource_count=data["datasource_count"],
            avg_fields_coverage=data["avg_fields_coverage"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询仪表板概览失败: {str(e)}")

@router.get("/chart-data/distribution")
@cache_statistics(cache_type="chart")
async def get_chart_distribution_data(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取用于柱状图展示的分布数据（按关系类型分布统计关系数量）
    """
    try:
        data = await service.get_chart_distribution_data()
        
        chart_data = []
        for item in data["chart_data"]:
            chart_data.append(ChartData(
                label=item["label"],
                value=item["value"]
            ))

        return {"chart_data": chart_data}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询关系类型分布数据失败: {str(e)}")

@router.get("/chart-data/monthly-trend")
@cache_statistics(cache_type="trend")
async def get_monthly_trend_chart_data(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取月度趋势图表数据
    """
    try:
        data = await service.get_monthly_trend_chart_data()
        
        chart_data = []
        for item in data["monthly_chart_data"]:
            chart_data.append(ChartData(
                label=item["label"],
                value=item["value"]
            ))

        return {"monthly_chart_data": chart_data}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询月度趋势数据失败: {str(e)}")

@router.get("/chart-data/database-type-distribution")
@cache_statistics(cache_type="chart")
async def get_database_type_chart_data(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取数据库类型分布的饼图数据
    """
    try:
        data = await service.get_database_type_chart_data()
        
        chart_data = []
        total_tables = sum(item["value"] for item in data["chart_data"])
        
        for item in data["chart_data"]:
            percentage = (item["value"] / total_tables * 100) if total_tables > 0 else 0
            chart_data.append({
                "label": item["label"],
                "value": item["value"],
                "percentage": round(percentage, 1)
            })

        return {"pie_chart_data": chart_data}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询数据库类型分布失败: {str(e)}")

@router.get("/overview", response_model=DatabaseOverviewStats)
@cache_statistics(cache_type="overview")
async def get_database_overview(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取数据库概览统计
    """
    try:
        data = await service.get_database_overview_data()
        
        return DatabaseOverviewStats(
            total_tables=data["total_tables"],
            total_fields=data["total_fields"],
            total_relationships=data["total_relationships"],
            total_datasources=data["total_datasources"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询数据库概览失败: {str(e)}")

@router.get("/distribution/by-datasource")
@cache_statistics(cache_type="distribution")
async def get_distribution_by_datasource(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    按数据源分布统计表数量
    """
    try:
        data = await service.get_distribution_by_datasource_data()
        
        stats = []
        for item in data:
            stats.append(DatabaseDistributionStats(
                dimension=item["dimension"],
                count=item["count"],
                percentage=item["percentage"]
            ))

        return {"distribution_stats": stats}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询按数据源分布失败: {str(e)}")

@router.get("/distribution/by-database-type")  
@cache_statistics(cache_type="distribution")
async def get_distribution_by_database_type(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    按数据库类型分布统计
    """
    try:
        data = await service.get_distribution_by_database_type_data()
        
        stats = []
        for item in data:
            stats.append(DatabaseDistributionStats(
                dimension=item["dimension"],
                count=item["count"],
                percentage=item["percentage"]
            ))

        return {"type_distribution": stats}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询按数据库类型分布失败: {str(e)}")

@router.get("/distribution/by-relationship-type")
@cache_statistics(cache_type="distribution")
async def get_distribution_by_relationship_type(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    按关系类型分布统计关系数量
    """
    try:
        # 使用图表分布数据方法，因为它已经包含了关系类型的分布逻辑
        data = await service.get_chart_distribution_data()
        
        stats = []
        chart_data = data["chart_data"]
        total_relationships = sum(item["value"] for item in chart_data)
        
        for item in chart_data:
            percentage = (item["value"] / total_relationships * 100) if total_relationships > 0 else 0
            stats.append(DatabaseDistributionStats(
                dimension=item["label"],
                count=item["value"],
                percentage=round(percentage, 1)
            ))

        return {"relationship_type_distribution": stats}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询按关系类型分布失败: {str(e)}")

@router.get("/field-analysis", response_model=FieldAnalysisStats)
@cache_statistics(cache_type="detailed")
async def get_field_analysis(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取字段分析统计
    """
    try:
        data = await service.get_field_analysis_data()
        
        return FieldAnalysisStats(
            avg_fields_per_table=data["avg_fields_per_table"],
            max_fields_table=data["max_fields_table"],
            max_fields_count=data["max_fields_count"],
            min_fields_table=data["min_fields_table"],
            min_fields_count=data["min_fields_count"],
            primary_key_coverage=data["primary_key_coverage"],
            foreign_key_coverage=data["foreign_key_coverage"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询字段分析失败: {str(e)}")

@router.get("/data-type-distribution")
@cache_statistics(cache_type="distribution")
async def get_data_type_distribution(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取数据类型分布统计
    """
    try:
        data = await service.get_data_type_distribution_data()
        
        distributions = []
        for item in data:
            distributions.append(DataTypeDistribution(
                data_type=item["data_type"],
                count=item["count"],
                percentage=item["percentage"]
            ))

        return {"data_type_distribution": distributions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询数据类型分布失败: {str(e)}")

@router.get("/table-size-distribution")
@cache_statistics(cache_type="distribution")
async def get_table_size_distribution(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取表规模分布统计（按字段数量分组）
    """
    try:
        data = await service.get_table_size_distribution_data()
        
        distributions = []
        for item in data:
            distributions.append(TableSizeDistribution(
                size_range=item["size_range"],
                table_count=item["table_count"],
                percentage=item["percentage"]
            ))

        return {"table_size_distribution": distributions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询表规模分布失败: {str(e)}")

@router.get("/comprehensive-stats")
@cache_statistics(cache_type="detailed")
async def get_comprehensive_database_stats(service: DatabaseStatisticsService = Depends(get_database_statistics_service)):
    """
    获取综合数据库统计信息
    """
    try:
        # 获取各项统计数据
        overview = await service.get_database_overview_data()
        field_analysis = await service.get_field_analysis_data()
        
        # 获取分布数据
        distribution_by_datasource = await service.get_distribution_by_datasource_data()
        distribution_by_type = await service.get_distribution_by_database_type_data()
        chart_distribution = await service.get_chart_distribution_data()
        datatype_distribution = await service.get_data_type_distribution_data()
        table_size_distribution = await service.get_table_size_distribution_data()

        # 转换关系类型分布数据格式
        relationship_type_distribution = []
        chart_data = chart_distribution["chart_data"]
        total_relationships = sum(item["value"] for item in chart_data)
        
        for item in chart_data:
            percentage = (item["value"] / total_relationships * 100) if total_relationships > 0 else 0
            relationship_type_distribution.append({
                "dimension": item["label"],
                "count": item["value"],
                "percentage": round(percentage, 1)
            })

        return {
            "overview": overview,
            "field_analysis": field_analysis,
            "distributions": {
                "by_datasource": distribution_by_datasource,
                "by_database_type": distribution_by_type,
                "by_relationship_type": relationship_type_distribution,
                "data_types": datatype_distribution,
                "table_sizes": table_size_distribution
            },
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询综合统计失败: {str(e)}")

@router.get("/trend-analysis")
@cache_statistics(cache_type="trend", cache_key_params=['days'])
async def get_database_trend_analysis(
    days: int = Query(default=30, ge=7, le=365, description="分析天数"), 
    service: DatabaseStatisticsService = Depends(get_database_statistics_service)
):
    """
    获取数据库变化趋势分析
    """
    try:
        data = await service.get_database_trend_analysis_data(days)
        
        # 这里可以添加更详细的趋势分析逻辑
        # 目前使用简化版本
        return {
            "period_days": data["period_days"],
            "trends": {
                "new_tables": [],  # 简化处理
                "new_fields": [],
                "new_relationships": []
            },
            "summary": {
                "total_new_tables": 0,
                "total_new_fields": 0,
                "total_new_relationships": 0
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询趋势分析失败: {str(e)}")

# ==================== 异步数据库健康检查接口 ====================

@router.get("/health")
async def check_database_health():
    """
    检查异步数据库健康状态
    """
    try:
        async with await get_db_session() as session:
            # 简单的健康检查查询
            result = await session.execute(text("SELECT 1 as health_check"))
            health_status = result.fetchone()
            
            if health_status:
                return {
                    "code": 200,
                    "message": "异步数据库连接正常",
                    "data": {
                        "status": "healthy",
                        "timestamp": str(datetime.now()),
                        "connection_type": "async"
                    }
                }
            else:
                raise HTTPException(status_code=503, detail="异步数据库连接异常")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"异步数据库健康检查失败: {str(e)}")

# ==================== 缓存管理接口 ====================

@router.delete("/cache/clear")
async def clear_database_statistics_cache():
    """
    清除数据库统计相关的所有缓存
    """
    try:
        # 清除特定模式的缓存
        from app.core.cache import StatisticsCache
        cache = StatisticsCache()
        
        # 清除所有数据库统计相关的缓存
        cleared_count = cache.clear_pattern("get_dashboard_overview*")
        cleared_count += cache.clear_pattern("get_chart_*")
        cleared_count += cache.clear_pattern("get_database_overview*")
        cleared_count += cache.clear_pattern("get_distribution_*")
        cleared_count += cache.clear_pattern("get_field_analysis*")
        cleared_count += cache.clear_pattern("get_data_type_distribution*")
        cleared_count += cache.clear_pattern("get_table_size_distribution*")
        cleared_count += cache.clear_pattern("get_comprehensive_*")
        cleared_count += cache.clear_pattern("get_database_trend_*")
        
        return {
            "status": "success",
            "message": f"成功清除 {cleared_count} 个数据库统计缓存",
            "cleared_count": cleared_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.delete("/cache/clear/{endpoint}")
async def clear_specific_cache(endpoint: str):
    """
    清除特定接口的缓存
    
    Args:
        endpoint: 接口名称，如 'get_dashboard_overview'
    """
    try:
        invalidate_cache(endpoint)
        return {
            "status": "success",
            "message": f"成功清除接口 {endpoint} 的缓存"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/cache/status")
async def get_cache_status():
    """
    获取缓存状态信息
    """
    try:
        from app.core.cache import StatisticsCache
        cache = StatisticsCache()
        
        if not cache.redis_client.is_connected():
            return {
                "status": "disconnected",
                "message": "Redis连接不可用",
                "cache_enabled": False
            }
        
        # 获取缓存键信息
        keys = cache.redis_client.client.keys(f"{cache.cache_prefix}*")
        
        return {
            "status": "connected",
            "message": "缓存系统正常运行",
            "cache_enabled": True,
            "total_cached_keys": len(keys),
            "cache_prefix": cache.cache_prefix
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取缓存状态失败: {str(e)}",
            "cache_enabled": False
        } 