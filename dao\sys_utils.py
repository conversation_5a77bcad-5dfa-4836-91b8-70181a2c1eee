"""
异步版本的系统工具函数
提供异步的数据库操作方法
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from dao.DatabaseEngine import DatabaseEngine
from dao.models.SysTables import SysDict, SysDictItem
from utils.logger import get_logger

logger = get_logger()


async def get_async_session() -> AsyncSession:
    """获取异步数据库会话"""
    return await DatabaseEngine.get_session()


async def get_dict():
    """异步获取所有系统字典"""
    async with await get_async_session() as session:
        try:
            statement = select(SysDict)
            result = await session.execute(statement)
            results = result.scalars().all()
            
            for sysdict in results:
                print(sysdict)
            return results
        except Exception as e:
            logger.error(f"Error getting dict: {str(e)}")
            raise
        finally:
            await session.close()


async def get_dict_item_by_dict_code(dict_code: str):
    """异步根据字典编码获取字典项"""
    async with await get_async_session() as session:
        try:
            statement = select(SysDictItem, SysDict).where(
                SysDictItem.dict_id == SysDict.id,
                SysDict.dict_code == dict_code
            )
            result = await session.execute(statement)
            results = result.fetchall()
            
            for sysDictItem, sysDict in results:
                print(sysDictItem.item_text, "=", sysDictItem.item_value, "=========", sysDict.dict_name)
            return results
        except Exception as e:
            logger.error(f"Error getting dict item by dict code: {str(e)}")
            raise
        finally:
            await session.close()


if __name__ == "__main__":
    import asyncio
    
    async def main():
        # 测试异步函数
        # await get_dict()
        await get_dict_item_by_dict_code("msg_category")
    
    asyncio.run(main())