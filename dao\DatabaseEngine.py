import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlmodel import Session

from config import settings


class DatabaseEngine:
    """异步MySQL数据库引擎单例类"""
    _async_instance: AsyncEngine = None
    _sync_instance = None
    _session_factory = None
    
    @classmethod
    def get_async_engine(cls) -> AsyncEngine:
        """获取异步MySQL数据库引擎"""
        if cls._async_instance is None:
            connection_string = settings.configuration.yq_dbconnection
            
            # 将同步连接字符串转换为异步格式
            if connection_string.startswith("mysql+pymysql://"):
                async_connection_string = connection_string.replace("mysql+pymysql://", "mysql+aiomysql://", 1)
            elif connection_string.startswith("mysql://"):
                async_connection_string = connection_string.replace("mysql://", "mysql+aiomysql://", 1)
            else:
                # 如果不是标准MySQL格式，假设需要添加aiomysql驱动
                if "mysql" in connection_string and "+aiomysql" not in connection_string:
                    async_connection_string = connection_string.replace("mysql:", "mysql+aiomysql:", 1)
                else:
                    async_connection_string = connection_string
            
            cls._async_instance = create_async_engine(
                async_connection_string,
                echo=False,  # 生产环境建议设为False
                pool_size=10,         # 连接池大小
                max_overflow=20,      # 连接池溢出大小
                pool_pre_ping=True,   # 连接前预检，避免断开的连接
                pool_recycle=3600,    # 连接回收时间（1小时）
                pool_timeout=30,      # 获取连接的超时时间
                connect_args={
                    "charset": "utf8mb4",  # 支持emoji和特殊字符
                }
            )
        
        return cls._async_instance
    
    @classmethod
    def get_engine(cls):
        """获取异步引擎（向后兼容接口）"""
        return cls.get_async_engine()
    
    @classmethod
    def get_sync_engine(cls):
        """获取同步引擎（用于兼容旧代码）"""
        if cls._sync_instance is None:
            connection_string = settings.configuration.yq_dbconnection
            
            cls._sync_instance = create_engine(
                connection_string,
                echo=False,  # 开发环境开启SQL日志则设置为True
                pool_size=10,         # 连接池大小
                max_overflow=20,      # 连接池溢出大小
                pool_pre_ping=True,   # 连接前预检，避免断开的连接
                pool_recycle=3600,    # 连接回收时间（1小时）
                pool_timeout=30,      # 获取连接的超时时间
                connect_args={
                    "charset": "utf8mb4",  # 支持emoji和特殊字符
                }
            )
        return cls._sync_instance
    
    @classmethod
    def get_session_factory(cls):
        """获取异步会话工厂"""
        if cls._session_factory is None:
            engine = cls.get_async_engine()
            cls._session_factory = sessionmaker(
                bind=engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        return cls._session_factory
    
    @classmethod
    async def get_session(cls):
        """获取异步数据库会话"""
        session_factory = cls.get_session_factory()
        return session_factory()
    
    @classmethod
    def get_sync_session(cls):
        """获取同步数据库会话（用于兼容旧代码）"""
        engine = cls.get_sync_engine()
        return Session(engine)
    
    @classmethod
    async def close_engine(cls):
        """关闭异步数据库引擎"""
        if cls._async_instance:
            await cls._async_instance.dispose()
            cls._async_instance = None
            cls._session_factory = None
    
    @classmethod
    def close_sync_engine(cls):
        """关闭同步数据库引擎"""
        if cls._sync_instance:
            cls._sync_instance.dispose()
            cls._sync_instance = None


# 为了向后兼容，创建一个别名
AsyncDatabaseEngine = DatabaseEngine