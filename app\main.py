import os

# 在导入任何其他模块之前，设置环境变量禁用OpenTelemetry追踪
os.environ["OTEL_SDK_DISABLED"] = "true"
os.environ["OTEL_TRACES_EXPORTER"] = "none"
os.environ["OTEL_METRICS_EXPORTER"] = "none"
os.environ["OTEL_LOGS_EXPORTER"] = "none"

from fastapi_offline import FastAPIOffline
from fastapi.middleware.cors import CORSMiddleware
from chainlit.utils import mount_chainlit
from contextlib import asynccontextmanager
import asyncio

from app.api.api_v1 import api_router
from app.core.cache_warmup import run_cache_warmup, should_run_warmup
from utils.logger import get_logger
from utils.conversion_pool import shutdown_conversion_executor

# 获取logger实例
logger = get_logger()


@asynccontextmanager
async def lifespan(app: FastAPIOffline):
    """
    FastAPI应用生命周期管理器
    处理应用启动和关闭时的任务
    """
    # ========== 应用启动时的操作 ==========
    logger.info("🚀 应用启动中...")
    
    # 缓存预热
    if should_run_warmup():
        logger.info("📦 开始Redis缓存预热...")
        try:
            # 在后台运行缓存预热，不阻塞应用启动
            warmup_task = asyncio.create_task(run_cache_warmup())
            
            # 等待一小段时间或者让预热任务完成
            try:
                # 等待最多30秒，避免应用启动时间过长
                warmup_result = await asyncio.wait_for(warmup_task, timeout=30.0)
                logger.info(f"✅ 缓存预热完成: {warmup_result.get('status', 'unknown')}")
                
                if warmup_result.get('failed_count', 0) > 0:
                    logger.warning(f"⚠️  有 {warmup_result['failed_count']} 个预热任务失败")
                    
            except asyncio.TimeoutError:
                logger.warning("⏰ 缓存预热超时(30秒)，任务将在后台继续执行")
                # 不取消任务，让它在后台继续运行
                
        except Exception as e:
            logger.error(f"❌ 缓存预热失败: {e}")
            # 预热失败不应该影响应用启动
    else:
        logger.info("⏭️  缓存预热已跳过 (缓存未启用或Redis未连接)")
    
    logger.info("✅ 应用启动完成")
    
    # 应用运行期间...
    yield
    
    # ========== 应用关闭时的操作 ==========
    logger.info("🛑 应用关闭中...")
    
    # 这里可以添加清理操作，比如关闭连接池等
    try:
        # 清理Redis连接等资源
        logger.info("🧹 清理应用资源...")
        
        # 关闭LibreOffice转换线程池
        logger.info("🔧 关闭LibreOffice转换线程池...")
        shutdown_conversion_executor()
        
        # 如果有需要清理的资源，在这里添加
        
    except Exception as e:
        logger.error(f"❌ 清理资源时出错: {e}")
    
    logger.info("✅ 应用已关闭")


app = FastAPIOffline(
    title="宇擎智库中台系统API",
    description="基于FASTAPI提供快捷的智库管理系统搭建接口",
    version="1.0",
    lifespan=lifespan  # 添加生命周期管理器
)

# 设置CORS，允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.include_router(api_router, prefix="/api/v1")

# @app.get("/")
# async def root():
#     return {"message": "宇擎智能体API服务正常运行"}

mount_chainlit(app=app, target="ui.py", path="/")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=False)
    # print("启动服务器，WebSocket支持已启用...")
    # uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True, ws="websockets")