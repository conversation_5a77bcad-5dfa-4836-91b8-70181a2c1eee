from typing import List
from pydantic import BaseModel, Field

# 从各个模块导入需要的基础模型
from app.schemas.text2sql.datasource import Text2sqlDatasourceResponse
from app.schemas.text2sql.schematable import Text2sqlSchematableResponse, Text2sqlSchematableCreate
from app.schemas.text2sql.schemacolumn import Text2sqlSchemacolumnResponse, Text2sqlSchemacolumnCreate
from app.schemas.text2sql.schemarelationship import Text2sqlSchemarelationshipResponse
from app.schemas.text2sql.valuemapping import Text2sqlValuemappingResponse, Text2sqlValuemappingCreate


# =============== 复合响应模型 ===============

class Text2sqlSchematableWithColumns(Text2sqlSchematableResponse):
    """包含列信息的表响应模型"""
    columns: List[Text2sqlSchemacolumnResponse] = Field(default_factory=list, description="表中的列")


class Text2sqlSchemacolumnWithMappings(Text2sqlSchemacolumnResponse):
    """包含值映射的列响应模型"""
    value_mappings: List[Text2sqlValuemappingResponse] = Field(default_factory=list, description="列的值映射")


class Text2sqlDatasourceWithTables(Text2sqlDatasourceResponse):
    """包含表信息的数据源响应模型"""
    tables: List[Text2sqlSchematableResponse] = Field(default_factory=list, description="数据源中的表")


class Text2sqlSchemaFullResponse(Text2sqlDatasourceResponse):
    """完整的数据库schema响应模型"""
    tables: List[Text2sqlSchematableWithColumns] = Field(default_factory=list, description="数据源中的表及其列")
    relationships: List[Text2sqlSchemarelationshipResponse] = Field(default_factory=list, description="表之间的关系")


# =============== 批量操作模型 ===============

class BatchText2sqlSchematableCreate(BaseModel):
    """批量创建表的请求模型"""
    connection_id: str = Field(..., description="数据源连接ID")
    tables: List[Text2sqlSchematableCreate] = Field(..., description="要创建的表列表")


class BatchText2sqlSchemacolumnCreate(BaseModel):
    """批量创建列的请求模型"""
    table_id: int = Field(..., description="表ID")
    columns: List[Text2sqlSchemacolumnCreate] = Field(..., description="要创建的列列表")


class BatchText2sqlValuemappingCreate(BaseModel):
    """批量创建值映射的请求模型"""
    column_id: int = Field(..., description="列ID")
    mappings: List[Text2sqlValuemappingCreate] = Field(..., description="要创建的值映射列表") 