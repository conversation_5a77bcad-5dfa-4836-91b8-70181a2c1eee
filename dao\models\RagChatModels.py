from typing import List, Optional

from sqlalchemy import DateTime, Index, String, Text, text, ForeignKeyConstraint
from sqlalchemy.dialects.mysql import INTEGER, TINYINT, JSON
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
import datetime

class Base(DeclarativeBase):
    pass


class RagChatSession(Base):
    """RAG 对话会话表"""
    __tablename__ = 'rag_chat_session'
    __table_args__ = (
        Index('idx_session_id', 'session_id', unique=True),
        Index('idx_user_id', 'user_id'),
        Index('idx_collection_name', 'collection_name'),
        Index('idx_create_time', 'create_time'),
        Index('idx_status', 'status'),
        {'comment': 'RAG对话会话表'}
    )

    id: Mapped[str] = mapped_column(String(36), primary_key=True, comment='主键ID')
    session_id: Mapped[str] = mapped_column(String(100), comment='会话ID，唯一标识')
    user_id: Mapped[str] = mapped_column(String(100), comment='用户ID')
    collection_name: Mapped[str] = mapped_column(String(100), comment='知识库集合名称')
    session_title: Mapped[Optional[str]] = mapped_column(String(200), comment='会话标题')
    status: Mapped[Optional[int]] = mapped_column(TINYINT(1), server_default=text("'1'"), comment='会话状态：1-活跃，2-结束，3-已删除')
    total_messages: Mapped[Optional[int]] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='总消息数')
    last_message_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='最后消息时间')
    session_config: Mapped[Optional[dict]] = mapped_column(JSON, comment='会话配置参数JSON')
    create_by: Mapped[Optional[str]] = mapped_column(String(50), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    update_by: Mapped[Optional[str]] = mapped_column(String(50), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新时间')
    
    # 关联对话历史
    chat_histories: Mapped[List['RagChatHistory']] = relationship('RagChatHistory', back_populates='session', cascade='all, delete-orphan')


class RagChatHistory(Base):
    """RAG 对话历史表"""
    __tablename__ = 'rag_chat_history'
    __table_args__ = (
        ForeignKeyConstraint(['session_id'], ['rag_chat_session.session_id'], name='fk_chat_history_session'),
        Index('idx_session_id', 'session_id'),
        Index('idx_create_time', 'create_time'),
        Index('idx_role', 'role'),
        Index('idx_message_order', 'message_order'),
        {'comment': 'RAG对话历史表'}
    )

    id: Mapped[str] = mapped_column(String(36), primary_key=True, comment='主键ID')
    session_id: Mapped[str] = mapped_column(String(100), comment='会话ID，关联rag_chat_session.session_id')
    role: Mapped[str] = mapped_column(String(20), comment='角色：user-用户，assistant-助手')
    content: Mapped[str] = mapped_column(Text, comment='消息内容')
    message_order: Mapped[int] = mapped_column(INTEGER(11), comment='消息顺序号')
    token_count: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='Token数量')
    source_info: Mapped[Optional[dict]] = mapped_column(JSON, comment='数据来源信息JSON')
    message_metadata: Mapped[Optional[dict]] = mapped_column(JSON, comment='消息元数据JSON')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    
    # 关联会话
    session: Mapped['RagChatSession'] = relationship('RagChatSession', back_populates='chat_histories') 