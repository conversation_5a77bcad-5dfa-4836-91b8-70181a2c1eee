<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.08"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.08"/>
    </linearGradient>
    
    <!-- 文档渐变 -->
    <linearGradient id="docGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1e40af"/>
    </linearGradient>
    
    <!-- 搜索镜渐变 -->
    <linearGradient id="searchGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    
    <!-- 高亮渐变 -->
    <linearGradient id="highlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24"/>
      <stop offset="100%" style="stop-color:#f59e0b"/>
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow3" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="1" dy="2" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.15"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow3" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 强发光效果 -->
    <filter id="strongGlow3" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 - 添加脉冲效果 -->
  <circle cx="128" cy="128" r="120" fill="url(#bg3)" stroke="#e2e8f0" stroke-width="2">
    <animate attributeName="r" values="120;125;120" dur="5s" repeatCount="indefinite"/>
    <animate attributeName="stroke-opacity" values="1;0.5;1" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主文档 - 添加页面翻动效果 -->
  <g transform="translate(50, 50)" filter="url(#shadow3)">
    <animateTransform attributeName="transform" type="translate" values="50,50; 50,45; 50,50" dur="4s" repeatCount="indefinite"/>
    
    <!-- 文档外框 -->
    <rect x="0" y="0" width="90" height="120" rx="8" fill="url(#docGrad)" opacity="0.9">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 文档页面 - 添加轻微3D效果 -->
    <g>
      <animateTransform attributeName="transform" type="skewY" values="0;1;0" dur="6s" repeatCount="indefinite"/>
      <rect x="4" y="4" width="82" height="112" rx="6" fill="white" opacity="0.95">
        <animate attributeName="opacity" values="0.95;1;0.95" dur="2s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 文档右上角折叠效果 - 添加翻动动画 -->
    <path d="M70 4 L70 20 L86 20 Z" fill="url(#docGrad)" opacity="0.7">
      <animateTransform attributeName="transform" type="scale" values="1;1.1;1" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="4s" repeatCount="indefinite"/>
    </path>
    <path d="M70 4 L70 20 L86 20 Z" fill="none" stroke="white" stroke-width="1">
      <animate attributeName="stroke-opacity" values="1;0.5;1" dur="4s" repeatCount="indefinite"/>
    </path>
    
    <!-- 文档内容线条 - 添加打字机效果 -->
    <g stroke="#6b7280" stroke-width="1.5" opacity="0.6">
      <line x1="12" y1="25" x2="65" y2="25">
        <animate attributeName="x2" values="12;65;12;65" dur="5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="35" x2="58" y2="35">
        <animate attributeName="x2" values="12;58;12;58" dur="5.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.7s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="45" x2="70" y2="45">
        <animate attributeName="x2" values="12;70;12;70" dur="6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="55" x2="50" y2="55">
        <animate attributeName="x2" values="12;50;12;50" dur="4.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="65" x2="62" y2="65">
        <animate attributeName="x2" values="12;62;12;62" dur="5.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.9s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="75" x2="45" y2="75">
        <animate attributeName="x2" values="12;45;12;45" dur="4.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.1s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="85" x2="68" y2="85">
        <animate attributeName="x2" values="12;68;12;68" dur="6.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3.1s" repeatCount="indefinite"/>
      </line>
      <line x1="12" y1="95" x2="55" y2="95">
        <animate attributeName="x2" values="12;55;12;55" dur="5.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.6s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 搜索高亮区域 - 增强脉冲效果 -->
    <rect x="10" y="62" width="25" height="8" rx="4" fill="url(#highlight)" opacity="0.3" filter="url(#glow3)">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="width" values="25;30;25" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="10" y="82" width="35" height="8" rx="4" fill="url(#highlight)" opacity="0.3" filter="url(#glow3)">
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="width" values="35;40;35" dur="2.5s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- 搜索放大镜 - 添加旋转和缩放动画 -->
  <g transform="translate(155, 120)" filter="url(#shadow3)">
    <animateTransform attributeName="transform" type="translate" values="155,120; 160,115; 155,120" dur="3s" repeatCount="indefinite"/>
    
    <!-- 放大镜整体旋转 -->
    <g>
      <animateTransform attributeName="transform" type="rotate" values="0 22.5 22.5;15 22.5 22.5;0 22.5 22.5;-15 22.5 22.5;0 22.5 22.5" dur="6s" repeatCount="indefinite"/>
      
      <!-- 放大镜镜面 -->
      <circle cx="15" cy="15" r="20" fill="none" stroke="url(#searchGrad)" stroke-width="4">
        <animate attributeName="stroke-width" values="4;6;4" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="r" values="20;22;20" dur="4s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="15" r="16" fill="white" opacity="0.8">
        <animate attributeName="opacity" values="0.8;0.95;0.8" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="r" values="16;18;16" dur="4s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 放大镜手柄 -->
      <line x1="30" y1="30" x2="45" y2="45" stroke="url(#searchGrad)" stroke-width="5" stroke-linecap="round">
        <animate attributeName="stroke-width" values="5;7;5" dur="3s" repeatCount="indefinite"/>
      </line>
      
      <!-- 镜面反光效果 - 添加移动反光 -->
      <circle cx="10" cy="10" r="6" fill="white" opacity="0.4" filter="url(#glow3)">
        <animateTransform attributeName="transform" type="translate" values="0,0; 3,3; 0,0; -3,-3; 0,0" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 搜索结果指示器 - 增强脉冲动画 -->
  <g transform="translate(60, 185)">
    <circle cx="0" cy="0" r="4" fill="#10b981" opacity="0.8" filter="url(#glow3)">
      <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="5" r="3.5" fill="#10b981" opacity="0.7" filter="url(#glow3)">
      <animate attributeName="r" values="3.5;7;3.5" dur="2.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="50" cy="-2" r="3" fill="#10b981" opacity="0.6" filter="url(#glow3)">
      <animate attributeName="r" values="3;6;3" dur="1.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 搜索结果连接线 -->
    <g stroke="#10b981" stroke-width="1.5" opacity="0.4">
      <line x1="4" y1="0" x2="21" y2="5">
        <animate attributeName="stroke-dasharray" values="0 20;10 10;0 20" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="29" y1="5" x2="46" y2="-2">
        <animate attributeName="stroke-dasharray" values="0 20;10 10;0 20" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
      </line>
    </g>
  </g>
  
  <!-- 搜索光束效果 - 增强扫描动画 -->
  <g transform="translate(170, 135)" opacity="0.4">
    <animateTransform attributeName="transform" type="rotate" values="0 170 135;360 170 135" dur="8s" repeatCount="indefinite"/>
    
    <path d="M0 0 L-15 -15 L-10 -20 L5 -5 Z" fill="url(#highlight)" filter="url(#strongGlow3)">
      <animate attributeName="opacity" values="0.4;0.9;0.4" dur="1.5s" repeatCount="indefinite"/>
    </path>
    
    <!-- 额外的扫描光束 -->
    <path d="M0 0 L-20 -10 L-15 -25 L0 -15 Z" fill="url(#highlight)" opacity="0.2">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 数据流动线条 - 增强流动效果 -->
  <g transform="translate(140, 70)" stroke="#10b981" stroke-width="2" fill="none" opacity="0.3">
    <path d="M0 0 Q10 10 0 20" filter="url(#glow3)">
      <animate attributeName="stroke-dasharray" values="0 30;15 15;0 30" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="stroke-width" values="2;4;2" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M5 5 Q15 15 5 25" filter="url(#glow3)">
      <animate attributeName="stroke-dasharray" values="0 30;15 15;0 30" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="stroke-width" values="2;3.5;2" dur="2.5s" repeatCount="indefinite"/>
    </path>
    <path d="M-5 3 Q5 13 -5 23" filter="url(#glow3)">
      <animate attributeName="stroke-dasharray" values="0 30;15 15;0 30" dur="2.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="2.2s" repeatCount="indefinite"/>
      <animate attributeName="stroke-width" values="2;3;2" dur="2.2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 搜索进度条效果 -->
  <g transform="translate(40, 40)">
    <rect x="0" y="0" width="100" height="4" rx="2" fill="#e2e8f0" opacity="0.3"/>
    <rect x="0" y="0" width="30" height="4" rx="2" fill="url(#searchGrad)" opacity="0.8">
      <animate attributeName="width" values="0;100;0" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite"/>
    </rect>
  </g>

  <!-- 添加旋转装饰环 -->
  <g transform="translate(128, 128)" opacity="0.2">
    <animateTransform attributeName="transform" type="rotate" values="0 128 128;-360 128 128" dur="25s" repeatCount="indefinite"/>
    <circle cx="0" cy="0" r="110" fill="none" stroke="#10b981" stroke-width="1" stroke-dasharray="8 15">
      <animate attributeName="stroke-opacity" values="0.2;0.5;0.2" dur="5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 搜索关键词飞舞效果 -->
  <g opacity="0.4">
    <text x="80" y="200" font-family="Arial" font-size="8" fill="#10b981" opacity="0.6">
      AI
      <animateTransform attributeName="transform" type="translate" values="0,0; 50,-30; 100,0; 50,30; 0,0" dur="12s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </text>
    <text x="160" y="80" font-family="Arial" font-size="6" fill="#3b82f6" opacity="0.5">
      文档
      <animateTransform attributeName="transform" type="translate" values="0,0; -40,20; -80,0; -40,-20; 0,0" dur="15s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" repeatCount="indefinite"/>
    </text>
    <text x="200" y="180" font-family="Arial" font-size="7" fill="#f59e0b" opacity="0.7">
      搜索
      <animateTransform attributeName="transform" type="translate" values="0,0; -60,40; -120,0; -60,-40; 0,0" dur="18s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
    </text>
  </g>
</svg> 