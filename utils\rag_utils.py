import base64
import hashlib
import os
import subprocess
import asyncio
import requests
from io import BytesIO
# pip install pymupdf
import fitz
from PIL import Image
from pptx import Presentation
from config import settings
from rag.ocr import ocr_to_text_from_llm
import time

# 注意：原来的并发限制器已移除，现在使用 run_jobs 控制并发
# 需要注意的是，虽然移除了信号量，但仍需要 asyncio.to_thread 来避免同步网络请求阻塞事件循环


def get_b64_image_from_path(image_path):
    """
    从文件路径或URL获取base64编码的图像
    支持本地文件路径和远程URL（包括Minio URL）
    
    参数:
        image_path: 图像文件路径或URL
    
    返回:
        str: base64编码的图像字符串
    """
    # 检查是否为URL
    if image_path.startswith(('http://', 'https://')):
        # 从URL获取图像
        try:
            response = requests.get(image_path, timeout=30)
            response.raise_for_status()
            return base64.b64encode(response.content).decode('utf-8')
        except requests.exceptions.RequestException as e:
            raise ValueError(f"无法从URL获取图像: {image_path}, 错误: {str(e)}")
    else:
        # 从本地文件路径获取图像
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except (IOError, OSError) as e:
            raise ValueError(f"无法读取本地图像文件: {image_path}, 错误: {str(e)}")

def process_table(file):
    """
    解析表格:
    1、借助ocr直接识别表格内容
    2、借助多模态大模型识别
    :param file:
    :return: 表格内容及对表格的描述信息
    """
    content = ocr_to_text_from_llm(file)
    llm = settings.deepseek_llm()
    response = llm.complete(f"你的职责是解释表格。"
                            f"你是将线性化表格转换成简单中文文本供大型语言模型（LLMs）使用的专家。"
                            f"请解释以下线性化表格： {content}")
    return content, response.text

def get_b64_image_from_content(image_content):
    """Convert image content to base64 encoded string."""
    img = Image.open(BytesIO(image_content))
    if img.mode != 'RGB':
        img = img.convert('RGB')
    buffered = BytesIO()
    img.save(buffered, format="JPEG")
    return base64.b64encode(buffered.getvalue()).decode("utf-8")

def _describe_image_sync(file_path):
    """同步版本的图像描述函数，用于在线程池中执行"""
    prompt = """
    请分析图片中的细节，并详细描述图片中的内容和主要信息,确保描述全面且细节丰富,但不要回答与图片所要表达主题无关的信息。
    """
    image_b64 = get_b64_image_from_path(file_path)
    client = settings.vllm()
    messages = [
        {
            "role": "user",
            "content":
                [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url":{"url": f"data:image/png;base64,{image_b64}"}
                     }
                ]
         }
    ]

    # 调用LLM生成几个相关的问题，多角度获取图片中的内容，自行完成
    # 参考LLmaindex结构化输出，生成多个相关问题
    # https://docs.llamaindex.ai/en/stable/examples/output_parsing/llm_program/

    completion = client.chat.completions.create(
        model=settings.configuration.vllm_model_name,
        messages=messages, temperature=0.5,
        seed=0, top_p=0.70, stream=False
    )
    # 返回多个问题汇总后的答案描述
    return completion.choices[0].message.content

def _describe_powerpoint_slide_sync(file_path):
    """
    专门用于PowerPoint幻灯片的同步图像描述函数，采用针对PPT的提示词
    重点提取纲领性质的内容
    """
    prompt = """
    这是一张PowerPoint幻灯片的截图。请分析并提取幻灯片的核心内容，重点关注：

    1. 标题和主要观点：识别幻灯片的标题、副标题和主要论点
    2. 结构化信息：提取列表、要点、步骤、流程等结构化内容
    3. 关键数据：识别图表、数据、统计信息等重要数值

    请以简洁、条理清晰的方式组织描述，突出幻灯片的核心信息和要点。
    避免过度描述装饰性元素，专注于实质性内容。
    """
    
    # 使用全局设置的llm，但由于它不支持图像，这里仍需要使用vllm
    image_b64 = get_b64_image_from_path(file_path)
    client = settings.vllm()
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt
                },
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{image_b64}"}
                }
            ]
        }
    ]

    completion = client.chat.completions.create(
        model=settings.configuration.vllm_model_name,
        messages=messages, 
        temperature=0.3,  # 降低温度以获得更一致的结果
        seed=0, 
        top_p=0.80, 
        stream=False
    )
    
    return completion.choices[0].message.content

async def describe_powerpoint_slide(file_path):
    """
    异步版本的PowerPoint幻灯片图像描述函数
    专门针对PPT幻灯片优化，提取纲领性质的内容
    """
    from utils.logger import get_logger
    logger = get_logger()
    
    try:
        logger.debug(f"开始处理PowerPoint幻灯片描述: {os.path.basename(file_path)}")
        start_time = time.time()
        
        # 使用 to_thread 避免同步网络请求阻塞事件循环
        result = await asyncio.to_thread(_describe_powerpoint_slide_sync, file_path)
        
        elapsed_time = time.time() - start_time
        logger.debug(f"PowerPoint幻灯片描述完成: {os.path.basename(file_path)}, 耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        logger.error(f"PowerPoint幻灯片描述失败: {str(e)}")
        raise e

async def _summarize_slide_content_async(slide_text, image_description):
    """
    异步版本的幻灯片内容总结函数
    将页面文本和图像描述进行综合总结
    """
    prompt = f"""
    请对以下PowerPoint幻灯片的内容进行综合总结：

    **文本内容：**
    {slide_text}

    **图像描述：**
    {image_description}

    请将上述文本内容和图像描述进行整合，生成一个条理清晰、内容完整的综合描述。要求：

    1. 保持信息的完整性和准确性
    2. 消除重复信息，突出核心要点  
    3. 按逻辑层次组织内容
    4. 使用简洁明了的语言
    5. 保留重要的数据和关键信息

    请直接输出整合后的内容描述，不需要额外的解释。
    """
    
    # 使用全局设置的LLM进行文本总结
    from llama_index.core import Settings
    llm = Settings.llm
    
    # 使用LlamaIndex的异步complete方法
    response = await llm.acomplete(prompt)
    
    return response.text

async def summarize_slide_content(slide_text, image_description):
    """
    异步版本的幻灯片内容总结函数
    将页面文本和图像描述进行综合总结
    """
    from utils.logger import get_logger
    logger = get_logger()
    
    try:
        logger.debug("开始综合总结幻灯片内容")
        start_time = time.time()
        
        # 直接调用异步函数，不需要 to_thread
        result = await _summarize_slide_content_async(slide_text, image_description)
        
        elapsed_time = time.time() - start_time
        logger.debug(f"幻灯片内容总结完成, 耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        logger.error(f"幻灯片内容总结失败: {str(e)}")
        raise e

async def describe_image(file_path):
    """
    异步版本的图像描述函数
    
    注意：虽然外部通过 run_jobs 控制并发，但内部仍需要 asyncio.to_thread 
    来避免同步网络请求阻塞事件循环
    """
    from utils.logger import get_logger
    logger = get_logger()
    
    try:
        logger.debug(f"开始处理图像描述: {os.path.basename(file_path)}")
        start_time = time.time()
        
        # 使用 to_thread 避免同步网络请求阻塞事件循环
        result = await asyncio.to_thread(_describe_image_sync, file_path)
        
        elapsed_time = time.time() - start_time
        logger.debug(f"图像描述完成: {os.path.basename(file_path)}, 耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        logger.error(f"图像描述失败: {str(e)}")
        raise e

def extract_text_around_item(text_blocks, bbox, page_height, threshold_percentage=0.1):
    """从页面上的给定边界框提取上方和下方的文本。"""
    before_text, after_text = "", ""  # 初始化上方和下方文本为空字符串
    vertical_threshold_distance = page_height * threshold_percentage  # 计算垂直阈值距离
    horizontal_threshold_distance = bbox.width * threshold_percentage  # 计算水平阈值距离

    for block in text_blocks:  # 遍历所有文本块
        block_bbox = fitz.Rect(block[:4])  # 获取当前文本块的边界框
        vertical_distance = min(abs(block_bbox.y1 - bbox.y0), abs(block_bbox.y0 - bbox.y1))  # 计算当前文本块与目标边界框的垂直距离
        horizontal_overlap = max(0, min(block_bbox.x1, bbox.x1) - max(block_bbox.x0, bbox.x0))  # 计算当前文本块与目标边界框的水平重叠

        if vertical_distance <= vertical_threshold_distance and horizontal_overlap >= -horizontal_threshold_distance:
            # 如果垂直距离小于等于阈值且水平重叠大于等于负阈值
            if block_bbox.y1 < bbox.y0 and not before_text:
                # 如果当前文本块在目标边界框上方且上方文本未被设置
                before_text = block[4]  # 更新上方文本
            elif block_bbox.y0 > bbox.y1 and not after_text:
                # 如果当前文本块在目标边界框下方且下方文本未被设置
                after_text = block[4]  # 更新下方文本
                break  # 结束循环

    return before_text, after_text  # 返回提取到的上方和下方文本

def process_text_blocks(text_blocks, char_count_threshold=1024):
    """根据字符数阈值对文本块进行分组。"""
    current_group = []  # 当前组的文本块列表
    grouped_blocks = []  # 分组后的文本块列表
    current_char_count = 0  # 当前组的总字符数

    for block in text_blocks:
        if block[-1] == 0:  # 检查块是否为文本类型
            block_text = block[4]  # 获取块的文本内容
            block_char_count = len(block_text)  # 计算块的字符数

            if current_char_count + block_char_count <= char_count_threshold:
                # 如果当前组的字符数加上新块的字符数不超过阈值
                current_group.append(block)  # 将新块添加到当前组
                current_char_count += block_char_count  # 更新当前组的字符计数
            else:
                # 如果超过阈值
                if current_group:
                    # 如果当前组不为空
                    grouped_content = "\n".join([b[4] for b in current_group])  # 合并当前组的内容
                    grouped_blocks.append((current_group[0], grouped_content))  # 将合并后的内容添加到分组后的块列表
                current_group = [block]  # 重置当前组
                current_char_count = block_char_count  # 重置当前组的字符计数

    # 处理最后一个组
    if current_group:
        grouped_content = "\n".join([b[4] for b in current_group])  # 合并最后一个组的内容
        grouped_blocks.append((current_group[0], grouped_content))  # 将合并后的内容添加到分组后的块列表

    return grouped_blocks  # 返回分组后的文本块列表

def _process_ppt_without_libreoffice_sync(ppt_path):
    """
    同步版本的PPT处理函数，不依赖LibreOffice
    直接提取文本内容，适用于纯文本提取需求
    
    参数:
    - ppt_path: PPT文件路径
    
    返回:
    - list: 包含Document对象的列表
    """
    try:
        from llama_index.core import Document
        from utils.logger import get_logger
        
        logger = get_logger()
        start_time = time.time()
        file_size = os.path.getsize(ppt_path) / (1024 * 1024)  # MB
        
        logger.info(f"开始纯文本模式处理PPT: {os.path.basename(ppt_path)} ({file_size:.2f}MB)")
        
        # 提取PPT中的文本和备注
        slide_texts = extract_text_and_notes_from_ppt(ppt_path)
        
        documents = []
        file_name = os.path.basename(ppt_path)
        ppt_name_without_ext = os.path.splitext(file_name)[0]
        
        for slide_num, (slide_text, notes) in enumerate(slide_texts, 1):
            # 限制文本长度以避免过长的内容
            max_slide_text_length = 800
            max_notes_length = 400
            
            # 截断过长的幻灯片文本
            if len(slide_text) > max_slide_text_length:
                slide_text = slide_text[:max_slide_text_length] + "..."
            
            # 截断过长的备注
            if len(notes) > max_notes_length:
                notes = notes[:max_notes_length] + "..."
            
            # 合并幻灯片文本和备注
            combined_text = f"幻灯片 {slide_num}:\n{slide_text}"
            if notes.strip():
                combined_text += f"\n\n备注: {notes}"
            
            # 如果幻灯片有内容，创建Document对象
            if slide_text.strip() or notes.strip():
                doc_id = f"{ppt_name_without_ext}-slide{slide_num}"
                
                # 创建精简的元数据，避免过长
                metadata = {
                    "source": doc_id,
                    "type": "text",
                    "page_num": slide_num - 1,  # 0-based索引
                    "file_name": file_name[:50],  # 限制文件名长度
                    "file_id": calculate_file_id(file_name),
                    "processing_method": "text_only"  # 标记为纯文本处理
                }
                
                document = Document(
                    text=combined_text,
                    metadata=metadata,
                    id_=doc_id
                )
                
                documents.append(document)
        
        # 记录处理完成信息
        total_time = time.time() - start_time
        logger.success(f"纯文本模式处理完成! 总耗时: {total_time:.2f}秒")
        logger.info(f"成功提取 {len(documents)} 张幻灯片的文本内容")
        logger.info(f"处理效率: {file_size/total_time:.2f}MB/秒")
        
        return documents
        
    except Exception as e:
        error_time = time.time() - start_time if 'start_time' in locals() else 0
        from utils.logger import get_logger
        logger = get_logger()
        logger.error(f"纯文本模式处理PPT失败，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {str(e)}")
        raise RuntimeError(f"处理PPT文件时出错: {str(e)}")

async def process_ppt_without_libreoffice(ppt_path):
    """
    异步版本的PPT处理函数
    
    参数:
    - ppt_path: PPT文件路径
    
    返回:
    - list: 包含Document对象的列表
    
    注意：此方法主要作为备选方案使用，但仍需要避免阻塞事件循环
    """
    from utils.logger import get_logger
    logger = get_logger()
    
    try:
        logger.debug(f"开始异步处理PPT: {os.path.basename(ppt_path)}")
        start_time = time.time()
        
        # 使用 to_thread 避免可能的阻塞操作
        result = await asyncio.to_thread(_process_ppt_without_libreoffice_sync, ppt_path)
        
        elapsed_time = time.time() - start_time
        logger.debug(f"PPT异步处理完成: {os.path.basename(ppt_path)}, 耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        logger.error(f"异步PPT处理失败: {str(e)}")
        raise e

def convert_office_to_docx(office_file_path):
    """
    通用的Office文件转DOCX函数，专门用于doc转docx
    
    参数:
    - office_file_path: Office文件路径（主要支持.doc文件）
    
    返回:
    - str: DOCX文件路径
    """
    import platform
    import shutil
    
    base_name = os.path.basename(office_file_path)
    file_name_without_ext = os.path.splitext(base_name)[0].replace(' ', '_')
    file_extension = os.path.splitext(base_name.lower())[1]
    
    new_dir_path = os.path.abspath("vectorstore/office_references")
    os.makedirs(new_dir_path, exist_ok=True)
    docx_path = os.path.join(new_dir_path, f"{file_name_without_ext}.docx")
    
    # 获取LibreOffice可执行文件路径
    libreoffice_exec = None
    
    if platform.system() == "Windows":
        # 在Windows上查找LibreOffice
        possible_paths = [
            "soffice.exe",  # 如果在PATH中
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\LibreOffice\program\soffice.exe".format(os.getenv('USERNAME', '')),
        ]
        
        for path in possible_paths:
            if path == "soffice.exe":
                # 检查是否在PATH中
                if shutil.which("soffice"):
                    libreoffice_exec = "soffice"
                    break
            elif os.path.exists(path):
                libreoffice_exec = path
                break
    else:
        # Linux/Mac系统
        if shutil.which("libreoffice"):
            libreoffice_exec = "libreoffice"
        elif shutil.which("soffice"):
            libreoffice_exec = "soffice"
    
    if not libreoffice_exec:
        raise FileNotFoundError(
            f"未找到LibreOffice安装。无法转换{file_extension}文件。\n"
            "请安装LibreOffice或确保它在系统PATH中。\n"
            "下载地址: https://www.libreoffice.org/download/\n"
            "Windows用户请确保安装后将LibreOffice添加到系统PATH，或者安装到默认路径。"
        )
    
    try:
        # 记录转换开始时间和相关信息
        from utils.logger import get_logger
        logger = get_logger()
        
        start_time = time.time()
        file_size = os.path.getsize(office_file_path) / (1024 * 1024)  # MB
        command = [libreoffice_exec, '--headless', '--convert-to', 'docx', '--outdir', new_dir_path, office_file_path]
        
        logger.info(f"开始LibreOffice转换 {file_extension.upper()} 文件: {base_name} ({file_size:.2f}MB)")
        logger.info(f"转换命令: {' '.join(command)}")
        result = subprocess.run(command, check=True, capture_output=True, text=True, timeout=120)  # 增加超时时间到120秒
        
        # 记录转换耗时
        conversion_time = time.time() - start_time
        logger.info(f"LibreOffice命令执行完成，耗时: {conversion_time:.2f}秒")
        
        # 检查DOCX文件是否成功创建
        if not os.path.exists(docx_path):
            logger.error(f"LibreOffice转换成功但未找到输出DOCX文件: {docx_path}")
            raise RuntimeError(f"LibreOffice转换成功但未找到输出DOCX文件: {docx_path}")
        
        # 记录转换成功信息
        docx_size = os.path.getsize(docx_path) / (1024 * 1024)  # MB
        total_time = time.time() - start_time
        logger.success(f"LibreOffice {file_extension.upper()} 转DOCX成功! 总耗时: {total_time:.2f}秒")
        logger.info(f"输出DOCX文件: {os.path.basename(docx_path)} ({docx_size:.2f}MB)")
        logger.info(f"转换效率: {file_size/total_time:.2f}MB/秒")
            
        return docx_path
        
    except subprocess.TimeoutExpired:
        error_time = time.time() - start_time
        logger.error(f"LibreOffice {file_extension.upper()} 转DOCX超时（120秒），实际耗时: {error_time:.2f}秒")
        raise RuntimeError(f"LibreOffice {file_extension.upper()} 转DOCX超时（120秒）")
    except subprocess.CalledProcessError as e:
        error_time = time.time() - start_time
        error_msg = f"LibreOffice {file_extension.upper()} 转DOCX失败: {e.stderr if e.stderr else str(e)}"
        logger.error(f"LibreOffice {file_extension.upper()} 转DOCX失败，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {error_msg}")
        if e.stdout:
            logger.debug(f"标准输出: {e.stdout}")
        if e.stderr:
            logger.debug(f"标准错误: {e.stderr}")
        raise RuntimeError(error_msg)
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"{file_extension.upper()} 转DOCX过程中发生未知错误，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {str(e)}")
        raise RuntimeError(f"{file_extension.upper()} 转DOCX过程中发生未知错误: {str(e)}")

def convert_office_to_pdf(office_file_path):
    """
    通用的Office文件转PDF函数，支持doc、xls、ppt、pptx等格式
    
    参数:
    - office_file_path: Office文件路径
    
    返回:
    - str: PDF文件路径
    """
    import platform
    import shutil
    
    base_name = os.path.basename(office_file_path)
    file_name_without_ext = os.path.splitext(base_name)[0].replace(' ', '_')
    file_extension = os.path.splitext(base_name.lower())[1]
    
    new_dir_path = os.path.abspath("vectorstore/office_references")
    os.makedirs(new_dir_path, exist_ok=True)
    pdf_path = os.path.join(new_dir_path, f"{file_name_without_ext}.pdf")
    
    # 获取LibreOffice可执行文件路径
    libreoffice_exec = None
    
    if platform.system() == "Windows":
        # 在Windows上查找LibreOffice
        possible_paths = [
            "soffice.exe",  # 如果在PATH中
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\LibreOffice\program\soffice.exe".format(os.getenv('USERNAME', '')),
        ]
        
        for path in possible_paths:
            if path == "soffice.exe":
                # 检查是否在PATH中
                if shutil.which("soffice"):
                    libreoffice_exec = "soffice"
                    break
            elif os.path.exists(path):
                libreoffice_exec = path
                break
    else:
        # Linux/Mac系统
        if shutil.which("libreoffice"):
            libreoffice_exec = "libreoffice"
        elif shutil.which("soffice"):
            libreoffice_exec = "soffice"
    
    if not libreoffice_exec:
        raise FileNotFoundError(
            f"未找到LibreOffice安装。无法转换{file_extension}文件。\n"
            "请安装LibreOffice或确保它在系统PATH中。\n"
            "下载地址: https://www.libreoffice.org/download/\n"
            "Windows用户请确保安装后将LibreOffice添加到系统PATH，或者安装到默认路径。"
        )
    
    try:
        # 记录转换开始时间和相关信息
        from utils.logger import get_logger
        logger = get_logger()
        
        start_time = time.time()
        file_size = os.path.getsize(office_file_path) / (1024 * 1024)  # MB
        command = [libreoffice_exec, '--headless', '--convert-to', 'pdf', '--outdir', new_dir_path, office_file_path]
        
        logger.info(f"开始LibreOffice转换 {file_extension.upper()} 文件: {base_name} ({file_size:.2f}MB)")
        logger.info(f"转换命令: {' '.join(command)}")
        result = subprocess.run(command, check=True, capture_output=True, text=True, timeout=120)  # 增加超时时间到120秒
        
        # 记录转换耗时
        conversion_time = time.time() - start_time
        logger.info(f"LibreOffice命令执行完成，耗时: {conversion_time:.2f}秒")
        
        # 检查PDF文件是否成功创建
        if not os.path.exists(pdf_path):
            logger.error(f"LibreOffice转换成功但未找到输出PDF文件: {pdf_path}")
            raise RuntimeError(f"LibreOffice转换成功但未找到输出PDF文件: {pdf_path}")
        
        # 记录转换成功信息
        pdf_size = os.path.getsize(pdf_path) / (1024 * 1024)  # MB
        total_time = time.time() - start_time
        logger.success(f"LibreOffice {file_extension.upper()} 转换成功! 总耗时: {total_time:.2f}秒")
        logger.info(f"输出PDF文件: {os.path.basename(pdf_path)} ({pdf_size:.2f}MB)")
        logger.info(f"转换效率: {file_size/total_time:.2f}MB/秒")
            
        return pdf_path
        
    except subprocess.TimeoutExpired:
        error_time = time.time() - start_time
        logger.error(f"LibreOffice {file_extension.upper()} 转换超时（120秒），实际耗时: {error_time:.2f}秒")
        raise RuntimeError(f"LibreOffice {file_extension.upper()} 转换超时（120秒）")
    except subprocess.CalledProcessError as e:
        error_time = time.time() - start_time
        error_msg = f"LibreOffice {file_extension.upper()} 转换失败: {e.stderr if e.stderr else str(e)}"
        logger.error(f"LibreOffice {file_extension.upper()} 转换失败，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {error_msg}")
        if e.stdout:
            logger.debug(f"标准输出: {e.stdout}")
        if e.stderr:
            logger.debug(f"标准错误: {e.stderr}")
        raise RuntimeError(error_msg)
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"{file_extension.upper()} 转PDF过程中发生未知错误，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {str(e)}")
        raise RuntimeError(f"{file_extension.upper()} 转PDF过程中发生未知错误: {str(e)}")

def convert_ppt_to_pptx(ppt_file_path):
    """
    将PPT文件转换为PPTX文件
    
    参数:
    - ppt_file_path: PPT文件路径
    
    返回:
    - str: PPTX文件路径
    """
    import platform
    import shutil
    
    base_name = os.path.basename(ppt_file_path)
    file_name_without_ext = os.path.splitext(base_name)[0].replace(' ', '_')
    
    new_dir_path = os.path.abspath("vectorstore/ppt_references")
    os.makedirs(new_dir_path, exist_ok=True)
    pptx_path = os.path.join(new_dir_path, f"{file_name_without_ext}.pptx")
    
    # 获取LibreOffice可执行文件路径
    libreoffice_exec = None
    
    if platform.system() == "Windows":
        # 在Windows上查找LibreOffice
        possible_paths = [
            "soffice.exe",  # 如果在PATH中
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\LibreOffice\program\soffice.exe".format(os.getenv('USERNAME', '')),
        ]
        
        for path in possible_paths:
            if path == "soffice.exe":
                # 检查是否在PATH中
                if shutil.which("soffice"):
                    libreoffice_exec = "soffice"
                    break
            elif os.path.exists(path):
                libreoffice_exec = path
                break
    else:
        # Linux/Mac系统
        if shutil.which("libreoffice"):
            libreoffice_exec = "libreoffice"
        elif shutil.which("soffice"):
            libreoffice_exec = "soffice"
    
    if not libreoffice_exec:
        raise FileNotFoundError(
            "未找到LibreOffice安装。无法转换PPT文件。\n"
            "请安装LibreOffice或确保它在系统PATH中。\n"
            "下载地址: https://www.libreoffice.org/download/\n"
            "Windows用户请确保安装后将LibreOffice添加到系统PATH，或者安装到默认路径。"
        )
    
    try:
        # 记录转换开始时间和相关信息
        from utils.logger import get_logger
        logger = get_logger()
        
        start_time = time.time()
        file_size = os.path.getsize(ppt_file_path) / (1024 * 1024)  # MB
        command = [libreoffice_exec, '--headless', '--convert-to', 'pptx', '--outdir', new_dir_path, ppt_file_path]
        
        logger.info(f"开始LibreOffice转换 PPT 文件: {base_name} ({file_size:.2f}MB)")
        logger.info(f"转换命令: {' '.join(command)}")
        result = subprocess.run(command, check=True, capture_output=True, text=True, timeout=120)  # 增加超时时间到120秒
        
        # 记录转换耗时
        conversion_time = time.time() - start_time
        logger.info(f"LibreOffice命令执行完成，耗时: {conversion_time:.2f}秒")
        
        # 检查PPTX文件是否成功创建
        if not os.path.exists(pptx_path):
            logger.error(f"LibreOffice转换成功但未找到输出PPTX文件: {pptx_path}")
            raise RuntimeError(f"LibreOffice转换成功但未找到输出PPTX文件: {pptx_path}")
        
        # 记录转换成功信息
        pptx_size = os.path.getsize(pptx_path) / (1024 * 1024)  # MB
        total_time = time.time() - start_time
        logger.success(f"LibreOffice PPT 转PPTX成功! 总耗时: {total_time:.2f}秒")
        logger.info(f"输出PPTX文件: {os.path.basename(pptx_path)} ({pptx_size:.2f}MB)")
        logger.info(f"转换效率: {file_size/total_time:.2f}MB/秒")
            
        return pptx_path
        
    except subprocess.TimeoutExpired:
        error_time = time.time() - start_time
        logger.error(f"LibreOffice PPT 转PPTX超时（120秒），实际耗时: {error_time:.2f}秒")
        raise RuntimeError(f"LibreOffice PPT 转PPTX超时（120秒）")
    except subprocess.CalledProcessError as e:
        error_time = time.time() - start_time
        error_msg = f"LibreOffice PPT 转PPTX失败: {e.stderr if e.stderr else str(e)}"
        logger.error(f"LibreOffice PPT 转PPTX失败，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {error_msg}")
        if e.stdout:
            logger.debug(f"标准输出: {e.stdout}")
        if e.stderr:
            logger.debug(f"标准错误: {e.stderr}")
        raise RuntimeError(error_msg)
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"PPT 转PPTX过程中发生未知错误，耗时: {error_time:.2f}秒")
        logger.error(f"错误详情: {str(e)}")
        raise RuntimeError(f"PPT 转PPTX过程中发生未知错误: {str(e)}")

def safe_convert_ppt_to_pptx(ppt_file_path):
    """
    安全的PPT文件转PPTX函数，如果LibreOffice不可用，则返回None
    
    参数:
    - ppt_file_path: PPT文件路径
    
    返回:
    - str: PPTX文件路径，如果转换失败则返回None
    """
    try:
        from utils.logger import get_logger
        logger = get_logger()
        
        logger.info(f"尝试安全转换PPT文件: {os.path.basename(ppt_file_path)}")
        result = convert_ppt_to_pptx(ppt_file_path)
        logger.success(f"PPT转PPTX成功: {os.path.basename(ppt_file_path)}")
        return result
    except FileNotFoundError as e:
        # LibreOffice未安装
        from utils.logger import get_logger
        logger = get_logger()
        logger.warning(f"LibreOffice未安装，无法转换PPT文件: {os.path.basename(ppt_file_path)}")
        logger.info("PPT文件需要LibreOffice支持进行格式转换")
        return None
    except Exception as e:
        # 其他转换错误
        from utils.logger import get_logger
        logger = get_logger()
        logger.error(f"PPT转PPTX失败: {str(e)}")
        logger.info("请检查文件是否损坏或LibreOffice是否正常工作")
        return None

def safe_convert_doc_to_docx(doc_file_path):
    """
    安全的DOC文件转DOCX函数，如果LibreOffice不可用，则返回None
    
    参数:
    - doc_file_path: DOC文件路径
    
    返回:
    - str: DOCX文件路径，如果转换失败则返回None
    """
    try:
        from utils.logger import get_logger
        logger = get_logger()
        
        logger.info(f"尝试安全转换DOC文件: {os.path.basename(doc_file_path)}")
        result = convert_office_to_docx(doc_file_path)
        logger.success(f"DOC转DOCX成功: {os.path.basename(doc_file_path)}")
        return result
    except FileNotFoundError as e:
        # LibreOffice未安装
        from utils.logger import get_logger
        logger = get_logger()
        logger.warning(f"LibreOffice未安装，无法转换DOC文件: {os.path.basename(doc_file_path)}")
        logger.info("DOC文件需要LibreOffice支持进行格式转换")
        return None
    except Exception as e:
        # 其他转换错误
        from utils.logger import get_logger
        logger = get_logger()
        logger.error(f"DOC转DOCX失败: {str(e)}")
        logger.info("请检查文件是否损坏或LibreOffice是否正常工作")
        return None

def safe_convert_office_to_pdf(office_file_path):
    """
    安全的Office文件转PDF函数，如果LibreOffice不可用，则返回None
    
    参数:
    - office_file_path: Office文件路径（支持 .doc, .xls, .ppt 等）
    
    返回:
    - str: PDF文件路径，如果转换失败则返回None
    """
    try:
        from utils.logger import get_logger
        logger = get_logger()
        
        file_extension = os.path.splitext(office_file_path.lower())[1]
        logger.info(f"尝试安全转换 {file_extension.upper()} 文件: {os.path.basename(office_file_path)}")
        result = convert_office_to_pdf(office_file_path)
        logger.success(f"安全转换成功: {os.path.basename(office_file_path)}")
        return result
    except FileNotFoundError as e:
        # LibreOffice未安装
        from utils.logger import get_logger
        logger = get_logger()
        file_extension = os.path.splitext(office_file_path.lower())[1]
        logger.warning(f"LibreOffice未安装，无法转换 {file_extension.upper()} 文件: {os.path.basename(office_file_path)}")
        logger.info("旧格式Office文件需要LibreOffice支持")
        return None
    except Exception as e:
        # 其他转换错误
        from utils.logger import get_logger
        logger = get_logger()
        file_extension = os.path.splitext(office_file_path.lower())[1]
        logger.error(f"{file_extension.upper()} 转PDF失败: {str(e)}")
        logger.info("请检查文件是否损坏或LibreOffice是否正常工作")
        return None

def convert_pdf_to_images(pdf_path):
    """Convert a PDF file to a series of images using PyMuPDF."""
    doc = fitz.open(pdf_path)
    base_name = os.path.basename(pdf_path)
    pdf_name_without_ext = os.path.splitext(base_name)[0].replace(' ', '_')
    new_dir_path = os.path.join(os.getcwd(), "vectorstore/ppt_references")
    os.makedirs(new_dir_path, exist_ok=True)
    image_paths = []

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap()
        output_image_path = os.path.join(new_dir_path, f"{pdf_name_without_ext}_{page_num:04d}.png")
        pix.save(output_image_path)
        image_paths.append((output_image_path, page_num))
    doc.close()
    return image_paths

def extract_text_and_notes_from_ppt(ppt_path):
    """Extract text and notes from a PowerPoint file."""
    prs = Presentation(ppt_path)
    text_and_notes = []
    for slide in prs.slides:
        slide_text = ' '.join([shape.text for shape in slide.shapes if hasattr(shape, "text")])
        try:
            notes = slide.notes_slide.notes_text_frame.text if slide.notes_slide else ''
        except:
            notes = ''
        text_and_notes.append((slide_text, notes))
    return text_and_notes

def save_uploaded_file(uploaded_file):
    """Save an uploaded file to a temporary directory."""
    temp_dir = os.path.join(os.getcwd(), "vectorstore", "ppt_references", "tmp")
    os.makedirs(temp_dir, exist_ok=True)
    temp_file_path = os.path.join(temp_dir, uploaded_file.name)

    with open(temp_file_path, "wb") as temp_file:
        temp_file.write(uploaded_file.read())

    return temp_file_path

def calculate_file_id(source: str) -> str:
    """
    根据file_name字段计算MD5哈希值作为file_id

    参数:
        source: 源字段字符串

    返回:
        str: MD5哈希值
    """
    if not source:
        return ""
    # 将source转换为UTF-8编码的字节串，然后计算MD5
    return hashlib.md5(str(source).encode('utf-8')).hexdigest()
