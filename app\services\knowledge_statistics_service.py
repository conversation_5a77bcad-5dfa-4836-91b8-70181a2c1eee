from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from dao.ChainlitDatabaseEngine import ChainlitDatabaseEngine

class StatisticsService:
    """统计服务类，提供AI对话系统的各种统计功能"""
    
    def __init__(self):
        self.engine = ChainlitDatabaseEngine.get_engine()
    
    async def get_conversation_stats(self, days: int = 30) -> Dict[str, Any]:
        """获取对话统计信息"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 总对话数
            total_query = text('SELECT COUNT(*) as count FROM threads')
            result = await session.execute(total_query)
            total_conversations = result.scalar() or 0
            
            # 指定时间段内的对话数
            period_query = text("""
                SELECT COUNT(*) as count 
                FROM threads 
                WHERE "createdAt" >= :start_date
            """)
            result = await session.execute(period_query, {"start_date": start_date})
            period_conversations = result.scalar() or 0
            
            return {
                "total_conversations": total_conversations,
                "period_conversations": period_conversations,
                "period_days": days
            }
    
    async def get_user_engagement_stats(self, days: int = 30) -> Dict[str, Any]:
        """获取用户参与度统计"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 活跃用户数
            active_users_query = text("""
                SELECT COUNT(DISTINCT "userId") as count 
                FROM threads 
                WHERE "createdAt" >= :start_date AND "userId" IS NOT NULL
            """)
            result = await session.execute(active_users_query, {"start_date": start_date})
            active_users = result.scalar() or 0
            
            # 新用户数（最近7天注册）
            new_user_date = (datetime.now() - timedelta(days=7)).isoformat()
            new_users_query = text("""
                SELECT COUNT(*) as count 
                FROM users 
                WHERE "createdAt" >= :start_date
            """)
            result = await session.execute(new_users_query, {"start_date": new_user_date})
            new_users = result.scalar() or 0
            
            # 用户平均对话数
            avg_conversations_query = text("""
                SELECT AVG(conversation_count) as avg_count
                FROM (
                    SELECT "userId", COUNT(*) as conversation_count
                    FROM threads 
                    WHERE "createdAt" >= :start_date AND "userId" IS NOT NULL
                    GROUP BY "userId"
                ) as user_conversations
            """)
            result = await session.execute(avg_conversations_query, {"start_date": start_date})
            avg_conversations = result.scalar() or 0
            
            return {
                "active_users": active_users,
                "new_users": new_users,
                "avg_conversations_per_user": round(float(avg_conversations), 2) if avg_conversations else 0,
                "period_days": days
            }
    
    async def get_satisfaction_metrics(self, days: int = 30) -> Dict[str, Any]:
        """获取满意度指标"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            satisfaction_query = text("""
                SELECT 
                    COUNT(*) as total_feedbacks,
                    SUM(CASE WHEN value > 0 THEN 1 ELSE 0 END) as positive_feedbacks,
                    SUM(CASE WHEN value < 0 THEN 1 ELSE 0 END) as negative_feedbacks,
                    AVG(value) as avg_rating
                FROM feedbacks f
                INNER JOIN threads t ON f."threadId" = t.id
                WHERE t."createdAt" >= :start_date
            """)
            
            result = await session.execute(satisfaction_query, {"start_date": start_date})
            feedback_data = result.fetchone()
            
            total_feedbacks = feedback_data.total_feedbacks or 0
            positive_feedbacks = feedback_data.positive_feedbacks or 0
            negative_feedbacks = feedback_data.negative_feedbacks or 0
            avg_rating = feedback_data.avg_rating or 0
            
            satisfaction_rate = 0.0
            if total_feedbacks > 0:
                satisfaction_rate = (positive_feedbacks / total_feedbacks) * 100
            
            return {
                "total_feedbacks": total_feedbacks,
                "positive_feedbacks": positive_feedbacks,
                "negative_feedbacks": negative_feedbacks,
                "satisfaction_rate": round(satisfaction_rate, 1),
                "average_rating": round(float(avg_rating), 2) if avg_rating else 0,
                "period_days": days
            }

    async def get_avg_session_duration(self, days: int = 30) -> Dict[str, Any]:
        """获取性能指标"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()

            # 平均会话时间（基于步骤时间）
            response_time_query = text("""
                SELECT AVG(
                    CASE 
                        WHEN s1."end" IS NOT NULL AND s1."start" IS NOT NULL 
                        THEN EXTRACT(EPOCH FROM (
                            TO_TIMESTAMP(s1."end", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') - 
                            TO_TIMESTAMP(s1."start", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')
                        ))
                        ELSE NULL
                    END
                ) as avg_response_time
                FROM steps s1
                WHERE s1."end" IS NOT NULL AND s1."start" IS NOT NULL
                AND s1."createdAt" >= :start_date
                AND s1.type = 'run'
            """)

            result = await session.execute(response_time_query, {"start_date": start_date})
            avg_session_duration = result.scalar() or 0
            return {
                "avg_session_duration_seconds": round(float(avg_session_duration), 2) if avg_session_duration else 0,
            }

    async def get_performance_metrics(self, days: int = 30) -> Dict[str, Any]:
        """获取性能指标"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 平均响应时间（基于步骤时间）
            response_time_query = text("""
                SELECT AVG(
                    CASE 
                        WHEN s1."end" IS NOT NULL AND s1."start" IS NOT NULL 
                        THEN EXTRACT(EPOCH FROM (
                            TO_TIMESTAMP(s1."end", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') - 
                            TO_TIMESTAMP(s1."start", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')
                        ))
                        ELSE NULL
                    END
                ) as avg_response_time
                FROM steps s1
                WHERE s1."end" IS NOT NULL AND s1."start" IS NOT NULL
                AND s1."createdAt" >= :start_date
                AND s1.type = 'assistant_message'
            """)
            
            result = await session.execute(response_time_query, {"start_date": start_date})
            avg_response_time = result.scalar() or 0
            
            # 对话完成率（有反馈的对话比例）
            completion_query = text("""
                SELECT 
                    COUNT(DISTINCT t.id) as total_threads,
                    COUNT(DISTINCT f."threadId") as completed_threads
                FROM threads t
                LEFT JOIN feedbacks f ON t.id = f."threadId"
                WHERE t."createdAt" >= :start_date
            """)
            
            result = await session.execute(completion_query, {"start_date": start_date})
            completion_data = result.fetchone()
            
            total_threads = completion_data.total_threads or 0
            completed_threads = completion_data.completed_threads or 0
            
            completion_rate = 0.0
            if total_threads > 0:
                completion_rate = (completed_threads / total_threads) * 100
            
            return {
                "avg_response_time_seconds": round(float(avg_response_time), 2) if avg_response_time else 0,
                "avg_response_time_minutes": round(float(avg_response_time) / 60, 2) if avg_response_time else 0,
                "completion_rate": round(completion_rate, 1),
                "total_threads": total_threads,
                "completed_threads": completed_threads,
                "period_days": days
            }
    
    async def get_usage_patterns(self, days: int = 30) -> Dict[str, Any]:
        """获取使用模式分析"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 每小时使用分布
            hourly_query = text("""
                SELECT 
                    EXTRACT(HOUR FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')) as hour,
                    COUNT(*) as conversation_count
                FROM threads 
                WHERE "createdAt" >= :start_date
                GROUP BY EXTRACT(HOUR FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'))
                ORDER BY hour ASC
            """)
            
            result = await session.execute(hourly_query, {"start_date": start_date})
            hourly_data = result.fetchall()
            
            # 每周使用分布 (PostgreSQL中Sunday=0, Monday=1, ..., Saturday=6)
            weekly_query = text("""
                SELECT 
                    EXTRACT(DOW FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')) as day_of_week,
                    COUNT(*) as conversation_count
                FROM threads 
                WHERE "createdAt" >= :start_date
                GROUP BY EXTRACT(DOW FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'))
                ORDER BY day_of_week ASC
            """)
            
            result = await session.execute(weekly_query, {"start_date": start_date})
            weekly_data = result.fetchall()
            
            # 格式化数据
            hourly_usage = {str(hour): 0 for hour in range(24)}
            for row in hourly_data:
                hourly_usage[str(int(row.hour))] = row.conversation_count
            
            week_days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
            weekly_usage = {week_days[i]: 0 for i in range(7)}
            for row in weekly_data:
                weekly_usage[week_days[int(row.day_of_week)]] = row.conversation_count
            
            return {
                "hourly_usage": hourly_usage,
                "weekly_usage": weekly_usage,
                "period_days": days
            }
    
    async def get_content_analysis(self, days: int = 30) -> Dict[str, Any]:
        """获取内容分析"""
        async with AsyncSession(self.engine) as session:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 查询类型分布 - 基于用户输入内容进行分类
            query_types_query = text("""
                SELECT 
                    CASE 
                        WHEN LOWER(input) LIKE '%查询%' OR LOWER(input) LIKE '%select%' OR LOWER(input) LIKE '%搜索%' OR LOWER(input) LIKE '%查找%' THEN '查询类'
                        WHEN LOWER(input) LIKE '%统计%' OR LOWER(input) LIKE '%count%' OR LOWER(input) LIKE '%数据%' OR LOWER(input) LIKE '%报告%' THEN '统计类'
                        WHEN LOWER(input) LIKE '%更新%' OR LOWER(input) LIKE '%修改%' OR LOWER(input) LIKE '%edit%' OR LOWER(input) LIKE '%update%' THEN '更新类'
                        WHEN LOWER(input) LIKE '%删除%' OR LOWER(input) LIKE '%delete%' OR LOWER(input) LIKE '%移除%' THEN '删除类'
                        WHEN LOWER(input) LIKE '%插入%' OR LOWER(input) LIKE '%insert%' OR LOWER(input) LIKE '%添加%' OR LOWER(input) LIKE '%新增%' THEN '插入类'
                        WHEN LOWER(input) LIKE '%帮助%' OR LOWER(input) LIKE '%help%' OR LOWER(input) LIKE '%如何%' OR LOWER(input) LIKE '%怎么%' THEN '帮助类'
                        WHEN LOWER(input) LIKE '%分析%' OR LOWER(input) LIKE '%analysis%' OR LOWER(input) LIKE '%分析%' THEN '分析类'
                        ELSE '其他类'
                    END as query_type,
                    COUNT(*) as count
                FROM steps
                WHERE type = 'user_message' 
                AND input IS NOT NULL 
                AND input != ''
                AND LENGTH(TRIM(input)) > 1
                AND "createdAt" >= :start_date
                GROUP BY query_type
                ORDER BY count DESC
            """)
            
            result = await session.execute(query_types_query, {"start_date": start_date})
            query_types = result.fetchall()
            
            # 平均输入长度
            avg_length_query = text("""
                SELECT AVG(LENGTH(input)) as avg_length
                FROM steps
                WHERE type = 'user_message' 
                AND input IS NOT NULL 
                AND input != ''
                AND "createdAt" >= :start_date
            """)
            
            result = await session.execute(avg_length_query, {"start_date": start_date})
            avg_input_length = result.scalar() or 0
            
            # 消息总数统计
            message_stats_query = text("""
                SELECT 
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN type = 'user_message' THEN 1 END) as user_messages,
                    COUNT(CASE WHEN type = 'assistant_message' THEN 1 END) as assistant_messages
                FROM steps
                WHERE "createdAt" >= :start_date
            """)
            
            result = await session.execute(message_stats_query, {"start_date": start_date})
            message_stats = result.fetchone()
            
            # 格式化查询类型分布
            query_distribution = {}
            for row in query_types:
                query_distribution[row.query_type] = row.count
            
            return {
                "query_type_distribution": query_distribution,
                "average_input_length": round(float(avg_input_length), 1) if avg_input_length else 0,
                "message_statistics": {
                    "total_messages": message_stats.total_messages or 0,
                    "user_messages": message_stats.user_messages or 0,
                    "assistant_messages": message_stats.assistant_messages or 0
                },
                "period_days": days
            }
    
    async def get_statistics_overview_data(self) -> Dict[str, Any]:
        """获取知识统计概览数据"""
        async with AsyncSession(self.engine) as session:
            # 总对话数
            total_conversations_query = text('SELECT COUNT(*) as count FROM threads')
            result = await session.execute(total_conversations_query)
            total_conversations = result.scalar() or 0

            # 活跃用户数
            thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
            active_users_query = text("""
                SELECT COUNT(DISTINCT "userId") as count 
                FROM threads 
                WHERE "createdAt" >= :start_date AND "userId" IS NOT NULL
            """)
            result = await session.execute(active_users_query, {"start_date": thirty_days_ago})
            active_users = result.scalar() or 0

            # 平均会话时长
            avg_duration_query = text("""
                SELECT AVG(
                    CASE 
                        WHEN s1."end" IS NOT NULL AND s1."start" IS NOT NULL 
                        THEN EXTRACT(EPOCH FROM (
                            TO_TIMESTAMP(s1."end", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') - 
                            TO_TIMESTAMP(s1."start", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')
                        )) / 60.0
                        ELSE NULL
                    END
                ) as avg_duration
                FROM steps s1
                WHERE s1."end" IS NOT NULL AND s1."start" IS NOT NULL
                AND s1."createdAt" >= :start_date
                AND s1.type = 'run'
            """)
            result = await session.execute(avg_duration_query, {"start_date": thirty_days_ago})
            avg_duration = result.scalar() or 0

            # 满意度
            satisfaction_query = text("""
                SELECT 
                    COUNT(*) as total_feedbacks,
                    SUM(CASE WHEN value > 0 THEN 1 ELSE 0 END) as positive_feedbacks
                FROM feedbacks f
                INNER JOIN threads t ON f."threadId" = t.id
                WHERE t."createdAt" >= :start_date
            """)
            result = await session.execute(satisfaction_query, {"start_date": thirty_days_ago})
            feedback_data = result.fetchone()
            
            satisfaction_rate = 0.0
            if feedback_data and feedback_data.total_feedbacks > 0:
                satisfaction_rate = (feedback_data.positive_feedbacks / feedback_data.total_feedbacks) * 100

            return {
                "total_conversations": total_conversations,
                "active_users": active_users,
                "avg_session_duration": round(float(avg_duration), 1) if avg_duration else 0.0,
                "satisfaction_rate": round(satisfaction_rate, 1)
            }
    
    async def get_weekly_trend_data(self) -> Dict[str, Any]:
        """获取周趋势数据"""
        async with AsyncSession(self.engine) as session:
            seven_days_ago = (datetime.now() - timedelta(days=6)).isoformat()
            
            weekly_query = text("""
                SELECT 
                    DATE_TRUNC('day', TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')) as conversation_date,
                    COUNT(*) as conversation_count
                FROM threads 
                WHERE "createdAt" >= :start_date
                GROUP BY DATE_TRUNC('day', TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'))
                ORDER BY conversation_date ASC
            """)
            
            result = await session.execute(weekly_query, {"start_date": seven_days_ago})
            daily_data = result.fetchall()

            trends = []
            total_conversations = 0
            weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            
            for i in range(7):
                current_date = datetime.now() - timedelta(days=6-i)
                date_str = current_date.strftime('%Y-%m-%d')
                weekday_name = weekdays[current_date.weekday()]
                
                conversation_count = 0
                for row in daily_data:
                    if row.conversation_date.strftime('%Y-%m-%d') == date_str:
                        conversation_count = row.conversation_count
                        break
                
                trends.append({
                    "date": weekday_name,
                    "conversations": conversation_count
                })
                total_conversations += conversation_count

            return {
                "trends": trends,
                "total_conversations": total_conversations
            }
    
    async def get_user_stats_data(self) -> Dict[str, Any]:
        """获取用户统计数据"""
        async with AsyncSession(self.engine) as session:
            thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
            
            # 新用户数（首次对话在30天内）
            new_users_query = text("""
                SELECT COUNT(DISTINCT "userId") as count
                FROM threads t1
                WHERE t1."userId" IS NOT NULL
                AND t1."createdAt" >= :start_date
                AND NOT EXISTS (
                    SELECT 1 FROM threads t2 
                    WHERE t2."userId" = t1."userId" 
                    AND t2."createdAt" < :start_date
                )
            """)
            result = await session.execute(new_users_query, {"start_date": thirty_days_ago})
            new_users = result.scalar() or 0

            # 回访用户数（30天内有对话且之前也有对话记录）
            returning_users_query = text("""
                SELECT COUNT(DISTINCT "userId") as count
                FROM threads t1
                WHERE t1."userId" IS NOT NULL
                AND t1."createdAt" >= :start_date
                AND EXISTS (
                    SELECT 1 FROM threads t2 
                    WHERE t2."userId" = t1."userId" 
                    AND t2."createdAt" < :start_date
                )
            """)
            result = await session.execute(returning_users_query, {"start_date": thirty_days_ago})
            returning_users = result.scalar() or 0

            # 活跃用户总数
            total_active_users = new_users + returning_users

            return {
                "new_users": new_users,
                "returning_users": returning_users,
                "total_active_users": total_active_users
            }
    
    async def get_satisfaction_stats_data(self) -> Dict[str, Any]:
        """获取满意度统计数据"""
        async with AsyncSession(self.engine) as session:
            thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
            
            satisfaction_query = text("""
                SELECT 
                    COUNT(*) as total_feedbacks,
                    SUM(CASE WHEN f.value > 0 THEN 1 ELSE 0 END) as positive_feedbacks,
                    SUM(CASE WHEN f.value <= 0 THEN 1 ELSE 0 END) as negative_feedbacks
                FROM feedbacks f
                INNER JOIN threads t ON f."threadId" = t.id
                WHERE t."createdAt" >= :start_date
            """)
            
            result = await session.execute(satisfaction_query, {"start_date": thirty_days_ago})
            feedback_data = result.fetchone()
            
            total_feedbacks = feedback_data.total_feedbacks or 0
            positive_feedbacks = feedback_data.positive_feedbacks or 0
            negative_feedbacks = feedback_data.negative_feedbacks or 0
            
            satisfaction_rate = 0.0
            if total_feedbacks > 0:
                satisfaction_rate = (positive_feedbacks / total_feedbacks) * 100

            return {
                "total_feedbacks": total_feedbacks,
                "positive_feedbacks": positive_feedbacks,
                "negative_feedbacks": negative_feedbacks,
                "satisfaction_rate": round(satisfaction_rate, 1)
            }
    
    async def get_hourly_stats_data(self) -> List[Dict[str, Any]]:
        """获取小时统计数据"""
        async with AsyncSession(self.engine) as session:
            today = datetime.now().strftime('%Y-%m-%d')
            today_start = f"{today}T00:00:00.000Z"
            today_end = f"{today}T23:59:59.999Z"
            
            hourly_query = text("""
                SELECT 
                    EXTRACT(hour FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"')) as hour,
                    COUNT(*) as conversations
                FROM threads
                WHERE "createdAt" >= :start_time AND "createdAt" <= :end_time
                GROUP BY EXTRACT(hour FROM TO_TIMESTAMP("createdAt", 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'))
                ORDER BY hour
            """)
            
            result = await session.execute(hourly_query, {
                "start_time": today_start,
                "end_time": today_end
            })
            hourly_data = result.fetchall()

            # 生成24小时完整数据
            hourly_stats = []
            for hour in range(24):
                conversations = 0
                for row in hourly_data:
                    if int(row.hour) == hour:
                        conversations = row.conversations
                        break
                
                hourly_stats.append({
                    "hour": hour,
                    "conversations": conversations
                })

            return hourly_stats
    
    async def get_popular_questions_data(self) -> List[Dict[str, Any]]:
        """获取热门问题数据"""
        async with AsyncSession(self.engine) as session:
            # 最近30天的时间点
            thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
            
            # 查询用户输入的热门问题
            popular_query = text("""
                SELECT 
                    LEFT(TRIM(input), 50) as question,
                    COUNT(*) as count
                FROM steps 
                WHERE type = 'user_message' 
                AND input IS NOT NULL 
                AND input != ''
                AND "createdAt" >= :start_date
                GROUP BY LEFT(TRIM(input), 50)
                ORDER BY count DESC
                LIMIT 5
            """)
            
            result = await session.execute(popular_query, {"start_date": thirty_days_ago})
            question_data = result.fetchall()

            # 计算总问题数用于百分比计算
            total_query = text("""
                SELECT COUNT(*) as total
                FROM steps 
                WHERE type = 'user_message' 
                AND input IS NOT NULL 
                AND input != ''
                AND "createdAt" >= :start_date
            """)
            result = await session.execute(total_query, {"start_date": thirty_days_ago})
            total_questions = result.scalar() or 1  # 避免除零

            questions = []
            for row in question_data:
                if row.question and len(row.question.strip()) > 5:  # 过滤太短的问题
                    percentage = (row.count / total_questions) * 100
                    questions.append({
                        "question": row.question + ("..." if len(row.question) >= 50 else ""),
                        "count": row.count,
                        "percentage": round(percentage, 1)
                    })

            return questions
    
    async def get_comprehensive_report_data(self, days: int = 30) -> Dict[str, Any]:
        """获取综合报告数据"""
        # 调用现有的generate_comprehensive_report方法
        return await self.generate_comprehensive_report(days)

    async def generate_comprehensive_report(self, days: int = 30) -> Dict[str, Any]:
        """生成综合报告"""
        # 获取各种统计数据
        conversation_stats = await self.get_conversation_stats(days)
        user_engagement = await self.get_user_engagement_stats(days)
        satisfaction = await self.get_satisfaction_metrics(days)
        avg_session_duration = await self.get_avg_session_duration(days)
        performance = await self.get_performance_metrics(days)
        usage_patterns = await self.get_usage_patterns(days)
        content_analysis = await self.get_content_analysis(days)
        
        # 计算一些关键指标
        growth_rate = 0.0
        if conversation_stats["total_conversations"] > 0:
            growth_rate = (conversation_stats["period_conversations"] / conversation_stats["total_conversations"]) * 100
        
        return {
            "report_period": f"{days}天",
            "summary": {
                "total_conversations": conversation_stats["total_conversations"],
                "period_conversations": conversation_stats["period_conversations"],
                "active_users": user_engagement["active_users"],
                "new_users": user_engagement["new_users"],
                "avg_session_duration":avg_session_duration["avg_session_duration_seconds"],
                "satisfaction_rate": satisfaction["satisfaction_rate"],
                "avg_response_time": performance["avg_response_time_seconds"],
                "completion_rate": performance["completion_rate"],
                "growth_rate": round(growth_rate, 1)
            },
            "detailed_stats": {
                "conversations": conversation_stats,
                "user_engagement": user_engagement,
                "satisfaction": satisfaction,
                "performance": performance,
                "usage_patterns": usage_patterns,
                "content_analysis": content_analysis
            }
        } 