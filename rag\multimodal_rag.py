import os
import fitz
import requests
import pandas as pd
import asyncio
from llama_index.core import Document
from llama_index.core.async_utils import run_jobs
from tempfile import NamedTemporaryFile
from docx import Document as DocxDocument
from openpyxl import load_workbook
from typing import Optional

from .base_rag import RAG
from utils.rag_utils import (describe_image, process_text_blocks,
                             extract_text_around_item, process_table,
                             convert_pdf_to_images,
                             calculate_file_id)
from persistent.minio_storage_client import get_minio_client
from utils.conversion_pool import (
    safe_convert_doc_to_docx_async,
    safe_convert_office_to_pdf_async
)
# 导入知识图谱管理工具类
# 导入实体向量化工具类
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

class MultiModalRAG(RAG):
    def __init__(self, *args, 
                 milvus_uri: Optional[str] = None,
                 milvus_collection_prefix: Optional[str] = None, 
                 use_kg: bool = True,
                 **kwargs):
        """
        初始化 MultiModalRAG 实例
        
        参数:
            args: 传递给基类的参数
            milvus_uri: Milvus服务URI，如果为None则使用默认配置
            milvus_collection_prefix: Milvus集合名称前缀，如果为None则使用默认配置
            use_kg: 是否使用知识图谱功能
            kwargs: 传递给基类的关键字参数
        """
        super().__init__(*args, 
                        milvus_uri=milvus_uri, 
                        milvus_collection_prefix=milvus_collection_prefix,
                        use_kg=use_kg, 
                        **kwargs)

    @staticmethod
    def is_valid_text_block(block, page):
        # 文本块的高度
        block_height = block[3] - block[1]
        # 文本内容
        text_content = block[4]

        # 如果是普通文本块
        if block[-1] == 0:
            # 过滤条件：
            # 1. 页眉：在顶部5%范围内且文本块高度小于页面高度的5%
            # 2. 页脚：在底部5%范围内且文本块高度小于页面高度的5%且内容较短
            # 3. 过滤掉以"qh_"开头的内容
            is_header = block[1] < page.rect.height * 0.05 and block_height < page.rect.height * 0.05
            is_footer = (block[3] > page.rect.height * 0.95 and
                         block_height < page.rect.height * 0.05 and
                         len(text_content.strip()) < 50)  # 页脚通常较短
            is_qh_content = text_content.strip().startswith('qh_')  # 过滤qh_开头的内容

            return not (is_header or is_footer or is_qh_content)
        return False



    @staticmethod
    async def parse_all_tables(filename, page, pagenum, text_blocks, ongoing_tables):
        """
        从PDF页面中提取表格并处理成文档。
        该函数在给定的PDF页面上识别表格，将表格转换为pandas DataFrame，并将其保存为Excel文件，
        直接从DataFrame中提取表格内容作为文本。

        参数:
        - filename: 被处理的PDF文件的名称。
        - page: 当前处理的页面对象。
        - pagenum: 页面编号。
        - text_blocks: 页面上的文本块列表。
        - ongoing_tables: 正在处理的表格列表（保留参数以保持接口兼容）。

        返回值:
        - table_docs: 包含所有表格文档的列表。
        - table_bboxes: 包含所有表格边界框的列表。
        - ongoing_tables: 原样返回（保持接口兼容）。
        """
        table_docs = []
        table_bboxes = []
        minio_client = get_minio_client()  # 获取MinioStorageClient实例

        try:
            # 在页面上查找表格
            tables = page.find_tables(horizontal_strategy="lines_strict", vertical_strategy="lines_strict")

            for table_idx, tab in enumerate(tables, 1):
                if not tab.header.external:
                    # 将表格转换为pandas DataFrame
                    pandas_df = tab.to_pandas()
                    
                    # 如果DataFrame为空，跳过此表格
                    if pandas_df.empty:
                        continue

                    # 获取表格的边界框
                    bbox = fitz.Rect(tab.bbox)
                    table_bboxes.append(bbox)

                    # 提取表格周围的文本
                    before_text, after_text = extract_text_around_item(text_blocks, bbox, page.rect.height)

                    # 生成唯一的文档ID
                    doc_id = f"{filename[:-4]}-page{pagenum}-table{table_idx}"
                    
                    # 创建临时文件保存Excel
                    tmp_xlsx = None
                    try:
                        with NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_xlsx:
                            pandas_df.to_excel(tmp_xlsx.name, index=False)
                            # 上传Excel文件到Minio
                            xlsx_object_key = f"table_references/{filename[:-4]}/table{table_idx}-page{pagenum}.xlsx"
                            with open(tmp_xlsx.name, "rb") as f:
                                xlsx_result = await minio_client.upload_file(
                                    object_key=xlsx_object_key,
                                    data=f.read(),
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
                            df_xlsx_path = xlsx_result.get("url", "")
                    finally:
                        # 清理临时Excel文件
                        if tmp_xlsx and os.path.exists(tmp_xlsx.name):
                            os.remove(tmp_xlsx.name)

                    # 获取列名
                    try:
                        columns_str = ", ".join(str(col) for col in pandas_df.columns.tolist() if str(col) != "None")
                    except:
                        columns_str = "无法识别列名"

                    # 将表格数据转换为文本格式（参考process_excel_file的逻辑）
                    table_content = ""
                    for row_idx, row in pandas_df.iterrows():
                        for col_idx, cell in enumerate(row):
                            if cell is not None:
                                col_name = pandas_df.columns[col_idx] if col_idx < len(pandas_df.columns) else f"列{col_idx + 1}"
                                table_content += f"{col_name}: {cell}\n"
                        table_content += "\n"

                    # 构建表格的标题
                    caption = before_text.replace("\n", " ") + " ".join(tab.header.names) + after_text.replace("\n", " ")
                    
                    # 构建表格的元数据
                    table_metadata = {
                        "source": doc_id,
                        "dataframe": df_xlsx_path,
                        "caption": caption,
                        "type": "table",
                        "page_num": pagenum,
                        "file_name": filename,  # 被处理的原始文件名
                    }

                    # 构建表格文本内容
                    table_text = f"这是一个表格，标题是: {caption}\n表格的列名是: {columns_str}\n表格的内容是:\n{table_content}"
                    
                    # 构建文档对象
                    doc = Document(
                        text=table_text,
                        metadata=table_metadata,
                        id_=doc_id)
                    table_docs.append(doc)
                    
        except Exception as e:
            # 处理表格提取过程中出现的异常
            logger.error(f"Error during table extraction: {e}")

        return table_docs, table_bboxes, ongoing_tables

    @staticmethod
    async def parse_all_images(filename, page, pagenum, text_blocks):
        """
        从PDF页面中提取所有图像，并生成包含图像及其元数据的文档列表。
        优化版本：并行处理图像描述以提高性能。

        参数:
        - filename (str): PDF文件名。
        - page (fitz.Page): 当前处理的PDF页面对象。
        - pagenum (int): 页面编号。
        - text_blocks (list): 页面上的文本块列表。
        返回:
        - image_docs (list): 包含提取的图像及其元数据的文档列表。
        """

        image_docs = []  # 初始化存储图像文档的列表
        image_info_list = page.get_image_info(xrefs=True)  # 获取页面中所有图像的信息
        page_rect = page.rect  # 获取页面的矩形区域
        minio_client = get_minio_client()  # 获取MinioStorageClient实例

        # 第一步：收集所有有效图像信息并进行基本处理
        image_processing_tasks = []
        valid_images = []
        
        for image_info in image_info_list:
            xref = image_info['xref']  # 获取图像的XREF编号
            if xref == 0:
                continue  # 跳过无效的XREF编号

            img_bbox = fitz.Rect(image_info['bbox'])  # 获取图像的边界框
            # 过滤掉尺寸过小的图像
            if img_bbox.width < page_rect.width / 20 or img_bbox.height < page_rect.height / 20:
                continue

            extracted_image = page.parent.extract_image(xref)  # 提取图像数据
            image_data = extracted_image["image"]  # 获取图像的二进制数据

            # 为图像文件生成唯一名称
            image_name = f"image{xref}-page{pagenum}.png"
            object_key = f"image_references/{filename[:-4]}/{image_name}"

            # 存储图像基本信息
            valid_images.append({
                'xref': xref,
                'bbox': img_bbox,
                'image_data': image_data,
                'image_name': image_name,
                'object_key': object_key,
                'ext': extracted_image["ext"]
            })

        # 第二步：并行上传图像到Minio
        async def upload_image_to_minio(image_info):
            """上传单个图像到Minio"""
            try:
                result = await minio_client.upload_file(
                    object_key=image_info['object_key'],
                    data=image_info['image_data'],
                    mime=image_info['ext']
                )
                image_url = result.get("url", "")
                if not image_url:
                    logger.error(f"Failed to upload image {image_info['image_name']} to Minio")
                    return None
                
                return {
                    **image_info,
                    'image_url': image_url
                }
            except Exception as e:
                logger.error(f"Error uploading image {image_info['image_name']}: {e}")
                return None

        # 并行上传所有图像
        upload_tasks = [upload_image_to_minio(img_info) for img_info in valid_images]
        uploaded_images = await run_jobs(upload_tasks, show_progress=False, workers=5)
        
        # 过滤掉上传失败的图像
        uploaded_images = [img for img in uploaded_images if img is not None]

        # 第三步：并行处理图像描述（直接使用Minio URL，无需下载临时文件）
        async def process_image_description(image_info):
            """处理单个图像的描述"""
            try:
                # 获取图像周围的文本
                before_text, after_text = extract_text_around_item(text_blocks, image_info['bbox'], page.rect.height)

                # 直接使用Minio URL调用describe_image（无需下载临时文件）
                image_description = await describe_image(image_info['image_url'])

                return {
                    **image_info,
                    'before_text': before_text,
                    'after_text': after_text,
                    'image_description': image_description
                }
            except Exception as e:
                logger.error(f"Error processing image description for {image_info['image_name']}: {e}")
                return None

        # 并行处理所有图像描述
        if uploaded_images:
            logger.info(f"开始并行处理 {len(uploaded_images)} 个图像的描述...")
            description_tasks = [process_image_description(img_info) for img_info in uploaded_images]
            
            # 使用 run_jobs 进行并发处理
            try:
                processed_images = await run_jobs(description_tasks, show_progress=False, workers=5)
                # 过滤掉None结果
                processed_images = [img for img in processed_images if img is not None]
            except Exception as e:
                logger.error(f"批量处理图像描述时出错: {e}")
                processed_images = []
            
            logger.info(f"成功处理 {len(processed_images)} 个图像的描述")
        else:
            processed_images = []

        # 第四步：构建文档对象
        for image_info in processed_images:
            caption = image_info['before_text'].replace("\n", " ")
            
            # 生成唯一的文档ID
            doc_id = f"{filename[:-4]}-page{pagenum}-image{image_info['xref']}"
            
            image_metadata = {
                "source": doc_id,  # 图像来源
                "image": image_info['image_url'],  # 图像URL
                "caption": caption,  # 图像标题
                "type": "image",  # 图像类型
                "page_num": pagenum,  # 图像所在页码
                "file_name": filename,  # 被处理的原始文件名
            }
            
            # 创建图像文档
            image_text = "这是一张图像，标题是： " + caption + f"\n图像的描述是：{image_info['before_text']}\n" + image_info['image_description'] + f"\n{image_info['after_text']}"
            image_docs.append(Document(
                text=image_text,
                metadata=image_metadata,
                id_=doc_id))  # 添加图像文档到列表

        return image_docs  # 返回包含图像及其元数据的文档列表

    @staticmethod
    async def process_pdf_file(pdf_file):
        """
        处理 PDF 文件并提取文本、表格和图像。
        优化版本：页面级并行处理，显著提升多页PDF的处理性能。

        :param pdf_file: 表示要处理的 PDF 文件的文件对象。
        :return: 包含提取信息的 Document 对象列表。
        """
        # 尝试打开 PDF 文件
        try:
            f = fitz.open(filename=pdf_file, filetype="pdf")
        except Exception as e:
            logger.error(f"pdf文件打开发生错误: {e}")
            return []

        file_name = os.path.basename(pdf_file)
        total_pages = len(f)
        
        logger.info(f"开始处理PDF文件: {file_name}，共 {total_pages} 页，将并行处理")

        # 定义单页处理函数
        async def process_single_page(page_index):
            """处理单个页面的所有内容"""
            try:
                page = f[page_index]
                page_documents = []
                
                # 从页面中提取文本块
                text_blocks = [block for block in page.get_text("blocks", sort=True)
                              if MultiModalRAG.is_valid_text_block(block, page)]
                
                # 组织文本块以更好地分类
                grouped_text_blocks = process_text_blocks(text_blocks)

                # 并行处理页面内容：表格、图像、文本
                async def process_page_tables():
                    """处理页面表格"""
                    # 注意：ongoing_tables 在并行处理中无法共享状态，这里单独处理每页的表格
                    table_docs, table_bboxes, _ = await MultiModalRAG.parse_all_tables(
                        file_name, page, page_index, text_blocks, {}
                    )
                    return table_docs, table_bboxes

                async def process_page_images():
                    """处理页面图像"""
                    return await MultiModalRAG.parse_all_images(file_name, page, page_index, text_blocks)

                async def process_page_text(table_bboxes):
                    """处理页面文本"""
                    text_docs = []
                    for text_block_ctr, (heading_block, content) in enumerate(grouped_text_blocks, 1):
                        heading_bbox = fitz.Rect(heading_block[:4])
                        # 检查标题框是否与任何表格框相交
                        if not any(heading_bbox.intersects(table_bbox) for table_bbox in table_bboxes):
                            bbox = {"x1": heading_block[0], "y1": heading_block[1], "x2": heading_block[2], "x3": heading_block[3]}
                            
                            # 合并标题和内容以便更好地提取实体和关系
                            text_content = f"{heading_block[4]}\n{content}"
                            doc_id = f"{file_name[:-4]}-page{page_index}-block{text_block_ctr}"

                            # 创建文本文档
                            text_doc = Document(
                                text=text_content,
                                metadata={
                                    **bbox,
                                    "type": "text",
                                    "page_num": page_index,
                                    "source": doc_id,
                                    "file_name": file_name,
                                },
                                id_=doc_id
                            )
                            text_docs.append(text_doc)
                    return text_docs

                # 先处理表格和图像（可以并行）
                table_task = process_page_tables()
                image_task = process_page_images()
                
                # 等待表格和图像处理完成
                results = await run_jobs([table_task, image_task], show_progress=False, workers=2)
                (table_docs, table_bboxes), image_docs = results[0], results[1]
                
                # 处理文本（需要table_bboxes信息）
                text_docs = await process_page_text(table_bboxes)
                
                # 合并当前页面的所有文档
                page_documents.extend(table_docs)
                page_documents.extend(image_docs)
                page_documents.extend(text_docs)
                
                logger.debug(f"页面 {page_index + 1} 处理完成，生成 {len(page_documents)} 个文档")
                return page_documents
                
            except Exception as e:
                logger.error(f"处理页面 {page_index + 1} 时出错: {e}")
                return []

        # 创建所有页面的处理任务
        page_tasks = [process_single_page(i) for i in range(total_pages)]
        
        # 并行处理所有页面
        try:
            all_page_results = await run_jobs(page_tasks, show_progress=False, workers=3)
        except Exception as e:
            logger.error(f"PDF页面并行处理时出错: {e}")
            all_page_results = []

        # 合并所有页面的结果
        all_pdf_documents = []
        successful_pages = 0
        
        for page_index, page_result in enumerate(all_page_results):
            if page_result is None:
                logger.error(f"页面 {page_index + 1} 处理失败")
            elif isinstance(page_result, list):
                all_pdf_documents.extend(page_result)
                successful_pages += 1
            else:
                logger.warning(f"页面 {page_index + 1} 返回了意外的结果类型: {type(page_result)}")

        # 关闭 PDF 文件
        f.close()
        
        logger.info(f"PDF文件处理完成: {file_name}，成功处理 {successful_pages}/{total_pages} 页，生成 {len(all_pdf_documents)} 个文档")
        return all_pdf_documents

    @staticmethod
    async def process_powerpoint_content(file_path, orig_path, orig_name, original_ext):
        """
        统一的PowerPoint内容处理函数，适用于PPT和PPTX文件
        
        参数:
        - file_path: PPT或PPTX文件路径
        - orig_path: 原始文件路径
        - orig_name: 原始文件名
        - original_ext: 原始文件扩展名 (.ppt 或 .pptx)
        
        返回:
        - tuple: (处理后的文档列表, 临时文件列表, 转换PDF文件列表, 临时图像文件列表)
        """
        documents = []
        temp_files = []
        converted_pdf_files = []
        temp_image_files = []
        
        try:
            logger.info(f"统一PowerPoint处理: {orig_name} (扩展名: {original_ext})")
            
            # 直接转换为PDF（使用统一的转换线程池）
            pdf_path = await safe_convert_office_to_pdf_async(file_path)
            
            if pdf_path is None:
                # 转换失败，尝试使用纯文本模式处理
                logger.warning(f"PDF转换失败，使用纯文本模式处理PowerPoint文件: {orig_name}")
                from utils.rag_utils import process_ppt_without_libreoffice
                try:
                    ppt_documents = await process_ppt_without_libreoffice(file_path)
                    # 计算file_id
                    file_id = calculate_file_id(orig_name)
                    # 更新每个文档的元数据
                    for doc in ppt_documents:
                        doc.metadata["source"] = orig_path
                        doc.metadata["file_name"] = orig_name
                        doc.metadata["file_id"] = file_id
                        doc.metadata["converted_from"] = original_ext
                        doc.metadata["fallback_mode"] = "text_only"
                        doc.metadata["processing_method"] = "text_only"
                    documents.extend(ppt_documents)
                    logger.info(f"成功使用纯文本模式处理PowerPoint文件 {orig_name}，生成 {len(ppt_documents)} 个文档")
                    return documents, temp_files, converted_pdf_files, temp_image_files
                except Exception as e:
                    logger.error(f"纯文本模式处理PowerPoint文件也失败: {str(e)}")
                    return documents, temp_files, converted_pdf_files, temp_image_files
            
            # 转换成功，使用双重描述模式处理
            logger.success(f"PowerPoint文件转换完成: {orig_name} -> PDF")
            
            # 记录转换生成的PDF文件以便后续清理
            converted_pdf_files.append(pdf_path)
            
            # 使用双重描述（文本+图片）处理
            logger.info(f"使用双重描述模式处理PowerPoint文件: {orig_name}")
            
            # 将PDF转换为图像
            images_data = convert_pdf_to_images(pdf_path)
            
            # 将生成的图像文件添加到跟踪列表
            for image_path, page_num in images_data:
                temp_image_files.append(image_path)
            
            # 从PDF中提取文本信息（替代原来的extract_text_and_notes_from_ppt）
            pdf_text_blocks = MultiModalRAG.extract_text_from_pdf(pdf_path)
            
            processed_data = []
            minio_client = get_minio_client()
            
            # 第一步：收集所有页面的图像和文本信息
            slide_data = []
            for (image_path, page_num), page_text in zip(images_data, pdf_text_blocks):
                slide_data.append({
                    'image_path': image_path,
                    'page_num': page_num,
                    'page_text': page_text,
                })
            
            # 第二步：并行上传所有图像到Minio
            async def upload_slide_image(slide_info):
                """上传单个幻灯片图像到Minio"""
                try:
                    img_object_key = f"ppt_slides/{os.path.splitext(orig_name)[0]}/slide{slide_info['page_num']}.jpg"
                    
                    with open(slide_info['image_path'], "rb") as f:
                        img_result = await minio_client.upload_file(
                            object_key=img_object_key,
                            data=f.read(),
                            mime="image/jpeg"
                        )
                    
                    page_image_url = img_result.get("url", "")
                    if not page_image_url:
                        logger.error(f"Failed to upload slide image {slide_info['page_num']} to Minio")
                        return None
                    
                    return {
                        **slide_info,
                        'image_url': page_image_url
                    }
                except Exception as e:
                    logger.error(f"Error uploading slide image {slide_info['page_num']}: {e}")
                    return None
            
            # 并行上传所有幻灯片图像
            upload_tasks = [upload_slide_image(slide_info) for slide_info in slide_data]
            uploaded_slides = await run_jobs(upload_tasks, show_progress=False, workers=5)
            
            # 过滤掉上传失败的幻灯片
            uploaded_slides = [slide for slide in uploaded_slides if slide is not None]
            
            # 第三步：并行处理所有幻灯片图像描述（使用PowerPoint专用的描述方法）
            async def process_slide_description(slide_info):
                """处理单个幻灯片的图像描述"""
                try:
                    # 使用PowerPoint专用的图像描述方法
                    from utils.rag_utils import describe_powerpoint_slide
                    try:
                        image_description = await describe_powerpoint_slide(slide_info['image_url'])
                    except Exception as e:
                        logger.error(f"PowerPoint幻灯片图像描述失败: {e}")
                        image_description = "图像描述失败"
                    
                    return {
                        **slide_info,
                        'image_description': image_description
                    }
                except Exception as e:
                    logger.error(f"Error processing slide description for page {slide_info['page_num']}: {e}")
                    return None
            
            # 并行处理所有幻灯片的图像描述
            if uploaded_slides:
                logger.info(f"开始并行处理 {len(uploaded_slides)} 个幻灯片的图像描述...")
                description_tasks = [process_slide_description(slide_info) for slide_info in uploaded_slides]
                
                # 使用 run_jobs 进行并发处理
                try:
                    processed_slides = await run_jobs(description_tasks, show_progress=False, workers=5)
                    # 过滤掉None结果
                    processed_slides = [slide for slide in processed_slides if slide is not None]
                except Exception as e:
                    logger.error(f"批量处理幻灯片图像描述时出错: {e}")
                    processed_slides = []
                
                logger.info(f"成功处理 {len(processed_slides)} 个幻灯片的图像描述")
            else:
                processed_slides = []
            
            # 第四步：并行生成综合总结的文档内容
            async def process_slide_summary(slide_info):
                """处理单个幻灯片的内容总结"""
                try:
                    # 使用大模型对页面文本和图像描述进行综合总结
                    from utils.rag_utils import summarize_slide_content
                    try:
                        document_text = await summarize_slide_content(
                            slide_info['page_text'], 
                            slide_info['image_description']
                        )
                    except Exception as e:
                        logger.error(f"幻灯片内容总结失败: {e}")
                        # 如果总结失败，回退到原来的简单拼接方式
                        document_text = f"这是一张PowerPoint幻灯片: {slide_info['page_text']}\n\n图像描述: {slide_info['image_description']}"
                    
                    return {
                        **slide_info,
                        'document_text': document_text
                    }
                except Exception as e:
                    logger.error(f"Error processing slide summary for page {slide_info['page_num']}: {e}")
                    return None
            
            # 并行处理所有幻灯片的内容总结
            if processed_slides:
                logger.info(f"开始并行生成 {len(processed_slides)} 个幻灯片的综合总结...")
                summary_tasks = [process_slide_summary(slide_info) for slide_info in processed_slides]
                
                try:
                    summarized_slides = await run_jobs(summary_tasks, show_progress=False, workers=3)
                    # 过滤掉None结果
                    summarized_slides = [slide for slide in summarized_slides if slide is not None]
                except Exception as e:
                    logger.error(f"批量生成幻灯片总结时出错: {e}")
                    summarized_slides = []
                
                logger.info(f"成功生成 {len(summarized_slides)} 个幻灯片的综合总结")
            else:
                summarized_slides = []
            
            # 第五步：构建文档对象
            for slide_info in summarized_slides:
                # 生成文档ID
                doc_id = f"{os.path.splitext(orig_name)[0]}-slide{slide_info['page_num']}"
                
                # 计算file_id
                file_id = calculate_file_id(orig_name)
                
                # 构建图像元数据（优化：移除过长的caption，避免元数据超长）
                image_metadata = {
                    "source": orig_path,
                    "image": slide_info['image_url'],
                    "type": "image",
                    "page_num": slide_info['page_num'],
                    "file_name": orig_name,
                    "file_id": file_id,
                    "converted_from": original_ext,
                    "processing_method": "unified_pdf_extraction_with_ai_summary"
                }
                
                # 使用AI生成的综合总结作为文档文本
                processed_data.append(
                    Document(
                        text=slide_info['document_text'],
                        metadata=image_metadata,
                        id_=doc_id
                    )
                )
            
            documents.extend(processed_data)
            logger.info(f"成功处理PowerPoint文件 {orig_name}，生成 {len(processed_data)} 个统一PDF提取文档")
            
        except Exception as e:
            logger.error(f"处理PowerPoint文件内容时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        return documents, temp_files, converted_pdf_files, temp_image_files

    @staticmethod
    def extract_text_from_pdf(pdf_path):
        """
        从PDF文件中提取每一页的文本内容
        
        参数:
        - pdf_path: PDF文件路径
        
        返回:
        - list: 每一页的文本内容列表
        """
        try:
            doc = fitz.open(pdf_path)
            page_texts = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                # 提取页面文本，保持基本格式
                page_text = page.get_text()
                
                # 清理和格式化文本
                if page_text.strip():
                    # 移除过多的空白行，但保留基本的段落结构
                    lines = page_text.split('\n')
                    cleaned_lines = []
                    for line in lines:
                        cleaned_line = line.strip()
                        if cleaned_line:  # 保留非空行
                            cleaned_lines.append(cleaned_line)
                    
                    page_text = '\n'.join(cleaned_lines)
                else:
                    page_text = ""
                
                page_texts.append(page_text)
            
            doc.close()
            return page_texts
            
        except Exception as e:
            logger.error(f"从PDF提取文本时出错: {e}")
            return []

    @staticmethod
    async def process_word_file(word_file):
        """
        处理Word文件并提取文本、图像等内容。
        参数:
        - word_file (str): Word文件的路径。

        返回:
        - tuple: (文档列表, 临时文件列表)
        """
        documents = []
        temp_files_created = []  # 跟踪创建的临时文件
        minio_client = get_minio_client()

        try:
            # 文件路径已经是本地文件路径
            local_file_path = word_file

            # 确保文件存在
            if not os.path.exists(local_file_path):
                logger.error(f"Word文件不存在: {local_file_path}")
                return []

            logger.info(f"开始处理Word文件: {local_file_path}")
            doc = DocxDocument(local_file_path)
            file_name = os.path.basename(word_file)

            # 提取全部文本
            full_text = ""
            for para in doc.paragraphs:
                full_text += para.text + "\n"

            # 提取表格内容
            tables_text = []
            for i, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text)
                    table_data.append(row_data)

                # 将表格数据转换为pandas DataFrame
                df = pd.DataFrame(table_data)
                if len(df) > 0:  # 确保表格非空
                    # 将第一行作为列名
                    df.columns = df.iloc[0]
                    df = df.iloc[1:]

                    # 创建临时文件保存Excel
                    with NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_xlsx:
                        df.to_excel(tmp_xlsx.name, index=False)
                        temp_files_created.append(tmp_xlsx.name)  # 添加到临时文件跟踪列表
                        # 上传Excel文件到Minio
                        xlsx_object_key = f"word_tables/{file_name[:-5]}/table{i + 1}.xlsx"
                        with open(tmp_xlsx.name, "rb") as f:
                            xlsx_result = await minio_client.upload_file(
                                object_key=xlsx_object_key,
                                data=f.read(),
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            )
                        df_xlsx_path = xlsx_result.get("url", "")

                    # 获取列名字符串
                    columns_str = ", ".join(df.columns.tolist())

                    # 将表格转换为文本格式
                    table_text = f"表格 {i + 1}:\n"
                    for row_idx, row in df.iterrows():
                        for col_idx, cell in enumerate(row):
                            table_text += f"{df.columns[col_idx]}: {cell}\n"
                        table_text += "\n"

                    tables_text.append(table_text)
                    
                    # 创建表格文档ID
                    table_doc_id = f"{file_name[:-5]}-table{i+1}"
                    
                    # 创建表格文档
                    table_metadata = {
                        "source": table_doc_id,
                        "dataframe": df_xlsx_path,
                        "type": "table",
                        "file_name": file_name,
                    }
                    
                    table_doc_text = f"这是一个Word文档中的表格。\n表格的列名是: {columns_str}\n表格的内容是:\n{table_text}"
                    table_doc = Document(
                        text=table_doc_text,
                        metadata=table_metadata,
                        id_=table_doc_id
                    )
                    documents.append(table_doc)

            # 提取图片（通过将Word转换为PDF然后处理）
            # 注意：这一步需要根据实际情况实现，这里只是一个简化版
            # 可能需要额外的工具或库来实现完整功能
            
            # 创建主文档ID
            main_doc_id = file_name[:-5]
            
            # 计算file_id
            file_id = calculate_file_id(file_name)
            
            # 创建主文档（文本内容）
            main_doc_metadata = {
                "source": main_doc_id,
                "type": "text",
                "file_name": file_name,
                "file_id": file_id,  # 添加基于file_name的file_id
            }

            main_doc = Document(
                text=full_text,
                metadata=main_doc_metadata,
                id_=main_doc_id
            )
            documents.append(main_doc)

            return documents, temp_files_created

        except Exception as e:
            print(f"处理Word文档出错: {e}")
            import traceback
            traceback.print_exc()
            return [], []

    @staticmethod
    async def process_excel_file(excel_file):
        """
        处理Excel文件并提取表格内容。
        参数:
        - excel_file (str): Excel文件的路径。

        返回:
        - tuple: (文档列表, 临时文件列表)
        """
        documents = []
        temp_files_created = []  # 跟踪创建的临时文件
        minio_client = get_minio_client()

        try:
            # 文件路径已经是本地文件路径
            local_file_path = excel_file

            # 确保文件存在
            if not os.path.exists(local_file_path):
                print(f"Excel文件不存在: {local_file_path}")
                return []

            print(f"开始处理Excel文件: {local_file_path}")
            file_name = os.path.basename(excel_file)
            workbook = load_workbook(local_file_path, data_only=True)

            # 遍历每个工作表
            for sheet_idx, sheet_name in enumerate(workbook.sheetnames):
                sheet = workbook[sheet_name]

                # 将工作表数据转换为二维列表
                data = []
                for row in sheet.iter_rows(values_only=True):
                    data.append(list(row))

                # 转换为pandas DataFrame进行处理
                if len(data) > 0:  # 确保表格非空
                    df = pd.DataFrame(data)
                    # 将第一行作为列名
                    if len(df) > 1:
                        df.columns = df.iloc[0]
                        df = df.iloc[1:]
                    
                    # 创建工作表文档ID
                    sheet_doc_id = f"{file_name[:-5]}-sheet{sheet_idx+1}_{sheet_name}"

                    # 将表格转换为文本格式
                    sheet_text = f"工作表名称: {sheet_name}\n"

                    # 如果DataFrame非空，则处理数据
                    if not df.empty:
                        # 创建临时文件保存Excel
                        with NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_xlsx:
                            df.to_excel(tmp_xlsx.name, index=False)
                            temp_files_created.append(tmp_xlsx.name)  # 添加到临时文件跟踪列表
                            # 上传Excel文件到Minio
                            xlsx_object_key = f"excel_sheets/{file_name[:-5]}/sheet{sheet_idx + 1}_{sheet_name}.xlsx"
                            with open(tmp_xlsx.name, "rb") as f:
                                xlsx_result = await minio_client.upload_file(
                                    object_key=xlsx_object_key,
                                    data=f.read(),
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
                            df_xlsx_path = xlsx_result.get("url", "")

                        # 获取列名列表
                        try:
                            columns_str = ", ".join(str(col) for col in df.columns.tolist() if str(col) != "None")
                        except:
                            columns_str = "无法识别列名"

                        # 将表格数据转换为文本
                        for row_idx, row in df.iterrows():
                            for col_idx, cell in enumerate(row):
                                if cell is not None:
                                    col_name = df.columns[col_idx] if col_idx < len(df.columns) else f"列{col_idx + 1}"
                                    sheet_text += f"{col_name}: {cell}\n"
                            sheet_text += "\n"

                        # 创建工作表文档
                        sheet_metadata = {
                            "source": sheet_doc_id,
                            "dataframe": df_xlsx_path,
                            "type": "table",
                            "file_name": file_name,
                        }
                        
                        # 创建完整的文档文本
                        document_text = f"这是Excel文件中的工作表。\n工作表名称: {sheet_name}\n表格的列名是: {columns_str}\n表格的内容是:\n{sheet_text}"
                        
                        # 创建工作表文档
                        sheet_doc = Document(
                            text=document_text,
                            metadata=sheet_metadata,
                            id_=sheet_doc_id
                        )
                        documents.append(sheet_doc)

            return documents, temp_files_created

        except Exception as e:
            print(f"处理Excel文件出错: {e}")
            import traceback
            traceback.print_exc()
            return [], []

    async def _process_image_file(self, file_path, original_file_path, original_file_name):
        """处理图像文件"""
        # 借助多模态大模型获取图片的解读
        # 建议多维度描述该图片，可以尝试多次调用
        image_text = await describe_image(file_path)
        # 计算file_id
        file_id = calculate_file_id(original_file_name)

        doc = Document(
            text=image_text,
            metadata={
                "source": original_file_path, 
                "type": "image", 
                "image": original_file_path,
                "file_name": original_file_name,
                "file_id": file_id,  # 添加基于orig_name的file_id
            },
            id_=os.path.basename(original_file_path)
        )
        return [doc], [], [], []

    async def _process_powerpoint_converted_pdf(self, file_path, original_file_path, original_file_name):
        """处理由PowerPoint转换而来的PDF文件，使用双重描述模式"""
        try:
            logger.info(f"检测到PowerPoint转换的PDF文件，使用双重描述模式处理: {original_file_name}")
            
            # 从文件名推断原始PowerPoint文件的扩展名
            # 文件名格式: "filename_from_powerpoint.pdf"
            base_name = original_file_name.replace("_from_powerpoint.pdf", "")
            
            # 推断原始扩展名（假设为.pptx，如果需要更精确识别可以在转换时传递更多信息）
            original_ext = ".pptx"  # 默认假设为PPTX
            
            # 将PDF转换为图像进行双重描述处理
            images_data = convert_pdf_to_images(file_path)
            
            # 从PDF中提取文本信息
            pdf_text_blocks = MultiModalRAG.extract_text_from_pdf(file_path)
            
            processed_data = []
            minio_client = get_minio_client()
            temp_image_files = []
            
            # 收集所有页面的图像和文本信息
            slide_data = []
            for (image_path, page_num), page_text in zip(images_data, pdf_text_blocks):
                temp_image_files.append(image_path)
                slide_data.append({
                    'image_path': image_path,
                    'page_num': page_num,
                    'page_text': page_text,
                })
            
            # 并行上传所有图像到Minio
            async def upload_slide_image(slide_info):
                """上传单个幻灯片图像到Minio"""
                try:
                    img_object_key = f"ppt_slides/{base_name}/slide{slide_info['page_num']}.jpg"
                    
                    with open(slide_info['image_path'], "rb") as f:
                        img_result = await minio_client.upload_file(
                            object_key=img_object_key,
                            data=f.read(),
                            mime="image/jpeg"
                        )
                    
                    page_image_url = img_result.get("url", "")
                    if not page_image_url:
                        logger.error(f"Failed to upload slide image {slide_info['page_num']} to Minio")
                        return None
                    
                    return {
                        **slide_info,
                        'image_url': page_image_url
                    }
                except Exception as e:
                    logger.error(f"Error uploading slide image {slide_info['page_num']}: {e}")
                    return None
            
            # 并行上传所有幻灯片图像
            upload_tasks = [upload_slide_image(slide_info) for slide_info in slide_data]
            uploaded_slides = await run_jobs(upload_tasks, show_progress=False, workers=5)
            
            # 过滤掉上传失败的幻灯片
            uploaded_slides = [slide for slide in uploaded_slides if slide is not None]
            
            # 并行处理所有幻灯片图像描述（使用PowerPoint专用的描述方法）
            async def process_slide_description(slide_info):
                """处理单个幻灯片的图像描述"""
                try:
                    # 使用PowerPoint专用的图像描述方法
                    from utils.rag_utils import describe_powerpoint_slide
                    try:
                        image_description = await describe_powerpoint_slide(slide_info['image_url'])
                    except Exception as e:
                        logger.error(f"PowerPoint幻灯片图像描述失败: {e}")
                        image_description = "图像描述失败"
                    
                    return {
                        **slide_info,
                        'image_description': image_description
                    }
                except Exception as e:
                    logger.error(f"Error processing slide description for page {slide_info['page_num']}: {e}")
                    return None
            
            # 并行处理所有幻灯片的图像描述
            if uploaded_slides:
                logger.info(f"开始并行处理 {len(uploaded_slides)} 个幻灯片的图像描述...")
                description_tasks = [process_slide_description(slide_info) for slide_info in uploaded_slides]
                
                # 使用 run_jobs 进行并发处理
                try:
                    processed_slides = await run_jobs(description_tasks, show_progress=False, workers=5)
                    # 过滤掉None结果
                    processed_slides = [slide for slide in processed_slides if slide is not None]
                except Exception as e:
                    logger.error(f"批量处理幻灯片图像描述时出错: {e}")
                    processed_slides = []
                
                logger.info(f"成功处理 {len(processed_slides)} 个幻灯片的图像描述")
            else:
                processed_slides = []
            
            # 并行生成综合总结的文档内容
            async def process_slide_summary(slide_info):
                """处理单个幻灯片的内容总结"""
                try:
                    # 使用大模型对页面文本和图像描述进行综合总结
                    from utils.rag_utils import summarize_slide_content
                    try:
                        document_text = await summarize_slide_content(
                            slide_info['page_text'], 
                            slide_info['image_description']
                        )
                    except Exception as e:
                        logger.error(f"幻灯片内容总结失败: {e}")
                        # 如果总结失败，回退到原来的简单拼接方式
                        document_text = f"这是一张PowerPoint幻灯片（通过PDF处理）: {slide_info['page_text']}\n\n图像描述: {slide_info['image_description']}"
                    
                    return {
                        **slide_info,
                        'document_text': document_text
                    }
                except Exception as e:
                    logger.error(f"Error processing slide summary for page {slide_info['page_num']}: {e}")
                    return None
            
            # 并行处理所有幻灯片的内容总结
            if processed_slides:
                logger.info(f"开始并行生成 {len(processed_slides)} 个幻灯片的综合总结...")
                summary_tasks = [process_slide_summary(slide_info) for slide_info in processed_slides]
                
                try:
                    summarized_slides = await run_jobs(summary_tasks, show_progress=False, workers=3)
                    # 过滤掉None结果
                    summarized_slides = [slide for slide in summarized_slides if slide is not None]
                except Exception as e:
                    logger.error(f"批量生成幻灯片总结时出错: {e}")
                    summarized_slides = []
                
                logger.info(f"成功生成 {len(summarized_slides)} 个幻灯片的综合总结")
            else:
                summarized_slides = []
            
            # 构建文档对象
            for slide_info in summarized_slides:
                # 生成文档ID
                doc_id = f"{base_name}-slide{slide_info['page_num']}"
                
                # 计算file_id
                file_id = calculate_file_id(original_file_name)
                
                # 构建图像元数据（优化：移除过长的caption，避免元数据超长）
                image_metadata = {
                    "source": original_file_path,
                    "image": slide_info['image_url'],
                    "type": "image",
                    "page_num": slide_info['page_num'],
                    "file_name": original_file_name,
                    "file_id": file_id,
                    "converted_from": original_ext,
                    "processing_method": "powerpoint_pdf_dual_description_with_ai_summary",
                    "powerpoint_origin": True
                }
                
                # 使用AI生成的综合总结作为文档文本
                processed_data.append(
                    Document(
                        text=slide_info['document_text'],
                        metadata=image_metadata,
                        id_=doc_id
                    )
                )
            
            logger.success(f"成功使用双重描述模式处理PowerPoint转换PDF {original_file_name}，生成 {len(processed_data)} 个文档")
            return processed_data, [], [], temp_image_files
            
        except Exception as e:
            logger.error(f"Error processing PowerPoint converted PDF {original_file_name}: {e}")
            import traceback
            traceback.print_exc()
            return [], [], [], []

    async def _process_pdf_file(self, file_path, original_file_path, original_file_name):
        """处理PDF文件，自动识别是否为PowerPoint转换而来"""
        try:
            # 检查是否为PowerPoint转换的PDF（通过文件名标识识别）
            if "_from_powerpoint.pdf" in original_file_name.lower():
                logger.info(f"识别到PowerPoint转换PDF，切换到双重描述模式: {original_file_name}")
                return await self._process_powerpoint_converted_pdf(file_path, original_file_path, original_file_name)
            
            # 普通PDF文件处理
            logger.info(f"处理普通PDF文件: {original_file_name}")
            pdf_documents = await MultiModalRAG.process_pdf_file(file_path)
            
            # 计算file_id
            file_id = calculate_file_id(original_file_name)
            
            # 更新每个文档的元数据，使用原始文件名
            for doc in pdf_documents:
                doc.metadata["source"] = doc.metadata["source"].replace(os.path.basename(file_path)[:-4],
                                                                        original_file_name[:-4])
                doc.metadata["file_name"] = original_file_name
                doc.metadata["file_id"] = file_id
                doc.metadata["processing_method"] = "native_pdf"  # 标记为原生PDF处理
            
            return pdf_documents, [], [], []
        except Exception as e:
            logger.error(f"Error processing PDF {original_file_name}: {e}")
            return [], [], [], []

    async def _process_doc_file(self, file_path, original_file_path, original_file_name):
        """处理DOC文件"""
        try:
            logger.info(f"检测到DOC文件，尝试转换为DOCX: {original_file_name}")
            
            # 尝试将DOC转换为DOCX（使用统一的转换线程池）
            docx_path = await safe_convert_doc_to_docx_async(file_path)
            
            if docx_path is not None:
                # 转换成功，使用现有的DOCX处理逻辑
                logger.success(f"DOC转DOCX成功，使用DOCX处理流程: {original_file_name}")
                
                # 直接调用现有的DOCX处理方法
                word_documents, word_temp_files = await MultiModalRAG.process_word_file(docx_path)
                
                # 计算file_id
                file_id = calculate_file_id(original_file_name)
                
                # 更新每个文档的元数据，使用原始DOC文件信息
                for doc in word_documents:
                    doc.metadata["source"] = original_file_path
                    doc.metadata["file_name"] = original_file_name  # 使用原始DOC文件名
                    doc.metadata["file_id"] = file_id
                    doc.metadata["converted_from"] = ".doc"
                    doc.metadata["docx_path"] = docx_path
                    doc.metadata["processing_method"] = "doc_to_docx"  # 标记为DOC转DOCX处理
                
                logger.info(f"成功通过DOCX处理DOC文件 {original_file_name}，生成 {len(word_documents)} 个文档")
                
                # 将临时转换的DOCX文件添加到temp_files中
                temp_files_with_docx = word_temp_files + [docx_path]
                
                return word_documents, temp_files_with_docx, [], []
            else:
                # DOC转DOCX失败，降级到PDF转换
                logger.warning("DOC转DOCX失败，降级使用PDF转换方式")
                pdf_path = await safe_convert_office_to_pdf_async(file_path)
                
                if pdf_path is None:
                    logger.error(f"无法处理DOC文件 {original_file_name}，跳过处理")
                    return [], [], [], []
                
                # 使用PDF处理方式
                pdf_documents = await MultiModalRAG.process_pdf_file(pdf_path)
                
                # 计算file_id
                file_id = calculate_file_id(original_file_name)
                
                # 更新每个文档的元数据
                for doc in pdf_documents:
                    doc.metadata["source"] = original_file_path
                    doc.metadata["file_name"] = original_file_name
                    doc.metadata["file_id"] = file_id
                    doc.metadata["converted_from"] = ".doc"
                    doc.metadata["pdf_path"] = pdf_path
                    doc.metadata["processing_method"] = "fallback_pdf"  # 标记为降级PDF处理
                
                logger.info(f"使用PDF转换方式处理DOC文件 {original_file_name}，生成 {len(pdf_documents)} 个文档")
                
                return pdf_documents, [], [pdf_path], []
                
        except Exception as e:
            logger.error(f"Error processing DOC file {original_file_name}: {e}")
            import traceback
            traceback.print_exc()
            return [], [], [], []

    async def _process_ppt_file(self, file_path, original_file_path, original_file_name):
        """处理PPT文件"""
        try:
            logger.info(f"检测到PPT文件，使用统一PDF处理模式: {original_file_name}")
            
            # 使用统一的PowerPoint处理逻辑，直接转换为PDF
            documents, temp_files, converted_pdf_files, temp_image_files = await MultiModalRAG.process_powerpoint_content(
                file_path, original_file_path, original_file_name, ".ppt"
            )
            
            return documents, temp_files, converted_pdf_files, temp_image_files
                
        except Exception as e:
            logger.error(f"Error processing PPT file {original_file_name}: {e}")
            import traceback
            traceback.print_exc()
            return [], [], [], []

    async def _process_pptx_file(self, file_path, original_file_path, original_file_name):
        """处理PPTX文件"""
        try:
            logger.info(f"检测到PPTX文件，使用统一PDF处理模式: {original_file_name}")
            
            # 使用统一的PowerPoint处理逻辑，直接转换为PDF
            documents, temp_files, converted_pdf_files, temp_image_files = await MultiModalRAG.process_powerpoint_content(
                file_path, original_file_path, original_file_name, ".pptx"
            )
            
            return documents, temp_files, converted_pdf_files, temp_image_files
            
        except Exception as e:
            logger.error(f"Error processing PPTX file {original_file_name}: {e}")
            import traceback
            traceback.print_exc()
            return [], [], [], []

    async def _process_xls_file(self, file_path, original_file_path, original_file_name):
        """处理XLS文件"""
        try:
            logger.info(f"检测到Excel文件，使用原生处理模式: {original_file_name}")
            
            try:
                # 首先尝试直接处理为Excel格式
                import pandas as pd
                
                # 读取XLS文件的所有工作表
                try:
                    # 先不指定header，读取所有数据
                    xls_data_raw = pd.read_excel(file_path, sheet_name=None, engine='xlrd', header=None)
                    xls_data = {}
                    
                    # 处理每个工作表，智能识别列名
                    for sheet_name, df_raw in xls_data_raw.items():
                        logger.info(f"处理工作表: {sheet_name}, 原始数据形状: {df_raw.shape}")
                        
                        # 寻找有效的列名行
                        header_row = 0
                        valid_header_found = False
                        
                        # 检查前5行，寻找有效的列名
                        for row_idx in range(min(5, len(df_raw))):
                            row_data = df_raw.iloc[row_idx]
                            # 检查这一行是否包含有效的非空值
                            non_null_count = row_data.notna().sum()
                            if non_null_count > 0:
                                # 检查是否包含文本内容（可能的列名）
                                text_count = sum(1 for val in row_data if isinstance(val, str) and val.strip())
                                if text_count > 0 or non_null_count >= len(df_raw.columns) * 0.5:
                                    header_row = row_idx
                                    valid_header_found = True
                                    logger.info(f"找到有效列名行: 第{row_idx + 1}行, 非空值数量: {non_null_count}")
                                    break
                        
                        # 重新读取数据，使用找到的列名行
                        if valid_header_found:
                            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd', header=header_row, skiprows=0)
                        else:
                            # 没找到有效列名，使用默认列名
                            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd', header=None)
                            # 创建默认列名
                            df.columns = [f"列{i+1}" for i in range(len(df.columns))]
                            logger.info(f"未找到有效列名，使用默认列名: {list(df.columns)}")
                        
                        # 进一步清理列名
                        new_columns = []
                        for i, col in enumerate(df.columns):
                            if pd.isna(col) or str(col).strip() in ['', 'nan', 'None']:
                                new_columns.append(f"列{i+1}")
                            else:
                                new_columns.append(str(col).strip())
                        df.columns = new_columns
                        
                        # 删除完全为空的行
                        df = df.dropna(how='all')
                        
                        xls_data[sheet_name] = df
                        logger.info(f"工作表 {sheet_name} 处理完成, 最终列名: {list(df.columns)}")
                        
                except Exception as e:
                    logger.warning(f"使用xlrd引擎读取失败，尝试openpyxl引擎: {e}")
                    # 如果xlrd失败，尝试其他方法
                    try:
                        from openpyxl import load_workbook
                        workbook = load_workbook(file_path, data_only=True)
                        xls_data = {}
                        for sheet_name in workbook.sheetnames:
                            sheet = workbook[sheet_name]
                            data = []
                            for row in sheet.iter_rows(values_only=True):
                                data.append(list(row))
                            if data:
                                df = pd.DataFrame(data)
                                # 智能处理列名
                                if len(data) > 0:
                                    # 使用第一行作为列名，如果第一行无效则使用默认列名
                                    first_row = data[0]
                                    valid_cols = []
                                    for i, val in enumerate(first_row):
                                        if val is not None and str(val).strip():
                                            valid_cols.append(str(val).strip())
                                        else:
                                            valid_cols.append(f"列{i+1}")
                                    
                                    if len(data) > 1:
                                        df = pd.DataFrame(data[1:], columns=valid_cols)
                                    else:
                                        df = pd.DataFrame(data, columns=valid_cols)
                                else:
                                    df.columns = [f"列{i+1}" for i in range(len(df.columns))]
                                xls_data[sheet_name] = df
                                logger.info(f"使用openpyxl处理工作表 {sheet_name}, 列名: {list(df.columns)}")
                    except Exception as e2:
                        logger.error(f"openpyxl引擎也失败: {e2}")
                        raise e2
                
                documents_local = []
                minio_client = get_minio_client()
                temp_files_local = []
                
                # 处理每个工作表
                for sheet_idx, (sheet_name, df) in enumerate(xls_data.items()):
                    if df.empty:
                        continue
                    
                    # 注意：列名已经在上面的智能识别过程中处理过了，不需要再次设置
                    logger.info(f"开始处理工作表 {sheet_name}, 数据形状: {df.shape}, 列名: {list(df.columns)}")
                    
                    # 创建工作表文档ID
                    sheet_doc_id = f"{os.path.splitext(original_file_name)[0]}-sheet{sheet_idx+1}_{sheet_name}"
                    
                    # 创建临时文件保存Excel - 修复Windows文件锁定问题
                    import tempfile
                    import time
                    
                    # 创建临时文件
                    temp_fd, temp_xlsx_path = tempfile.mkstemp(suffix=".xlsx")
                    try:
                        # 关闭文件描述符，避免文件锁定
                        os.close(temp_fd)
                        
                        # 将DataFrame保存到临时Excel文件
                        df.to_excel(temp_xlsx_path, index=False)
                        
                        # 确保文件写入完成
                        time.sleep(0.1)  # 短暂延迟确保文件写入完成
                        
                        # 上传Excel文件到Minio
                        xlsx_object_key = f"xls_native/{os.path.splitext(original_file_name)[0]}/sheet{sheet_idx + 1}_{sheet_name}.xlsx"
                        
                        # 安全地读取文件并上传
                        max_retries = 3
                        for retry in range(max_retries):
                            try:
                                with open(temp_xlsx_path, "rb") as f:
                                    file_data = f.read()
                                
                                xlsx_result = await minio_client.upload_file(
                                    object_key=xlsx_object_key,
                                    data=file_data,
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
                                df_xlsx_path = xlsx_result.get("url", "")
                                break  # 成功则跳出重试循环
                            except (PermissionError, OSError) as e:
                                if retry < max_retries - 1:
                                    logger.warning(f"文件访问失败，重试 {retry + 1}/{max_retries}: {e}")
                                    time.sleep(0.2 * (retry + 1))  # 递增延迟
                                else:
                                    logger.error(f"文件访问失败，已达最大重试次数: {e}")
                                    raise e
                    finally:
                        # 将临时文件添加到跟踪列表，由统一清理负责
                        temp_files_local.append(temp_xlsx_path)
                    
                    # 获取列名列表 - 确保不包含nan或空值
                    try:
                        valid_columns = []
                        for col in df.columns.tolist():
                            col_str = str(col).strip()
                            if col_str and col_str.lower() not in ['nan', 'none', '']:
                                valid_columns.append(col_str)
                            else:
                                # 如果列名无效，使用位置编号
                                col_index = df.columns.get_loc(col) + 1
                                valid_columns.append(f"列{col_index}")
                        columns_str = ", ".join(valid_columns)
                        logger.info(f"工作表 {sheet_name} 有效列名: {columns_str}")
                    except Exception as e:
                        logger.warning(f"获取列名失败: {e}")
                        columns_str = ", ".join([f"列{i+1}" for i in range(len(df.columns))])
                    
                    # 将表格数据转换为文本（保持完整性，不分页）
                    sheet_text = f"工作表名称: {sheet_name}\n"
                    
                    # 限制显示的行数以避免过长的文本
                    max_rows_to_show = 100  # 最多显示100行数据
                    rows_to_process = df.head(max_rows_to_show) if len(df) > max_rows_to_show else df
                    
                    for row_idx, row in rows_to_process.iterrows():
                        for col_idx, cell in enumerate(row):
                            if cell is not None and str(cell).strip():
                                # 获取有效的列名
                                if col_idx < len(df.columns):
                                    original_col_name = str(df.columns[col_idx]).strip()
                                    if original_col_name and original_col_name.lower() not in ['nan', 'none', '']:
                                        col_name = original_col_name
                                    else:
                                        col_name = f"列{col_idx + 1}"
                                else:
                                    col_name = f"列{col_idx + 1}"
                                sheet_text += f"{col_name}: {cell}\n"
                        sheet_text += "\n"
                    
                    # 如果数据被截断，添加提示
                    if len(df) > max_rows_to_show:
                        sheet_text += f"... （注：此工作表共有 {len(df)} 行数据，仅显示前 {max_rows_to_show} 行）\n"
                    
                    # 计算file_id
                    file_id = calculate_file_id(original_file_name)
                    
                    # 创建工作表文档
                    sheet_metadata = {
                        "source": original_file_path,
                        "dataframe": df_xlsx_path,
                        "type": "table",
                        "file_name": original_file_name,
                        "file_id": file_id,
                        "sheet_name": sheet_name,
                        "total_rows": len(df),
                        "total_columns": len(df.columns),
                        "processing_method": "native_xls"  # 标记为原生XLS处理
                    }
                    
                    # 创建完整的文档文本
                    document_text = f"这是Excel文件中的工作表，保持了完整的表格结构。\n工作表名称: {sheet_name}\n表格的列名是: {columns_str}\n表格总行数: {len(df)}\n表格总列数: {len(df.columns)}\n表格的内容是:\n{sheet_text}"
                    
                    # 创建工作表文档
                    sheet_doc = Document(
                        text=document_text,
                        metadata=sheet_metadata,
                        id_=sheet_doc_id
                    )
                    documents_local.append(sheet_doc)
                
                logger.success(f"成功原生处理Excel文件 {original_file_name}，生成 {len(documents_local)} 个完整工作表文档")
                return documents_local, temp_files_local, [], []
                
            except Exception as e:
                logger.warning(f"原生处理XLS文件失败: {e}")
                logger.info("尝试使用PDF转换方式处理XLS文件")
                
                # 如果原生处理失败，降级到PDF转换
                pdf_path = await safe_convert_office_to_pdf_async(file_path)
                if pdf_path is None:
                    logger.error(f"无法处理XLS文件 {original_file_name}，跳过处理")
                    return [], [], [], []
                
                # 使用PDF处理方式
                pdf_documents = await MultiModalRAG.process_pdf_file(pdf_path)
                
                # 计算file_id
                file_id = calculate_file_id(original_file_name)
                
                # 更新每个文档的元数据
                for doc in pdf_documents:
                    doc.metadata["source"] = original_file_path
                    doc.metadata["file_name"] = original_file_name
                    doc.metadata["file_id"] = file_id
                    doc.metadata["converted_from"] = ".xls"
                    doc.metadata["pdf_path"] = pdf_path
                    doc.metadata["processing_method"] = "fallback_pdf"  # 标记为降级PDF处理
                
                logger.info(f"使用PDF转换方式处理XLS文件 {original_file_name}，生成 {len(pdf_documents)} 个文档")
                
                return pdf_documents, [], [pdf_path], []
                
        except Exception as e:
            logger.error(f"Error processing Excel file {original_file_name}: {e}")
            import traceback
            traceback.print_exc()
            return [], [], [], []

    async def _process_docx_file(self, file_path, original_file_path, original_file_name):
        """处理DOCX文件"""
        try:
            word_documents, word_temp_files = await MultiModalRAG.process_word_file(file_path)
            # 计算file_id
            file_id = calculate_file_id(original_file_name)
            # 更新每个文档的元数据
            for doc in word_documents:
                doc.metadata["source"] = original_file_path
                doc.metadata["file_name"] = original_file_name
                doc.metadata["file_id"] = file_id
            return word_documents, word_temp_files, [], []
        except Exception as e:
            logger.error(f"Error processing Word {original_file_name}: {e}")
            return [], [], [], []

    async def _process_xlsx_file(self, file_path, original_file_path, original_file_name):
        """处理XLSX文件"""
        try:
            excel_documents, excel_temp_files = await MultiModalRAG.process_excel_file(file_path)
            # 计算file_id
            file_id = calculate_file_id(original_file_name)
            # 更新每个文档的元数据
            for doc in excel_documents:
                doc.metadata["source"] = original_file_path
                doc.metadata["file_name"] = original_file_name
                doc.metadata["file_id"] = file_id
            return excel_documents, excel_temp_files, [], []
        except Exception as e:
            logger.error(f"Error processing Excel {original_file_name}: {e}")
            return [], [], [], []

    async def _process_text_file(self, file_path, original_file_path, original_file_name):
        """处理文本文件"""
        try:
            with open(file_path, "rb") as file:
                text = file.read().decode("utf-8")
                # 计算file_id
                file_id = calculate_file_id(original_file_name)
                doc = Document(
                    text=text,
                    metadata={
                        "source": original_file_path, 
                        "type": "text", 
                        "file_name": original_file_name,
                        "file_id": file_id,  # 添加基于orig_name的file_id
                    },
                    id_=os.path.basename(original_file_path)
                )
                return [doc], [], [], []
        except Exception as e:
            logger.error(f"Error processing text file {original_file_name}: {e}")
            return [], [], [], []

    async def _get_file_processor(self, file_extension):
        """根据文件扩展名获取对应的处理器"""
        processors = {
            '.png': self._process_image_file,
            '.jpg': self._process_image_file,
            '.jpeg': self._process_image_file,
            '.pdf': self._process_pdf_file,
            '.doc': self._process_doc_file,
            '.ppt': self._process_ppt_file,
            '.pptx': self._process_pptx_file,
            '.xls': self._process_xls_file,
            '.docx': self._process_docx_file,
            '.xlsx': self._process_xlsx_file,
        }
        return processors.get(file_extension, self._process_text_file)

    def _cleanup_temp_files(self, temp_files, converted_pdf_files, temp_image_files):
        """
        统一的临时文件清理方法，避免重复清理
        
        参数:
            temp_files: 远程文件下载、Word/Excel处理以及DOC转DOCX产生的临时文件
            converted_pdf_files: Office转换PDF产生的临时文件（包括PPT/PPTX/DOC/XLS等转换的PDF）
            temp_image_files: 处理过程中产生的临时图像文件
            
        注意:
            不清理 vectorstore/office_references 和 vectorstore/ppt_references 目录，
            避免在多用户环境下产生竞争条件。
            PPT和PPTX文件现在直接转换为PDF，不再产生中间的PPTX临时文件。
            DOC转DOCX产生的临时文件现在包含在temp_files参数中。
        """
        cleanup_stats = {
            'temp_files': 0,
            'converted_pdf_files': 0,
            'temp_image_files': 0
        }
        
        # 收集所有需要清理的文件，避免重复清理
        files_to_clean = set()
        
        # 1. 清理远程文件下载、Word/Excel处理以及DOC转DOCX产生的临时文件
        for temp_file in temp_files:
            if temp_file and temp_file not in files_to_clean:
                files_to_clean.add(temp_file)
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        cleanup_stats['temp_files'] += 1
                        logger.debug(f"已清理临时文件: {os.path.basename(temp_file)}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {os.path.basename(temp_file)}, 错误: {e}")
        
        # 2. 清理Office转换PDF产生的临时文件（包括PPT/PPTX/DOC/XLS等转换的PDF）
        for pdf_file in converted_pdf_files:
            if pdf_file and pdf_file not in files_to_clean:
                files_to_clean.add(pdf_file)
                try:
                    if os.path.exists(pdf_file):
                        os.remove(pdf_file)
                        cleanup_stats['converted_pdf_files'] += 1
                        logger.debug(f"已清理转换PDF文件: {os.path.basename(pdf_file)}")
                except Exception as e:
                    logger.warning(f"清理转换PDF文件失败: {os.path.basename(pdf_file)}, 错误: {e}")
        
        # 3. 清理处理过程中产生的临时图像文件
        for image_file in temp_image_files:
            if image_file and image_file not in files_to_clean:
                files_to_clean.add(image_file)
                try:
                    if os.path.exists(image_file):
                        os.remove(image_file)
                        cleanup_stats['temp_image_files'] += 1
                        logger.debug(f"已清理临时图像文件: {os.path.basename(image_file)}")
                except Exception as e:
                    logger.warning(f"清理临时图像文件失败: {os.path.basename(image_file)}, 错误: {e}")
        
        # 4. 注意：不清理 vectorstore/office_references 和 vectorstore/ppt_references 目录
        # 原因：在多用户环境下，这些共享目录可能被其他用户同时使用，
        # 清理操作可能会与其他用户的文件创建操作产生竞争条件，导致问题。
        # 这些目录中的文件应该由各自的业务逻辑负责管理。
        
        # 5. 统计清理信息
        total_cleaned_files = sum(cleanup_stats.values())
        if total_cleaned_files > 0:
            logger.info(f"文件清理完成：")
            logger.info(f"  - 临时文件（包括远程下载、Word/Excel、DOC转DOCX）: {cleanup_stats['temp_files']} 个")
            logger.info(f"  - Office转换PDF文件: {cleanup_stats['converted_pdf_files']} 个")
            logger.info(f"  - 临时图像文件: {cleanup_stats['temp_image_files']} 个")
            logger.info(f"  - 总计清理文件: {total_cleaned_files} 个")
        
        return cleanup_stats

    async def load_data(self) -> list[Document]:
        """
        加载并处理多种文件类型。
        此方法处理多模态文件，但不执行知识图谱处理，因为知识图谱处理已在基类的create_index和create_index_local方法中实现。
        
        返回:
            list[Document]: 处理后的文档列表
        """
        documents = []
        tasks = []
        temp_files = []  # 用于跟踪临时文件
        converted_pdf_files = []  # 用于跟踪转换生成的PDF文件
        temp_image_files = []  # 用于跟踪处理过程中生成的临时图像文件

        # 处理每个文件，将远程文件下载到本地临时文件
        processed_files = []
        original_paths = {}  # 用于存储原始文件路径和名称的映射

        for file_path in self.files:
            local_file_path = file_path
            original_file_path = file_path  # 保存原始文件路径

            # 处理远程文件
            if RAG.is_remote_file(file_path):
                try:
                    # 下载远程文件到临时文件
                    response = requests.get(file_path, stream=True)
                    response.raise_for_status()  # 确保请求成功

                    # 获取文件名
                    file_name = os.path.basename(file_path.split("?")[0])  # 移除URL参数

                    # 创建临时文件
                    temp_file = NamedTemporaryFile(delete=False, suffix=f"_{file_name}")
                    for chunk in response.iter_content(chunk_size=8192):
                        temp_file.write(chunk)
                    temp_file.close()

                    local_file_path = temp_file.name
                    temp_files.append(temp_file.name)  # 记录临时文件以便后续清理
                except Exception as e:
                    raise ValueError(f"Error downloading remote file {file_path}: {str(e)}")

            processed_files.append(local_file_path)
            original_paths[local_file_path] = original_file_path  # 记录临时文件与原始路径的映射关系

        # 继续处理文件
        try:
            for file_path in processed_files:
                original_file_path = original_paths.get(file_path, file_path)  # 获取原始文件路径
                original_file_name = os.path.basename(original_file_path)  # 获取原始文件名
                file_extension = os.path.splitext(original_file_name.lower())[1]

                # 获取对应的处理器
                processor = await self._get_file_processor(file_extension)
                
                # 创建处理任务
                async def process_file_task(processor, file_path, original_file_path, original_file_name):
                    try:
                        docs, temp_files_local, converted_pdf_files_local, temp_image_files_local = await processor(file_path, original_file_path, original_file_name)
                        
                        # 将文档添加到结果列表
                        documents.extend(docs)
                        
                        # 将临时文件添加到跟踪列表
                        temp_files.extend(temp_files_local)
                        converted_pdf_files.extend(converted_pdf_files_local)
                        temp_image_files.extend(temp_image_files_local)
                        
                    except Exception as e:
                        logger.error(f"Error processing file {original_file_name}: {e}")
                        import traceback
                        traceback.print_exc()

                task = process_file_task(processor, file_path, original_file_path, original_file_name)
                tasks.append(task)

            # 执行所有任务
            await run_jobs(tasks, show_progress=True, workers=3)
            return documents
        finally:
            # 使用统一的清理方法，避免重复清理（注意：DOC转DOCX的临时文件现在包含在temp_files中）
            self._cleanup_temp_files(temp_files, converted_pdf_files, temp_image_files)

