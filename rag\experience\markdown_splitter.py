"""Markdown and Plain Text splitter."""

import re
from typing import Callable, List, Optional, Sequence, Dict, Any

from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.callbacks.base import CallbackManager
from llama_index.core.constants import DEFAULT_CHUNK_SIZE
from llama_index.core.node_parser.interface import (
    MetadataAwareTextSplitter,
)
from llama_index.core.node_parser.node_utils import default_id_func
from llama_index.core.node_parser.text.utils import (
    split_by_char,
    split_by_regex,
    split_by_sentence_tokenizer,
    split_by_sep,
)
from llama_index.core.schema import BaseNode, Document, MetadataMode, TextNode
from llama_index.core.utils import get_tqdm_iterable
from llama_index.core.utils import get_tokenizer

# 默认块重叠
MARKDOWN_CHUNK_OVERLAP = 200
# 默认正则表达式
MARKDOWN_CHUNKING_REGEX = r"[^,.;。？！]+[,.;。？！]?|[,.;。？！]"
# 默认段落分隔符
DEFAULT_PARAGRAPH_SEP = "\n\n\n"
# 语义结束标点符号
SEMANTIC_END_MARKERS = ['.', '。', '!', '！', '?', '？']


class MarkdownTextSplitter(MetadataAwareTextSplitter):
    """Parse markdown and text with semantic awareness.

    This splitter aims to keep sentences and paragraphs together while respecting 
    semantic boundaries (punctuation marks that indicate the end of a thought or sentence).
    It also handles images differently from text content.
    """

    chunk_size: int = Field(
        default=DEFAULT_CHUNK_SIZE,
        description="The token chunk size for each chunk.",
        gt=0,
    )
    chunk_overlap: int = Field(
        default=MARKDOWN_CHUNK_OVERLAP,
        description="The token overlap of each chunk when splitting.",
        ge=0,
    )
    separator: str = Field(
        default=" ", description="Default separator for splitting into words"
    )
    paragraph_separator: str = Field(
        default=DEFAULT_PARAGRAPH_SEP, description="Separator between paragraphs."
    )
    semantic_chunking_regex: Optional[str] = Field(
        default=MARKDOWN_CHUNKING_REGEX, description="Regex for splitting into semantic units."
    )

    _chunking_tokenizer_fn: Callable[[str], List[str]] = PrivateAttr()
    _tokenizer: Callable = PrivateAttr()
    _split_fns: List[Callable] = PrivateAttr()
    _sub_sentence_split_fns: List[Callable] = PrivateAttr()

    def __init__(
        self,
        separator: str = " ",
        chunk_size: int = DEFAULT_CHUNK_SIZE,
        chunk_overlap: int = MARKDOWN_CHUNK_OVERLAP,
        tokenizer: Optional[Callable] = None,
        paragraph_separator: str = DEFAULT_PARAGRAPH_SEP,
        chunking_tokenizer_fn: Optional[Callable[[str], List[str]]] = None,
        semantic_chunking_regex: Optional[str] = MARKDOWN_CHUNKING_REGEX,
        callback_manager: Optional[CallbackManager] = None,
        include_metadata: bool = True,
        include_prev_next_rel: bool = True,
        id_func: Optional[Callable] = None,
    ):
        """Initialize with parameters."""
        if chunk_overlap > chunk_size:
            raise ValueError(
                f"Got a larger chunk overlap ({chunk_overlap}) than chunk size "
                f"({chunk_size}), should be smaller."
            )
        id_func = id_func or default_id_func
        callback_manager = callback_manager or CallbackManager([])
        super().__init__(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            semantic_chunking_regex=semantic_chunking_regex,
            separator=separator,
            paragraph_separator=paragraph_separator,
            callback_manager=callback_manager,
            include_metadata=include_metadata,
            include_prev_next_rel=include_prev_next_rel,
            id_func=id_func,
        )
        self._chunking_tokenizer_fn = (
            chunking_tokenizer_fn or split_by_sentence_tokenizer()
        )
        self._tokenizer = tokenizer or get_tokenizer()

        self._split_fns = [
            split_by_sep(paragraph_separator),
            self._chunking_tokenizer_fn,
        ]

        if semantic_chunking_regex:
            self._sub_sentence_split_fns = [
                split_by_regex(semantic_chunking_regex),
                split_by_sep(separator),
                split_by_char(),
            ]
        else:
            self._sub_sentence_split_fns = [
                split_by_sep(separator),
                split_by_char(),
            ]

    @classmethod
    def class_name(cls) -> str:
        return "MarkdownTextSplitter"

    def _parse_nodes(
        self,
        nodes: Sequence[BaseNode],
        show_progress: bool = False,
        **kwargs: Any,
    ) -> List[BaseNode]:
        all_nodes: List[BaseNode] = []
        nodes_with_progress = get_tqdm_iterable(nodes, show_progress, "Parsing documents into nodes")

        for node in nodes_with_progress:
            if isinstance(node, Document):
                # 检查document的元数据中type是否为image
                doc_type = node.metadata.get("type", "").lower()
                if doc_type == "image":
                    # 如果是图像，单独作为一块
                    all_nodes.append(node)
                else:
                    # 如果是markdown或text或其他文本类型
                    text = node.get_content(metadata_mode=MetadataMode.NONE)
                    metadata = node.metadata
                    
                    # 进行文本分块
                    if self.include_metadata:
                        metadata_str = node.get_metadata_str(mode=MetadataMode.LLM)
                        text_chunks = self.split_text_metadata_aware(text, metadata_str)
                    else:
                        text_chunks = self.split_text(text)
                        
                    # 从分块创建节点
                    for i, chunk in enumerate(text_chunks):
                        chunk_node = TextNode(
                            text=chunk,
                            metadata=metadata,
                            id_=self.id_func(i, node),
                            relationships={}
                        )
                        all_nodes.append(chunk_node)
            else:
                # 如果不是Document对象，直接添加到结果中
                all_nodes.append(node)

        return all_nodes

    def split_text_metadata_aware(self, text: str, metadata_str: str) -> List[str]:
        metadata_len = len(self._tokenizer(metadata_str))
        effective_chunk_size = self.chunk_size - metadata_len
        if effective_chunk_size <= 0:
            raise ValueError(
                f"Metadata length ({metadata_len}) is longer than chunk size "
                f"({self.chunk_size}). Consider increasing the chunk size or "
                "decreasing the size of your metadata to avoid this."
            )
        elif effective_chunk_size < 50:
            print(
                f"Metadata length ({metadata_len}) is close to chunk size "
                f"({self.chunk_size}). Resulting chunks are less than 50 tokens. "
                "Consider increasing the chunk size or decreasing the size of "
                "your metadata to avoid this.",
                flush=True,
            )

        return self._split_text(text, chunk_size=effective_chunk_size)

    def split_text(self, text: str) -> List[str]:
        return self._split_text(text, chunk_size=self.chunk_size)

    def _split_text(self, text: str, chunk_size: int) -> List[str]:
        """Split text preserving semantic boundaries and end markers."""
        if text == "":
            return [text]

        # 使用分块策略进行分割
        splits = self._split(text, chunk_size)
        
        # 合并分块
        chunks = self._merge_splits(splits, chunk_size)
        
        # 确保每个分块在语义结束标记处截断
        chunks = self._ensure_semantic_boundaries(chunks)
        
        return chunks

    def _split(self, text: str, chunk_size: int) -> List[Dict[str, Any]]:
        """将文本分割成更小的单元，确保每个单元不超过chunk大小。
        
        按照块大小>段落>分隔符>正则>句子>单字符的优先级进行分块。
        """
        token_size = self._token_size(text)
        if token_size <= chunk_size:
            return [{"text": text, "is_semantic_unit": True, "token_size": token_size}]

        # 尝试按照分块函数顺序进行分块
        text_splits_by_fns, is_semantic_unit = self._get_splits_by_fns(text)

        text_splits = []
        for text_split in text_splits_by_fns:
            token_size = self._token_size(text_split)
            if token_size <= chunk_size:
                text_splits.append({
                    "text": text_split,
                    "is_semantic_unit": is_semantic_unit,
                    "token_size": token_size
                })
            else:
                # 递归分块
                recursive_text_splits = self._split(text_split, chunk_size=chunk_size)
                text_splits.extend(recursive_text_splits)
        
        return text_splits

    def _merge_splits(self, splits: List[Dict[str, Any]], chunk_size: int) -> List[str]:
        """将分块合并成最终的chunks，确保不超过chunk_size。"""
        chunks = []
        current_chunk = []
        current_chunk_size = 0
        
        for split in splits:
            # 如果当前分块+当前chunk大小超过限制，则结束当前chunk
            if current_chunk_size + split["token_size"] > chunk_size and current_chunk:
                chunks.append("".join(current_chunk))
                current_chunk = []
                current_chunk_size = 0
            
            # 添加当前分块到当前chunk
            current_chunk.append(split["text"])
            current_chunk_size += split["token_size"]
        
        # 处理最后一个chunk
        if current_chunk:
            chunks.append("".join(current_chunk))
        
        # 处理重叠部分
        if self.chunk_overlap > 0 and len(chunks) > 1:
            chunks_with_overlap = [chunks[0]]
            
            for i in range(1, len(chunks)):
                prev_chunk = chunks[i-1]
                curr_chunk = chunks[i]
                
                # 找到前一个chunk的最后n个token作为重叠
                overlap_size = min(self.chunk_overlap, self._token_size(prev_chunk))
                if overlap_size > 0:
                    # 简化处理：取大约对应的字符数
                    avg_token_len = len(prev_chunk) / max(1, self._token_size(prev_chunk))
                    char_overlap = int(overlap_size * avg_token_len)
                    overlap_text = prev_chunk[-char_overlap:]
                    chunks_with_overlap.append(overlap_text + curr_chunk)
                else:
                    chunks_with_overlap.append(curr_chunk)
            
            chunks = chunks_with_overlap
        
        return self._postprocess_chunks(chunks)

    def _ensure_semantic_boundaries(self, chunks: List[str]) -> List[str]:
        """确保每个chunk在语义结束标记处截断。"""
        result_chunks = []
        
        for chunk in chunks:
            # 检查这个chunk是否已经在语义结束符处结束
            if chunk and chunk[-1] in SEMANTIC_END_MARKERS:
                result_chunks.append(chunk)
                continue
                
            # 查找最后一个语义结束标记的位置
            last_end_pos = -1
            for marker in SEMANTIC_END_MARKERS:
                pos = chunk.rfind(marker)
                if pos > last_end_pos:
                    last_end_pos = pos
            
            if last_end_pos >= 0:
                # 在语义结束标记处截断
                result_chunks.append(chunk[:last_end_pos+1])
                
                # 如果截断后还有内容，尝试将剩余部分添加到下一个chunk
                remainder = chunk[last_end_pos+1:].strip()
                if remainder and len(result_chunks) < len(chunks):
                    # 将剩余部分添加到下一个chunk的开头
                    chunks[len(result_chunks)] = remainder + " " + chunks[len(result_chunks)]
            else:
                # 如果找不到任何语义结束标记，保持原样
                result_chunks.append(chunk)
        
        return result_chunks

    def _postprocess_chunks(self, chunks: List[str]) -> List[str]:
        """处理分块，移除空白块并去除前导和尾随空白字符。"""
        new_chunks = []
        for chunk in chunks:
            stripped_chunk = chunk.strip()
            if stripped_chunk == "":
                continue
            new_chunks.append(stripped_chunk)
        return new_chunks

    def _token_size(self, text: str) -> int:
        """计算文本的token数量。"""
        return len(self._tokenizer(text))

    def _get_splits_by_fns(self, text: str) -> tuple[List[str], bool]:
        """使用不同的分割函数尝试分割文本。"""
        # 首先尝试段落和句子级分割
        for split_fn in self._split_fns:
            splits = split_fn(text)
            if len(splits) > 1:
                return splits, True

        # 然后尝试子句级分割
        for split_fn in self._sub_sentence_split_fns:
            splits = split_fn(text)
            if len(splits) > 1:
                break

        return splits, False 