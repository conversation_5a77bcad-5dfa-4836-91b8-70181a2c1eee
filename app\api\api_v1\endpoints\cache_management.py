"""
缓存管理接口
提供缓存预热、状态监控、手动管理等功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel

from app.services.cache_management_service import cache_management_service
from utils.logger import get_logger

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
        503: {"description": "服务不可用"}
    }
)
logger = get_logger()


class CacheWarmupRequest(BaseModel):
    """缓存预热请求模型"""
    include_database_stats: bool = True
    include_knowledge_stats: bool = True
    max_concurrent: int = 3


class CacheWarmupResponse(BaseModel):
    """缓存预热响应模型"""
    status: str
    message: str
    task_id: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class CacheStatusResponse(BaseModel):
    """缓存状态响应模型"""
    redis_status: Dict[str, Any]
    warmup_status: Dict[str, Any]
    config: Dict[str, Any]


@router.get("/status", response_model=CacheStatusResponse)
async def get_cache_management_status():
    """
    获取缓存管理状态
    """
    try:
        result = await cache_management_service.get_cache_management_status()
        return CacheStatusResponse(**result)
        
    except Exception as e:
        logger.error(f"获取缓存状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存状态失败: {str(e)}")


@router.post("/warmup", response_model=CacheWarmupResponse)
async def trigger_cache_warmup(
    request: CacheWarmupRequest,
    background_tasks: BackgroundTasks
):
    """
    手动触发缓存预热
    """
    try:
        result = await cache_management_service.trigger_cache_warmup(
            include_database_stats=request.include_database_stats,
            include_knowledge_stats=request.include_knowledge_stats,
            max_concurrent=request.max_concurrent,
            background_task_func=background_tasks.add_task
        )
        return CacheWarmupResponse(**result)
        
    except Exception as e:
        logger.error(f"触发缓存预热失败: {e}")
        raise HTTPException(status_code=500, detail=f"触发缓存预热失败: {str(e)}")


@router.get("/warmup/status")
async def get_warmup_status():
    """
    获取预热任务状态
    """
    return await cache_management_service.get_warmup_status()


@router.post("/warmup/quick")
async def quick_cache_warmup(background_tasks: BackgroundTasks):
    """
    快速缓存预热（仅预热高优先级数据）
    """
    try:
        result = await cache_management_service.quick_cache_warmup(
            background_task_func=background_tasks.add_task
        )
        return result
        
    except Exception as e:
        logger.error(f"快速缓存预热失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/clear")
async def clear_all_cache():
    """
    清除所有统计缓存
    """
    try:
        result = await cache_management_service.clear_all_cache()
        return result
        
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.delete("/clear/pattern")
async def clear_cache_by_pattern(
    pattern: str = Query(..., description="缓存键模式，如 'database_*' 或 'knowledge_*'")
):
    """
    按模式清除缓存
    """
    try:
        result = await cache_management_service.clear_cache_by_pattern(pattern)
        return result
        
    except Exception as e:
        logger.error(f"清除模式缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除模式缓存失败: {str(e)}")


@router.get("/keys")
async def list_cache_keys(
    pattern: Optional[str] = Query(None, description="筛选模式，如 '*stats*'"),
    limit: int = Query(100, ge=1, le=1000, description="返回键数量限制")
):
    """
    列出缓存键
    """
    try:
        result = await cache_management_service.list_cache_keys(pattern, limit)
        return result
        
    except Exception as e:
        logger.error(f"列出缓存键失败: {e}")
        if "Redis连接不可用" in str(e):
            raise HTTPException(status_code=503, detail=str(e))
        raise HTTPException(status_code=500, detail=f"列出缓存键失败: {str(e)}")


@router.get("/performance")
async def get_cache_performance():
    """
    获取缓存性能统计
    """
    try:
        result = await cache_management_service.get_cache_performance()
        return result
        
    except Exception as e:
        logger.error(f"获取缓存性能统计失败: {e}")
        if "Redis连接不可用" in str(e):
            raise HTTPException(status_code=503, detail=str(e))
        raise HTTPException(status_code=500, detail=f"获取缓存性能统计失败: {str(e)}")


@router.post("/test")
async def test_cache_operations():
    """
    测试缓存操作（开发调试用）
    """
    try:
        result = await cache_management_service.test_cache_operations()
        return result
        
    except Exception as e:
        logger.error(f"缓存操作测试失败: {e}")
        if "Redis连接不可用" in str(e):
            raise HTTPException(status_code=503, detail=str(e))
        raise HTTPException(status_code=500, detail=f"缓存操作测试失败: {str(e)}")


@router.get("/health")
async def cache_health_check():
    """
    缓存系统健康检查
    """
    return await cache_management_service.cache_health_check()