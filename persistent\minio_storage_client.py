import io
import asyncio
import time
import threading
from typing import Dict, Union, Any, Optional
from contextlib import asynccontextmanager
from queue import Queue, Empty
from dataclasses import dataclass

from chainlit.data.storage_clients.base import BaseStorageClient
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()
from minio import Minio

import config.settings as settings

@dataclass
class MinioConnection:
    """MinIO连接包装类"""
    client: Minio
    created_at: float
    last_used: float
    is_healthy: bool = True
    connection_id: str = ""

class MinioConnectionPool:
    """MinIO连接池管理器"""
    
    def __init__(self, 
                 min_connections: int = 2,
                 max_connections: int = 10,
                 connection_timeout: float = 300.0,  # 5分钟超时
                 health_check_interval: float = 60.0,  # 1分钟健康检查
                 max_idle_time: float = 600.0):  # 10分钟最大空闲时间
        
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.health_check_interval = health_check_interval
        self.max_idle_time = max_idle_time
        
        # 连接池队列
        self._pool = Queue(maxsize=max_connections)
        self._active_connections = 0
        self._connection_counter = 0
        self._lock = threading.Lock()
        
        # 健康检查任务
        self._health_check_task = None
        self._shutdown = False
        
        # 初始化连接池
        self._initialize_pool()
        
        # 启动健康检查任务
        self._start_health_check()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            # 创建最小连接数
            for _ in range(self.min_connections):
                connection = self._create_connection()
                if connection:
                    self._pool.put(connection)
                    self._active_connections += 1
            
            logger.info(f"MinIO连接池初始化完成，创建了 {self._active_connections} 个连接")
        except Exception as e:
            logger.error(f"MinIO连接池初始化失败: {e}")
    
    def _create_connection(self) -> Optional[MinioConnection]:
        """创建新的MinIO连接"""
        try:
            with self._lock:
                self._connection_counter += 1
                connection_id = f"minio_conn_{self._connection_counter}"
            
            client = Minio(
                endpoint=settings.configuration.minio_endpoint,
                access_key=settings.configuration.minio_access_key,
                secret_key=settings.configuration.minio_secret_key,
                secure=False
            )
            
            # 简单的连接测试
            bucket_name = settings.configuration.minio_bucket_name
            client.bucket_exists(bucket_name)
            
            connection = MinioConnection(
                client=client,
                created_at=time.time(),
                last_used=time.time(),
                is_healthy=True,
                connection_id=connection_id
            )
            
            logger.debug(f"创建新的MinIO连接: {connection_id}")
            return connection
            
        except Exception as e:
            logger.error(f"创建MinIO连接失败: {e}")
            return None
    
    def _is_connection_healthy(self, connection: MinioConnection) -> bool:
        """检查连接健康状态"""
        try:
            # 检查连接是否超时
            if time.time() - connection.last_used > self.max_idle_time:
                logger.debug(f"连接 {connection.connection_id} 超过最大空闲时间")
                return False
            
            # 简单的健康检查 - 检查bucket是否存在
            bucket_name = settings.configuration.minio_bucket_name
            connection.client.bucket_exists(bucket_name)
            
            connection.is_healthy = True
            return True
            
        except Exception as e:
            logger.warning(f"连接健康检查失败 {connection.connection_id}: {e}")
            connection.is_healthy = False
            return False
    
    def _start_health_check(self):
        """启动健康检查任务"""
        def health_check_worker():
            while not self._shutdown:
                try:
                    self._perform_health_check()
                    time.sleep(self.health_check_interval)
                except Exception as e:
                    logger.error(f"健康检查任务出错: {e}")
                    time.sleep(self.health_check_interval)
        
        self._health_check_task = threading.Thread(target=health_check_worker, daemon=True)
        self._health_check_task.start()
        logger.info("MinIO连接池健康检查任务已启动")
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 临时存储健康的连接
            healthy_connections = []
            unhealthy_count = 0
            
            # 检查所有连接
            while not self._pool.empty():
                try:
                    connection = self._pool.get(timeout=1.0)
                    
                    if self._is_connection_healthy(connection):
                        healthy_connections.append(connection)
                    else:
                        unhealthy_count += 1
                        with self._lock:
                            self._active_connections -= 1
                        logger.debug(f"移除不健康的连接: {connection.connection_id}")
                
                except Empty:
                    break
                except Exception as e:
                    logger.warning(f"健康检查过程中出错: {e}")
            
            # 将健康的连接放回池中
            for connection in healthy_connections:
                self._pool.put(connection)
            
            # 如果连接数不足，创建新连接
            current_connections = len(healthy_connections)
            if current_connections < self.min_connections:
                needed = self.min_connections - current_connections
                for _ in range(needed):
                    if self._active_connections < self.max_connections:
                        new_connection = self._create_connection()
                        if new_connection:
                            self._pool.put(new_connection)
                            with self._lock:
                                self._active_connections += 1
            
            if unhealthy_count > 0:
                logger.info(f"健康检查完成，移除 {unhealthy_count} 个不健康连接，当前连接数: {self._active_connections}")
        
        except Exception as e:
            logger.error(f"执行健康检查失败: {e}")
    
    @asynccontextmanager
    async def get_connection(self, timeout: float = 30.0):
        """获取连接的异步上下文管理器"""
        connection = None
        try:
            # 异步等待连接
            connection = await asyncio.get_event_loop().run_in_executor(
                None, self._get_connection_sync, timeout
            )
            
            if connection is None:
                raise Exception("无法获取MinIO连接")
            
            # 更新最后使用时间
            connection.last_used = time.time()
            
            yield connection.client
            
        except Exception as e:
            logger.error(f"获取MinIO连接失败: {e}")
            raise
        finally:
            if connection:
                await asyncio.get_event_loop().run_in_executor(
                    None, self._return_connection_sync, connection
                )
    
    def _get_connection_sync(self, timeout: float) -> Optional[MinioConnection]:
        """同步获取连接"""
        try:
            # 首先尝试从池中获取
            try:
                connection = self._pool.get(timeout=timeout)
                
                # 检查连接是否仍然健康
                if self._is_connection_healthy(connection):
                    return connection
                else:
                    # 连接不健康，丢弃并创建新连接
                    with self._lock:
                        self._active_connections -= 1
                    logger.debug(f"丢弃不健康连接: {connection.connection_id}")
            
            except Empty:
                # 池中没有可用连接
                pass
            
            # 如果没有可用连接且未达到最大连接数，创建新连接
            if self._active_connections < self.max_connections:
                connection = self._create_connection()
                if connection:
                    with self._lock:
                        self._active_connections += 1
                    return connection
            
            # 如果仍然无法获取连接，再次尝试从池中获取
            return self._pool.get(timeout=timeout)
            
        except Empty:
            logger.error("获取MinIO连接超时")
            return None
        except Exception as e:
            logger.error(f"获取MinIO连接失败: {e}")
            return None
    
    def _return_connection_sync(self, connection: MinioConnection):
        """同步归还连接"""
        try:
            if connection.is_healthy and not self._shutdown:
                connection.last_used = time.time()
                self._pool.put(connection)
            else:
                # 连接不健康或正在关闭，减少活动连接计数
                with self._lock:
                    self._active_connections -= 1
                logger.debug(f"连接 {connection.connection_id} 未归还到池中")
        
        except Exception as e:
            logger.error(f"归还MinIO连接失败: {e}")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return {
            "active_connections": self._active_connections,
            "available_connections": self._pool.qsize(),
            "max_connections": self.max_connections,
            "min_connections": self.min_connections,
            "pool_utilization": self._active_connections / self.max_connections,
        }
    
    def shutdown(self):
        """关闭连接池"""
        self._shutdown = True
        
        # 等待健康检查任务结束
        if self._health_check_task and self._health_check_task.is_alive():
            self._health_check_task.join(timeout=5.0)
        
        # 清空连接池
        while not self._pool.empty():
            try:
                connection = self._pool.get(timeout=1.0)
                logger.debug(f"关闭连接: {connection.connection_id}")
            except Empty:
                break
        
        logger.info("MinIO连接池已关闭")

# 全局连接池实例
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool() -> MinioConnectionPool:
    """获取全局连接池实例"""
    global _connection_pool
    
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                # 从配置中读取连接池参数
                pool_config = getattr(settings.configuration, 'minio_pool_config', {})
                
                _connection_pool = MinioConnectionPool(
                    min_connections=pool_config.get('min_connections', 2),
                    max_connections=pool_config.get('max_connections', 10),
                    connection_timeout=pool_config.get('connection_timeout', 300.0),
                    health_check_interval=pool_config.get('health_check_interval', 60.0),
                    max_idle_time=pool_config.get('max_idle_time', 600.0)
                )
    
    return _connection_pool

class MinioStorageClient(BaseStorageClient):
    """MinIO存储客户端 - 连接池版本"""
    
    def __init__(self):
        """初始化MinIO客户端"""
        self.pool = get_connection_pool()
        logger.info("MinioStorageClient (连接池模式) 初始化完成")

    async def upload_file(self, object_key: str, data: Union[bytes, str], mime: str = 'application/octet-stream',
                          overwrite: bool = True, use_public_url: bool = True) -> Dict[str, Any]:
        """上传文件到MinIO"""
        try:
            if isinstance(data, str):
                data_bytes = data.encode('utf-8')
            else:
                data_bytes = data
            
            data_stream = io.BytesIO(data_bytes)
            bucket_name = settings.configuration.minio_bucket_name
            
            # 使用连接池
            async with self.pool.get_connection() as client:
                client.put_object(
                    bucket_name=bucket_name,
                    object_name=object_key,
                    data=data_stream,
                    length=len(data_bytes),
                    content_type=mime
                )
                
                # 根据参数选择返回公开URL还是presigned URL
                if use_public_url:
                    url = self.get_public_url(object_key)
                else:
                    url = client.get_presigned_url("GET", bucket_name, object_key)
                
                return {"object_key": object_key, "url": url}
                
        except Exception as e:
            logger.warning(f"MinioStorageClient, upload_file error: {e}")
            return {}

    def get_public_url(self, object_key: str) -> str:
        """
        获取对象的公开URL（无过期时间）
        注意：需要bucket设置为公开访问或者有相应的bucket policy
        """
        try:
            bucket_name = settings.configuration.minio_bucket_name
            # 使用外部endpoint生成公开URL，解决docker-compose部署时的访问问题
            external_endpoint = settings.configuration.minio_external_endpoint
            
            # 构造公开URL
            if external_endpoint.startswith('http'):
                base_url = external_endpoint
            else:
                # 根据secure设置决定协议
                protocol = 'https' if getattr(settings.configuration, 'minio_secure', False) else 'http'
                base_url = f"{protocol}://{external_endpoint}"
            
            public_url = f"{base_url}/{bucket_name}/{object_key}"
            return public_url
        except Exception as e:
            logger.warning(f"MinioStorageClient, get_public_url error: {e}")
            return ""

    async def set_bucket_public_read(self) -> bool:
        """设置bucket为公开读取"""
        try:
            bucket_name = settings.configuration.minio_bucket_name
            
            # 创建公开读取策略
            policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject"],
                        "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
                    }
                ]
            }
            
            import json
            
            # 使用连接池
            async with self.pool.get_connection() as client:
                client.set_bucket_policy(bucket_name, json.dumps(policy))
                logger.info(f"Bucket {bucket_name} 已设置为公开读取")
                return True
                
        except Exception as e:
            logger.warning(f"MinioStorageClient, set_bucket_public_read error: {e}")
            return False

    async def delete_file(self, object_key: str) -> bool:
        """删除文件"""
        try:
            bucket_name = settings.configuration.minio_bucket_name
            
            # 使用连接池
            async with self.pool.get_connection() as client:
                client.remove_object(bucket_name, object_key)
                return True
                
        except Exception as e:
            logger.warning(f"MinioStorageClient, delete_file error: {e}")
            return False

    async def get_read_url(self, object_key: str, use_public_url: bool = True) -> str:
        """获取文件读取URL"""
        try:
            bucket_name = settings.configuration.minio_bucket_name
            
            if use_public_url:
                # 返回公开URL（无过期时间）
                return self.get_public_url(object_key)
            else:
                # 生成一个预签名的GET URL，默认有效期为7天
                async with self.pool.get_connection() as client:
                    url = client.get_presigned_url("GET", bucket_name, object_key)
                    return url
                    
        except Exception as e:
            logger.warning(f"MinioStorageClient, get_read_url error: {e}")
            return ""
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return self.pool.get_pool_stats()

# 全局MinioStorageClient实例管理器
_client_instance = None
_client_lock = threading.Lock()

def get_minio_client() -> MinioStorageClient:
    """获取全局MinioStorageClient实例"""
    global _client_instance
    
    if _client_instance is None:
        with _client_lock:
            if _client_instance is None:
                _client_instance = MinioStorageClient()
    
    return _client_instance

def cleanup_minio_client():
    """清理全局MinioStorageClient实例"""
    global _client_instance
    if _client_instance:
        _client_instance = None
    cleanup_minio_pool()

# 应用关闭时的清理函数
def cleanup_minio_pool():
    """清理MinIO连接池"""
    global _connection_pool
    if _connection_pool:
        _connection_pool.shutdown()
        _connection_pool = None