import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastmcp import FastMCP, Context
from pydantic import BaseModel

# 导入服务层
from app.services.cache_management_service import CacheManagementService
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建缓存MCP服务器实例
cache_server = FastMCP("Cache Server")

# 创建缓存管理服务实例
cache_management_service = CacheManagementService()

# ============================================================================
# 缓存管理相关工具
# ============================================================================

@cache_server.tool
async def clear_cache(cache_key: Optional[str] = None, ctx: Context = None) -> Dict[str, Any]:
    """
    清除缓存（对应FastAPI接口 /clear）
    
    Args:
        cache_key: 要清除的缓存键（可选，如果不提供则清除所有缓存）
        
    Returns:
        操作结果
    """
    try:
        if cache_key:
            await ctx.info(f"正在清除缓存键: {cache_key}")
            # 复用服务层的业务逻辑
            result = await cache_management_service.clear_cache_by_pattern(cache_key)
        else:
            await ctx.info("正在清除所有统计缓存")
            # 复用服务层的业务逻辑
            result = await cache_management_service.clear_all_cache()
        
        await ctx.info("缓存清除完成")
        return result
        
    except Exception as e:
        error_msg = f"清除缓存时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def get_cache_status(ctx: Context) -> Dict[str, Any]:
    """
    获取缓存状态（对应FastAPI接口 /status）
    
    Returns:
        缓存状态信息
    """
    try:
        await ctx.info("正在获取缓存状态")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.get_cache_management_status()
        
        await ctx.info("缓存状态获取完成")
        return {
            "status": "success",
            **result
        }
        
    except Exception as e:
        error_msg = f"获取缓存状态时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }


@cache_server.tool
async def get_cache_keys_pattern(pattern: str = "*", ctx: Context = None) -> Dict[str, Any]:
    """
    根据模式获取缓存键列表
    
    Args:
        pattern: 匹配模式（默认*表示所有键）
        
    Returns:
        匹配的缓存键列表
    """
    try:
        await ctx.info(f"正在获取匹配模式 '{pattern}' 的缓存键")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.list_cache_keys(pattern)
        
        await ctx.info("缓存键列表获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取缓存键时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def trigger_cache_warmup(include_database_stats: bool = True, include_knowledge_stats: bool = True, max_concurrent: int = 3, ctx: Context = None) -> Dict[str, Any]:
    """
    手动触发缓存预热（对应FastAPI接口 /warmup）
    
    Args:
        include_database_stats: 是否包含数据库统计预热
        include_knowledge_stats: 是否包含知识统计预热
        max_concurrent: 最大并发数
        
    Returns:
        预热任务启动结果
    """
    try:
        await ctx.info("正在触发缓存预热任务")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.trigger_cache_warmup(
            include_database_stats=include_database_stats,
            include_knowledge_stats=include_knowledge_stats,
            max_concurrent=max_concurrent
        )
        
        await ctx.info("缓存预热任务已启动")
        return result
        
    except Exception as e:
        error_msg = f"触发缓存预热时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def get_warmup_status(ctx: Context) -> Dict[str, Any]:
    """
    获取预热任务状态
    
    Returns:
        预热任务状态信息
    """
    try:
        await ctx.info("正在获取预热任务状态")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.get_warmup_status()
        
        await ctx.info("预热任务状态获取完成")
        return {
            "status": "success",
            **result
        }
        
    except Exception as e:
        error_msg = f"获取预热任务状态时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def quick_cache_warmup(ctx: Context) -> Dict[str, Any]:
    """
    快速缓存预热（仅预热高优先级数据）
    
    Returns:
        快速预热任务启动结果
    """
    try:
        await ctx.info("正在启动快速缓存预热")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.quick_cache_warmup()
        
        await ctx.info("快速缓存预热任务已启动")
        return result
        
    except Exception as e:
        error_msg = f"快速缓存预热时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def clear_cache_by_pattern(pattern: str, ctx: Context) -> Dict[str, Any]:
    """
    根据模式清除缓存
    
    Args:
        pattern: 匹配模式
        
    Returns:
        清除结果
    """
    try:
        await ctx.info(f"正在根据模式清除缓存: {pattern}")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.clear_cache_by_pattern(pattern)
        
        await ctx.info("模式缓存清除完成")
        return result
        
    except Exception as e:
        error_msg = f"清除模式缓存时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def list_cache_keys(pattern: Optional[str] = None, limit: int = 100, ctx: Context = None) -> Dict[str, Any]:
    """
    列出缓存键
    
    Args:
        pattern: 匹配模式（可选）
        limit: 限制数量
        
    Returns:
        缓存键列表
    """
    try:
        await ctx.info("正在列出缓存键")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.list_cache_keys(pattern, limit)
        
        await ctx.info("缓存键列表获取完成")
        return result
        
    except Exception as e:
        error_msg = f"列出缓存键时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def get_cache_performance(ctx: Context) -> Dict[str, Any]:
    """
    获取缓存性能指标
    
    Returns:
        缓存性能信息
    """
    try:
        await ctx.info("正在获取缓存性能指标")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.get_cache_performance()
        
        await ctx.info("缓存性能指标获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取缓存性能指标时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def test_cache_operations(ctx: Context) -> Dict[str, Any]:
    """
    测试缓存操作
    
    Returns:
        测试结果
    """
    try:
        await ctx.info("正在测试缓存操作")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.test_cache_operations()
        
        await ctx.info("缓存操作测试完成")
        return result
        
    except Exception as e:
        error_msg = f"测试缓存操作时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@cache_server.tool
async def cache_health_check(ctx: Context) -> Dict[str, Any]:
    """
    缓存健康检查
    
    Returns:
        健康检查结果
    """
    try:
        await ctx.info("正在进行缓存健康检查")
        
        # 复用服务层的业务逻辑
        result = await cache_management_service.cache_health_check()
        
        await ctx.info("缓存健康检查完成")
        return result
        
    except Exception as e:
        error_msg = f"缓存健康检查时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

# ============================================================================
# 缓存相关提示模板
# ============================================================================

@cache_server.prompt
def cache_usage_prompt() -> str:
    """缓存系统使用指南"""
    return """# 缓存系统使用指南

本缓存系统基于重构后的服务层架构，提供以下核心功能：

## 主要功能

### 1. 缓存管理
- `clear_cache`: 清除指定缓存或所有缓存
- `get_cache_status`: 获取缓存系统状态信息

### 2. 缓存预热
- `trigger_cache_warmup`: 手动触发完整缓存预热
- `quick_cache_warmup`: 快速预热高优先级数据
- `get_warmup_status`: 获取预热任务状态

### 3. 缓存维护
- `clear_cache_by_pattern`: 根据模式清除缓存
- `list_cache_keys`: 列出缓存键
- `get_cache_keys_pattern`: 根据模式获取缓存键

### 4. 性能监控
- `get_cache_performance`: 获取缓存性能指标
- `test_cache_operations`: 测试缓存操作
- `cache_health_check`: 缓存健康检查

## 架构优势
- 复用服务层业务逻辑，避免重复实现
- 统一的错误处理和日志记录
- 支持多种缓存策略和预热机制
- 更好的代码维护性和扩展性

## 缓存类型
1. **统计缓存**: 数据库和知识库统计信息
2. **查询缓存**: 常用查询结果缓存
3. **会话缓存**: 用户会话和状态信息
4. **配置缓存**: 系统配置和元数据
"""

@cache_server.prompt
def cache_best_practices_prompt() -> str:
    """缓存系统最佳实践"""
    return """# 缓存系统最佳实践

## 缓存管理最佳实践
1. **缓存策略选择**
   - 根据数据特性选择合适的缓存策略
   - 设置合理的过期时间
   - 区分热点数据和冷数据

2. **预热策略**
   - 系统启动时预热核心数据
   - 定期预热更新频率较高的数据
   - 根据业务需求调整预热优先级

## 性能优化建议
1. **内存管理**
   - 监控缓存内存使用情况
   - 及时清理过期和无效缓存
   - 设置合理的缓存大小限制

2. **并发控制**
   - 避免缓存击穿和雪崩
   - 使用分布式锁控制并发更新
   - 实现缓存降级机制

3. **数据一致性**
   - 及时更新缓存数据
   - 处理缓存与数据库的数据同步
   - 实现缓存版本控制

## 监控和维护
1. **性能监控**
   - 监控缓存命中率
   - 跟踪缓存响应时间
   - 分析缓存使用模式

2. **定期维护**
   - 清理过期缓存数据
   - 优化缓存键结构
   - 更新缓存策略配置

3. **故障处理**
   - 建立缓存故障检测机制
   - 实现缓存失效时的降级策略
   - 定期备份关键缓存数据

## 安全考虑
1. **数据安全**
   - 加密敏感缓存数据
   - 控制缓存访问权限
   - 定期审计缓存使用情况

2. **系统安全**
   - 防止缓存注入攻击
   - 限制缓存操作频率
   - 监控异常缓存访问模式
"""

@cache_server.prompt
def cache_performance_tuning_prompt() -> str:
    """缓存性能调优指南"""
    return """# 缓存性能调优指南

## 性能优化策略

### 1. 缓存命中率优化
**提高命中率的方法**:
- 分析访问模式，优化缓存键设计
- 合理设置缓存过期时间
- 使用预热机制提前加载热点数据
- 实现多级缓存架构

### 2. 内存使用优化
**内存管理技巧**:
- 选择合适的数据结构存储缓存
- 压缩大型缓存对象
- 实现LRU等淘汰策略
- 设置内存使用阈值告警

### 3. 网络传输优化
**减少网络开销**:
- 批量操作减少网络往返
- 使用连接池复用连接
- 实现本地缓存减少远程调用
- 优化序列化方式

## 监控指标

### 1. 核心性能指标
- **命中率**: 缓存命中次数/总请求次数
- **响应时间**: 平均缓存操作延迟
- **内存使用率**: 缓存占用内存/总可用内存
- **QPS**: 每秒查询数

### 2. 业务指标
- **数据新鲜度**: 缓存数据的时效性
- **一致性**: 缓存与数据源的一致性
- **可用性**: 缓存服务的可用时间比例
- **扩展性**: 系统负载增长时的性能表现

### 3. 系统指标
- **CPU使用率**: 缓存操作的CPU消耗
- **磁盘I/O**: 持久化操作的磁盘使用
- **网络带宽**: 缓存数据传输的网络使用
- **连接数**: 缓存服务的连接数量

## 调优实践

### 1. 预热策略调优
- 根据业务访问模式调整预热时机
- 优化预热数据的选择算法
- 控制预热过程的资源消耗
- 实现增量预热机制

### 2. 过期策略调优
- 基于数据更新频率设置过期时间
- 实现智能过期策略
- 避免大量数据同时过期
- 使用惰性删除和定期清理

### 3. 分片策略调优
- 根据数据特性选择分片算法
- 实现数据均匀分布
- 优化跨分片查询性能
- 支持动态分片调整

## 故障诊断

### 1. 性能问题诊断
- 分析慢查询和性能瓶颈
- 检查内存泄漏和资源占用
- 监控网络延迟和丢包
- 分析缓存穿透和击穿

### 2. 数据一致性问题
- 检查缓存更新机制
- 验证数据同步策略
- 分析并发访问冲突
- 检查事务一致性

### 3. 可用性问题
- 监控服务健康状态
- 检查集群节点状态
- 分析故障转移机制
- 验证备份恢复策略
""" 