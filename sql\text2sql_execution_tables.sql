-- Text2SQL执行过程持久化数据库表结构
-- 创建时间: 2024年

-- 1. 主执行记录表
CREATE TABLE `text2sql_executions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `session_id` varchar(64) NOT NULL COMMENT '会话ID，用于关联前端会话',
    `query_id` varchar(64) NOT NULL COMMENT '查询唯一标识，UUID格式',
    `user_query` text NOT NULL COMMENT '用户原始查询',
    `optimized_query` text COMMENT '大模型优化后的查询',
    `connection_id` varchar(64) COMMENT '数据库连接ID',
    `db_type` varchar(32) COMMENT '数据库类型：MySQL, PostgreSQL, SQLite等',
    `execution_status` varchar(32) NOT NULL DEFAULT 'RUNNING' COMMENT '执行状态：RUNNING, SUCCESS, FAILED, TIMEOUT',
    `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始执行时间',
    `end_time` timestamp NULL COMMENT '结束执行时间',
    `total_duration_ms` bigint COMMENT '总执行时长(毫秒)',
    `error_message` text COMMENT '错误信息',
    `final_sql` text COMMENT '最终生成的SQL语句',
    `sql_explanation` text COMMENT 'SQL解释',
    `query_results_count` int COMMENT '查询结果行数',
    `query_results_size_kb` decimal(10,2) COMMENT '查询结果大小(KB)',
    `visualization_recommendation` json COMMENT '可视化推荐结果',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_query_id` (`query_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_connection_id` (`connection_id`),
    KEY `idx_status_time` (`execution_status`, `start_time`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Text2SQL主执行记录表';

-- 2. 智能体执行详情表
CREATE TABLE `text2sql_agent_executions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `agent_name` varchar(64) NOT NULL COMMENT '智能体名称：schema_retriever, query_analyzer等',
    `agent_display_name` varchar(128) NOT NULL COMMENT '智能体显示名称',
    `execution_order` int NOT NULL COMMENT '执行顺序',
    `status` varchar(32) NOT NULL DEFAULT 'RUNNING' COMMENT '执行状态：RUNNING, SUCCESS, FAILED, SKIPPED',
    `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始执行时间',
    `end_time` timestamp NULL COMMENT '结束执行时间',
    `duration_ms` bigint COMMENT '执行时长(毫秒)',
    `input_data` json COMMENT '输入数据',
    `output_data` json COMMENT '输出数据',
    `stream_messages_count` int DEFAULT 0 COMMENT '流式消息数量',
    `error_message` text COMMENT '错误信息',
    `model_calls_count` int DEFAULT 0 COMMENT 'LLM调用次数',
    `model_tokens_used` int DEFAULT 0 COMMENT 'LLM使用的token数量',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_agent_name` (`agent_name`),
    KEY `idx_execution_order` (`execution_order`),
    KEY `idx_status_time` (`status`, `start_time`),
    CONSTRAINT `fk_agent_exec_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能体执行详情表';

-- 3. 流式消息记录表
CREATE TABLE `text2sql_stream_messages` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `agent_execution_id` bigint NOT NULL COMMENT '关联智能体执行ID',
    `message_sequence` int NOT NULL COMMENT '消息序号',
    `source` varchar(128) NOT NULL COMMENT '消息来源',
    `content` text COMMENT '消息内容',
    `content_format` varchar(32) DEFAULT 'markdown' COMMENT '内容格式：markdown, plain, json',
    `is_final` boolean DEFAULT FALSE COMMENT '是否为最终消息',
    `message_size_bytes` int COMMENT '消息大小(字节)',
    `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间戳',
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_agent_execution_id` (`agent_execution_id`),
    KEY `idx_sequence` (`message_sequence`),
    KEY `idx_timestamp` (`timestamp`),
    CONSTRAINT `fk_stream_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_stream_agent` FOREIGN KEY (`agent_execution_id`) REFERENCES `text2sql_agent_executions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流式消息记录表';

-- 4. 性能指标统计表
CREATE TABLE `text2sql_performance_metrics` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `metric_name` varchar(128) NOT NULL COMMENT '指标名称',
    `metric_value` decimal(15,4) COMMENT '指标数值',
    `metric_unit` varchar(32) COMMENT '指标单位：ms, bytes, count等',
    `metric_category` varchar(64) COMMENT '指标分类：performance, resource, quality等',
    `collected_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收集时间',
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_metric_name` (`metric_name`),
    KEY `idx_category` (`metric_category`),
    KEY `idx_collected_at` (`collected_at`),
    CONSTRAINT `fk_metrics_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标统计表';

-- 5. 错误日志表
CREATE TABLE `text2sql_error_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `agent_name` varchar(64) COMMENT '出错的智能体名称',
    `error_type` varchar(128) NOT NULL COMMENT '错误类型',
    `error_message` text NOT NULL COMMENT '错误信息',
    `error_stack` text COMMENT '错误堆栈',
    `error_context` json COMMENT '错误上下文信息',
    `severity` varchar(32) DEFAULT 'ERROR' COMMENT '严重程度：INFO, WARNING, ERROR, CRITICAL',
    `occurred_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_agent_name` (`agent_name`),
    KEY `idx_error_type` (`error_type`),
    KEY `idx_severity` (`severity`),
    KEY `idx_occurred_at` (`occurred_at`),
    CONSTRAINT `fk_error_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误日志表';

-- 6. 表结构检索记录表
CREATE TABLE `text2sql_schema_retrievals` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `connection_id` varchar(64) NOT NULL COMMENT '数据库连接ID',
    `original_query` text NOT NULL COMMENT '原始查询',
    `optimized_query` text COMMENT '大模型优化后的查询',
    `vector_search_results_count` int COMMENT '向量搜索结果数量',
    `initial_tables_count` int COMMENT '初始相关表数量',
    `final_tables_count` int COMMENT '最终关联表数量',
    `kg_query_depth` int COMMENT '知识图谱查询深度',
    `schema_info_size_kb` decimal(10,2) COMMENT '表结构信息大小(KB)',
    `execution_time_ms` bigint COMMENT '执行时间(毫秒)',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_connection_id` (`connection_id`),
    KEY `idx_tables_count` (`final_tables_count`),
    CONSTRAINT `fk_schema_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表结构检索记录表';

-- 7. SQL执行结果表
CREATE TABLE `text2sql_query_results` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `query_id` varchar(64) NOT NULL COMMENT '关联查询ID',
    `sql_statement` text NOT NULL COMMENT '执行的SQL语句',
    `execution_status` varchar(32) NOT NULL COMMENT '执行状态：SUCCESS, FAILED, TIMEOUT',
    `results_count` int COMMENT '结果行数',
    `results_size_kb` decimal(10,2) COMMENT '结果大小(KB)',
    `execution_time_ms` bigint COMMENT 'SQL执行时间(毫秒)',
    `columns_info` json COMMENT '列信息',
    `sample_data` json COMMENT '样本数据(前几行)',
    `error_message` text COMMENT '执行错误信息',
    `executed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_query_id` (`query_id`),
    KEY `idx_execution_status` (`execution_status`),
    KEY `idx_results_count` (`results_count`),
    KEY `idx_executed_at` (`executed_at`),
    CONSTRAINT `fk_results_query` FOREIGN KEY (`query_id`) REFERENCES `text2sql_executions` (`query_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SQL执行结果表';

-- 创建视图：性能统计概览
CREATE VIEW `v_text2sql_performance_overview` AS
SELECT 
    DATE(e.created_at) as execution_date,
    COUNT(*) as total_executions,
    COUNT(CASE WHEN e.execution_status = 'SUCCESS' THEN 1 END) as successful_executions,
    COUNT(CASE WHEN e.execution_status = 'FAILED' THEN 1 END) as failed_executions,
    AVG(e.total_duration_ms) as avg_duration_ms,
    AVG(e.query_results_count) as avg_results_count,
    AVG(e.query_results_size_kb) as avg_results_size_kb,
    COUNT(DISTINCT e.connection_id) as unique_connections,
    COUNT(DISTINCT e.session_id) as unique_sessions
FROM text2sql_executions e
GROUP BY DATE(e.created_at)
ORDER BY execution_date DESC;

-- 创建视图：智能体性能统计
CREATE VIEW `v_text2sql_agent_performance` AS
SELECT 
    a.agent_name,
    a.agent_display_name,
    COUNT(*) as total_executions,
    COUNT(CASE WHEN a.status = 'SUCCESS' THEN 1 END) as successful_executions,
    COUNT(CASE WHEN a.status = 'FAILED' THEN 1 END) as failed_executions,
    AVG(a.duration_ms) as avg_duration_ms,
    AVG(a.model_calls_count) as avg_model_calls,
    AVG(a.model_tokens_used) as avg_tokens_used,
    MAX(a.duration_ms) as max_duration_ms,
    MIN(a.duration_ms) as min_duration_ms
FROM text2sql_agent_executions a
WHERE a.status IN ('SUCCESS', 'FAILED')
GROUP BY a.agent_name, a.agent_display_name
ORDER BY a.execution_order; 