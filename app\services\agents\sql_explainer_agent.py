from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent
from autogen_agentchat.base import TaskResult
from autogen_core import message_handler, TopicId, MessageContext, type_subscription

from .base_agent import (
    BaseAgent, AGENT_NAMES, sql_explainer_topic_type, sql_executor_topic_type
)
from app.schemas import SqlMessage, SqlExplanationMessage

# 获取logger实例
from utils.logger import get_logger
logger = get_logger()


@type_subscription(topic_type=sql_explainer_topic_type)
class SqlExplainerAgent(BaseAgent):
    """SQL解释智能体，负责解释SQL语句的含义"""
    
    def __init__(self, db_type=None, analytics_service=None, query_id: str = None):
        super().__init__("sql_explainer_agent", db_type, analytics_service, query_id)
        self._prompt_template = f"""
        你是一名专业的SQL解释专家，你的任务是以准确、易懂的方式向非技术人员解释给定的SQL语句的含义和作用。

        ## 数据库类型
        [[db_type]]

        ## 数据库结构
        ```sql
        [[db_schema]]
        ```

        ## 用户问题
        [[query]]

        ## 需要解释的SQL语句
        [[sql]]

        ## 规则

        1.  **使用通俗易懂的语言：** 解释应该避免使用过于专业或技术性的术语。目标是让没有任何编程或数据库知识的人也能理解。
        2.  **准确且全面地解释：** 确保解释的准确性，并覆盖SQL语句的主要功能和逻辑。
        3.  **解释关键子句：** 针对SQL语句中的每个主要子句（例如 `SELECT`, `FROM`, `WHERE`, `GROUP BY`, `ORDER BY`, `JOIN` 等）解释其作用和目的。
        4.  **说明查询结果：** 清晰地描述执行这条SQL语句后，预计会从数据库中返回什么类型的数据和结果。
        5.  **解释复杂特性：**
            * **聚合函数：** 如果SQL语句中使用了聚合函数（如 `SUM`, `AVG`, `COUNT`, `MAX`, `MIN`），解释这些函数的作用以及它们是如何计算结果的。
            * **表连接：** 如果使用了表连接（如 `JOIN`），解释为什么要进行连接，以及连接是如何根据相关字段将不同表中的数据关联起来的。可以结合数据库结构进行解释。
            * **子查询：** 如果使用了子查询（嵌套查询），解释子查询的目的以及它是如何帮助主查询获取所需数据的。
        6.  **结合数据库结构：** 在解释过程中，可以适当引用提供的数据库表结构，帮助理解表名、字段名的含义以及表之间的关系。例如，解释 `users.name` 时，可以说明 `name` 是 `users` 表中的一个字段，用于存储用户的姓名。
        7.  **保持简洁明了：** 尽量用简短的句子表达清楚意思，避免冗长的描述。解释的长度一般不超过200字。
        8.  **直接解释提供的SQL：** 你的解释应该直接针对用户问题 `[[query]]` 和 `[[sql]]` 部分提供的具体SQL代码。

        **示例解释框架：**

        "这条SQL语句的作用是[整体功能描述]。它首先从 `[表名]` 表中[FROM子句的解释]。然后，它会筛选出满足[WHERE子句的解释]的记录。如果使用了 `GROUP BY`，则会按照[GROUP BY子句的解释]进行分组，并且可能会使用[聚合函数]计算每个组的结果。最后，结果可能会按照[ORDER BY子句的解释]进行排序。总的来说，这条语句会返回[对查询结果的总结性描述]。"
        """

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["sql_explainer"]

    def _prepare_prompt(self, query: str, sql: str, schema_context: str = None) -> str:
        """准备提示模板

        Args:
            query: 用户查询
            sql: SQL语句
            schema_context: 可选的表结构信息

        Returns:
            str: 准备好的提示
        """
        if not query or not query.strip():
            raise ValueError("用户查询不能为空")
        
        if not sql or not sql.strip():
            raise ValueError("SQL语句不能为空")

        # 填充模板
        prompt = self._prompt_template
        prompt = prompt.replace("[[query]]", query)
        prompt = prompt.replace("[[sql]]", sql)
        prompt = prompt.replace("[[db_type]]", self.db_type)
        prompt = prompt.replace("[[db_schema]]", schema_context or "未提供数据库结构信息")

        return prompt

    async def _explain_sql(self, query: str, sql: str, schema_context: str = None) -> str:
        """解释SQL语句
        
        Args:
            query: 用户查询
            sql: SQL语句
            schema_context: 表结构信息
            
        Returns:
            str: SQL解释内容
        """
        try:
            # 准备提示
            system_message = self._prepare_prompt(query, sql, schema_context)

            agent = AssistantAgent(
                name="sql_explainer",
                model_client=self.model_client,
                system_message=system_message,
                model_client_stream=True,
            )

            stream = agent.run_stream(task=f"解释以下SQL语句：\n{sql}")
            explanation_content = ""

            async for event in stream:
                if isinstance(event, ModelClientStreamingChunkEvent):
                    await self.send_stream_message(event.content,content_format="")
                elif isinstance(event, TaskResult):
                    if event.messages:
                        explanation_content = event.messages[-1].content.strip()

            return explanation_content
            
        except Exception as e:
            logger.error(f"解释SQL时出错: {str(e)}")
            raise

    @message_handler
    async def handle_message(self, message: SqlMessage, ctx: MessageContext) -> None:
        """处理SQL消息并生成解释"""
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "sql": message.sql,
                "sql_length": len(message.sql) if message.sql else 0
            })
            
            # 验证消息
            if not self.validate_message(message, ['query', 'sql']):
                error = ValueError("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            # 发送开始解释的消息
            await self.send_stream_message("正在解释SQL语句...\n\n", message_sequence=1)

            try:
                explanation_content = await self._explain_sql(
                    message.query, 
                    message.sql, 
                    getattr(message, 'schema_context', None)
                )
                # 记录模型调用统计
                await self.increment_model_usage(calls_count=1, tokens_used=len(explanation_content))
                
            except ValueError as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.send_error_message(e, "SQL解释参数验证")
                return
            except Exception as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.handle_exception(e, "SQL解释")
                return

            await self.send_stream_message("\n\n解释完成", is_final=True, message_sequence=2)

            # 完成执行，记录成功状态
            output_data = {
                "explanation_content": explanation_content,
                "explanation_length": len(explanation_content),
                "explanation_completed": True
            }
            await self.finish_execution("SUCCESS", output_data=output_data)

            # 传递给SQL执行智能体
            await self.publish_message(
                SqlExplanationMessage(
                    query=message.query,
                    sql=message.sql,
                    explanation=explanation_content,
                ),
                topic_id=TopicId(type=sql_executor_topic_type, source=self.id.key)
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理SQL消息") 