-- RAG 对话历史相关数据表
-- 创建时间: 2024-12-21
-- 描述: 用于存储 knowledge_graph_chat_stream 接口的对话会话和历史记录

-- 1. 创建对话会话表
CREATE TABLE IF NOT EXISTS `rag_chat_session` (
    `id` VARCHAR(36) NOT NULL COMMENT '主键ID',
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID，唯一标识',
    `user_id` VARCHAR(100) NOT NULL COMMENT '用户ID',
    `collection_name` VARCHAR(100) NOT NULL COMMENT '知识库集合名称',
    `session_title` VARCHAR(200) DEFAULT NULL COMMENT '会话标题',
    `status` TINYINT(1) DEFAULT 1 COMMENT '会话状态：1-活跃，2-结束，3-已删除',
    `total_messages` INT(11) DEFAULT 0 COMMENT '总消息数',
    `last_message_time` DATETIME DEFAULT NULL COMMENT '最后消息时间',
    `session_config` JSON DEFAULT NULL COMMENT '会话配置参数JSON',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_session_id` (`session_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_collection_name` (`collection_name`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG对话会话表';

-- 2. 创建对话历史表
CREATE TABLE IF NOT EXISTS `rag_chat_history` (
    `id` VARCHAR(36) NOT NULL COMMENT '主键ID',
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID，关联rag_chat_session.session_id',
    `role` VARCHAR(20) NOT NULL COMMENT '角色：user-用户，assistant-助手',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `message_order` INT(11) NOT NULL COMMENT '消息顺序号',
    `token_count` INT(11) DEFAULT NULL COMMENT 'Token数量',
    `source_info` JSON DEFAULT NULL COMMENT '数据来源信息JSON',
    `message_metadata` JSON DEFAULT NULL COMMENT '消息元数据JSON',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_role` (`role`),
    KEY `idx_message_order` (`message_order`),
    CONSTRAINT `fk_chat_history_session` FOREIGN KEY (`session_id`) REFERENCES `rag_chat_session` (`session_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG对话历史表';

-- 3. 创建复合索引以优化查询性能
CREATE INDEX `idx_session_user_status` ON `rag_chat_session` (`user_id`, `status`, `create_time`);
CREATE INDEX `idx_history_session_order` ON `rag_chat_history` (`session_id`, `message_order`);

-- 4. 插入示例数据（可选）
-- INSERT INTO `rag_chat_session` (`id`, `session_id`, `user_id`, `collection_name`, `session_title`, `create_by`) 
-- VALUES ('550e8400-e29b-41d4-a716-446655440000', 'test_session_001', 'user_001', 'default', '测试对话会话', 'user_001');

-- INSERT INTO `rag_chat_history` (`id`, `session_id`, `role`, `content`, `message_order`) 
-- VALUES 
-- ('550e8400-e29b-41d4-a716-446655440001', 'test_session_001', 'user', '你好，请介绍一下你的功能', 1),
-- ('550e8400-e29b-41d4-a716-446655440002', 'test_session_001', 'assistant', '您好！我是智能助手，可以帮助您进行知识问答...', 2);

-- 5. 创建定期清理历史数据的事件（可选）
-- 注意：需要确保 MySQL 的 event_scheduler 开启
/*
DELIMITER //
CREATE EVENT IF NOT EXISTS `clean_old_chat_history`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP + INTERVAL 1 DAY
DO
BEGIN
    -- 删除30天前的历史记录
    DELETE FROM `rag_chat_history` 
    WHERE `create_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 删除没有历史记录的会话
    DELETE FROM `rag_chat_session` 
    WHERE `id` NOT IN (
        SELECT DISTINCT `session_id` FROM `rag_chat_history`
    ) AND `create_time` < DATE_SUB(NOW(), INTERVAL 7 DAY);
END //
DELIMITER ;
*/ 