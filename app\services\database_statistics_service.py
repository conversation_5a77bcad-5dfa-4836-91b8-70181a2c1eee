"""
数据库统计服务类
提供数据库统计相关的业务逻辑，供FastAPI接口和缓存预热复用
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from dao.DatabaseEngine import DatabaseEngine
from utils.logger import get_logger

logger = get_logger()


class DatabaseStatisticsService:
    """异步数据库统计服务类"""
    
    def __init__(self):
        self.async_engine = DatabaseEngine.get_async_engine()
    
    async def get_db_session(self) -> AsyncSession:
        """获取异步数据库会话"""
        return await DatabaseEngine.get_session()
    
    async def get_dashboard_overview_data(self) -> Dict[str, Any]:
        """获取仪表板概览数据"""
        async with await self.get_db_session() as session:
            try:
                # 数据库表统计
                tables_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schematable')
                result = await session.execute(tables_query)
                database_count = result.scalar() or 0

                # 计算总记录数（所有表的字段总和，模拟学段总数的概念）
                records_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schemacolumn')
                result = await session.execute(records_query)
                total_records = result.scalar() or 0

                # 表关系数量
                relationships_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schemarelationship')
                result = await session.execute(relationships_query)
                relationship_count = result.scalar() or 0

                # 数据源数量
                datasources_query = text('SELECT COUNT(*) as count FROM text2sql_sys_datasource')
                result = await session.execute(datasources_query)
                datasource_count = result.scalar() or 0

                # 计算平均字段覆盖率
                avg_fields_query = text("""
                    SELECT AVG(field_count) as avg_fields
                    FROM (
                        SELECT COUNT(*) as field_count
                        FROM text2sql_sys_schemacolumn c
                        JOIN text2sql_sys_schematable t ON c.table_id = t.id
                        GROUP BY t.id
                    ) as table_field_counts
                """)
                result = await session.execute(avg_fields_query)
                avg_fields_coverage = result.scalar() or 0

                return {
                    "database_count": database_count,
                    "total_records": total_records,
                    "relationship_count": relationship_count,
                    "datasource_count": datasource_count,
                    "avg_fields_coverage": round(float(avg_fields_coverage), 1)
                }
            except Exception as e:
                logger.error(f"Error getting dashboard overview data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_database_overview_data(self) -> Dict[str, Any]:
        """获取数据库基础概览数据"""
        async with await self.get_db_session() as session:
            try:
                # 查询数据库表总数
                tables_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schematable')
                result = await session.execute(tables_query)
                total_tables = result.scalar() or 0

                # 查询字段总数
                fields_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schemacolumn')
                result = await session.execute(fields_query)
                total_fields = result.scalar() or 0

                # 查询表关系总数
                relationships_query = text('SELECT COUNT(*) as count FROM text2sql_sys_schemarelationship')
                result = await session.execute(relationships_query)
                total_relationships = result.scalar() or 0

                # 查询数据源总数
                datasources_query = text('SELECT COUNT(*) as count FROM text2sql_sys_datasource')
                result = await session.execute(datasources_query)
                total_datasources = result.scalar() or 0

                return {
                    "total_tables": total_tables,
                    "total_fields": total_fields,
                    "total_relationships": total_relationships,
                    "total_datasources": total_datasources
                }
            except Exception as e:
                logger.error(f"Error getting database overview data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_chart_distribution_data(self) -> Dict[str, Any]:
        """获取关系分布图表数据"""
        async with await self.get_db_session() as session:
            try:
                # 按关系类型分布统计关系数量（用于柱状图）
                distribution_query = text("""
                    SELECT 
                        COALESCE(r.relationship_type, '未分类关系') as relationship_type,
                        COUNT(r.id) as relationship_count
                    FROM text2sql_sys_schemarelationship r
                    GROUP BY r.relationship_type
                    ORDER BY relationship_count DESC
                    LIMIT 10
                """)
                
                result = await session.execute(distribution_query)
                distribution_data = result.fetchall()

                chart_data = []
                for row in distribution_data:
                    # 对关系类型进行中文化处理
                    relationship_type_name = row.relationship_type
                    if relationship_type_name == "one_to_one":
                        relationship_type_name = "一对一"
                    elif relationship_type_name == "one_to_many":
                        relationship_type_name = "一对多"
                    elif relationship_type_name == "many_to_one":
                        relationship_type_name = "多对一"
                    elif relationship_type_name == "many_to_many":
                        relationship_type_name = "多对多"
                    elif relationship_type_name == "foreign_key":
                        relationship_type_name = "外键关系"
                    elif relationship_type_name is None or relationship_type_name == "未分类关系":
                        relationship_type_name = "未分类关系"
                    
                    chart_data.append({
                        "label": relationship_type_name,
                        "value": row.relationship_count
                    })

                # 如果没有关系数据，创建一些默认的关系类型展示
                if not chart_data:
                    default_types = ["一对一", "一对多", "多对一", "外键关系", "未分类关系"]
                    chart_data = [{"label": rel_type, "value": 0} for rel_type in default_types]

                return {"chart_data": chart_data}
            except Exception as e:
                logger.error(f"Error getting chart distribution data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_database_type_chart_data(self) -> Dict[str, Any]:
        """获取数据库类型分布图表数据"""
        async with await self.get_db_session() as session:
            try:
                type_query = text("""
                    SELECT 
                        COALESCE(d.db_type, '未知类型') as db_type,
                        COUNT(d.id) as type_count
                    FROM text2sql_sys_datasource d
                    GROUP BY d.db_type
                    ORDER BY type_count DESC
                """)
                
                result = await session.execute(type_query)
                type_data = result.fetchall()

                chart_data = []
                for row in type_data:
                    db_type_name = row.db_type
                    if db_type_name == "mysql":
                        db_type_name = "MySQL"
                    elif db_type_name == "postgresql":
                        db_type_name = "PostgreSQL"
                    elif db_type_name == "oracle":
                        db_type_name = "Oracle"
                    elif db_type_name == "sqlserver":
                        db_type_name = "SQL Server"
                    elif db_type_name is None or db_type_name == "未知类型":
                        db_type_name = "未知类型"
                    
                    chart_data.append({
                        "label": db_type_name,
                        "value": row.type_count
                    })

                if not chart_data:
                    chart_data = [{"label": "暂无数据", "value": 0}]

                return {"chart_data": chart_data}
            except Exception as e:
                logger.error(f"Error getting database type chart data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_distribution_by_datasource_data(self) -> List[Dict[str, Any]]:
        """获取按数据源分布的数据"""
        async with await self.get_db_session() as session:
            try:
                query = text("""
                    SELECT 
                        COALESCE(d.name, d.code) as dimension,
                        COUNT(t.id) as count
                    FROM text2sql_sys_datasource d
                    LEFT JOIN text2sql_sys_schematable t ON d.id = t.connection_id
                    GROUP BY d.id, d.name, d.code
                    ORDER BY count DESC
                    LIMIT 20
                """)
                
                result = await session.execute(query)
                data = result.fetchall()

                total = sum(row.count for row in data) or 1
                
                return [
                    {
                        "dimension": row.dimension or "未命名数据源",
                        "count": row.count,
                        "percentage": round((row.count / total) * 100, 1)
                    }
                    for row in data
                ]
            except Exception as e:
                logger.error(f"Error getting distribution by datasource data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_distribution_by_database_type_data(self) -> List[Dict[str, Any]]:
        """获取按数据库类型分布的数据"""
        async with await self.get_db_session() as session:
            try:
                query = text("""
                    SELECT 
                        COALESCE(d.db_type, '未知类型') as db_type,
                        COUNT(t.id) as count
                    FROM text2sql_sys_datasource d
                    LEFT JOIN text2sql_sys_schematable t ON d.id = t.connection_id
                    GROUP BY d.db_type
                    ORDER BY count DESC
                """)
                
                result = await session.execute(query)
                data = result.fetchall()

                total = sum(row.count for row in data) or 1
                
                result_data = []
                for row in data:
                    db_type_name = row.db_type
                    if db_type_name == "mysql":
                        db_type_name = "MySQL"
                    elif db_type_name == "postgresql":
                        db_type_name = "PostgreSQL"
                    elif db_type_name == "oracle":
                        db_type_name = "Oracle"
                    elif db_type_name == "sqlserver":
                        db_type_name = "SQL Server"
                    elif db_type_name is None or db_type_name == "未知类型":
                        db_type_name = "未知类型"
                    
                    result_data.append({
                        "dimension": db_type_name,
                        "count": row.count,
                        "percentage": round((row.count / total) * 100, 1)
                    })

                return result_data
            except Exception as e:
                logger.error(f"Error getting distribution by database type data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_database_trend_analysis_data(self, days: int = 30) -> Dict[str, Any]:
        """获取数据库趋势分析数据"""
        async with await self.get_db_session() as session:
            try:
                # 基础统计
                base_query = text("""
                    SELECT 
                        COUNT(DISTINCT t.id) as total_tables,
                        COUNT(DISTINCT c.id) as total_columns,
                        COUNT(DISTINCT r.id) as total_relationships,
                        COUNT(DISTINCT d.id) as total_datasources
                    FROM text2sql_sys_schematable t
                    LEFT JOIN text2sql_sys_schemacolumn c ON t.id = c.table_id
                    LEFT JOIN text2sql_sys_schemarelationship r ON t.id = r.source_table_id OR t.id = r.target_table_id
                    LEFT JOIN text2sql_sys_datasource d ON t.connection_id = d.id
                """)
                
                result = await session.execute(base_query)
                base_stats = result.fetchone()
                
                return {
                    "period_days": days,
                    "total_tables": base_stats.total_tables or 0,
                    "total_columns": base_stats.total_columns or 0,
                    "total_relationships": base_stats.total_relationships or 0,
                    "total_datasources": base_stats.total_datasources or 0,
                    "growth_rate": 0.0,  # 简化处理，实际需要历史数据比较
                    "trend_data": []  # 简化处理，实际需要按时间分组
                }
            except Exception as e:
                logger.error(f"Error getting database trend analysis data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_monthly_trend_chart_data(self) -> Dict[str, Any]:
        """获取月度趋势图表数据"""
        async with await self.get_db_session() as session:
            try:
                # 获取最近6个月的数据
                six_months_ago = (datetime.now() - timedelta(days=180)).isoformat()
                
                monthly_query = text("""
                    SELECT 
                        DATE_FORMAT(created_at, '%m月') as month,
                        COUNT(*) as table_count
                    FROM text2sql_sys_schematable 
                    WHERE created_at >= :start_date
                    GROUP BY DATE_FORMAT(created_at, '%Y-%m'), DATE_FORMAT(created_at, '%m月')
                    ORDER BY DATE_FORMAT(created_at, '%Y-%m') ASC
                    LIMIT 6
                """)
                
                result = await session.execute(monthly_query, {"start_date": six_months_ago})
                monthly_data = result.fetchall()

                # 如果没有足够的月度数据，创建默认数据
                if not monthly_data:
                    default_months = ["01月", "02月", "03月", "04月", "05月", "06月"]
                    chart_data = [{"label": month, "value": 0} for month in default_months]
                else:
                    chart_data = [{"label": row.month, "value": row.table_count} for row in monthly_data]

                return {"monthly_chart_data": chart_data}
            except Exception as e:
                logger.error(f"Error getting monthly trend chart data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_field_analysis_data(self) -> Dict[str, Any]:
        """获取字段分析统计数据"""
        async with await self.get_db_session() as session:
            try:
                # 计算平均每表字段数
                avg_fields_query = text("""
                    SELECT AVG(field_count) as avg_fields
                    FROM (
                        SELECT COUNT(*) as field_count
                        FROM text2sql_sys_schemacolumn c
                        JOIN text2sql_sys_schematable t ON c.table_id = t.id
                        GROUP BY t.id
                    ) as table_field_counts
                """)
                result = await session.execute(avg_fields_query)
                avg_fields = result.scalar() or 0

                # 查找字段最多和最少的表
                field_extremes_query = text("""
                    SELECT 
                        t.table_name,
                        COUNT(c.id) as field_count,
                        ROW_NUMBER() OVER (ORDER BY COUNT(c.id) DESC) as max_rank,
                        ROW_NUMBER() OVER (ORDER BY COUNT(c.id) ASC) as min_rank
                    FROM text2sql_sys_schematable t
                    LEFT JOIN text2sql_sys_schemacolumn c ON t.id = c.table_id
                    GROUP BY t.id, t.table_name
                    ORDER BY field_count DESC
                """)
                result = await session.execute(field_extremes_query)
                field_data = result.fetchall()

                max_fields_table = "无"
                max_fields_count = 0
                min_fields_table = "无"
                min_fields_count = 0

                if field_data:
                    # 最多字段的表
                    max_record = field_data[0]
                    max_fields_table = max_record.table_name
                    max_fields_count = max_record.field_count
                    
                    # 最少字段的表
                    min_record = field_data[-1]
                    min_fields_table = min_record.table_name
                    min_fields_count = min_record.field_count

                # 计算主键覆盖率
                pk_coverage_query = text("""
                    SELECT 
                        COUNT(DISTINCT t.id) as total_tables,
                        COUNT(DISTINCT CASE WHEN c.is_primary_key = 1 THEN t.id END) as tables_with_pk
                    FROM text2sql_sys_schematable t
                    LEFT JOIN text2sql_sys_schemacolumn c ON t.id = c.table_id
                """)
                result = await session.execute(pk_coverage_query)
                pk_data = result.fetchone()
                
                primary_key_coverage = 0.0
                if pk_data and pk_data.total_tables > 0:
                    primary_key_coverage = (pk_data.tables_with_pk / pk_data.total_tables) * 100

                # 计算外键覆盖率
                fk_coverage_query = text("""
                    SELECT 
                        COUNT(DISTINCT t.id) as total_tables,
                        COUNT(DISTINCT CASE WHEN c.is_foreign_key = 1 THEN t.id END) as tables_with_fk
                    FROM text2sql_sys_schematable t
                    LEFT JOIN text2sql_sys_schemacolumn c ON t.id = c.table_id
                """)
                result = await session.execute(fk_coverage_query)
                fk_data = result.fetchone()
                
                foreign_key_coverage = 0.0
                if fk_data and fk_data.total_tables > 0:
                    foreign_key_coverage = (fk_data.tables_with_fk / fk_data.total_tables) * 100

                return {
                    "avg_fields_per_table": round(float(avg_fields), 1),
                    "max_fields_table": max_fields_table,
                    "max_fields_count": max_fields_count,
                    "min_fields_table": min_fields_table,
                    "min_fields_count": min_fields_count,
                    "primary_key_coverage": round(primary_key_coverage, 1),
                    "foreign_key_coverage": round(foreign_key_coverage, 1)
                }
            except Exception as e:
                logger.error(f"Error getting field analysis data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_data_type_distribution_data(self) -> List[Dict[str, Any]]:
        """获取数据类型分布统计数据"""
        async with await self.get_db_session() as session:
            try:
                datatype_query = text("""
                    SELECT 
                        data_type,
                        COUNT(*) as count
                    FROM text2sql_sys_schemacolumn
                    GROUP BY data_type
                    ORDER BY count DESC
                    LIMIT 10
                """)
                
                result = await session.execute(datatype_query)
                datatype_data = result.fetchall()

                # 计算总字段数
                total_fields = sum(row.count for row in datatype_data)
                
                distributions = []
                for row in datatype_data:
                    if total_fields > 0:
                        percentage = (row.count / total_fields) * 100
                    else:
                        percentage = 0.0
                        
                    distributions.append({
                        "data_type": row.data_type,
                        "count": row.count,
                        "percentage": round(percentage, 1)
                    })

                return distributions
            except Exception as e:
                logger.error(f"Error getting data type distribution data: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def get_table_size_distribution_data(self) -> List[Dict[str, Any]]:
        """获取表规模分布统计数据"""
        async with await self.get_db_session() as session:
            try:
                size_distribution_query = text("""
                    SELECT 
                        CASE 
                            WHEN field_count = 0 THEN '0字段'
                            WHEN field_count BETWEEN 1 AND 5 THEN '1-5字段'
                            WHEN field_count BETWEEN 6 AND 10 THEN '6-10字段'
                            WHEN field_count BETWEEN 11 AND 20 THEN '11-20字段'
                            WHEN field_count BETWEEN 21 AND 50 THEN '21-50字段'
                            ELSE '50+字段'
                        END as size_range,
                        COUNT(*) as table_count
                    FROM (
                        SELECT 
                            t.id,
                            COUNT(c.id) as field_count
                        FROM text2sql_sys_schematable t
                        LEFT JOIN text2sql_sys_schemacolumn c ON t.id = c.table_id
                        GROUP BY t.id
                    ) as table_sizes
                    GROUP BY size_range
                    ORDER BY 
                        CASE size_range
                            WHEN '0字段' THEN 1
                            WHEN '1-5字段' THEN 2
                            WHEN '6-10字段' THEN 3
                            WHEN '11-20字段' THEN 4
                            WHEN '21-50字段' THEN 5
                            WHEN '50+字段' THEN 6
                        END
                """)
                
                result = await session.execute(size_distribution_query)
                size_data = result.fetchall()

                # 计算总表数
                total_tables = sum(row.table_count for row in size_data)
                
                distributions = []
                for row in size_data:
                    if total_tables > 0:
                        percentage = (row.table_count / total_tables) * 100
                    else:
                        percentage = 0.0
                        
                    distributions.append({
                        "size_range": row.size_range,
                        "table_count": row.table_count,
                        "percentage": round(percentage, 1)
                    })

                return distributions
            except Exception as e:
                logger.error(f"Error getting table size distribution data: {str(e)}")
                raise
            finally:
                await session.close() 