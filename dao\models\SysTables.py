from typing import Optional

from sqlalchemy import Date, DateTime, Index, String, text
from sqlalchemy.dialects.mysql import INTEGER, TINYINT, VARCHAR
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
import datetime

class Base(DeclarativeBase):
    pass


class SysDict(Base):
    __tablename__ = 'sys_dict'
    __table_args__ = (
        Index('uk_sd_dict_code', 'dict_code', unique=True),
        Index('uk_sd_tenant_id', 'tenant_id')
    )

    id: Mapped[str] = mapped_column(String(32), primary_key=True)
    dict_name: Mapped[str] = mapped_column(String(100), comment='字典名称')
    dict_code: Mapped[str] = mapped_column(String(100), comment='字典编码')
    description: Mapped[Optional[str]] = mapped_column(String(255), comment='描述')
    del_flag: Mapped[Optional[int]] = mapped_column(INTEGER(1), comment='删除状态')
    create_by: Mapped[Optional[str]] = mapped_column(String(32), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='创建时间')
    update_by: Mapped[Optional[str]] = mapped_column(String(32), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新时间')
    type: Mapped[Optional[int]] = mapped_column(INTEGER(1), server_default=text("'0'"), comment='字典类型0为string,1为number')
    tenant_id: Mapped[Optional[int]] = mapped_column(INTEGER(10), server_default=text("'0'"), comment='租户ID')
    low_app_id: Mapped[Optional[str]] = mapped_column(String(32), comment='低代码应用ID')


class SysDictItem(Base):
    __tablename__ = 'sys_dict_item'
    __table_args__ = (
        Index('idx_sditem_dict_val', 'dict_id', 'item_value'),
        Index('idx_sditem_role_dict_id', 'dict_id'),
        Index('idx_sditem_role_sort_order', 'sort_order'),
        Index('idx_sditem_status', 'status')
    )

    id: Mapped[str] = mapped_column(String(32), primary_key=True)
    item_text: Mapped[str] = mapped_column(String(100), comment='字典项文本')
    item_value: Mapped[str] = mapped_column(String(100), comment='字典项值')
    dict_id: Mapped[Optional[str]] = mapped_column(String(32), comment='字典id')
    item_color: Mapped[Optional[str]] = mapped_column(String(10), comment='字典项颜色')
    description: Mapped[Optional[str]] = mapped_column(String(255), comment='描述')
    sort_order: Mapped[Optional[int]] = mapped_column(INTEGER(10), comment='排序')
    status: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='状态（1启用 0不启用）')
    create_by: Mapped[Optional[str]] = mapped_column(String(32))
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    update_by: Mapped[Optional[str]] = mapped_column(String(32))
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)


class SysUser(Base):
    __tablename__ = 'sys_user'
    __table_args__ = (
        Index('idx_su_del_flag', 'del_flag'),
        Index('idx_su_del_username', 'username', 'del_flag'),
        Index('idx_su_status', 'status'),
        Index('uniq_sys_user_email', 'email', unique=True),
        Index('uniq_sys_user_phone', 'phone', unique=True),
        Index('uniq_sys_user_username', 'username', unique=True),
        Index('uniq_sys_user_work_no', 'work_no', unique=True),
        {'comment': '用户表'}
    )

    id: Mapped[str] = mapped_column(String(32), primary_key=True, comment='主键id')
    username: Mapped[Optional[str]] = mapped_column(String(100), comment='登录账号')
    realname: Mapped[Optional[str]] = mapped_column(VARCHAR(100), comment='真实姓名')
    password: Mapped[Optional[str]] = mapped_column(String(255), comment='密码')
    salt: Mapped[Optional[str]] = mapped_column(String(45), comment='md5密码盐')
    avatar: Mapped[Optional[str]] = mapped_column(String(255), comment='头像')
    birthday: Mapped[Optional[datetime.date]] = mapped_column(Date, comment='生日')
    sex: Mapped[Optional[int]] = mapped_column(TINYINT(1), comment='性别(0-默认未知,1-男,2-女)')
    email: Mapped[Optional[str]] = mapped_column(String(45), comment='电子邮件')
    phone: Mapped[Optional[str]] = mapped_column(String(45), comment='电话')
    org_code: Mapped[Optional[str]] = mapped_column(String(64), comment='登录会话的机构编码')
    status: Mapped[Optional[int]] = mapped_column(TINYINT(1), comment='性别(1-正常,2-冻结)')
    del_flag: Mapped[Optional[int]] = mapped_column(TINYINT(1), comment='删除状态(0-正常,1-已删除)')
    third_id: Mapped[Optional[str]] = mapped_column(String(100), comment='第三方登录的唯一标识')
    third_type: Mapped[Optional[str]] = mapped_column(String(100), comment='第三方类型')
    activiti_sync: Mapped[Optional[int]] = mapped_column(TINYINT(1), comment='同步工作流引擎(1-同步,0-不同步)')
    work_no: Mapped[Optional[str]] = mapped_column(String(100), comment='工号，唯一键')
    telephone: Mapped[Optional[str]] = mapped_column(String(45), comment='座机号')
    create_by: Mapped[Optional[str]] = mapped_column(String(32), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='创建时间')
    update_by: Mapped[Optional[str]] = mapped_column(String(32), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新时间')
    user_identity: Mapped[Optional[int]] = mapped_column(TINYINT(1), comment='身份（1普通成员 2上级）')
    depart_ids: Mapped[Optional[str]] = mapped_column(String(1000), comment='负责部门')
    client_id: Mapped[Optional[str]] = mapped_column(String(64), comment='设备ID')
    login_tenant_id: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='上次登录选择租户ID')
    bpm_status: Mapped[Optional[str]] = mapped_column(String(2), comment='流程入职离职状态')
