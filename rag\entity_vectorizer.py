import asyncio
from typing import Dict, List, Any, Optional, Tuple

from llama_index.core import VectorStoreIndex, Settings
from llama_index.core.schema import TextNode
from llama_index.core.storage import StorageContext
from llama_index.core.vector_stores import MetadataFilters, MetadataFilter, FilterOperator
from llama_index.vector_stores.milvus import MilvusVectorStore

from utils.kg_utils import KnowledgeGraphManager
from config import settings
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

class EntityVectorizer:
    """
    实体向量化处理器
    将知识图谱中的实体和关系转换为向量并存储到Milvus向量数据库中
    """
    
    def __init__(self, 
                 milvus_uri: Optional[str] = None,
                 milvus_collection_prefix: Optional[str] = None,
                 embed_model = None):
        """
        初始化实体向量化处理器
        
        参数:
            milvus_uri: Milvus服务URI，如果为None则使用默认配置
            milvus_collection_prefix: Milvus集合名称前缀，如果为None则使用默认配置
            embed_model: 嵌入模型实例，如果为None则使用全局默认模型
        """
        self.kg_manager = KnowledgeGraphManager.get_instance()
                
        # 使用字典缓存不同collection的索引
        self._index_cache = {}
        
        # Milvus配置
        self.milvus_uri = milvus_uri or settings.configuration.milvus_uri
        self.milvus_collection_prefix = milvus_collection_prefix or settings.configuration.kg_milvus_collection_prefix
        self.embed_model = embed_model  # 存储嵌入模型
        
    async def get_entity_index(self, collection_name: str) -> VectorStoreIndex:
        """获取实体索引"""
        index_key = f"{self.milvus_collection_prefix}{settings.configuration.kg_entity_collection}_{collection_name}"
        if index_key not in self._index_cache:
            await self._load_or_create_index("entity", collection_name)
        return self._index_cache[index_key]
    
    async def get_relation_index(self, collection_name: str) -> VectorStoreIndex:
        """获取关系索引"""
        index_key = f"{self.milvus_collection_prefix}{settings.configuration.kg_relation_collection}_{collection_name}"
        if index_key not in self._index_cache:
            await self._load_or_create_index("relation", collection_name)
        return self._index_cache[index_key]
        
    async def get_contextual_entity_index(self, collection_name: str) -> VectorStoreIndex:
        """获取上下文增强的实体索引"""
        index_key = f"{self.milvus_collection_prefix}{settings.configuration.kg_contextual_entity_collection}_{collection_name}"
        if index_key not in self._index_cache:
            await self._load_or_create_index("contextual_entity", collection_name)
        return self._index_cache[index_key]
        
    async def _load_or_create_index(self, index_type: str,collection_name: str):
        """加载或创建索引"""
        if index_type not in ["entity", "relation", "contextual_entity"]:
            raise ValueError(f"不支持的索引类型: {index_type}")

        # 使用配置的集合名
        collection_name_map = {
            "entity":  settings.configuration.kg_entity_collection,
            "relation": settings.configuration.kg_relation_collection,
            "contextual_entity": settings.configuration.kg_contextual_entity_collection,
        }
        
        # 获取完整集合名称
        full_collection_name = f"{self.milvus_collection_prefix}{collection_name_map[index_type]}_{collection_name}"
        
        try:
            # 创建Milvus向量存储
            vector_store = MilvusVectorStore(
                uri=self.milvus_uri,
                collection_name=full_collection_name,
                dim=int(settings.configuration.embedding_model_dim_bge_large),
                overwrite=False
            )
            
            # 检查集合是否已存在
            from pymilvus import connections, Collection
            connections.connect(uri=self.milvus_uri)
            collection = Collection(full_collection_name)
            
            # 直接从向量存储创建或加载索引，无论集合是否为空
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            if self.embed_model:
                index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=self.embed_model)
            else:
                # 使用默认的本地嵌入模型
                default_embed_model = settings.local_bge_large_embed_model()
                index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=default_embed_model)
            
            if collection.is_empty:
                logger.info(f"已创建空的 Milvus {index_type} 索引 (集合: {full_collection_name})")
            else:
                logger.info(f"已加载 Milvus {index_type} 索引 (集合: {full_collection_name})")
                
            # 设置索引到缓存中，使用与获取索引时相同的键格式
            self._index_cache[full_collection_name] = index
                
        except Exception as e:
            logger.error(f"创建/加载 Milvus {index_type} 索引时出错: {e}")
            import traceback
            traceback.print_exc()
    async def vectorize_entity(self, entity: Dict[str, Any]) -> TextNode:
        """
        将实体转换为文本节点
        
        参数:
            entity: 实体字典，包含id、name、type等字段
            
        返回:
            TextNode: 转换后的文本节点
        """
        entity_id = entity.get("id")
        entity_name = entity.get("name", "未知实体")
        entity_type = entity.get("type", "未知类型")
        entity_source_id = entity.get("source_id", "未知来源")
        entity_file_id = entity.get("file_id", "未知文件")
        entity_collection_name = entity.get("collection_name", "default")
        
        # 获取实体详细信息
        try:
            entity_details = await self.kg_manager.get_entity_details(entity_id)
            
            # 提取文档信息
            doc_titles = []
            for doc in entity_details.get("documents", []):
                doc_title = doc.get("title")
                if doc_title:
                    doc_titles.append(doc_title)
            
            # 提取关系信息
            relations = []
            for rel in entity_details.get("outgoing_relations", []):
                relation = f"{entity_name} --[{rel.get('relation_type', '关系')}]--> {rel.get('name', '目标实体')}"
                relations.append(relation)
                
            for rel in entity_details.get("incoming_relations", []):
                relation = f"{rel.get('name', '来源实体')} --[{rel.get('relation_type', '关系')}]--> {entity_name}"
                relations.append(relation)
                
            # 构建实体描述文本
            entity_text = f"实体: {entity_name}\n类型: {entity_type}\n"
            
            if doc_titles:
                entity_text += f"\n出现在文档:\n" + "\n".join([f"- {title}" for title in doc_titles[:5]])
                
            if relations:
                entity_text += f"\n相关关系:\n" + "\n".join([f"- {relation}" for relation in relations[:5]])
                
        except Exception as e:
            logger.error(f"获取实体 '{entity_id}' 详细信息时出错: {e}")
            # 如果无法获取详细信息，使用基本信息创建文本
            entity_text = f"实体: {entity_name}\n类型: {entity_type}"
        
        # 创建文本节点
        metadata = {
            "entity_id": entity_id,
            "name": entity_name,
            "type": entity_type,
            "node_type": "entity",
            "details": entity_text,
            "source_id": entity_source_id,
            "file_id": entity_file_id,
            "collection_name": entity_collection_name
        }
        
        return TextNode(
            # text=entity_name,
            text = entity_text,#嵌入文本尽量详细，增加语义关联
            metadata=metadata,
            id_=f"entity_{entity_id}_{entity_collection_name}"
        )
    
    async def vectorize_relation(self, relation: Dict[str, Any]) -> TextNode:
        """
        将关系转换为文本节点
        
        参数:
            relation: 关系字典，包含source、target、relation、description等字段
            
        返回:
            TextNode: 转换后的文本节点
        """
        source = relation.get("source", "未知来源")
        source_type = relation.get("source_type", "未知类型")
        target = relation.get("target", "未知目标")
        target_type = relation.get("target_type", "未知类型")
        relation_type = relation.get("type", "未知关系")
        description = relation.get("description", "")
        source_id = relation.get("source_id", "未知来源")
        file_id = relation.get("file_id", "未知文件")
        collection_name = relation.get("collection_name", "default")
        
        # 构建关系描述文本
        relation_text = f"关系: {source} --[{relation_type}]--> {target}\n"
        relation_text += f"源实体类型: {source_type}\n"
        relation_text += f"目标实体类型: {target_type}\n"
        
        if description:
            relation_text += f"描述: {description}"
        
        # 创建唯一ID
        relation_id = f"{source}_{relation_type}_{target}_{collection_name}".replace(" ", "_")
        
        # 创建文本节点
        metadata = {
            "source": source,
            "source_type": source_type,
            "target": target,
            "target_type": target_type,
            "relation_type": relation_type,
            "description": description,
            "node_type": "relation",
            "details": relation_text,
            "source_id": source_id,
            "file_id": file_id,
            "collection_name": collection_name
        }
        
        return TextNode(
            text=relation_text,
            metadata=metadata,
            id_=f"relation_{relation_id}"
        )
    
    async def index_document_entities(self, doc_id: str) -> Tuple[int, int]:
        """
        索引文档中的实体和关系
        
        参数:
            doc_id: 文档ID
        返回:
            Tuple[int, int]: 索引的实体数量和关系数量
        """
        try:
            # 获取文档实体
            doc_entities = await self.kg_manager.get_document_entities(doc_id)
            
            if not doc_entities or "doc_id" not in doc_entities:
                logger.warning(f"文档 '{doc_id}' 未找到实体和关系")
                return 0, 0
                
            # 获取实体和关系
            nodes = doc_entities.get("nodes", [])
            links = doc_entities.get("links", [])
            
            # 创建实体节点
            entity_nodes = []
            collection_name = "default"  # 默认值
            for node in nodes:
                if "collection_name" in node:
                    collection_name = node["collection_name"]
                node["file_id"] = doc_id
                entity_node = await self.vectorize_entity(node)
                entity_nodes.append(entity_node)
                
            # 创建关系节点
            relation_nodes = []
            for link in links:
                link["file_id"] = doc_id
                # 确保关系也有collection_name
                if "collection_name" not in link:
                    link["collection_name"] = collection_name
                relation_node = await self.vectorize_relation(link)
                relation_nodes.append(relation_node)
                
            # 获取索引
            entity_index = await self.get_entity_index(collection_name)
            relation_index = await self.get_relation_index(collection_name)

            # 添加到索引
            if entity_nodes:
                entity_index.insert_nodes(entity_nodes)
                
            if relation_nodes:
                relation_index.insert_nodes(relation_nodes)
                
            return len(entity_nodes), len(relation_nodes)
            
        except Exception as e:
            logger.error(f"索引文档 '{doc_id}' 的实体和关系时出错: {e}")
            import traceback
            traceback.print_exc()
            return 0, 0
    
    async def batch_index_documents(self, doc_ids: List[str], doc_titles: Optional[List[str]] = None) -> Dict[str, int]:
        """
        批量索引多个文档的实体和关系
        
        参数:
            doc_ids: 文档ID列表
            doc_titles: 文档标题列表，与doc_ids一一对应
            
        返回:
            Dict[str, int]: 统计信息，包括处理的文档数、索引的实体数和关系数
        """
        total_entities = 0
        total_relations = 0
        successful_docs = 0
        
        doc_titles = doc_titles or [None] * len(doc_ids)
        
        for i, (doc_id, doc_title) in enumerate(zip(doc_ids, doc_titles)):
            logger.info(f"正在索引文档 {i+1}/{len(doc_ids)}: {doc_id}")
            entities_count, relations_count = await self.index_document_entities(doc_id, doc_title)
            
            if entities_count > 0 or relations_count > 0:
                successful_docs += 1
                total_entities += entities_count
                total_relations += relations_count
                
        return {
            "total_docs": len(doc_ids),
            "successful_docs": successful_docs,
            "failed_docs": len(doc_ids) - successful_docs,
            "total_entities": total_entities,
            "total_relations": total_relations
        }
    
    async def search_entities(self, query: str, top_k: int = 5,collection_name="default" ) -> List[Dict[str, Any]]:
        """
        通过向量搜索查询实体
        
        参数:
            query: 查询文本
            top_k: 返回的最大结果数量
            
        返回:
            List[Dict]: 相关实体列表
        """
        index = await self.get_entity_index(collection_name)
        retriever = index.as_retriever(similarity_top_k=top_k)
        
        try:
            nodes = retriever.retrieve(query)
            
            results = []
            for node in nodes:
                metadata = node.metadata
                if metadata.get("node_type") == "entity":
                    results.append({
                        "id": metadata.get("id"),
                        "name": metadata.get("name"),
                        "type": metadata.get("type"),
                        "score": node.score if hasattr(node, "score") else None,
                        "text": node.text
                    })
                    
            return results
            
        except Exception as e:
            logger.error(f"向量搜索实体时出错: {e}")
            return []
    
    async def search_relations(self, query: str, top_k: int = 5, collection_name="default") -> List[Dict[str, Any]]:
        """
        通过向量搜索查询关系
        
        参数:
            query: 查询文本
            top_k: 返回的最大结果数量
            collection_name: 集合名称
            
        返回:
            List[Dict]: 相关关系列表
        """
        index = await self.get_relation_index(collection_name)
        retriever = index.as_retriever(similarity_top_k=top_k)
        
        try:
            nodes = retriever.retrieve(query)
            
            results = []
            for node in nodes:
                metadata = node.metadata
                if metadata.get("node_type") == "relation":
                    results.append({
                        "source": metadata.get("source"),
                        "source_type": metadata.get("source_type"),
                        "target": metadata.get("target"),
                        "target_type": metadata.get("target_type"),
                        "relation_type": metadata.get("relation_type"),
                        "description": metadata.get("description"),
                        "score": node.score if hasattr(node, "score") else None,
                        "text": node.text
                    })
                    
            return results
            
        except Exception as e:
            logger.error(f"向量搜索关系时出错: {e}")
            return []
            
    async def close(self):
        """关闭资源"""
        pass 

    async def batch_extract_entity_attributes(self, entities: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        批量提取多个实体的属性特征
        
        参数:
            entities: 实体列表
            
        返回:
            Dict[str, List[str]]: 实体ID到属性列表的映射
        """
        try:
            # 构建批量提示词
            batch_prompt = "基于以下实体信息，提取每个实体的关键属性和特征。\n\n"
            
            for i, entity in enumerate(entities, 1):
                entity_name = entity.get("name", "未知实体")
                entity_type = entity.get("type", "未知类型")
                batch_prompt += f"实体 {i}:\n"
                batch_prompt += f"名称: {entity_name}\n"
                batch_prompt += f"类型: {entity_type}\n\n"
            
            batch_prompt += "请为每个实体返回其属性列表，格式如下：\n"
            batch_prompt += "实体1: [属性1: 值1, 属性2: 值2, ...]\n"
            batch_prompt += "实体2: [属性1: 值1, 属性2: 值2, ...]\n"
            
            # 使用LLM批量提取属性
            llm = Settings.llm
            response = await llm.acomplete(batch_prompt)
            import re
            # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
            filter_response_text = re.sub(r'<think>.*?</think>', '', response.text, flags=re.DOTALL)
            # 解析响应
            attributes_map = {}

            
            # 使用正则表达式匹配每个实体的属性列表
            entity_pattern = r"实体(\d+):\s*\[(.*?)\]"
            matches = re.finditer(entity_pattern, filter_response_text, re.DOTALL)
            
            for match in matches:
                entity_idx = int(match.group(1)) - 1  # 转换为0-based索引
                if entity_idx < len(entities):
                    entity_id = entities[entity_idx].get("id")
                    attributes_text = match.group(2)
                    attributes = []
                    
                    # 解析属性列表
                    for line in attributes_text.split(','):
                        line = line.strip().strip('"\'[] ')
                        if line and ':' in line:
                            attributes.append(line)
                            
                    attributes_map[entity_id] = attributes
                    
            return attributes_map
            
        except Exception as e:
            logger.error(f"批量提取实体属性时出错: {e}")
            return {}

    async def batch_get_entity_details(self, entity_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        批量获取多个实体的详细信息
        
        参数:
            entity_ids: 实体ID列表
            
        返回:
            Dict[str, Dict[str, Any]]: 实体ID到详细信息的映射
        """
        try:
            # 并行获取所有实体的详细信息
            tasks = [self.kg_manager.get_entity_details(entity_id) for entity_id in entity_ids]
            details_list = await asyncio.gather(*tasks)
            
            # 构建ID到详细信息的映射
            return {entity_id: details for entity_id, details in zip(entity_ids, details_list)}
            
        except Exception as e:
            logger.error(f"批量获取实体详细信息时出错: {e}")
            return {}

    async def create_multi_dimensional_entity_embedding(self, entity: Dict[str, Any]) -> TextNode:
        """
        创建多维度实体嵌入，捕捉实体在不同上下文中的语义信息
        
        参数:
            entity: 实体字典，包含id、name、type等字段
            
        返回:
            TextNode: 转换后的增强文本节点
        """
        entity_id = entity.get("id")
        entity_name = entity.get("name", "未知实体")
        entity_type = entity.get("type", "未知类型")
        entity_source_id = entity.get("source_id", "未知来源")
        entity_file_id = entity.get("file_id", "未知文件")
        entity_collection_name = entity.get("collection_name", "default")
        
        try:
            # 获取实体详细信息
            entity_details = await self.kg_manager.get_entity_details(entity_id)
            
            # 构建多维度上下文表示
            contexts = {
                "definition": f"实体: {entity_name}\n类型: {entity_type}",
                "documents": [],
                "relations": [],
                "attributes": []
            }
            
            # 提取文档上下文
            for doc in entity_details.get("documents", []):
                doc_title = doc.get("title", "")
                doc_type = doc.get("type", "")
                if doc_title:
                    contexts["documents"].append(f"{doc_title} ({doc_type})")
            
            # 提取关系上下文
            for rel in entity_details.get("outgoing_relations", []):
                relation_context = f"{entity_name} --[{rel.get('relation_type', '关系')}]--> {rel.get('name', '目标实体')}"
                if rel.get("description"):
                    relation_context += f" ({rel.get('description')})"
                contexts["relations"].append(relation_context)
                
            for rel in entity_details.get("incoming_relations", []):
                relation_context = f"{rel.get('name', '来源实体')} --[{rel.get('relation_type', '关系')}]--> {entity_name}"
                if rel.get("description"):
                    relation_context += f" ({rel.get('description')})"
                contexts["relations"].append(relation_context)
            
            # 使用LLM提取实体属性特征
            llm = Settings.llm
            entity_prompt = f"""
            基于以下实体信息，提取该实体的关键属性和特征。
            返回格式为以属性名为键，属性值为值的列表:
            [
              "属性1: 值1",
              "属性2: 值2",
              ...
            ]
            
            实体名称: {entity_name}
            实体类型: {entity_type}
            相关文档: {', '.join(contexts['documents'][:5]) if contexts['documents'] else '无'}
            相关关系: {'; '.join(contexts['relations'][:5]) if contexts['relations'] else '无'}
            """
            
            try:
                # 使用LLM提取属性
                response = await llm.acomplete(entity_prompt)
                # 从响应中提取属性列表
                import re
                attributes_match = re.search(r'\[(.*?)\]', response.text, re.DOTALL)
                if attributes_match:
                    attributes_text = attributes_match.group(1)
                    # 解析属性列表
                    attributes = []
                    for line in attributes_text.split('\n'):
                        line = line.strip().strip('"\'[], ')
                        if line and ':' in line:
                            attributes.append(line)
                    contexts["attributes"] = attributes
            except Exception as e:
                logger.error(f"提取实体 '{entity_id}' 属性时出错: {e}")
            
            # 构建综合文本表示
            entity_text = contexts["definition"] + "\n\n"
            
            if contexts["attributes"]:
                entity_text += "关键属性:\n" + "\n".join([f"- {attr}" for attr in contexts["attributes"][:5]]) + "\n\n"
                
            if contexts["documents"]:
                entity_text += "相关文档:\n" + "\n".join([f"- {doc}" for doc in contexts["documents"][:5]]) + "\n\n"
                
            if contexts["relations"]:
                entity_text += "相关关系:\n" + "\n".join([f"- {rel}" for rel in contexts["relations"][:5]])
            
            # 创建增强文本节点
            metadata = {
                "entity_id": entity_id,
                "name": entity_name,
                "type": entity_type,
                "node_type": "contextual_entity",
                "attributes": contexts["attributes"],
                "documents": contexts["documents"],
                "relations": contexts["relations"],
                "details": entity_text,
                "source_id": entity_source_id,
                "file_id": entity_file_id,
                "collection_name": entity_collection_name,
            }
            
            return TextNode(
                text=entity_name,
                metadata=metadata,
                id_=f"contextual_entity_{entity_id}_{entity_collection_name}"
            )
            
        except Exception as e:
            logger.error(f"创建实体 '{entity_id}' 多维度嵌入时出错: {e}")
            # 如果出错，返回基本实体节点
            metadata = {
                "id": entity_id,
                "name": entity_name,
                "type": entity_type,
                "source_id": entity_source_id,
                "file_id": entity_file_id,
                "collection_name": entity_collection_name,
                "node_type": "contextual_entity"
            }
            
            return TextNode(
                text=entity_name,
                metadata=metadata,
                id_=f"contextual_entity_{entity_id}_{entity_collection_name}"
            )

    async def batch_create_multi_dimensional_entity_embeddings(self, entities: List[Dict[str, Any]]) -> List[TextNode]:
        """
        批量创建多个实体的多维度嵌入
        
        参数:
            entities: 实体列表
            
        返回:
            List[TextNode]: 转换后的增强文本节点列表
        """
        try:
            # 获取配置的批次大小
            batch_size = settings.configuration.kg_entity_batch_size
            
            # 将实体列表分成多个批次
            batches = [entities[i:i + batch_size] for i in range(0, len(entities), batch_size)]
            logger.info(f"将 {len(entities)} 个实体分为 {len(batches)} 个批次处理，每批 {batch_size} 个实体")
            
            all_nodes = []
            for batch_idx, batch in enumerate(batches, 1):
                logger.info(f"正在处理第 {batch_idx}/{len(batches)} 批实体...")
                
                # 批量获取实体详细信息
                entity_ids = [entity.get("id") for entity in batch]
                entity_details_map = await self.batch_get_entity_details(entity_ids)
                
                # 批量提取实体属性
                attributes_map = await self.batch_extract_entity_attributes(batch)
                
                # 创建文本节点
                batch_nodes = []
                for entity in batch:
                    entity_id = entity.get("id")
                    entity_name = entity.get("name", "未知实体")
                    entity_type = entity.get("type", "未知类型")
                    entity_source_id = entity.get("source_id", "未知来源")
                    entity_file_id = entity.get("file_id", "未知文件")
                    entity_collection_name = entity.get("collection_name", "default")
                    
                    # 获取实体详细信息
                    entity_details = entity_details_map.get(entity_id, {})
                    
                    # 构建多维度上下文表示
                    contexts = {
                        "definition": f"实体: {entity_name}\n类型: {entity_type}",
                        "documents": [],
                        "relations": [],
                        "attributes": attributes_map.get(entity_id, [])
                    }
                    
                    # 提取文档上下文
                    for doc in entity_details.get("documents", []):
                        doc_title = doc.get("title", "")
                        doc_type = doc.get("type", "")
                        if doc_title:
                            contexts["documents"].append(f"{doc_title} ({doc_type})")
                    
                    # 提取关系上下文
                    for rel in entity_details.get("outgoing_relations", []):
                        relation_context = f"{entity_name} --[{rel.get('relation_type', '关系')}]--> {rel.get('name', '目标实体')}"
                        if rel.get("description"):
                            relation_context += f" ({rel.get('description')})"
                        contexts["relations"].append(relation_context)
                        
                    for rel in entity_details.get("incoming_relations", []):
                        relation_context = f"{rel.get('name', '来源实体')} --[{rel.get('relation_type', '关系')}]--> {entity_name}"
                        if rel.get("description"):
                            relation_context += f" ({rel.get('description')})"
                        contexts["relations"].append(relation_context)
                    
                    # 构建综合文本表示
                    entity_text = contexts["definition"] + "\n\n"
                    
                    if contexts["attributes"]:
                        entity_text += "关键属性:\n" + "\n".join([f"- {attr}" for attr in contexts["attributes"][:5]]) + "\n\n"
                        
                    if contexts["documents"]:
                        entity_text += "相关文档:\n" + "\n".join([f"- {doc}" for doc in contexts["documents"][:5]]) + "\n\n"
                        
                    if contexts["relations"]:
                        entity_text += "相关关系:\n" + "\n".join([f"- {rel}" for rel in contexts["relations"][:5]])
                    
                    # 创建增强文本节点
                    metadata = {
                        "entity_id": entity_id,
                        "name": entity_name,
                        "type": entity_type,
                        "node_type": "contextual_entity",
                        "attributes": contexts["attributes"],
                        "documents": contexts["documents"],
                        "relations": contexts["relations"],
                        "details": entity_text,
                        "source_id": entity_source_id,
                        "file_id": entity_file_id,
                        "collection_name": entity_collection_name,
                    }
                    
                    node = TextNode(
                        # text=entity_name,
                        text=entity_text,#使用丰富的文本增强语义关联
                        metadata=metadata,
                        id_=f"contextual_entity_{entity_id}_{entity_collection_name}"
                    )
                    batch_nodes.append(node)
                
                all_nodes.extend(batch_nodes)
                logger.info(f"第 {batch_idx} 批处理完成，生成了 {len(batch_nodes)} 个节点")
            
            logger.success(f"所有批次处理完成，共生成 {len(all_nodes)} 个节点")
            return all_nodes
            
        except Exception as e:
            logger.error(f"批量创建实体多维度嵌入时出错: {e}")
            # 如果出错，返回基本实体节点
            return [
                TextNode(
                    text=entity.get("name", "未知实体"),
                    metadata={
                        "id": entity.get("id"),
                        "name": entity.get("name", "未知实体"),
                        "type": entity.get("type", "未知类型"),
                        "source_id": entity.get("source_id", "未知来源"),
                        "file_id": entity.get("file_id", "未知文件"),
                        "collection_name": entity.get("collection_name", "default"),
                        "node_type": "contextual_entity"
                    },
                    id_=f"contextual_entity_{entity.get('id')}_{entity.get('collection_name', 'default')}"
                )
                for entity in entities
            ]

    async def index_document_entities_with_contextual(self, doc_id: str) -> Tuple[int, int, int]:
        """
        索引文档中的实体、关系和上下文增强的实体
        
        参数:
            doc_id: 文档ID
        返回:
            Tuple[int, int, int]: 索引的实体数量、关系数量和上下文实体数量
        """
        try:
            # 基本实体和关系索引
            entity_count, relation_count = await self.index_document_entities(doc_id)
            
            # 获取文档实体
            doc_entities = await self.kg_manager.get_document_entities(doc_id)
            
            if not doc_entities or "doc_id" not in doc_entities:
                return entity_count, relation_count, 0
                
            # 获取实体节点
            nodes = doc_entities.get("nodes", [])
            
            # 为每个节点添加file_id，并获取collection_name
            collection_name = "default"
            for node in nodes:
                node["file_id"] = doc_id
                if "collection_name" in node:
                    collection_name = node["collection_name"]
            
            # 批量创建上下文增强的实体节点
            contextual_entity_nodes = await self.batch_create_multi_dimensional_entity_embeddings(nodes)
                
            # 获取上下文实体索引
            contextual_index = await self.get_contextual_entity_index(collection_name)
            
            # 添加到索引
            if contextual_entity_nodes:
                contextual_index.insert_nodes(contextual_entity_nodes)
                
            return entity_count, relation_count, len(contextual_entity_nodes)
            
        except Exception as e:
            logger.error(f"索引文档 '{doc_id}' 上下文实体时出错: {e}")
            return 0, 0, 0
    
    async def search_contextual_entities(self, query: str, top_k: int = 5,collection_name="default") -> List[Dict[str, Any]]:
        """
        使用多维度实体嵌入搜索上下文相关的实体
        
        参数:
            query: 搜索查询
            top_k: 返回的最大结果数量
            
        返回:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            contextual_index = await self.get_contextual_entity_index(collection_name)
            
            # 使用向量搜索
            # 定义过滤条件：只检索 "collection_name"下的实体
            # filters = MetadataFilters(
            #     filters=[
            #         MetadataFilter(key="collection_name", value=collection_name, operator=FilterOperator.EQ)
            #     ],
            # )
            # retriever = contextual_index.as_retriever(similarity_top_k=top_k,filters=filters)
            retriever = contextual_index.as_retriever(similarity_top_k=top_k)
            nodes = await retriever.aretrieve(query)
            
            # 提取结果
            results = []
            for node in nodes:
                metadata = node.metadata
                
                # 构建结果
                result = {
                    "id": metadata.get("entity_id"),
                    "name": metadata.get("name"),
                    "type": metadata.get("type"),
                    "score": node.score,  # 相似度分数
                    "attributes": metadata.get("attributes", []),
                    "documents": metadata.get("documents", []),
                    "relations": metadata.get("relations", [])
                }
                
                results.append(result)
                
            return results
        except Exception as e:
            logger.error(f"搜索上下文增强实体时出错: {e}")
            return []

    #todo 具体的搜索算法待考虑
    async def search_text2sql_tables(self, query: str, connection_id: str = None, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索text2sql数据库表信息
        
        参数:
            query: 搜索查询
            connection_id: 可选的连接ID，用于过滤特定数据源的表
            top_k: 返回的最大结果数量
            
        返回:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            # 创建text2sql_doc集合的索引
            collection_name = f"{self.milvus_collection_prefix}text2sql_doc"
            
            # 创建Milvus向量存储
            vector_store = MilvusVectorStore(
                uri=self.milvus_uri,
                collection_name=collection_name,
                dim=int(settings.configuration.embedding_model_dim_bge_large),
                overwrite=False
            )
            
            # 获取嵌入模型
            embed_model = settings.local_bge_large_embed_model()
            
            # 创建索引
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=embed_model)
            
            # 构建过滤条件
            filters = None
            if connection_id:
                filters = MetadataFilters(
                    filters=[
                        MetadataFilter(key="connection_id", value=connection_id, operator=FilterOperator.EQ),
                        MetadataFilter(key="node_type", value="text2sql_table", operator=FilterOperator.EQ)
                    ],
                )
            else:
                filters = MetadataFilters(
                    filters=[
                        MetadataFilter(key="node_type", value="text2sql_table", operator=FilterOperator.EQ)
                    ],
                )
            
            # 创建检索器
            retriever = index.as_retriever(similarity_top_k=top_k, filters=filters)
            nodes = await retriever.aretrieve(query)
            
            # 提取结果
            results = []
            for node in nodes:
                metadata = node.metadata
                
                # 构建结果
                result = {
                    "table_id": metadata.get("table_id"),
                    "table_name": metadata.get("table_name"),
                    "connection_id": metadata.get("connection_id"),
                    "connection_name": metadata.get("connection_name"),
                    "columns_count": metadata.get("columns_count", 0),
                    "relationships_count": metadata.get("relationships_count", 0),
                    "score": node.score,  # 相似度分数
                    "text": node.text,  # 表的详细描述文本
                    "node_type": metadata.get("node_type")
                }
                
                results.append(result)
                
            return results
            
        except Exception as e:
            logger.error(f"搜索text2sql表信息时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def get_text2sql_table_by_id(self, table_id: int, connection_id: str) -> Dict[str, Any]:
        """
        根据表ID和连接ID获取特定的text2sql表信息
        
        参数:
            table_id: 表ID
            connection_id: 连接ID
            
        返回:
            Dict[str, Any]: 表信息，如果未找到则返回空字典
        """
        try:
            # 创建text2sql_doc集合的索引
            collection_name = f"{self.milvus_collection_prefix}text2sql_doc"
            
            # 创建Milvus向量存储
            vector_store = MilvusVectorStore(
                uri=self.milvus_uri,
                collection_name=collection_name,
                dim=int(settings.configuration.embedding_model_dim_bge_large),
                overwrite=False
            )
            
            # 获取嵌入模型
            embed_model = settings.local_bge_large_embed_model()
            
            # 创建索引
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=embed_model)
            
            # 构建过滤条件
            filters = MetadataFilters(
                filters=[
                    MetadataFilter(key="table_id", value=table_id, operator=FilterOperator.EQ),
                    MetadataFilter(key="connection_id", value=connection_id, operator=FilterOperator.EQ),
                    MetadataFilter(key="node_type", value="text2sql_table", operator=FilterOperator.EQ)
                ],
            )
            
            # 创建检索器，使用表名作为查询（因为我们知道具体要找的表）
            retriever = index.as_retriever(similarity_top_k=1, filters=filters)
            nodes = await retriever.aretrieve("table")  # 使用通用查询词
            
            if nodes:
                node = nodes[0]
                metadata = node.metadata
                
                return {
                    "table_id": metadata.get("table_id"),
                    "table_name": metadata.get("table_name"),
                    "connection_id": metadata.get("connection_id"),
                    "connection_name": metadata.get("connection_name"),
                    "columns_count": metadata.get("columns_count", 0),
                    "relationships_count": metadata.get("relationships_count", 0),
                    "text": node.text,  # 表的详细描述文本
                    "node_type": metadata.get("node_type")
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"获取text2sql表信息时出错: {e}")
            return {}

    async def clear_text2sql_connection_data(self, connection_id: str) -> bool:
        """
        清除指定connection_id的所有text2sql向量数据
        
        参数:
            connection_id: 要清除数据的连接ID
            
        返回:
            bool: 清除是否成功
        """
        try:
            # 创建text2sql_doc集合的索引
            collection_name = f"{self.milvus_collection_prefix}text2sql_doc"
            
            # 检查集合是否存在
            from pymilvus import connections, Collection
            connections.connect(uri=self.milvus_uri)
            
            try:
                collection = Collection(collection_name)
                collection_exists = True
            except Exception:
                collection_exists = False
                logger.info(f"集合 '{collection_name}' 不存在，跳过清除操作")
                return True
            
            if collection_exists and not collection.is_empty:
                # 构建删除表达式
                delete_expr = f"connection_id == '{connection_id}'"
                
                # 执行删除
                delete_result = collection.delete(delete_expr)
                collection.flush()
                
                logger.info(f"已清除集合 '{collection_name}' 中连接 '{connection_id}' 的向量数据")
                return True
            else:
                logger.info(f"集合 '{collection_name}' 为空，跳过清除操作")
                return True
                
        except Exception as e:
            logger.error(f"清除连接 '{connection_id}' 的向量数据时出错: {e}")
            import traceback
            traceback.print_exc()
            return False 