#!/usr/bin/env python3
"""
MCP客户端测试脚本

用于测试YQ AI V3 MCP服务器的各个组件功能：
1. RAG服务器的get_rag_collections功能
2. 知识图谱服务器的get_knowledge_graph_statistics功能  
3. Text2SQL服务器的text2sql_usage_prompt功能
4. Text2SQL训练服务器的training_troubleshooting_prompt功能

使用 FastMCP 2.0+ 客户端实现
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from fastmcp import Client
    from fastmcp.exceptions import ClientError, McpError
except ImportError:
    print("❌ 错误：未找到FastMCP客户端库")
    print("请安装FastMCP客户端库：pip install fastmcp")
    sys.exit(1)

from utils.logger import get_logger

# 获取logger实例
logger = get_logger()


class MCPClientTester:
    """FastMCP客户端测试器"""
    
    def __init__(self, server_path: Optional[str] = None):
        """
        初始化FastMCP客户端测试器
        
        Args:
            server_path: MCP服务器路径，默认使用本地mcp_server.py
        """
        if server_path is None:
            # 默认使用当前目录的mcp_server.py
            server_path = str(project_root / "mcp_server.py")
        
        self.server_path = server_path
        
        # FastMCP会自动推断传输类型
        # .py文件会使用PythonStdioTransport
        self.client = Client(server_path, timeout=30.0)  # 30秒全局超时
    
    async def diagnose_server(self):
        """诊断服务器问题"""
        print("\n🔍 开始服务器诊断...")
        
        # 检查是否是本地Python文件
        if self.server_path.endswith('.py'):
            server_file = Path(self.server_path)
            if not server_file.exists():
                print(f"❌ 服务器文件不存在: {self.server_path}")
                return False
            
            print(f"✅ 服务器文件存在: {self.server_path}")
            
            # 尝试简单的语法检查
            try:
                import subprocess
                result = subprocess.run(
                    [sys.executable, "-m", "py_compile", str(server_file)], 
                    capture_output=True, 
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    print("✅ 服务器文件语法检查通过")
                else:
                    print(f"❌ 服务器文件语法错误:")
                    print(result.stderr)
                    return False
            except Exception as e:
                print(f"⚠️ 无法进行语法检查: {e}")
            
            # 尝试运行服务器进行快速测试
            try:
                print("🧪 尝试快速启动服务器测试...")
                result = subprocess.run(
                    [sys.executable, str(server_file), "--help"], 
                    capture_output=True, 
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    print("✅ 服务器可以正常启动")
                else:
                    print(f"❌ 服务器启动失败:")
                    print("STDOUT:", result.stdout)
                    print("STDERR:", result.stderr)
                    return False
            except subprocess.TimeoutExpired:
                print("⚠️ 服务器启动超时，但这可能是正常的")
            except Exception as e:
                print(f"❌ 服务器启动测试失败: {e}")
                return False
        
        elif self.server_path.startswith('http'):
            print(f"🌐 检查HTTP服务器连接: {self.server_path}")
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    # 尝试访问根路径或健康检查端点
                    base_url = self.server_path.replace('/mcp', '')
                    health_urls = [
                        f"{base_url}/health",
                        f"{base_url}/",
                        f"{base_url}/info"
                    ]
                    
                    for url in health_urls:
                        try:
                            async with session.get(url, timeout=5) as response:
                                print(f"✅ 服务器响应 {url}: {response.status}")
                                return True
                        except Exception as e:
                            print(f"⚠️ 无法访问 {url}: {e}")
                    
                    print("❌ 所有健康检查端点都无法访问")
                    return False
                        
            except ImportError:
                print("⚠️ 需要安装aiohttp进行HTTP服务器检查: pip install aiohttp")
            except Exception as e:
                print(f"❌ HTTP服务器检查失败: {e}")
                return False
        
        return True
        
    async def test_connection(self):
        """测试与MCP服务器的连接"""
        try:
            print("🔗 正在测试MCP服务器连接...")
            print(f"🔧 传输类型: {self.client.transport}")
            
            async with self.client:
                # 尝试ping服务器
                await self.client.ping()
                print("✅ MCP服务器连接成功")
                return True
                
        except Exception as e:
            print(f"❌ 连接MCP服务器失败: {e}")
            
            # 提供诊断建议
            transport_str = str(self.client.transport).lower()
            if "http" in transport_str:
                if "ssl" in str(e).lower() or "wrong version number" in str(e).lower():
                    print("💡 建议: SSL错误，请尝试使用 http:// 而不是 https://")
                    print("   例如: python mcp_client_test.py http://127.0.0.1:8001/mcp")
                elif "connection" in str(e).lower():
                    print("💡 建议: 请确保MCP服务器已启动并在指定端口运行")
                    print("   启动服务器: python mcp_server.py --transport streamable-http --port 8001")
            elif "stdio" in transport_str:
                if "connection closed" in str(e).lower():
                    print("💡 建议: stdio连接关闭，可能的原因:")
                    print("   1. 服务器脚本有语法错误或导入错误")
                    print("   2. 服务器启动时崩溃")  
                    print("   3. 请先单独测试服务器: python mcp_server.py")
                
            return False
    
    async def list_server_capabilities(self):
        """列出服务器能力"""
        try:
            print("\n📋 正在获取服务器能力...")
            
            async with self.client:
                # 获取工具列表
                tools = await self.client.list_tools()
                print(f"🔧 可用工具数量: {len(tools)}")
                
                # 获取资源列表
                resources = await self.client.list_resources()
                print(f"📁 可用资源数量: {len(resources)}")
                
                # 获取提示模板列表
                prompts = await self.client.list_prompts()
                print(f"💬 可用提示模板数量: {len(prompts)}")
                
                # 详细列出需要的工具和提示
                tool_names = [tool.name for tool in tools]
                prompt_names = [prompt.name for prompt in prompts]
                
                print(f"\n🔍 查找目标工具/提示:")
                target_tools = ["rag_get_rag_collections", "kg_get_knowledge_graph_statistics"]
                target_prompts = ["text2sql_text2sql_usage_prompt", "train_training_troubleshooting_prompt"]
                
                for tool in target_tools:
                    status = "✅" if tool in tool_names else "❌"
                    print(f"  {status} 工具: {tool}")
                    
                for prompt in target_prompts:
                    status = "✅" if prompt in prompt_names else "❌"
                    print(f"  {status} 提示: {prompt}")
                
                return {
                    "tools": tool_names,
                    "resources": [resource.name for resource in resources],
                    "prompts": prompt_names
                }
            
        except Exception as e:
            print(f"❌ 获取服务器能力失败: {e}")
            return None
    
    async def test_rag_get_collections(self):
        """测试RAG服务器的get_rag_collections功能"""
        try:
            print("\n🔍 测试 RAG 服务器 - get_rag_collections")
            print("-" * 50)
            
            async with self.client:
                # 调用工具
                result = await self.client.call_tool("rag_get_rag_collections", {})
                
                print("✅ RAG集合列表获取成功")
                
                # FastMCP返回的是content列表，通常是TextContent
                if result and len(result) > 0:
                    # 尝试解析JSON内容
                    content_text = result[0].text if hasattr(result[0], 'text') else str(result[0])
                    try:
                        parsed_result = json.loads(content_text)
                        print(f"📊 结果: {json.dumps(parsed_result, indent=2, ensure_ascii=False)}")
                        return parsed_result
                    except json.JSONDecodeError:
                        print(f"📊 原始结果: {content_text}")
                        return content_text
                else:
                    print("📊 结果为空")
                    return None
            
        except ClientError as e:
            print(f"❌ 测试RAG get_rag_collections失败 (工具错误): {e}")
            return None
        except Exception as e:
            print(f"❌ 测试RAG get_rag_collections失败: {e}")
            return None
    
    async def test_knowledge_graph_statistics(self):
        """测试知识图谱服务器的get_knowledge_graph_statistics功能"""
        try:
            print("\n🔍 测试知识图谱服务器 - get_knowledge_graph_statistics")
            print("-" * 50)
            
            async with self.client:
                # 调用工具
                result = await self.client.call_tool("kg_get_knowledge_graph_statistics", {})
                
                print("✅ 知识图谱统计信息获取成功")
                
                # 处理返回结果
                if result and len(result) > 0:
                    content_text = result[0].text if hasattr(result[0], 'text') else str(result[0])
                    try:
                        parsed_result = json.loads(content_text)
                        print(f"📊 结果: {json.dumps(parsed_result, indent=2, ensure_ascii=False)}")
                        return parsed_result
                    except json.JSONDecodeError:
                        print(f"📊 原始结果: {content_text}")
                        return content_text
                else:
                    print("📊 结果为空")
                    return None
            
        except ClientError as e:
            print(f"❌ 测试知识图谱统计信息获取失败 (工具错误): {e}")
            return None
        except Exception as e:
            print(f"❌ 测试知识图谱统计信息获取失败: {e}")
            return None
    
    async def test_text2sql_usage_prompt(self):
        """测试Text2SQL服务器的text2sql_usage_prompt功能"""
        try:
            print("\n🔍 测试 Text2SQL 服务器 - text2sql_usage_prompt")
            print("-" * 50)
            
            async with self.client:
                # 调用提示模板
                result = await self.client.get_prompt("text2sql_text2sql_usage_prompt", {})
                
                print("✅ Text2SQL使用提示获取成功")
                
                # FastMCP返回的提示结果包含messages列表
                if result and result.messages and len(result.messages) > 0:
                    # 获取第一个消息的内容
                    message = result.messages[0]
                    if hasattr(message.content, 'text'):
                        content_text = message.content.text
                    else:
                        content_text = str(message.content)
                    
                    print(f"💬 提示内容预览: {content_text[:200]}...")
                    print(f"📝 完整提示长度: {len(content_text)} 字符")
                    
                    return content_text
                else:
                    print("📊 提示内容为空")
                    return None
            
        except ClientError as e:
            print(f"❌ 测试Text2SQL使用提示获取失败 (提示错误): {e}")
            return None
        except Exception as e:
            print(f"❌ 测试Text2SQL使用提示获取失败: {e}")
            return None
    
    async def test_training_troubleshooting_prompt(self): 
        """测试Text2SQL训练服务器的training_troubleshooting_prompt功能"""
        try:
            print("\n🔍 测试 Text2SQL 训练服务器 - training_troubleshooting_prompt")
            print("-" * 50)
            
            async with self.client:
                # 调用提示模板
                result = await self.client.get_prompt("train_training_troubleshooting_prompt", {})
                
                print("✅ 训练故障排除提示获取成功")
                
                # 处理返回的提示结果
                if result and result.messages and len(result.messages) > 0:
                    message = result.messages[0]
                    if hasattr(message.content, 'text'):
                        content_text = message.content.text
                    else:
                        content_text = str(message.content)
                    
                    print(f"💬 提示内容预览: {content_text[:200]}...")
                    print(f"📝 完整提示长度: {len(content_text)} 字符")
                    
                    return content_text
                else:
                    print("📊 提示内容为空")
                    return None
            
        except ClientError as e:
            print(f"❌ 测试训练故障排除提示获取失败 (提示错误): {e}")
            return None
        except Exception as e:
            print(f"❌ 测试训练故障排除提示获取失败: {e}")
            return None
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FastMCP服务器功能测试")
        print("=" * 60)
        
        # 首先进行服务器诊断
        if not await self.diagnose_server():
            print("\n❌ 服务器诊断失败，跳过连接测试")
            return False
        
        # 然后测试连接
        if not await self.test_connection():
            return False
        
        # 获取服务器能力
        capabilities = await self.list_server_capabilities()
        
        # 执行各项测试
        results = {}
        
        # 测试1: RAG集合获取
        results['rag_collections'] = await self.test_rag_get_collections()
        
        # 测试2: 知识图谱统计
        results['kg_statistics'] = await self.test_knowledge_graph_statistics()
        
        # 测试3: Text2SQL使用提示
        results['text2sql_usage'] = await self.test_text2sql_usage_prompt()
        
        # 测试4: 训练故障排除提示
        results['training_troubleshooting'] = await self.test_training_troubleshooting_prompt()
        
        # 汇总测试结果
        print("\n📊 测试结果汇总")
        print("=" * 60)
        
        success_count = 0
        total_count = len(results)
        
        for test_name, result in results.items():
            status = "✅ 成功" if result is not None else "❌ 失败"
            print(f"{test_name}: {status}")
            if result is not None:
                success_count += 1
        
        print(f"\n🎯 总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # 如果有成功的测试，保存详细结果到文件
        if success_count > 0:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"fastmcp_test_results_{timestamp}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "client_type": "FastMCP Client",
                    "server_capabilities": capabilities,
                    "test_results": results,
                    "summary": {
                        "success_count": success_count,
                        "total_count": total_count,
                        "success_rate": success_count/total_count
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"📄 详细结果已保存到: {result_file}")
        
        return success_count == total_count


async def main():
    """主函数"""
    print("YQ AI V3 FastMCP服务器功能测试工具")
    print("=" * 60)
    
    # 检查命令行参数
    server_path = None
    if len(sys.argv) > 1:
        # 如果提供了参数，使用自定义服务器路径
        server_path = sys.argv[1]
        print(f"📡 使用自定义服务器路径: {server_path}")
    else:
        print("📡 使用默认服务器路径: mcp_server.py")
    
    # 提供使用建议
    if server_path and server_path.startswith('https://'):
        print("💡 注意: 如果遇到SSL错误，请尝试使用 http:// 替代 https://")
    
    # 创建测试器并运行测试
    tester = MCPClientTester(server_path)
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 所有测试都通过了！FastMCP服务器功能正常")
            sys.exit(0)
        else:
            print("\n⚠️ 部分测试失败，请根据上述建议检查服务器配置")
            print("\n📖 常见解决方案:")
            print("1. HTTP服务器: 确保使用 http:// 而不是 https://")
            print("2. Stdio服务器: 检查服务器脚本是否有语法错误")
            print("3. 端口问题: 确保服务器在正确端口运行")
            print("4. 防火墙: 检查防火墙设置是否阻止连接")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        import traceback
        print("\n📋 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 