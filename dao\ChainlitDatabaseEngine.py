from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine
from config.config import RAGConfig

class ChainlitDatabaseEngine:
    """Chainlit PostgreSQL数据库引擎单例类"""
    _instance: AsyncEngine = None
    
    @classmethod
    def get_engine(cls) -> AsyncEngine:
        """获取Chainlit PostgreSQL数据库引擎"""
        if cls._instance is None:
            config = RAGConfig()
            connection_string = config.pg_connection_string
            
            if not connection_string:
                raise ValueError("PostgreSQL连接字符串未配置，请设置PG_CONNECTION_STRING环境变量")
            
            # 如果连接字符串不是异步格式，转换为异步格式
            if connection_string.startswith("postgresql://"):
                connection_string = connection_string.replace("postgresql://", "postgresql+asyncpg://", 1)
            elif not connection_string.startswith("postgresql+asyncpg://"):
                # 如果是其他格式，假设是asyncpg格式
                if not connection_string.startswith("postgresql+"):
                    connection_string = "postgresql+asyncpg://" + connection_string
            
            cls._instance = create_async_engine(
                connection_string,
                echo=False,  # 生产环境建议设为False
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600  # 1小时回收连接
            )
        
        return cls._instance
    
    @classmethod
    async def close_engine(cls):
        """关闭数据库引擎"""
        if cls._instance:
            await cls._instance.dispose()
            cls._instance = None 