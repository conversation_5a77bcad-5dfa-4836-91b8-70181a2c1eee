"""
Redis缓存配置模块
提供统一的缓存配置管理
"""
from typing import Optional


class RedisCacheConfig:
    """Redis缓存配置类"""
    
    def __init__(self):
        """初始化配置，从settings.configuration获取"""
        # 延迟导入避免循环依赖
        from config.settings import configuration
        self.config = configuration
    
    # Redis连接配置
    @property
    def REDIS_HOST(self) -> str:
        return self.config.redis_host
    
    @property
    def REDIS_PORT(self) -> int:
        return self.config.redis_port
    
    @property
    def REDIS_DB(self) -> int:
        return self.config.redis_db
    
    @property
    def REDIS_PASSWORD(self) -> Optional[str]:
        return self.config.redis_password
    
    # 连接池配置
    @property
    def REDIS_MAX_CONNECTIONS(self) -> int:
        return self.config.redis_max_connections
    
    @property
    def REDIS_SOCKET_TIMEOUT(self) -> float:
        return self.config.redis_socket_timeout
    
    @property
    def REDIS_SOCKET_CONNECT_TIMEOUT(self) -> float:
        return self.config.redis_socket_connect_timeout
    
    @property
    def REDIS_HEALTH_CHECK_INTERVAL(self) -> int:
        return self.config.redis_health_check_interval
    
    # 缓存时间配置（秒）
    @property
    def CACHE_TIME_SHORT(self) -> int:
        return self.config.cache_time_short
    
    @property
    def CACHE_TIME_MEDIUM(self) -> int:
        return self.config.cache_time_medium
    
    @property
    def CACHE_TIME_LONG(self) -> int:
        return self.config.cache_time_long
    
    @property
    def CACHE_TIME_VERY_LONG(self) -> int:
        return self.config.cache_time_very_long
    
    # 统计接口专用缓存时间配置
    @property
    def STATISTICS_OVERVIEW_CACHE_TIME(self) -> int:
        return self.config.statistics_overview_cache_time
    
    @property
    def STATISTICS_TREND_CACHE_TIME(self) -> int:
        return self.config.statistics_trend_cache_time
    
    @property
    def STATISTICS_DISTRIBUTION_CACHE_TIME(self) -> int:
        return self.config.statistics_distribution_cache_time
    
    @property
    def STATISTICS_DETAILED_CACHE_TIME(self) -> int:
        return self.config.statistics_detailed_cache_time
    
    @property
    def STATISTICS_CHART_CACHE_TIME(self) -> int:
        return self.config.statistics_chart_cache_time
    
    # 缓存键前缀配置
    @property
    def CACHE_PREFIX(self) -> str:
        return self.config.cache_prefix
    
    @property
    def STATISTICS_CACHE_PREFIX(self) -> str:
        return self.config.statistics_cache_prefix
    
    @property
    def DATABASE_STATS_PREFIX(self) -> str:
        return self.config.database_stats_prefix
    
    @property
    def KNOWLEDGE_STATS_PREFIX(self) -> str:
        return self.config.knowledge_stats_prefix
    
    # 缓存开关配置
    @property
    def ENABLE_CACHE(self) -> bool:
        return self.config.enable_cache
    
    @property
    def ENABLE_STATISTICS_CACHE(self) -> bool:
        return self.config.enable_statistics_cache
    
    # 缓存清理配置
    @property
    def AUTO_CLEAR_EXPIRED_CACHE(self) -> bool:
        return self.config.auto_clear_expired_cache
    
    @property
    def CACHE_CLEANUP_INTERVAL(self) -> int:
        return self.config.cache_cleanup_interval
    
    # 性能配置
    @property
    def CACHE_SERIALIZATION_FORMAT(self) -> str:
        return self.config.cache_serialization_format
    
    @property
    def COMPRESS_CACHE_DATA(self) -> bool:
        return self.config.compress_cache_data
    
    def get_redis_url(self) -> str:
        """
        获取Redis连接URL
        
        Returns:
            str: Redis连接URL
        """
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        else:
            return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    def get_connection_params(self) -> dict:
        """
        获取Redis连接参数字典
        
        Returns:
            dict: 连接参数
        """
        params = {
            'host': self.REDIS_HOST,
            'port': self.REDIS_PORT,
            'db': self.REDIS_DB,
            'decode_responses': True,
            'socket_timeout': self.REDIS_SOCKET_TIMEOUT,
            'socket_connect_timeout': self.REDIS_SOCKET_CONNECT_TIMEOUT,
            'retry_on_timeout': True,
            'health_check_interval': self.REDIS_HEALTH_CHECK_INTERVAL,
            'max_connections': self.REDIS_MAX_CONNECTIONS,
        }
        
        if self.REDIS_PASSWORD:
            params['password'] = self.REDIS_PASSWORD
            
        return params
    
    def is_cache_enabled(self) -> bool:
        """
        检查缓存是否启用
        
        Returns:
            bool: 缓存是否启用
        """
        return self.ENABLE_CACHE and self.ENABLE_STATISTICS_CACHE
    
    def get_cache_time_by_type(self, cache_type: str) -> int:
        """
        根据缓存类型获取缓存时间
        
        Args:
            cache_type: 缓存类型 (overview, trend, distribution, detailed, chart)
            
        Returns:
            int: 缓存时间（秒）
        """
        cache_times = {
            'overview': self.STATISTICS_OVERVIEW_CACHE_TIME,
            'trend': self.STATISTICS_TREND_CACHE_TIME,
            'distribution': self.STATISTICS_DISTRIBUTION_CACHE_TIME,
            'detailed': self.STATISTICS_DETAILED_CACHE_TIME,
            'chart': self.STATISTICS_CHART_CACHE_TIME,
            'short': self.CACHE_TIME_SHORT,
            'medium': self.CACHE_TIME_MEDIUM,
            'long': self.CACHE_TIME_LONG,
            'very_long': self.CACHE_TIME_VERY_LONG,
        }
        
        return cache_times.get(cache_type, self.CACHE_TIME_SHORT)


# 创建全局配置实例
redis_config = RedisCacheConfig()


# 环境变量配置说明
"""
Redis缓存配置环境变量说明：

基础连接配置：
- REDIS_HOST: Redis服务器地址 (默认: localhost)
- REDIS_PORT: Redis服务器端口 (默认: 6379)
- REDIS_DB: 使用的数据库编号 (默认: 0)
- REDIS_PASSWORD: Redis密码 (默认: 123456)

连接池配置：
- REDIS_MAX_CONNECTIONS: 最大连接数 (默认: 20)
- REDIS_SOCKET_TIMEOUT: 套接字超时时间 (默认: 5.0秒)
- REDIS_SOCKET_CONNECT_TIMEOUT: 连接超时时间 (默认: 5.0秒)
- REDIS_HEALTH_CHECK_INTERVAL: 健康检查间隔 (默认: 30秒)

缓存时间配置：
- CACHE_TIME_SHORT: 短时间缓存 (默认: 300秒)
- CACHE_TIME_MEDIUM: 中等时间缓存 (默认: 600秒)
- CACHE_TIME_LONG: 长时间缓存 (默认: 1800秒)
- CACHE_TIME_VERY_LONG: 超长时间缓存 (默认: 3600秒)

统计接口专用缓存时间：
- STATISTICS_OVERVIEW_CACHE_TIME: 概览数据缓存时间 (默认: 300秒)
- STATISTICS_TREND_CACHE_TIME: 趋势数据缓存时间 (默认: 600秒)
- STATISTICS_DISTRIBUTION_CACHE_TIME: 分布数据缓存时间 (默认: 900秒)
- STATISTICS_DETAILED_CACHE_TIME: 详细分析缓存时间 (默认: 1800秒)
- STATISTICS_CHART_CACHE_TIME: 图表数据缓存时间 (默认: 300秒)

缓存开关配置：
- ENABLE_CACHE: 是否启用缓存 (默认: true)
- ENABLE_STATISTICS_CACHE: 是否启用统计缓存 (默认: true)

缓存键前缀配置：
- CACHE_PREFIX: 总缓存前缀 (默认: yq_ai_cache:)
- STATISTICS_CACHE_PREFIX: 统计缓存前缀 (默认: stats_cache:)
- DATABASE_STATS_PREFIX: 数据库统计前缀 (默认: db_stats_)
- KNOWLEDGE_STATS_PREFIX: 知识统计前缀 (默认: know_stats_)

性能配置：
- AUTO_CLEAR_EXPIRED_CACHE: 自动清理过期缓存 (默认: true)
- CACHE_CLEANUP_INTERVAL: 缓存清理间隔 (默认: 3600秒)
- CACHE_SERIALIZATION_FORMAT: 序列化格式 (默认: json)
- COMPRESS_CACHE_DATA: 是否压缩缓存数据 (默认: false)

注意：所有配置现在统一通过config/config.py中的RAGConfig管理，
不再直接使用os.getenv()获取环境变量。

使用示例：
export REDIS_HOST=127.0.0.1
export REDIS_PORT=6379
export REDIS_PASSWORD=your_password
export STATISTICS_OVERVIEW_CACHE_TIME=300
export ENABLE_STATISTICS_CACHE=true
""" 