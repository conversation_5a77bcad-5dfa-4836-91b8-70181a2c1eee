<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="3" y="11" width="18" height="10" rx="3" fill="url(#robotGradient)" stroke="#ffffff" stroke-width="1"/>
  <circle cx="12" cy="5" r="2.5" fill="url(#robotGradient)" stroke="#ffffff" stroke-width="1"/>
  <path d="M12 7.5V11" stroke="#A78BFA" stroke-width="2" stroke-linecap="round"/>
  <circle cx="8.5" cy="16" r="1.5" fill="#ffffff"/>
  <circle cx="15.5" cy="16" r="1.5" fill="#ffffff"/>
  <rect x="10" y="18.5" width="4" height="1.5" rx="0.75" fill="rgba(255,255,255,0.7)"/>
  <circle cx="8.5" cy="16" r="0.5" fill="url(#robotGradient)"/>
  <circle cx="15.5" cy="16" r="0.5" fill="url(#robotGradient)"/>
</svg> 