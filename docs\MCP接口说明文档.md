# 宇擎智库中台系统 MCP 接口说明文档

## 概述

本文档详细描述了宇擎智库中台系统的 MCP（Model Context Protocol）接口，基于 FastMCP 框架实现，提供统一的工具调用和资源访问能力。所有接口均支持 **STDIO**、**Streamable HTTP** 和 **SSE** 三种传输协议。

**协议信息**
- 协议: MCP (Model Context Protocol)
- 框架: FastMCP
- 服务器: YQ AI V3 MCP Server
- 版本: 1.0.0
- 架构: 模块化组合

## 目录

1. [传输协议支持](#传输协议支持)
2. [服务器架构](#服务器架构)
3. [工具接口 (Tools)](#工具接口-tools)
4. [资源接口 (Resources)](#资源接口-resources)
5. [提示模板 (Prompts)](#提示模板-prompts)
6. [启动和部署](#启动和部署)
7. [客户端连接](#客户端连接)
8. [错误处理](#错误处理)

---

## 传输协议支持

### 🔌 支持的传输协议

所有 MCP 接口均支持以下三种传输协议，可根据使用场景灵活选择：

#### 1. STDIO 协议（默认推荐）
- **用途**: 本地工具集成、命令行应用
- **适用场景**: Claude Desktop 集成、本地脚本调用
- **特点**: 
  - 标准输入输出通信
  - 进程间直接通信
  - 最低延迟
  - 自动进程管理
- **启动方式**: `python mcp_server.py`

#### 2. Streamable HTTP 协议（Web推荐）
- **用途**: Web 应用、微服务、远程访问
- **适用场景**: 分布式部署、HTTP API 集成
- **特点**:
  - 基于 HTTP 的流式传输
  - 支持跨网络访问
  - RESTful 风格
  - 易于负载均衡
- **启动方式**: `python mcp_server.py --transport streamable-http --port 8001`
- **端点地址**: `http://localhost:8001/mcp`

#### 3. SSE 协议（已弃用）
- **用途**: 现有 SSE 基础设施兼容
- **适用场景**: 历史系统迁移
- **特点**:
  - Server-Sent Events 通信
  - 单向推送
  - 已弃用但仍支持
- **启动方式**: `python mcp_server.py --transport sse --port 8001`

---

## 服务器架构

### 🏗️ 模块化架构

YQ AI V3 MCP Server 采用模块化设计，包含以下功能模块：

| 模块 | 前缀 | 功能描述 |
|------|------|----------|
| **RAG Server** | `rag_*` | 检索增强生成、文档管理、向量化 |
| **Text2SQL Server** | `text2sql_*` | 自然语言转SQL、查询执行 |
| **Knowledge Graph Server** | `kg_*` | 知识图谱操作、实体关系管理 |
| **Statistics Server** | `stats_*` | 数据统计分析、报表生成 |
| **Cache Server** | `cache_*` | 缓存管理、性能优化 |
| **Train Server** | `train_*` | 模型训练、数据管理 |
| **Analytics Server** | `analytics_*` | 性能分析、监控统计 |

### 🔧 组件统计
- **工具数量**: 70+ 个工具
- **资源数量**: 20+ 个资源
- **提示模板**: 10+ 个模板

---

## 工具接口 (Tools)

### 📋 工具分类概览

#### RAG 检索增强生成工具 (`rag_*`)

##### 文档管理工具
- `rag_upload_files` - 上传文档进行向量化
- `rag_upload_files_by_docid` - 根据文档ID上传文件
- `rag_delete_document` - 删除指定文档
- `rag_query_documents` - 查询文档列表

##### 集合管理工具
- `rag_create_collection` - 创建向量集合
- `rag_delete_collection` - 删除向量集合
- `rag_list_collections` - 获取集合列表

##### 查询工具
- `rag_query` - 执行RAG查询
- `rag_stream_query` - 流式RAG查询
- `rag_get_embedding` - 获取文本向量

##### 任务管理工具
- `rag_get_embedding_status` - 获取嵌入状态
- `rag_get_all_tasks` - 获取所有任务
- `rag_clear_task` - 清除指定任务

**使用示例**:
```python
# MCP工具调用
result = await client.call_tool("rag_query", {
    "question": "什么是人工智能？",
    "collection_name": "knowledge_base",
    "top_k": 5,
    "use_hybrid": True
})
```

#### Text2SQL 生成工具 (`text2sql_*`)

##### SQL生成工具
- `text2sql_generate_sql` - 生成SQL查询
- `text2sql_execute_sql` - 执行SQL查询
- `text2sql_explain_sql` - 解释SQL语句
- `text2sql_optimize_sql` - 优化SQL查询

##### 数据库管理工具
- `text2sql_get_connections` - 获取数据库连接
- `text2sql_discover_schema` - 发现数据库结构
- `text2sql_get_schema` - 获取表结构信息
- `text2sql_publish_schema` - 发布结构元数据

##### 会话管理工具
- `text2sql_start_session` - 启动查询会话
- `text2sql_get_session_status` - 获取会话状态
- `text2sql_cancel_session` - 取消会话

**使用示例**:
```python
# 生成并执行SQL
sql_result = await client.call_tool("text2sql_generate_sql", {
    "question": "查询销售额前10的产品",
    "connection_id": "db_conn_1"
})

execution_result = await client.call_tool("text2sql_execute_sql", {
    "sql": sql_result["sql"],
    "connection_id": "db_conn_1"
})
```

#### 知识图谱工具 (`kg_*`)

##### 图谱查询工具
- `kg_search_entities` - 搜索实体
- `kg_get_entity_details` - 获取实体详情
- `kg_query_graph` - 查询知识图谱
- `kg_get_relations` - 获取关系信息

##### 统计工具
- `kg_get_statistics` - 获取图谱统计
- `kg_get_entity_types` - 获取实体类型
- `kg_get_relation_types` - 获取关系类型

##### 文档工具
- `kg_get_documents` - 获取文档列表
- `kg_get_document_graph` - 获取文档图谱

**使用示例**:
```python
# 搜索实体
entities = await client.call_tool("kg_search_entities", {
    "query": "人工智能",
    "entity_types": ["Technology", "Concept"],
    "limit": 10
})
```

#### 统计分析工具 (`stats_*`)

##### 数据库统计工具
- `stats_db_overview` - 数据库概览统计
- `stats_db_distribution` - 分布统计分析
- `stats_db_field_analysis` - 字段分析统计
- `stats_db_trend_analysis` - 趋势分析

##### 知识库统计工具
- `stats_kb_overview` - 知识库概览统计
- `stats_kb_user_stats` - 用户统计分析
- `stats_kb_popular_questions` - 热门问题统计
- `stats_kb_satisfaction` - 满意度统计

**使用示例**:
```python
# 获取数据库统计概览
overview = await client.call_tool("stats_db_overview", {
    "include_charts": True,
    "date_range": "30d"
})
```

#### 缓存管理工具 (`cache_*`)

##### 缓存操作工具
- `cache_get_status` - 获取缓存状态
- `cache_warmup` - 执行缓存预热
- `cache_clear_all` - 清除所有缓存
- `cache_clear_pattern` - 按模式清除缓存

##### 性能监控工具
- `cache_get_performance` - 获取性能统计
- `cache_health_check` - 缓存健康检查
- `cache_list_keys` - 列出缓存键

**使用示例**:
```python
# 执行缓存预热
warmup_result = await client.call_tool("cache_warmup", {
    "include_database_stats": True,
    "include_knowledge_stats": True,
    "max_concurrent": 3
})
```

#### 训练管理工具 (`train_*`)

##### 数据管理工具
- `train_get_training_data` - 获取训练数据
- `train_add_training_data` - 添加训练数据
- `train_remove_training_data` - 删除训练数据
- `train_clear_history` - 清除历史记录

##### 问题生成工具
- `train_generate_questions` - 生成示例问题
- `train_generate_followup` - 生成后续问题
- `train_auto_train` - 自动训练

**使用示例**:
```python
# 添加训练数据
result = await client.call_tool("train_add_training_data", {
    "question": "查询销售额最高的产品",
    "sql": "SELECT * FROM products ORDER BY sales DESC LIMIT 1",
    "ddl": "CREATE TABLE products(...)",
    "documentation": "产品销售数据表"
})
```

#### 分析监控工具 (`analytics_*`)

##### 性能分析工具
- `analytics_get_overview` - 获取性能概览
- `analytics_get_daily_stats` - 获取每日统计
- `analytics_get_agent_performance` - 获取智能体性能
- `analytics_get_error_summary` - 获取错误摘要

##### 数据导出工具
- `analytics_export_csv` - 导出CSV数据
- `analytics_get_realtime_stats` - 获取实时统计
- `analytics_health_check` - 健康检查

**使用示例**:
```python
# 获取性能分析概览
performance = await client.call_tool("analytics_get_overview", {
    "days": 7,
    "include_trends": True
})
```

---

## 资源接口 (Resources)

### 📚 资源分类

#### 系统配置资源
- `config://version` - 获取系统版本信息
- `config://server_info` - 获取服务器配置信息
- `config://capabilities` - 获取系统能力列表

#### 统计数据资源
- `stats://system_health` - 系统健康状态
- `stats://performance_metrics` - 性能指标数据
- `stats://usage_statistics` - 使用统计数据

#### 数据库资源
- `db://connections` - 数据库连接信息
- `db://schema_metadata` - 数据库结构元数据
- `db://table_statistics` - 表统计信息

#### 知识库资源
- `kb://collections` - 知识库集合信息
- `kb://documents` - 文档资源列表
- `kb://embeddings_status` - 向量化状态

**使用示例**:
```python
# 获取系统信息资源
server_info = await client.read_resource("config://server_info")

# 获取系统健康状态
health_status = await client.read_resource("stats://system_health")
```

---

## 提示模板 (Prompts)

### 💬 可用提示模板

#### 系统帮助模板
- `help_prompt` - 显示系统帮助信息和工具列表
- `deployment_guide_prompt` - 部署指南和配置说明

#### 功能指导模板
- `rag_usage_prompt` - RAG功能使用指南
- `text2sql_usage_prompt` - Text2SQL使用指南
- `kg_usage_prompt` - 知识图谱操作指南

#### 故障排除模板
- `troubleshooting_prompt` - 常见问题和解决方案
- `performance_tuning_prompt` - 性能优化建议

**使用示例**:
```python
# 获取系统帮助
help_content = await client.get_prompt("help_prompt")

# 获取部署指南
deploy_guide = await client.get_prompt("deployment_guide_prompt")
```

---

## 启动和部署

### 🚀 服务器启动方式

#### 1. 使用 Python 直接启动

```bash
# STDIO模式（默认）- 适用于本地工具集成
python mcp_server.py

# Streamable HTTP模式 - 适用于Web服务
python mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001

# SSE模式 - 兼容现有SSE部署
python mcp_server.py --transport sse --host 0.0.0.0 --port 8001

# 详细日志模式
python mcp_server.py --transport streamable-http --verbose --log-level debug
```

#### 2. 使用 FastMCP CLI（推荐）

```bash
# 使用CLI运行（推荐方式）
fastmcp run mcp_server.py --transport streamable-http --port 8001

# 开发模式（包含MCP Inspector调试界面）
fastmcp dev mcp_server.py

# 生产部署
fastmcp run mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001
```

#### 3. 生产环境部署

```bash
# 后台运行
nohup python mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001 > mcp.log 2>&1 &

# 使用 systemd 管理
sudo systemctl start yq-mcp-server

# 使用 Docker 部署
docker run -p 8001:8001 yq-ai-v3-mcp --transport streamable-http --host 0.0.0.0
```

### 🔧 配置参数

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `--transport` | 传输协议 | stdio | streamable-http |
| `--host` | 服务器主机 | 127.0.0.1 | 0.0.0.0 |
| `--port` | 服务器端口 | 8001 | 8080 |
| `--path` | MCP端点路径 | /mcp | /api/mcp |
| `--log-level` | 日志级别 | info | debug |
| `--verbose` | 详细输出 | False | True |

---

## 客户端连接

### 🔌 不同协议的连接方式

#### 1. STDIO 连接（本地集成）

```python
from mcp import Client
from mcp.client.stdio import stdio_client

# Python客户端连接
async with stdio_client(
    command="python",
    args=["mcp_server.py"]
) as client:
    # 调用RAG工具
    result = await client.call_tool("rag_query", {
        "question": "什么是人工智能？",
        "collection_name": "default",
        "top_k": 5
    })
    
    # 读取资源
    server_info = await client.read_resource("config://server_info")
    
    # 获取提示
    help_text = await client.get_prompt("help_prompt")
```

#### 2. Streamable HTTP 连接（Web集成）

```python
from mcp.client.sse import sse_client

# HTTP客户端连接
async with sse_client("http://localhost:8001/mcp") as client:
    # 执行Text2SQL查询
    sql_result = await client.call_tool("text2sql_generate_sql", {
        "question": "查询销售额前10的产品",
        "connection_id": "db_connection_1"
    })
    
    # 获取统计数据
    stats = await client.call_tool("stats_db_overview", {})
```

#### 3. JavaScript/Web 集成

```javascript
// 使用fetch API调用MCP工具
const callMCPTool = async (toolName, parameters) => {
    const response = await fetch('http://localhost:8001/mcp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'tools/call',
            params: {
                name: toolName,
                arguments: parameters
            },
            id: Date.now()
        })
    });
    
    return await response.json();
};

// 调用RAG查询
const ragResult = await callMCPTool('rag_query', {
    question: '什么是机器学习？',
    collection_name: 'ai_knowledge',
    top_k: 3
});
```

#### 4. Claude Desktop 配置

```json
{
  "mcpServers": {
    "yq-ai-v3": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {
        "PYTHONPATH": "/path/to/project"
      }
    }
  }
}
```

#### 5. cURL 命令行调用

```bash
# 调用工具
curl -X POST http://localhost:8001/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "rag_query",
      "arguments": {
        "question": "什么是深度学习？",
        "collection_name": "default"
      }
    },
    "id": 1
  }'

# 读取资源
curl -X POST http://localhost:8001/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "resources/read",
    "params": {
      "uri": "config://server_info"
    },
    "id": 2
  }'
```

---

## 错误处理

### ❌ 常见错误码

#### MCP协议错误
- `-32700`: Parse error - JSON解析错误
- `-32600`: Invalid Request - 无效请求
- `-32601`: Method not found - 方法不存在
- `-32602`: Invalid params - 参数无效
- `-32603`: Internal error - 内部错误

#### 业务逻辑错误
- `1001`: 数据库连接失败
- `1002`: SQL语法错误
- `1003`: 权限不足
- `1004`: 会话已过期
- `1005`: 文件上传失败
- `1006`: 向量化处理失败
- `1007`: 缓存操作失败
- `1008`: 知识图谱查询失败

### 🔧 错误处理示例

```python
try:
    result = await client.call_tool("text2sql_generate_sql", {
        "question": "无效查询",
        "connection_id": "invalid_id"
    })
except MCPError as e:
    if e.code == 1001:
        print("数据库连接失败，请检查连接配置")
    elif e.code == 1002:
        print("SQL语法错误，请检查查询语句")
    else:
        print(f"发生错误: {e.message}")
```

### 📊 健康检查端点

#### HTTP模式健康检查
```bash
# 基本健康检查
curl http://localhost:8001/health

# 详细服务器信息
curl http://localhost:8001/info
```

#### MCP协议健康检查
```python
# 获取系统健康状态
health = await client.read_resource("stats://system_health")

# 检查各模块状态
server_info = await client.read_resource("config://server_info")
```

---

## 性能优化建议

### ⚡ 最佳实践

#### 1. 传输协议选择
- **本地集成**: 使用 STDIO 协议（最低延迟）
- **Web应用**: 使用 Streamable HTTP 协议（最佳兼容性）
- **现有SSE**: 使用 SSE 协议（兼容性考虑）

#### 2. 缓存策略
- 启用缓存预热：`cache_warmup`
- 定期清理缓存：`cache_clear_pattern`
- 监控缓存性能：`cache_get_performance`

#### 3. 并发控制
- 合理设置并发数：`max_concurrent: 3`
- 使用会话管理：`text2sql_start_session`
- 及时释放资源：`text2sql_cancel_session`

#### 4. 监控和分析
- 定期检查性能：`analytics_get_overview`
- 监控错误率：`analytics_get_error_summary`
- 分析使用模式：`stats_kb_user_stats`

---

## 版本信息

**当前版本**: 1.0.0  
**MCP协议版本**: 兼容 MCP 2024-11-05  
**FastMCP版本**: 最新稳定版  
**最后更新**: 2024年1月  

## 技术支持

- **文档**: 本文档和API接口说明文档
- **健康检查**: `/health` 端点和 `stats://system_health` 资源
- **调试工具**: FastMCP Inspector (`fastmcp dev`)
- **日志**: 详细模式 `--verbose` 和日志级别 `--log-level debug`

---

**注意**: 所有接口均支持 STDIO、Streamable HTTP 和 SSE 三种传输协议，可根据部署环境和使用场景灵活选择。推荐在本地开发时使用 STDIO，在生产环境使用 Streamable HTTP。 