#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱实体和关系提取测试脚本
用于测试优化后的提示词效果
"""

import asyncio
from utils.kg_utils import KnowledgeGraphManager

async def test_extraction():
    """测试实体和关系提取"""
    
    # 测试文本 - 基于你提到的产品定位文档
    test_text = """
    产品定位
    
    企业级智能知识管理平台
    
    宇擎智库管理系统是基于大语言模型、检索增强生成、知识图谱和智能体技术的企业级智能知识管理平台，为企业提供多模态文档对话、知识库检索、数据库问答等综合智能服务，助力企业高效管理和利用知识资源。
    
    核心价值
    
    企业降本增效：以多模态RAG技术精确解读文档中的图表和文本信息，提升知识获取效率；Text2Sql快速对接企业已有系统，分钟级实现定制化表需求，减少传统应用定制化开发成本。
    
    低成本实现企业AI+传统应用：基于框架创新和程序优化，实现小参数量的模型也有惊喜的效果。数据本地存储，开放接口对接已有系统，保障企业数据安全。
    """
    
    print("🚀 开始测试知识图谱实体和关系提取")
    print("=" * 60)
    print(f"测试文本长度: {len(test_text)} 字符")
    print("=" * 60)
    
    try:
        # 使用优化后的方法提取实体和关系
        result = await KnowledgeGraphManager.extract_from_text(test_text)
        
        print("\n📊 提取结果统计:")
        print(f"- 实体数量: {len(result.get('entities', []))}")
        print(f"- 关系数量: {len(result.get('relations', []))}")
        
        print("\n🏷️  提取的实体:")
        print("-" * 40)
        for i, entity in enumerate(result.get("entities", []), 1):
            print(f"{i:2d}. {entity.get('name')} (类型: {entity.get('type')})")
        
        print("\n🔗 提取的关系:")
        print("-" * 40)
        for i, relation in enumerate(result.get("relations", []), 1):
            print(f"{i:2d}. {relation.get('source')} --[{relation.get('relation')}]--> {relation.get('target')}")
            if relation.get('description'):
                print(f"    📝 描述: {relation.get('description')}")
        
        # 检查关键实体是否被正确识别
        print("\n✅ 关键实体识别检查:")
        print("-" * 40)
        key_entities = [
            "企业级智能知识管理平台",
            "宇擎智库管理系统", 
            "核心价值",
            "企业降本增效",
            "AI+传统应用"
        ]
        
        entity_names = [e.get('name', '') for e in result.get('entities', [])]
        
        for key_entity in key_entities:
            # 检查完全匹配或部分匹配
            found = any(key_entity in name or name in key_entity for name in entity_names)
            status = "✅ 已识别" if found else "❌ 未识别"
            print(f"  {key_entity}: {status}")
        
        print("\n🎯 关系质量分析:")
        print("-" * 40)
        relations = result.get("relations", [])
        if relations:
            print(f"- 总关系数: {len(relations)}")
            
            # 分析关系类型分布
            relation_types = {}
            for rel in relations:
                rel_type = rel.get('relation', '未知')
                relation_types[rel_type] = relation_types.get(rel_type, 0) + 1
            
            print("- 关系类型分布:")
            for rel_type, count in relation_types.items():
                print(f"  * {rel_type}: {count} 个")
        else:
            print("- 没有识别到关系")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔬 知识图谱实体和关系提取测试")
    print("测试优化后的提示词效果\n")
    
    # 运行异步测试
    asyncio.run(test_extraction())
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n💡 优化建议:")
    print("1. 检查关键实体是否都被正确识别")
    print("2. 验证关系是否有明确的文本依据")
    print("3. 确认实体名称的完整性和准确性")
    print("4. 观察关系类型是否合理且多样化")

if __name__ == "__main__":
    main() 