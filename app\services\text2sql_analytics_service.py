"""
Text2SQL分析服务
提供性能指标计算和数据查询功能
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any
from decimal import Decimal

from sqlalchemy import func, desc, and_, select, case

from dao.models.Text2sqlAnalytics import (
    Text2SQLExecution, Text2SQLAgentExecution, Text2SQLPerformanceMetric,
    Text2SQLErrorLog, Text2SQLSchemaRetrieval, Text2SQLQueryResult,
    Text2SQLStreamMessage, ExecutionStatus, AgentStatus, ErrorSeverity, MetricCategory,
    PerformanceMetrics, AgentPerformanceMetrics, ExecutionSummary,
    PerformanceOverview, Text2SQLAnalytics
)
from dao.DatabaseEngine import DatabaseEngine
from utils.logger import get_logger
from utils.encode_utils import clean_dict_for_json, serialize_for_db

logger = get_logger()


class Text2SQLAnalyticsService:
    """Text2SQL分析服务类"""
    
    def __init__(self):
        """初始化分析服务"""
        pass

    async def create_execution_record(self, session_id: str, user_query: str, 
                                    connection_id: str = None, db_type: str = None) -> str:
        """创建执行记录
        
        Args:
            session_id: 会话ID
            user_query: 用户查询
            connection_id: 数据库连接ID
            db_type: 数据库类型
            
        Returns:
            str: 查询ID
        """
        try:
            query_id = str(uuid.uuid4())
            
            session = await DatabaseEngine.get_session()
            async with session:
                execution = Text2SQLExecution(
                    session_id=session_id,
                    query_id=query_id,
                    user_query=user_query,
                    connection_id=connection_id,
                    db_type=db_type,
                    execution_status=ExecutionStatus.RUNNING,
                    start_time=datetime.utcnow()
                )
                session.add(execution)
                await session.commit()
                
            logger.info(f"创建执行记录成功，查询ID: {query_id}")
            return query_id
            
        except Exception as e:
            logger.error(f"创建执行记录失败: {str(e)}")
            raise

    async def update_execution_status(self, query_id: str, status: ExecutionStatus, 
                                    error_message: str = None, final_sql: str = None,
                                    sql_explanation: str = None, 
                                    visualization_recommendation: Dict[str, Any] = None) -> None:
        """更新执行状态
        
        Args:
            query_id: 查询ID
            status: 执行状态
            error_message: 错误信息
            final_sql: 最终SQL
            sql_explanation: SQL解释
            visualization_recommendation: 可视化推荐
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                # 使用query_id字段查询，而不是主键查询
                result = await session.execute(
                    select(Text2SQLExecution).where(Text2SQLExecution.query_id == query_id)
                )
                execution = result.scalar_one_or_none()
                
                if execution:
                    execution.execution_status = status
                    execution.end_time = datetime.utcnow()
                    execution.total_duration_ms = int(
                        (execution.end_time - execution.start_time).total_seconds() * 1000
                    ) if execution.start_time else None
                    
                    if error_message:
                        execution.error_message = error_message
                    if final_sql:
                        execution.final_sql = final_sql
                    if sql_explanation:
                        execution.sql_explanation = sql_explanation
                    if visualization_recommendation:
                        # 清理可视化推荐中的不可序列化对象
                        execution.visualization_recommendation = clean_dict_for_json(visualization_recommendation)
                        
                    await session.commit()
                    logger.info(f"更新执行状态成功，查询ID: {query_id}, 状态: {status}")
                else:
                    logger.warning(f"未找到查询记录: {query_id}")
                    
        except Exception as e:
            logger.error(f"更新执行状态失败: {str(e)}")
            raise

    async def create_agent_execution(self, query_id: str, agent_name: str, 
                                   agent_display_name: str, execution_order: int,
                                   input_data: Dict[str, Any] = None) -> int:
        """创建智能体执行记录
        
        Args:
            query_id: 查询ID
            agent_name: 智能体名称
            agent_display_name: 智能体显示名称
            execution_order: 执行顺序
            input_data: 输入数据
            
        Returns:
            int: 智能体执行ID
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                # 清理输入数据中的不可序列化对象
                cleaned_input_data = clean_dict_for_json(input_data) if input_data else None
                
                agent_execution = Text2SQLAgentExecution(
                    query_id=query_id,
                    agent_name=agent_name,
                    agent_display_name=agent_display_name,
                    execution_order=execution_order,
                    status=AgentStatus.RUNNING,
                    start_time=datetime.utcnow(),
                    input_data=cleaned_input_data
                )
                session.add(agent_execution)
                await session.commit()
                await session.refresh(agent_execution)
                
                logger.info(f"创建智能体执行记录成功，ID: {agent_execution.id}")
                return agent_execution.id
                
        except Exception as e:
            logger.error(f"创建智能体执行记录失败: {str(e)}")
            raise

    async def update_agent_execution(self, agent_execution_id: int, status: AgentStatus,
                                   output_data: Dict[str, Any] = None, 
                                   error_message: str = None,
                                   model_calls_count: int = 0,
                                   model_tokens_used: int = 0) -> None:
        """更新智能体执行状态
        
        Args:
            agent_execution_id: 智能体执行ID
            status: 执行状态
            output_data: 输出数据
            error_message: 错误信息
            model_calls_count: 模型调用次数
            model_tokens_used: 使用的token数量
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                agent_execution = await session.get(Text2SQLAgentExecution, agent_execution_id)
                if agent_execution:
                    agent_execution.status = status
                    agent_execution.end_time = datetime.utcnow()
                    agent_execution.duration_ms = int(
                        (agent_execution.end_time - agent_execution.start_time).total_seconds() * 1000
                    ) if agent_execution.start_time else None
                    
                    if output_data:
                        # 清理输出数据中的不可序列化对象
                        agent_execution.output_data = clean_dict_for_json(output_data)
                    if error_message:
                        agent_execution.error_message = error_message
                    if model_calls_count > 0:
                        agent_execution.model_calls_count = model_calls_count
                    if model_tokens_used > 0:
                        agent_execution.model_tokens_used = model_tokens_used
                        
                    await session.commit()
                    logger.info(f"更新智能体执行状态成功，ID: {agent_execution_id}")
                else:
                    logger.warning(f"未找到智能体执行记录: {agent_execution_id}")
                    
        except Exception as e:
            logger.error(f"更新智能体执行状态失败: {str(e)}")
            raise

    async def record_performance_metric(self, query_id: str, metric_name: str, 
                                      metric_value: float, metric_unit: str = None,
                                      metric_category: MetricCategory = MetricCategory.PERFORMANCE) -> None:
        """记录性能指标
        
        Args:
            query_id: 查询ID
            metric_name: 指标名称
            metric_value: 指标值
            metric_unit: 指标单位
            metric_category: 指标分类
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                metric = Text2SQLPerformanceMetric(
                    query_id=query_id,
                    metric_name=metric_name,
                    metric_value=Decimal(str(metric_value)),
                    metric_unit=metric_unit,
                    metric_category=metric_category,
                    collected_at=datetime.utcnow()
                )
                session.add(metric)
                await session.commit()
                
                logger.debug(f"记录性能指标成功: {metric_name}={metric_value}{metric_unit or ''}")
                
        except Exception as e:
            logger.error(f"记录性能指标失败: {str(e)}")
            raise

    async def record_stream_message(self, query_id: str, agent_execution_id: int, 
                                  message_sequence: int, source: str, content: str,
                                  is_final: bool = False, content_format: str = "markdown") -> None:
        """记录流式消息
        
        Args:
            query_id: 查询ID
            agent_execution_id: 智能体执行ID
            message_sequence: 消息序号
            source: 消息来源
            content: 消息内容
            is_final: 是否为最终消息
            content_format: 内容格式
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                stream_message = Text2SQLStreamMessage(
                    query_id=query_id,
                    agent_execution_id=agent_execution_id,
                    message_sequence=message_sequence,
                    source=source,
                    content=content,
                    content_format=content_format,
                    is_final=is_final,
                    message_size_bytes=len(content.encode('utf-8')) if content else 0,
                    timestamp=datetime.utcnow()
                )
                session.add(stream_message)
                await session.commit()
                
                logger.debug(f"记录流式消息成功: {source} - 序号{message_sequence}")
                
        except Exception as e:
            logger.error(f"记录流式消息失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    async def record_schema_retrieval(self, query_id: str, connection_id: str, 
                                    original_query: str, optimized_query: str = None,
                                    vector_search_results_count: int = None,
                                    initial_tables_count: int = None,
                                    final_tables_count: int = None,
                                    kg_query_depth: int = None,
                                    schema_info_size_kb: float = None,
                                    execution_time_ms: int = None) -> None:
        """记录表结构检索信息
        
        Args:
            query_id: 查询ID
            connection_id: 数据库连接ID
            original_query: 原始查询
            optimized_query: 优化后的查询
            vector_search_results_count: 向量搜索结果数量
            initial_tables_count: 初始表数量
            final_tables_count: 最终表数量
            kg_query_depth: 知识图谱查询深度
            schema_info_size_kb: 表结构信息大小(KB)
            execution_time_ms: 执行时间(毫秒)
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                schema_retrieval = Text2SQLSchemaRetrieval(
                    query_id=query_id,
                    connection_id=connection_id,
                    original_query=original_query,
                    optimized_query=optimized_query,
                    vector_search_results_count=vector_search_results_count,
                    initial_tables_count=initial_tables_count,
                    final_tables_count=final_tables_count,
                    kg_query_depth=kg_query_depth,
                    schema_info_size_kb=Decimal(str(schema_info_size_kb)) if schema_info_size_kb else None,
                    execution_time_ms=execution_time_ms,
                    created_at=datetime.utcnow()
                )
                session.add(schema_retrieval)
                await session.commit()
                
                logger.info(f"记录表结构检索信息成功: {query_id}")
                
        except Exception as e:
            logger.error(f"记录表结构检索信息失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    async def record_query_result(self, query_id: str, sql_statement: str, 
                                execution_status: str, results_count: int = None,
                                results_size_kb: float = None, execution_time_ms: int = None,
                                columns_info: List[str] = None, sample_data: List[Dict[str, Any]] = None,
                                error_message: str = None) -> None:
        """记录SQL执行结果
        
        Args:
            query_id: 查询ID
            sql_statement: SQL语句
            execution_status: 执行状态
            results_count: 结果行数
            results_size_kb: 结果大小(KB)
            execution_time_ms: 执行时间(毫秒)
            columns_info: 列信息
            sample_data: 样本数据
            error_message: 错误信息
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                # 清理样本数据中的不可序列化对象
                cleaned_sample_data = None
                if sample_data:
                    if isinstance(sample_data, list):
                        cleaned_sample_data = [clean_dict_for_json(item) if isinstance(item, dict) else serialize_for_db(item) for item in sample_data]
                    else:
                        cleaned_sample_data = serialize_for_db(sample_data)
                
                query_result = Text2SQLQueryResult(
                    query_id=query_id,
                    sql_statement=sql_statement,
                    execution_status=execution_status,
                    results_count=results_count,
                    results_size_kb=Decimal(str(results_size_kb)) if results_size_kb else None,
                    execution_time_ms=execution_time_ms,
                    columns_info=columns_info,
                    sample_data=cleaned_sample_data,
                    error_message=error_message,
                    executed_at=datetime.utcnow()
                )
                session.add(query_result)
                await session.commit()
                
                logger.info(f"记录SQL执行结果成功: {query_id} - {execution_status}")
                
        except Exception as e:
            logger.error(f"记录SQL执行结果失败: {str(e)}")
            # 不抛出异常，避免影响主流程

    async def record_error_log(self, query_id: str, error_type: str, error_message: str,
                             agent_name: str = None, error_stack: str = None,
                             error_context: Dict[str, Any] = None,
                             severity: ErrorSeverity = ErrorSeverity.ERROR) -> None:
        """记录错误日志
        
        Args:
            query_id: 查询ID
            error_type: 错误类型
            error_message: 错误信息
            agent_name: 智能体名称
            error_stack: 错误堆栈
            error_context: 错误上下文
            severity: 严重程度
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                # 清理错误上下文中的不可序列化对象
                cleaned_error_context = clean_dict_for_json(error_context) if error_context else None
                
                error_log = Text2SQLErrorLog(
                    query_id=query_id,
                    agent_name=agent_name,
                    error_type=error_type,
                    error_message=error_message,
                    error_stack=error_stack,
                    error_context=cleaned_error_context,
                    severity=severity,
                    occurred_at=datetime.utcnow()
                )
                session.add(error_log)
                await session.commit()
                
                logger.info(f"记录错误日志成功: {error_type}")
                
        except Exception as e:
            logger.error(f"记录错误日志失败: {str(e)}")
            raise

    async def get_performance_overview(self, days: int = 7) -> PerformanceMetrics:
        """获取性能概览
        
        Args:
            days: 统计天数
            
        Returns:
            PerformanceMetrics: 性能指标
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            session = await DatabaseEngine.get_session()
            async with session:
                # 基础统计
                total_query = select(func.count(Text2SQLExecution.id)).filter(
                    Text2SQLExecution.created_at >= start_date
                )
                total_result = await session.execute(total_query)
                total_executions = total_result.scalar() or 0
                
                success_query = select(func.count(Text2SQLExecution.id)).filter(
                    and_(
                        Text2SQLExecution.created_at >= start_date,
                        Text2SQLExecution.execution_status == ExecutionStatus.SUCCESS
                    )
                )
                success_result = await session.execute(success_query)
                successful_executions = success_result.scalar() or 0
                
                failed_query = select(func.count(Text2SQLExecution.id)).filter(
                    and_(
                        Text2SQLExecution.created_at >= start_date,
                        Text2SQLExecution.execution_status == ExecutionStatus.FAILED
                    )
                )
                failed_result = await session.execute(failed_query)
                failed_executions = failed_result.scalar() or 0
                
                # 平均指标
                avg_query = select(
                    func.avg(Text2SQLExecution.total_duration_ms).label('avg_duration'),
                    func.avg(Text2SQLExecution.query_results_count).label('avg_results_count'),
                    func.avg(Text2SQLExecution.query_results_size_kb).label('avg_results_size')
                ).filter(
                    and_(
                        Text2SQLExecution.created_at >= start_date,
                        Text2SQLExecution.execution_status == ExecutionStatus.SUCCESS
                    )
                )
                avg_result = await session.execute(avg_query)
                avg_metrics = avg_result.first()
                
                success_rate = successful_executions / total_executions if total_executions > 0 else 0
                
                return PerformanceMetrics(
                    total_executions=total_executions,
                    successful_executions=successful_executions,
                    failed_executions=failed_executions,
                    avg_duration_ms=float(avg_metrics.avg_duration or 0),
                    avg_results_count=float(avg_metrics.avg_results_count or 0),
                    avg_results_size_kb=float(avg_metrics.avg_results_size or 0),
                    success_rate=success_rate
                )
                
        except Exception as e:
            logger.error(f"获取性能概览失败: {str(e)}")
            raise

    async def get_agent_performance(self, days: int = 7) -> List[AgentPerformanceMetrics]:
        """获取智能体性能统计
        
        Args:
            days: 统计天数
            
        Returns:
            List[AgentPerformanceMetrics]: 智能体性能列表
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            session = await DatabaseEngine.get_session()
            async with session:
                query = select(
                    Text2SQLAgentExecution.agent_name,
                    Text2SQLAgentExecution.agent_display_name,
                    func.count().label('total_executions'),
                    func.sum(case((Text2SQLAgentExecution.status == AgentStatus.SUCCESS, 1), else_=0)).label('successful_executions'),
                    func.sum(case((Text2SQLAgentExecution.status == AgentStatus.FAILED, 1), else_=0)).label('failed_executions'),
                    func.avg(Text2SQLAgentExecution.duration_ms).label('avg_duration_ms'),
                    func.avg(Text2SQLAgentExecution.model_calls_count).label('avg_model_calls'),
                    func.avg(Text2SQLAgentExecution.model_tokens_used).label('avg_tokens_used'),
                    func.max(Text2SQLAgentExecution.duration_ms).label('max_duration_ms'),
                    func.min(Text2SQLAgentExecution.duration_ms).label('min_duration_ms')
                ).filter(
                    Text2SQLAgentExecution.created_at >= start_date
                ).group_by(
                    Text2SQLAgentExecution.agent_name,
                    Text2SQLAgentExecution.agent_display_name
                ).order_by(
                    Text2SQLAgentExecution.execution_order
                )
                result = await session.execute(query)
                results = result.all()
                
                agent_metrics = []
                for result in results:
                    total = result.total_executions or 0
                    success = result.successful_executions or 0
                    success_rate = success / total if total > 0 else 0
                    
                    agent_metrics.append(AgentPerformanceMetrics(
                        agent_name=result.agent_name,
                        agent_display_name=result.agent_display_name,
                        total_executions=total,
                        successful_executions=success,
                        failed_executions=result.failed_executions or 0,
                        avg_duration_ms=float(result.avg_duration_ms or 0),
                        avg_model_calls=float(result.avg_model_calls or 0),
                        avg_tokens_used=float(result.avg_tokens_used or 0),
                        max_duration_ms=result.max_duration_ms,
                        min_duration_ms=result.min_duration_ms,
                        success_rate=success_rate
                    ))
                
                return agent_metrics
                
        except Exception as e:
            logger.error(f"获取智能体性能统计失败: {str(e)}")
            raise

    async def get_daily_performance(self, days: int = 30) -> List[PerformanceOverview]:
        """获取每日性能统计
        
        Args:
            days: 统计天数
            
        Returns:
            List[PerformanceOverview]: 每日性能列表
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            session = await DatabaseEngine.get_session()
            async with session:
                query = select(
                    func.date(Text2SQLExecution.created_at).label('execution_date'),
                    func.count().label('total_executions'),
                    func.sum(case((Text2SQLExecution.execution_status == ExecutionStatus.SUCCESS, 1), else_=0)).label('successful_executions'),
                    func.sum(case((Text2SQLExecution.execution_status == ExecutionStatus.FAILED, 1), else_=0)).label('failed_executions'),
                    func.avg(Text2SQLExecution.total_duration_ms).label('avg_duration_ms'),
                    func.avg(Text2SQLExecution.query_results_count).label('avg_results_count'),
                    func.avg(Text2SQLExecution.query_results_size_kb).label('avg_results_size_kb'),
                    func.count(func.distinct(Text2SQLExecution.connection_id)).label('unique_connections'),
                    func.count(func.distinct(Text2SQLExecution.session_id)).label('unique_sessions')
                ).filter(
                    Text2SQLExecution.created_at >= start_date
                ).group_by(
                    func.date(Text2SQLExecution.created_at)
                ).order_by(
                    desc(func.date(Text2SQLExecution.created_at))
                )
                result = await session.execute(query)
                results = result.all()
                
                daily_stats = []
                for result in results:
                    total = result.total_executions or 0
                    success = result.successful_executions or 0
                    success_rate = success / total if total > 0 else 0
                    
                    metrics = PerformanceMetrics(
                        total_executions=total,
                        successful_executions=success,
                        failed_executions=result.failed_executions or 0,
                        avg_duration_ms=float(result.avg_duration_ms or 0),
                        avg_results_count=float(result.avg_results_count or 0),
                        avg_results_size_kb=float(result.avg_results_size_kb or 0),
                        success_rate=success_rate
                    )
                    
                    daily_stats.append(PerformanceOverview(
                        execution_date=str(result.execution_date),
                        metrics=metrics,
                        unique_connections=result.unique_connections or 0,
                        unique_sessions=result.unique_sessions or 0
                    ))
                
                return daily_stats
                
        except Exception as e:
            logger.error(f"获取每日性能统计失败: {str(e)}")
            raise

    async def get_recent_executions(self, limit: int = 20) -> List[ExecutionSummary]:
        """获取最近的执行记录
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[ExecutionSummary]: 执行摘要列表
        """
        try:
            session = await DatabaseEngine.get_session()
            async with session:
                query = select(Text2SQLExecution).order_by(
                    desc(Text2SQLExecution.created_at)
                ).limit(limit)
                result = await session.execute(query)
                executions = result.scalars().all()
                
                summaries = []
                for execution in executions:
                    summaries.append(ExecutionSummary(
                        query_id=execution.query_id,
                        session_id=execution.session_id,
                        user_query=execution.user_query,
                        execution_status=ExecutionStatus(execution.execution_status),
                        start_time=execution.start_time,
                        end_time=execution.end_time,
                        total_duration_ms=execution.total_duration_ms,
                        db_type=execution.db_type,
                        connection_id=execution.connection_id
                    ))
                
                return summaries
                
        except Exception as e:
            logger.error(f"获取最近执行记录失败: {str(e)}")
            raise

    async def get_error_summary(self, days: int = 7) -> Dict[str, int]:
        """获取错误摘要统计
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, int]: 错误类型统计
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            session = await DatabaseEngine.get_session()
            async with session:
                query = select(
                    Text2SQLErrorLog.error_type,
                    func.count().label('error_count')
                ).filter(
                    Text2SQLErrorLog.occurred_at >= start_date
                ).group_by(
                    Text2SQLErrorLog.error_type
                ).order_by(
                    desc(func.count())
                )
                result = await session.execute(query)
                results = result.all()
                
                error_summary = {}
                for result in results:
                    error_summary[result.error_type] = result.error_count
                
                return error_summary
                
        except Exception as e:
            logger.error(f"获取错误摘要失败: {str(e)}")
            raise

    async def get_comprehensive_analytics(self, days: int = 7) -> Text2SQLAnalytics:
        """获取综合分析数据
        
        Args:
            days: 统计天数
            
        Returns:
            Text2SQLAnalytics: 综合分析数据
        """
        try:
            # 并发获取各种统计数据
            overview = await self.get_performance_overview(days)
            daily_stats = await self.get_daily_performance(days)
            agent_performance = await self.get_agent_performance(days)
            recent_executions = await self.get_recent_executions(10)
            error_summary = await self.get_error_summary(days)
            
            return Text2SQLAnalytics(
                overview=overview,
                daily_stats=daily_stats,
                agent_performance=agent_performance,
                recent_executions=recent_executions,
                error_summary=error_summary
            )
            
        except Exception as e:
            logger.error(f"获取综合分析数据失败: {str(e)}")
            raise 