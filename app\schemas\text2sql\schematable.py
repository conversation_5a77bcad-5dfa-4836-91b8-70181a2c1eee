from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict


class Text2sqlSchematableBase(BaseModel):
    """数据库表基础模型"""
    connection_id: str = Field(..., description="数据源连接ID", max_length=100)
    table_name: str = Field(..., description="表名", max_length=255)
    description: Optional[str] = Field(None, description="表描述")
    ui_metadata: Optional[Dict[str, Any]] = Field(None, description="UI元数据")


class Text2sqlSchematableCreate(Text2sqlSchematableBase):
    """创建数据库表请求模型"""
    pass


class Text2sqlSchematableUpdate(BaseModel):
    """更新数据库表请求模型"""
    table_name: Optional[str] = Field(None, description="表名", max_length=255)
    description: Optional[str] = Field(None, description="表描述")
    ui_metadata: Optional[Dict[str, Any]] = Field(None, description="UI元数据")


class Text2sqlSchematableResponse(Text2sqlSchematableBase):
    """数据库表响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="表ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    

class Text2sqlSchematableFilter(BaseModel):
    """表查询过滤器"""
    connection_id: Optional[str] = Field(None, description="数据源连接ID")
    table_name: Optional[str] = Field(None, description="表名")

# Properties with relationships
class Text2sqlSchematableWithRelationships(Text2sqlSchematableResponse):
    columns: List[Any] = []
    relationships: List[Any] = []