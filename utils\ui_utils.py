import chainlit.data as cl_data
import requests
import jwt
from dotenv import load_dotenv
from llama_index.tools.tavily_research import TavilyToolSpec

from dao.knowledge_utils import get_collections, get_knowledge_info

load_dotenv()
from persistent.minio_storage_client import get_minio_client
from persistent.postgresql_data_layer import PostgreSQLDataLayer
from config import settings
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

# 实现聊天数据持久化 - 使用连接池模式
storage_client = get_minio_client()
cl_data._data_layer = PostgreSQLDataLayer(conninfo=settings.configuration.pg_connection_string, storage_provider=storage_client)
# cl_data._data_layer = MySqlDataLayer(conninfo=settings.configuration.mysql_connection_string, storage_provider=storage_client)

async def list_collections(identifier: str):
    try:
        collections = await get_collections(identifier)
        # 将collections转换成dict,包括id和name两个属性，格式为{name:id}
        collections_dict = {}

        if collections and isinstance(collections, list) and len(collections) > 0:
            # 新的数据格式：[{'id': 'xxx', 'name': 'xxx'}, ...]
            for collection in collections:
                if isinstance(collection, dict) and 'name' in collection and 'id' in collection:
                    if collection['name'] is not None:
                        collections_dict[collection['name']] = 'yq_' + collection['id']
        # 添加默认值
        collections_dict['默认'] = 'default'
        return collections_dict
    except Exception as e:
        logger.error(f"获取集合列表时发生错误: {str(e)}")
        return {"默认": "default"}  # 发生错误时返回默认值

async def check_knowledge_use_kg(knowledge_id):
    """
    检查知识库是否启用了图谱功能
    Args:
        knowledge_id: 知识库ID，需要去掉前缀'yq_'
    Returns:
        bool: True表示启用图谱功能，False表示未启用
    """
    try:
        # 如果ID带有yq_前缀，则去掉前缀
        if knowledge_id.startswith('yq_'):
            knowledge_id = knowledge_id[3:]

        use_kg,model_name = await get_knowledge_info(knowledge_id)
        # 如果use_kg为1，返回True，否则返回False
        return use_kg == 1,model_name
    except Exception as e:
        logger.error(f"检查知识库图谱功能时发生错误: {str(e)}")
        return False  # 发生错误时默认返回False

async def getToken():
    response = requests.get(settings.configuration.thirdapp_login_api)
    result = response.json()
    token = result['result']['token']
    return token

def get_embed_model_by_name(model_name: str = None):
    """根据模型名称获取对应的嵌入模型实例"""
    if model_name == "bge-large-zh-v1.5":
        logger.info(f"使用嵌入模型: bge-large-zh-v1.5")
        return settings.local_bge_large_embed_model()
    elif model_name == "Qwen3-Embedding-0.6B":
        logger.info(f"使用嵌入模型: Qwen3-Embedding-0.6B")
        return settings.local_qwen_embed_model()
        # return settings.local_bge_large_embed_model()
    else:
        # 默认使用bge-large模型
        logger.info(f"未选择或未知嵌入模型: {model_name}，使用默认的 bge-large-zh-v1.5")
        return settings.local_bge_large_embed_model()

async def websearchtool(query:str):
    """联网查询工具"""
    tavily_tool = TavilyToolSpec(api_key=settings.configuration.tavily_api_key)
    search_docs = tavily_tool.search(query=query, max_results=2)
    return  "\n".join([doc.text for doc in search_docs])

async def get_username_from_token(token: str):
    """
    获取token中的信息无需secret验证也能得到
    
    Args:
        token: JWT token字符串
        
    Returns:
        str: token中包含的用户名，解析失败时返回None
    """
    try:
        # 解码JWT token，不验证签名(verify=False)
        decoded_jwt = jwt.decode(token, options={"verify_signature": False})
        # 获取username声明
        username = decoded_jwt.get("username")
        return username
    except jwt.DecodeError as e:
        logger.warning(f"JWT解码异常: {str(e)}")
        return None
    except Exception as e:
        logger.warning(f"解析JWT token时发生未知错误: {str(e)}")
        return None

