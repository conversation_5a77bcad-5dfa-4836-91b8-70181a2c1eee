from typing import Any, List, Dict

from fastapi import APIRouter, HTTPException, Depends

# 导入数据源相关的schema
from app.schemas import Text2sqlDatasourceResponse, Text2sqlSchematableResponse, Text2sqlSchemacolumnResponse, \
    Text2sqlSchemarelationshipDetailed, Text2sqlSchematableWithRelationships, Text2sqlSchematableUpdate, \
    Text2sqlSchemacolumnUpdate, Text2sqlValuemappingResponse, Text2sqlValuemappingCreate, Text2sqlValuemappingUpdate

from app.services.text2sql_sys_service import text2sql_sys_service
from utils.kg_utils import KnowledgeGraphManager

# 导入日志
from utils.logger import get_logger
from utils.r import R

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        404: {"description": "资源不存在"},
        500: {"description": "服务器内部错误"}
    }
)

# 获取logger实例
logger = get_logger()


# 获取所有数据库连接
@router.get("/connections/", 
           response_model=List[Text2sqlDatasourceResponse],
           summary="获取所有数据库连接",
           description="获取系统中配置的所有数据库连接信息，包括连接参数、状态和元数据")
async def get_all_connections() -> Any:
    """
    获取所有数据库连接
    
    Returns:
        List[Text2sqlDatasourceResponse]: 所有数据源连接列表
    """
    try:
        result = await text2sql_sys_service.get_all_connections()
        
        if result["success"]:
            return result["data"]
        else:
            raise HTTPException(status_code=500, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库连接失败: {str(e)}")


# 根据connection_id获取数据库连接
@router.get("/connections/{connection_id}", 
           response_model=Text2sqlDatasourceResponse,
           summary="获取指定数据库连接",
           description="根据连接ID获取特定数据库连接的详细信息")
async def get_connection_by_id(connection_id: str) -> Any:
    """
    根据connection_id获取数据库连接
    
    Args:
        connection_id (str): 数据源ID或编码
        
    Returns:
        Text2sqlDatasourceResponse: 数据源连接信息
        
    Raises:
        HTTPException: 当数据源不存在时返回404错误
    """
    try:
        result = await text2sql_sys_service.get_connection_by_id(connection_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"获取数据库连接失败: {str(e)}"
        )

#发现并保存数据库结构
@router.post("/connections/{connection_id}/discover-and-save", 
            response_model=Dict[str, Any],
            summary="发现并保存数据库结构",
            description="自动发现指定数据库连接的表结构、字段信息和关系，并保存到系统中")
async def discover_and_save_schema(connection_id: str) -> Any:
    """
    Discover schema from a database connection and save it to the database.
    """
    try:
        result = await text2sql_sys_service.discover_and_save_schema(connection_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error discovering and saving schema: {str(e)}")

#获取某个数据源的表结构
@router.get("/schema/{connection_id}/metadata", 
           response_model=List[Text2sqlSchematableWithRelationships],
           summary="获取数据库表结构元数据",
           description="获取指定数据库连接的维护的表结构元数据，包括表、字段和关系信息")
async def get_schema_metadata(connection_id: str) -> Any:
    """
    Get maintained schema metadata for a connection.
    """
    try:
        result = await text2sql_sys_service.get_schema_metadata(connection_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving schema metadata: {str(e)}")

#
@router.get("/schema/{connection_id}/saved", 
           response_model=Dict[str, Any],
           summary="获取已保存的数据库结构",
           description="获取指定数据库连接的已保存结构信息，包含UI元数据")
async def get_saved_schema(connection_id: str,) -> Any:
    """
    Get saved schema with UI metadata for a connection.
    """
    try:
        result = await text2sql_sys_service.get_saved_schema(connection_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving saved schema: {str(e)}")

@router.post("/schema/{connection_id}/publish", 
            response_model=Dict[str, Any],
            summary="发布数据库结构",
            description="将数据库结构元数据发布到MySQL和图数据库中，使其可用于Text2SQL查询")
async def publish_schema(connection_id: str, schema_data: Dict[str, Any]) -> Any:
    """
    Publish schema metadata to MySQL and Graph DB.
    """
    try:
        result = await text2sql_sys_service.publish_schema(connection_id, schema_data)
        
        if result["success"]:
            return {"status": "success", "message": result["message"]}
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"Error publishing schema: {str(e)}\n{error_trace}")
        raise HTTPException(status_code=500, detail=f"Error publishing schema: {str(e)}")

@router.put("/schema/tables/{table_id}", 
           response_model=Text2sqlSchematableResponse,
           summary="更新表信息",
           description="更新指定表的信息，包括表名、描述、显示名称等")
async def update_table(table_id: int, table_in: Text2sqlSchematableUpdate) -> Any:
    """
    Update a table's information.
    """
    try:
        result = await text2sql_sys_service.update_table(table_id, table_in)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating table: {str(e)}")


@router.put("/schema/columns/{column_id}", 
           response_model=Text2sqlSchemacolumnResponse,
           summary="更新字段信息",
           description="更新指定字段的信息，包括字段名、数据类型、描述等")
async def update_column(column_id: int, column_in: Text2sqlSchemacolumnUpdate) -> Any:
    """
    Update a column's information.
    """
    try:
        result = await text2sql_sys_service.update_column(column_id, column_in)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating column: {str(e)}")


@router.get("/relationship-tips/", 
           response_model=Dict[str, Dict[str, str]],
           summary="获取关系类型提示",
           description="获取数据库表关系类型的提示信息，用于前端表单选择")
async def get_relationship_tips() -> Any:
    """
    获取关系类型的提示信息
    """
    try:
        result = await text2sql_sys_service.get_relationship_tips()
        
        if result["success"]:
            return result["data"]
        else:
            raise HTTPException(status_code=500, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取关系类型提示信息失败: {str(e)}")

# 获取知识图谱管理器实例的依赖函数
async def get_kg_manager():
    return KnowledgeGraphManager.get_instance()

@router.get("/graph-visualization/{connection_id}", 
           response_model=Dict[str, Any],
           summary="获取图形化可视化数据",
           description="获取指定数据库连接的图形化可视化数据，用于展示表关系图谱")
async def get_graph_data(connection_id: str, kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)) -> Dict[str, Any]:
    """
    根据前端传入的connection_id获取知识图谱数据
    返回适合前端图形可视化的节点和边数据
    """
    try:
        result = await text2sql_sys_service.get_graph_data(connection_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"获取知识图谱数据失败: {str(e)}\n{error_trace}")
        raise HTTPException(
            status_code=500, 
            detail=f"获取知识图谱数据失败: {str(e)}"
        )


@router.get("/value-mappings/", 
           response_model=List[Text2sqlValuemappingResponse],
           summary="获取值映射列表",
           description="获取字段值映射关系列表，用于处理枚举值和编码转换")
async def read_value_mappings(
    column_id: int = None, 
    skip: int = 0, 
    limit: int = 100
) -> Any:
    """
    Retrieve value mappings, optionally filtered by column, with pagination support.
    
    Args:
        column_id: Optional column ID to filter mappings
        skip: Number of records to skip (default: 0)
        limit: Maximum number of records to return (default: 100)
    """
    try:
        result = await text2sql_sys_service.read_value_mappings(column_id, skip, limit)
        
        if result["success"]:
            return result["data"]
        else:
            raise HTTPException(status_code=500, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取值映射失败: {str(e)}")


@router.post("/value-mappings/", 
            response_model=Text2sqlValuemappingResponse,
            summary="创建值映射",
            description="为指定字段创建新的值映射关系")
async def create_value_mapping(mapping_in: Text2sqlValuemappingCreate) -> Any:
    """
    Create new value mapping.
    """
    try:
        result = await text2sql_sys_service.create_value_mapping(mapping_in)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建值映射失败: {str(e)}")


@router.get("/value-mappings/{mapping_id}", 
           response_model=Text2sqlValuemappingResponse,
           summary="获取值映射详情",
           description="获取指定值映射的详细信息")
async def read_value_mapping(mapping_id: int,) -> Any:
    """
    Get value mapping by ID.
    """
    try:
        result = await text2sql_sys_service.read_value_mapping(mapping_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取值映射失败: {str(e)}")


@router.put("/value-mappings/{mapping_id}", 
           response_model=Text2sqlValuemappingResponse,
           summary="更新值映射",
           description="更新指定值映射的信息")
async def update_value_mapping(mapping_id: int, mapping_in: Text2sqlValuemappingUpdate) -> Any:
    """
    Update a value mapping.
    """
    try:
        result = await text2sql_sys_service.update_value_mapping(mapping_id, mapping_in)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新值映射失败: {str(e)}")


@router.delete("/value-mappings/{mapping_id}", 
              response_model=Text2sqlValuemappingResponse,
              summary="删除值映射",
              description="删除指定的值映射关系")
async def delete_value_mapping(mapping_id: int) -> Any:
    """
    Delete a value mapping.
    """
    try:
        result = await text2sql_sys_service.delete_value_mapping(mapping_id)
        
        if result["success"]:
            return result["data"]
        else:
            status_code = result.get("status_code", 500)
            raise HTTPException(status_code=status_code, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除值映射失败: {str(e)}")

@router.post("/connections/delete-all-data",
            summary="删除连接所有数据",
            description="删除指定数据库连接的所有相关数据，包括表结构、字段信息和关系")
async def delete_connection_all_data_endpoint(request_body: Dict[str, Any]) -> Any:
    """
    删除指定连接的所有相关数据，包括知识图谱和向量库数据
    
    这个接口会删除：
    1. MySQL数据库中的表、列、关系、值映射数据
    2. Neo4j知识图谱中的相关节点和关系
    3. Milvus向量库中的相关向量数据
    
    Args:
        request_body (Dict[str, Any]): 请求体，包含connection_id
        
    Returns:
        R.ok 或 R.error: 删除操作的详细统计结果
    """
    try:
        result = await text2sql_sys_service.delete_connection_all_data_endpoint(request_body)
        
        if result["success"]:
            return R.ok(result["message"])
        else:
            status_code = result.get("status_code", 500)
            return R.error(result["message"], status_code=status_code)
    except Exception as e:
        error_msg = f"删除连接数据时发生未知错误: {str(e)}"
        logger.error(error_msg)
        import traceback
        traceback.print_exc()
        
        return R.error(f"删除连接数据失败: {str(e)}", status_code=500)