from pydantic import BaseModel
from typing import List, Dict, Optional


class RAGStreamQueryEntity(BaseModel):
    """RAG流式查询实体"""
    question: str
    collection_name: str = "default"
    top_k: int = 5
    use_hybrid: bool = True
    use_rerank: bool = True
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    chat_history: Optional[List[Dict[str, str]]] = None  # 对话历史


class KnowledgeGraphChatEntity(BaseModel):
    """知识图谱对话请求实体"""
    question: str
    collection_name: str = "default"
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    chat_history: Optional[List[Dict[str, str]]] = None
    files: Optional[List[str]] = None  # 文件路径列表，用于本地聊天模式 