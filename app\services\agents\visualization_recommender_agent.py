import json
import time
from typing import List, Dict, Any
from autogen_agentchat.agents import AssistantAgent
from autogen_core import message_handler, MessageContext, type_subscription

from .base_agent import (
    BaseAgent, VisualizationException, AGENT_NAMES,
    visualization_recommender_topic_type
)
from app.schemas import SqlResultMessage, Text2SQLResponse

# 获取logger实例
from utils.logger import get_logger
from utils.encode_utils import safe_json_dumps

logger = get_logger()


@type_subscription(topic_type=visualization_recommender_topic_type)
class VisualizationRecommenderAgent(BaseAgent):
    """数据可视化推荐智能体，负责建议合适的可视化方式"""
    
    def __init__(self, db_type=None, analytics_service=None, query_id: str = None):
        super().__init__("visualization_recommender_agent", db_type, analytics_service, query_id)
        self._prompt = """```
        你是一名数据可视化专家。根据用户指令、SQL查询及结果，推荐最合适的数据可视化类型，并提供相应的ECharts配置JSON或特定表格JSON。
        ## 核心任务  
        1.  **分析输入：**
            * 理解SQL查询目标（如趋势、比较、占比、分布、明细）。
            * 分析结果数据结构（字段、数据类型、组织方式，如时序、分类、数值）。
        2.  **推荐可视化类型 (并简述理由)：**
            * `"line"` (折线图): 时间序列趋势。
            * `"bar"` (柱状图): 类别间数值比较。
            * `"pie"` (饼图): 各部分占总体比例 (类别≤8，数据为正)。
            * `"scatter"` (散点图): 两数值变量间关系或分布。
            * `"table"` (表格): 数据复杂、多维，或无法清晰图表化。
        3.  **生成配置 (参考提供的ECharts JSON示例和表格格式)：**
            * **对于ECharts图表：**
                * 输出**完整、可直接使用**的ECharts JSON配置。
                * 必须包含合理的 `title`, `tooltip`, `legend`, `grid` (适用时)。
                * **关键数据映射 (务必根据实际查询结果填充)：**
                    * **柱状图/折线图:** `xAxis.data` (分类标签), `series[].data` (数值数组)。折线图时序数据 `xAxis.boundaryGap` 通常为 `false`。
                    * **饼图:** `series[].data` 采用 `[{"value": 数值, "name": "标签"}]` 格式。
                    * **散点图:** `series[].data` 采用 `[[x值, y值]]` 二维数组格式；`xAxis.type` 和 `yAxis.type` 通常为 `"value"`。
            * **对于表格：**
                * 输出格式：`{"type": "table", "config": {"title": "表格标题", "columns": ["列名1", "列名2"]}}`。
        ## 重要规则
        * **用户指令优先：** 如果用户明确指定图表类型，则**严格遵守**，此为最高优先级。
        * **输出格式：** 必须严格符合所提供的ECharts JSON示例的结构或指定的表格JSON格式。
        ## 支持的可视化类型及主要用途
        * `"bar"`: 柱状图 - 比较类别数值。
        * `"line"`: 折线图 - 展示时序趋势。
        * `"pie"`: 饼图 - 展示占比关系。
        * `"scatter"`: 散点图 - 展示双变量关系。
        * `"table"`: 表格 - 展示详细或复杂数据。
        ---
        **柱状图示例:**
        ```json
        {
          "title": {
            "text": "图表标题",
            "left": "center"
          },
          "tooltip": {
            "trigger": "axis",
            "axisPointer": {
              "type": "shadow"
            }
          },
          "legend": {
            "data": ["系列名称"],
            "top": "bottom"
          },
          "grid": {
            "left": "3%",
            "right": "4%",
            "bottom": "10%",
            "containLabel": true
          },
          "xAxis": {
            "type": "category",
            "data": ["类别1", "类别2", "类别3"]
          },
          "yAxis": {
            "type": "value"
          },
          "series": [
            {
              "name": "系列名称",
              "type": "bar",
              "data": [120, 200, 150]
            }
          ]
        }
        ```
        **折线图示例:**
        ```json
        {
          "title": {
            "text": "图表标题",
            "left": "center"
          },
          "tooltip": {
            "trigger": "axis"
          },
          "legend": {
            "data": ["系列名称"],
            "top": "bottom"
          },
          "grid": {
            "left": "3%",
            "right": "4%",
            "bottom": "10%",
            "containLabel": true
          },
          "xAxis": {
            "type": "category",
            "boundaryGap": false,
            "data": ["1月", "2月", "3月", "4月", "5月"]
          },
          "yAxis": {
            "type": "value"
          },
          "series": [
            {
              "name": "系列名称",
              "type": "line",
              "data": [820, 932, 901, 934, 1290],
              "smooth": true
            }
          ]
        }
        ```
        **饼图示例:**
        ```json
        {
          "title": {
            "text": "图表标题",
            "left": "center"
          },
          "tooltip": {
            "trigger": "item",
            "formatter": "{a} <br/>{b}: {c} ({d}%)"
          },
          "legend": {
            "orient": "vertical",
            "left": "left"
          },
          "series": [
            {
              "name": "数据分布",
              "type": "pie",
              "radius": "50%",
              "center": ["50%", "60%"],
              "data": [
                {"value": 335, "name": "直接访问"},
                {"value": 310, "name": "邮件营销"},
                {"value": 234, "name": "联盟广告"},
                {"value": 135, "name": "视频广告"},
                {"value": 1548, "name": "搜索引擎"}
              ],
              "emphasis": {
                "itemStyle": {
                  "shadowBlur": 10,
                  "shadowOffsetX": 0,
                  "shadowColor": "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        }
        ```
        **散点图示例:**
        ```json
        {
          "title": {
            "text": "图表标题",
            "left": "center"
          },
          "tooltip": {
            "trigger": "item",
            "formatter": "{a} <br/>{b}: ({c})"
          },
          "legend": {
            "data": ["系列名称"],
            "top": "bottom"
          },
          "grid": {
            "left": "3%",
            "right": "4%",
            "bottom": "10%",
            "containLabel": true
          },
          "xAxis": {
            "type": "value",
            "scale": true
          },
          "yAxis": {
            "type": "value",
            "scale": true
          },
          "series": [
            {
              "name": "系列名称",
              "type": "scatter",
              "data": [[161.2, 51.6], [167.5, 59.0], [159.5, 49.2]],
              "symbolSize": 8
            }
          ]
        }
        ```
        **表格格式:**
        ```json
        {
            "type": "table",
            "config": {
                "title": "数据表格",
                "columns": ["字段名1", "字段名2", "字段名3"]
            }
        }
        ```
        """

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["visualization_recommender"]

    def _extract_json_from_response(self, response: str) -> str:
        """从LLM响应中提取JSON内容
        
        Args:
            response: LLM的原始响应
            
        Returns:
            str: 清理后的JSON字符串
        """
        # 移除代码块标记
        cleaned = response.strip()
        
        # 移除```json和```标记
        if "```json" in cleaned:
            # 提取```json和```之间的内容
            start = cleaned.find("```json") + 7
            end = cleaned.find("```", start)
            if end != -1:
                cleaned = cleaned[start:end].strip()
            else:
                cleaned = cleaned[start:].strip()
        elif "```" in cleaned:
            # 提取```和```之间的内容
            start = cleaned.find("```") + 3
            end = cleaned.find("```", start)
            if end != -1:
                cleaned = cleaned[start:end].strip()
            else:
                cleaned = cleaned[start:].strip()
        
        # 查找第一个{和最后一个}
        start_brace = cleaned.find("{")
        end_brace = cleaned.rfind("}")
        
        if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
            cleaned = cleaned[start_brace:end_brace + 1]
        
        return cleaned
    
    def _validate_visualization_config(self, config: Dict[str, Any], results: List[Dict[str, Any]]) -> bool:
        """验证可视化配置是否合理
        
        Args:
            config: 可视化配置
            results: 查询结果数据
            
        Returns:
            bool: 配置是否合理
        """
        try:
            # 如果是表格类型，直接返回True
            if config.get("type") == "table":
                return True
            
            # 检查ECharts配置
            if "series" in config:
                series = config["series"]
                if not isinstance(series, list) or len(series) == 0:
                    return False
                
                # 检查第一个系列的数据
                first_series = series[0]
                if "data" not in first_series:
                    return False
                
                series_data = first_series["data"]
                if not isinstance(series_data, list):
                    return False
                
                # 检查数据类型是否合理
                if len(series_data) > 0:
                    first_item = series_data[0]
                    # 对于柱状图和折线图，数据应该是数值
                    chart_type = first_series.get("type", "bar")
                    if chart_type in ["bar", "line"]:
                        # 检查是否所有数据都可以转换为数值
                        for item in series_data:
                            try:
                                if isinstance(item, str) and not self._is_numeric_string(item):
                                    # 如果是日期字符串作为数值，这是不合理的
                                    if "T" in item or "-" in item:
                                        return False
                                float(item)
                            except (ValueError, TypeError):
                                return False
                
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"验证可视化配置时出错: {str(e)}")
            return False
    
    def _is_numeric_string(self, s: str) -> bool:
        """检查字符串是否为数值字符串
        
        Args:
            s: 要检查的字符串
            
        Returns:
            bool: 是否为数值字符串
        """
        try:
            float(s)
            return True
        except ValueError:
            return False

    def _create_default_visualization(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建默认可视化配置
        
        Args:
            results: 查询结果
            
        Returns:
            Dict[str, Any]: 默认可视化配置
        """
        if not results:
            return {
                "type": "table",
                "config": {
                    "title": "数据表格",
                    "columns": []
                }
            }
            
        first_row = results[0]
        columns = list(first_row.keys())
        
        # 智能选择可视化方式
        if len(columns) >= 2:
            # 分析数据类型，选择合适的可视化方式
            category_column = None
            value_column = None
            
            # 查找合适的分类列和数值列
            for col in columns:
                sample_value = first_row[col]
                if category_column is None and self._is_good_category_column(col, sample_value):
                    category_column = col
                elif value_column is None and self._is_numeric_value(sample_value):
                    value_column = col
            
            # 如果找到了合适的列，创建图表配置
            if category_column and value_column:
                try:
                    # 提取数据
                    categories = []
                    values = []
                    
                    for row in results:
                        cat = str(row[category_column])
                        val = row[value_column]
                        
                        # 确保值是数值类型
                        if self._is_numeric_value(val):
                            categories.append(cat)
                            values.append(float(val) if isinstance(val, (int, float, str)) else val)
                    
                    # 如果有有效数据，创建图表
                    if categories and values and len(categories) == len(values):
                        # 判断是否为时间序列数据
                        is_time_series = self._is_time_series_data(category_column, categories)
                        chart_type = "line" if is_time_series else "bar"
                        
                        return {
                            "title": {
                                "text": f"{value_column}的分布",
                                "left": "center"
                            },
                            "tooltip": {
                                "trigger": "axis",
                                "axisPointer": {
                                    "type": "shadow" if chart_type == "bar" else "line"
                                }
                            },
                            "grid": {
                                "left": "3%",
                                "right": "4%",
                                "bottom": "3%",
                                "containLabel": True
                            },
                            "xAxis": {
                                "type": "category",
                                "data": categories,
                                "boundaryGap": chart_type == "bar"
                            },
                            "yAxis": {
                                "type": "value"
                            },
                            "series": [
                                {
                                    "name": value_column,
                                    "type": chart_type,
                                    "data": values,
                                    "smooth": chart_type == "line"
                                }
                            ]
                        }
                except Exception as e:
                    logger.debug(f"创建默认图表配置时出错: {str(e)}")
        # 如果无法创建图表，返回表格
        return {
            "type": "table",
            "config": {
                "title": "数据表格",
                "columns": columns
            }
        }
    
    def _is_good_category_column(self, column_name: str, sample_value: Any) -> bool:
        """判断是否为合适的分类列
        
        Args:
            column_name: 列名
            sample_value: 样本值
            
        Returns:
            bool: 是否为合适的分类列
        """
        # ID类型的列通常是好的分类列
        if "id" in column_name.lower():
            return True
        
        # 日期类型的列也是好的分类列
        if "date" in column_name.lower() or "time" in column_name.lower():
            return True
        
        # 名称类型的列
        if "name" in column_name.lower():
            return True
        
        # 检查值的类型
        if isinstance(sample_value, str):
            return len(sample_value) < 50  # 短字符串更适合作为分类
        
        return True
    
    def _is_numeric_value(self, value: Any) -> bool:
        """判断值是否为数值类型
        
        Args:
            value: 要检查的值
            
        Returns:
            bool: 是否为数值类型
        """
        if isinstance(value, (int, float)):
            return True
        
        if isinstance(value, str):
            try:
                float(value)
                return True
            except ValueError:
                return False
        
        return False
    
    def _is_time_series_data(self, column_name: str, categories: List[str]) -> bool:
        """判断是否为时间序列数据
        
        Args:
            column_name: 列名
            categories: 分类数据
            
        Returns:
            bool: 是否为时间序列数据
        """
        # 基于列名判断
        if "date" in column_name.lower() or "time" in column_name.lower():
            return True
        
        # 基于数据内容判断
        if len(categories) > 0:
            sample = categories[0]
            if isinstance(sample, str) and ("-" in sample or "T" in sample):
                return True
        
        return False

    async def _generate_visualization_recommendation(self, query: str, sql: str, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成可视化推荐
        
        Args:
            query: 用户查询
            sql: SQL语句
            results: 查询结果
            
        Returns:
            Dict[str, Any]: 可视化配置
        """
        try:
            results_json = safe_json_dumps(results)
            task = f"""
            ## 用户指令
             {query}

            ## 待分析的SQL查询
            {sql}

            ## SQL查询结果数据
            ```json
            {results_json}
            ```

            请根据提供的上述信息，分析并输出最合适的可视化类型和配置，输出必须是有效的JSON
            """
            
            agent = AssistantAgent(
                name="visualization_recommender",
                model_client=self.model_client,
                system_message=self._prompt,
                model_client_stream=True,
            )

            result = await agent.run(task=task)
            
            if not result or not result.messages:
                logger.warning("可视化推荐智能体未返回有效结果，使用默认配置")
                return self._create_default_visualization(results)
                
            visualization_json = result.messages[-1].content
            
            try:
                # 改进的JSON解析逻辑
                cleaned_json = self._extract_json_from_response(visualization_json)
                visualization = json.loads(cleaned_json)
                
                # 验证生成的可视化配置是否合理
                if self._validate_visualization_config(visualization, results):
                    return visualization
                else:
                    logger.warning("生成的可视化配置不合理，使用默认配置")
                    return self._create_default_visualization(results)
                    
            except json.JSONDecodeError as e:
                logger.warning(f"解析可视化配置JSON失败: {str(e)}，使用默认配置")
                logger.debug(f"原始响应内容: {visualization_json}")
                return self._create_default_visualization(results)
                
        except Exception as e:
            logger.error(f"生成可视化推荐时出错: {str(e)}")
            return self._create_default_visualization(results)

    @message_handler
    async def handle_message(self, message: SqlResultMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，推荐可视化方式"""
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "sql": message.sql,
                "results_count": len(message.results) if message.results else 0
            })
            
            # 验证消息
            if not self.validate_message(message, ['query', 'sql', 'results']):
                error = VisualizationException("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            # 发送处理中消息
            await self.send_stream_message("正在分析数据，生成可视化建议...\n\n", message_sequence=1)
            
            try:
                visualization = await self._generate_visualization_recommendation(
                    message.query, 
                    message.sql, 
                    message.results
                )
                # 记录模型调用统计
                await self.increment_model_usage(calls_count=1, tokens_used=len(str(visualization)))
                
            except Exception as e:
                await self.record_error(e)
                await self.handle_exception(e, "生成可视化推荐")
                # 使用默认配置继续
                visualization = self._create_default_visualization(message.results)

            # 如果是表格，则直接返回，因为表格已经在上个智能体中已经呈现
            if visualization.get("type") == "table":
                await self.send_stream_message("可视化分析已完成【内容为表格形式，直接参考数据查询区域】", is_final=True, message_sequence=2)
                
                # 完成执行，记录成功状态
                output_data = {
                    "visualization_type": "table",
                    "visualization_completed": True
                }
                await self.finish_execution("SUCCESS", output_data=output_data)
                return
                
            # 构建最终结果 - 处理新的ECharts格式
            if "type" in visualization:
                # 表格格式
                visualization_type = visualization.get("type", "bar")
                visualization_config = visualization.get("config", {})
            else:
                # ECharts标准格式 - 从series中提取图表类型
                series = visualization.get("series", [])
                if series and len(series) > 0:
                    visualization_type = series[0].get("type", "bar")
                else:
                    visualization_type = "bar"
                visualization_config = visualization
                
            final_result = Text2SQLResponse(
                sql=message.sql,
                explanation=getattr(message, 'explanation', ""),
                results=message.results,
                visualization_type=visualization_type,
                visualization_config=visualization_config
            )
            
            # 完成执行，记录成功状态
            output_data = {
                "visualization_type": visualization_type,
                "visualization_config": visualization_config,
                "visualization_completed": True
            }
            await self.finish_execution("SUCCESS", output_data=output_data)
            
            # 清理最终结果中的不可序列化对象
            from utils.encode_utils import clean_dict_for_json
            cleaned_result = clean_dict_for_json(final_result.model_dump())
            
            await self.send_stream_message(
                "处理完成，返回最终结果",
                is_final=True,
                result=cleaned_result,
                message_sequence=3
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理SQL结果消息") 