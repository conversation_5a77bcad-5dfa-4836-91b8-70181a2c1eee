"""
RAG MCP 服务器
基于重构后的RAG服务层，提供MCP标准的RAG功能接口
"""
import asyncio
import json
from typing import Dict, Any, List, Optional

from fastmcp import FastMCP, Context
from pydantic import BaseModel

from app.services.rag_service import rag_service
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建RAG MCP服务器实例
rag_server = FastMCP("RAG Server")

# ============================================================================
# RAG相关数据模型
# ============================================================================

class UploadFileRequest(BaseModel):
    file_path: str
    collection_name: str = "default"
    use_kg: bool = False

class RAGQueryRequest(BaseModel):
    question: str
    collection_name: str = "default"
    top_k: int = 5
    use_hybrid: bool = True
    use_rerank: bool = True

# ============================================================================
# RAG相关工具
# ============================================================================

@rag_server.tool
async def upload_file_by_doc_id(doc_id: str, collection_name: str = "default", use_kg: bool = False, embedding_model: str = "local", ctx: Context = None) -> Dict[str, Any]:
    """
    通过文档ID上传文件到RAG系统进行向量化处理，默认使用多模态模式（对应FastAPI接口 /uploadfilesbydocid）
    
    Args:
        doc_id: 文档ID
        collection_name: 集合名称
        use_kg: 是否使用知识图谱
        embedding_model: 嵌入模型类型
        
    Returns:
        上传结果的字典
    """
    try:
        await ctx.info(f"开始通过文档ID {doc_id} 上传文件到集合 {collection_name}")
        
        # 复用服务层的业务逻辑
        result = await rag_service.upload_files_by_docid(
            collection_name=collection_name,
            use_kg=use_kg,
            doc_id=doc_id,
            embedding_model=embedding_model
        )
        
        await ctx.info("文档上传完成")
        return result
        
    except Exception as e:
        error_msg = f"上传文件时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }


@rag_server.tool
async def get_rag_collections(identifier: str = "admin", ctx: Context = None) -> Dict[str, Any]:
    """
    获取用户可访问的RAG知识库集合（对应FastAPI接口 /collections/）
    
    Args:
        identifier: 用户标识符（用户名），默认为"admin"
    
    Returns:
        用户可访问的知识库集合列表
    """
    try:
        await ctx.info(f"正在获取用户 {identifier} 的RAG集合列表")
        
        # 复用服务层的业务逻辑，传递用户身份信息
        result = await rag_service.list_collections(identifier)
        
        if result.get("success", False):
            collections_data = result.get("data", {})
            await ctx.info(f"成功获取 {len(collections_data)} 个可访问的知识库集合")
        else:
            await ctx.info("获取集合列表失败")
            
        return result
        
    except Exception as e:
        error_msg = f"获取集合列表时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def delete_rag_collection(collection_name: str, ctx: Context) -> Dict[str, Any]:
    """
    删除RAG集合（对应FastAPI接口 /collections/{collection_name}）
    
    Args:
        collection_name: 要删除的集合名称
        
    Returns:
        删除结果
    """
    try:
        await ctx.info(f"正在删除RAG集合: {collection_name}")
        
        # 复用服务层的业务逻辑
        result = await rag_service.delete_collection(collection_name)
        
        await ctx.info("集合删除完成")
        return result
        
    except Exception as e:
        error_msg = f"删除集合时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def query_rag_documents(collection_name: str, file_name: Optional[str] = None, ctx: Context = None) -> Dict[str, Any]:
    """
    查询RAG文档（对应FastAPI接口 /documents/）
    
    Args:
        collection_name: 集合名称
        file_name: 文件名（可选）
        
    Returns:
        文档列表
    """
    try:
        await ctx.info(f"正在查询集合 {collection_name} 中的文档")
        
        # 复用服务层的业务逻辑
        result = await rag_service.query_documents(collection_name, file_name)
        
        await ctx.info("文档查询完成")
        return result
        
    except Exception as e:
        error_msg = f"查询文档时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def get_embedding_status(doc_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取嵌入任务状态（对应FastAPI接口 /embedding/status/{doc_id}）
    
    Args:
        doc_id: 文档ID
        
    Returns:
        嵌入状态
    """
    try:
        await ctx.info(f"正在获取文档 {doc_id} 的嵌入状态")
        
        # 复用服务层的业务逻辑
        result = await rag_service.get_embedding_status(doc_id)
        
        await ctx.info("嵌入状态获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取嵌入状态时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def get_all_embedding_tasks(ctx: Context) -> Dict[str, Any]:
    """
    获取所有嵌入任务状态（对应FastAPI接口 /embedding/tasks/）
    
    Returns:
        所有嵌入任务状态
    """
    try:
        await ctx.info("正在获取所有嵌入任务状态")
        
        # 复用服务层的业务逻辑
        result = await rag_service.get_all_embedding_tasks()
        
        await ctx.info("嵌入任务状态获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取嵌入任务状态时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def delete_rag_document(collection_name: str, file_name: str, ctx: Context) -> Dict[str, Any]:
    """
    删除RAG文档（对应FastAPI接口 /documents/{collection_name}/{file_name}）
    
    Args:
        collection_name: 集合名称
        file_name: 文件名
        
    Returns:
        删除结果
    """
    try:
        await ctx.info(f"正在删除文档: {file_name}")
        
        # 复用服务层的业务逻辑
        result = await rag_service.delete_document(collection_name, file_name)
        
        await ctx.info("文档删除完成")
        return result
        
    except Exception as e:
        error_msg = f"删除文档时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def rag_stream_query(
    question: str, 
    collection_name: str = "default", 
    top_k: int = 5, 
    use_hybrid: bool = True, 
    use_rerank: bool = True,
    chat_history: Optional[List[Dict[str, str]]] = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    RAG流式查询（对应FastAPI接口 /rag_stream/）
    
    Args:
        question: 查询问题
        collection_name: 集合名称
        top_k: 返回结果数量
        use_hybrid: 是否使用混合检索
        use_rerank: 是否使用重排序
        chat_history: 对话历史，格式为[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
        
    Returns:
        流式查询结果概要
    """
    try:
        await ctx.info(f"正在启动流式查询: {question}")
        
        # 收集流式响应并返回概要
        response_parts = []
        async for sse_message in rag_service.stream_rag_query(
            question=question,
            collection_name=collection_name,
            top_k=top_k,
            use_hybrid=use_hybrid,
            use_rerank=use_rerank,
            chat_history=chat_history  # 传递对话历史
        ):
            # 简单解析SSE消息
            if "data: " in sse_message:
                import json
                try:
                    data_part = sse_message.split("data: ", 1)[1].strip()
                    data = json.loads(data_part)
                    if data.get("type") == "token" and data.get("content"):
                        response_parts.append(data["content"])
                except:
                    pass
        
        full_response = "".join(response_parts)
        
        await ctx.info("流式查询完成")
        return {
            "status": "success",
            "question": question,
            "collection_name": collection_name,
            "answer": full_response,
            "chat_history_length": len(chat_history) if chat_history else 0,
            "query_params": {
                "top_k": top_k,
                "use_hybrid": use_hybrid,
                "use_rerank": use_rerank
            }
        }
        
    except Exception as e:
        error_msg = f"流式查询时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@rag_server.tool
async def get_embedding_text(query: str, ctx: Context) -> Dict[str, Any]:
    """
    获取文本向量嵌入（对应FastAPI接口 /embedding/text/）
    
    Args:
        query: 要嵌入的文本
        
    Returns:
        文本向量嵌入结果
    """
    try:
        await ctx.info(f"正在获取文本向量嵌入")
        
        # 复用服务层的业务逻辑
        result = await rag_service.get_embedding_text(query)
        
        await ctx.info("文本向量嵌入获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取文本向量嵌入时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

# ============================================================================
# RAG相关提示模板
# ============================================================================

@rag_server.prompt
def rag_usage_prompt() -> str:
    """RAG系统使用指南"""
    return """# RAG (检索增强生成) 系统使用指南

本RAG系统基于重构后的服务层架构，提供以下核心功能：

## 主要功能

### 1. 文档上传与嵌入
- `upload_file_by_doc_id`: 通过文档ID上传并嵌入文件
- 默认使用多模态处理，支持知识图谱增强
- 支持多种嵌入模型选择

### 2. 集合管理
- `get_rag_collections`: 获取所有集合
- `delete_rag_collection`: 删除指定集合

### 3. 文档管理
- `query_rag_documents`: 查询集合中的文档
- `delete_rag_document`: 删除指定文档

### 4. 任务监控
- `get_embedding_status`: 获取嵌入任务状态
- `get_all_embedding_tasks`: 获取所有任务状态

### 5. 向量服务
- `get_embedding_text`: 获取文本向量嵌入

## 架构优势
- 复用服务层业务逻辑，避免重复实现
- 统一的错误处理和日志记录
- 更好的代码维护性和扩展性
"""

@rag_server.prompt
def rag_best_practices_prompt() -> str:
    """RAG系统最佳实践"""
    return """# RAG系统最佳实践

## 文档处理最佳实践
1. **选择合适的嵌入模型**
   - 英文文档：推荐OpenAI embeddings
   - 中文文档：推荐BGE-large模型
   - 多语言：推荐多语言嵌入模型

2. **优化文档分块**
   - 代码文档：按函数/类分块
   - 长文档：按段落或章节分块
   - 结构化文档：保持语义完整性

3. **合理设置检索参数**
   - top_k: 通常设置为5-10
   - 混合检索：适用于精确匹配需求
   - 重排序：提高结果相关性

## 性能优化建议
1. **集合管理**
   - 按主题或类型组织集合
   - 定期清理不需要的文档
   - 监控集合大小和性能

2. **查询优化**
   - 使用流式查询提升用户体验
   - 缓存常见查询结果
   - 优化检索参数配置

3. **资源管理**
   - 监控嵌入任务状态
   - 合理安排批量处理
   - 注意内存和存储使用
"""

@rag_server.prompt
def rag_troubleshooting_prompt() -> str:
    """RAG系统故障排查"""
    return """# RAG系统故障排查指南

## 常见问题及解决方案

### 1. 文档上传问题
**问题**: 文档上传失败
**排查步骤**:
- 检查文档ID是否存在
- 验证文件路径是否正确
- 确认Milvus连接状态
- 查看嵌入任务状态

### 2. 查询无结果
**问题**: RAG查询返回空结果
**排查步骤**:
- 确认集合是否存在文档
- 检查查询参数设置
- 验证嵌入模型兼容性
- 调整相似度阈值

### 3. 嵌入任务卡住
**问题**: 嵌入任务长时间处理中
**排查步骤**:
- 检查后台任务日志
- 验证线程池状态
- 重启嵌入任务
- 清理异常任务状态

### 4. 性能问题
**问题**: 查询响应慢
**排查步骤**:
- 检查向量数据库性能
- 优化检索参数
- 考虑分片策略
- 启用查询缓存

## 监控和维护
1. **定期检查**
   - 嵌入任务状态
   - 集合健康状况
   - 系统资源使用

2. **数据维护**
   - 清理过期文档
   - 更新文档嵌入
   - 优化向量索引

3. **性能调优**
   - 监控查询性能
   - 调整缓存策略
   - 优化并发设置
"""

@rag_server.prompt
def rag_usage_examples_prompt() -> str:
    """RAG系统使用示例"""
    return """# RAG系统使用示例

## 基础使用流程

### 1. 上传文档
```
# 上传单个文档
upload_file_by_doc_id(
    doc_id="doc_123",
    collection_name="technical_docs",
    use_kg=True,
    embedding_model="local"
)
```

### 2. 管理集合
```
# 查看所有集合
get_rag_collections()

# 查看集合中的文档
query_rag_documents(
    collection_name="technical_docs",
    file_name="config_guide.md"
)
```

### 3. 监控任务
```
# 检查特定文档的嵌入状态
get_embedding_status(doc_id="doc_123")

# 查看所有嵌入任务
get_all_embedding_tasks()
```

## 高级用例

### 知识图谱增强处理
```
upload_file_by_doc_id(
    doc_id="complex_doc",
    collection_name="mixed_content",
    use_kg=True,      # 启用知识图谱增强
    embedding_model="local"
)
```

### 专业领域查询
```
# 技术文档查询
query_rag(
    question="API接口如何实现身份验证？",
    collection_name="api_docs",
    top_k=3,
    use_hybrid=True
)

# 业务流程查询
rag_stream_query(
    question="用户注册流程的步骤是什么？",
    collection_name="business_docs",
    top_k=5
)
```
"""

@rag_server.prompt
def multimodal_rag_prompt() -> str:
    """多模态RAG指南"""
    return """# 多模态RAG系统指南

## 系统特点
本RAG系统默认使用多模态模式，支持处理多种类型的文档内容：

### 支持的内容类型
1. **文本内容**
   - 纯文本文档
   - Markdown格式
   - 结构化文本

2. **图像内容**
   - 文档中的图片
   - 图表和示意图
   - 截图和界面图

3. **混合内容**
   - 包含图文的复合文档
   - 技术文档和手册
   - 演示文稿

## 多模态处理优势
1. **完整信息提取**
   - 不丢失图像中的关键信息
   - 理解图文之间的关系
   - 提供更准确的查询结果

2. **智能内容理解**
   - 自动识别图像内容
   - 提取图表数据信息
   - 理解视觉元素含义

## 系统设计
- **统一处理模式**: 所有文档都使用多模态处理，无需手动选择
- **最佳用户体验**: 自动适配不同类型的文档内容
- **简化操作流程**: 减少用户配置复杂度

## 使用建议
1. **文档准备**
   - 确保图文内容清晰可读
   - 保持文档结构的逻辑性
   - 避免过度压缩图像质量

2. **性能考虑**
   - 多模态处理需要更多计算资源
   - 嵌入时间相对较长
   - 建议合理规划处理批次

3. **查询优化**
   - 可以查询图像相关内容
   - 支持跨模态信息检索
   - 结果包含图文关联信息
""" 