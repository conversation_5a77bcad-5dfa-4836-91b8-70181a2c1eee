import asyncio
from typing import Dict, Any, List, Optional

from fastmcp import FastMCP, Context
from pydantic import BaseModel

from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建知识图谱MCP服务器实例
kg_server = FastMCP("Knowledge Graph Server")

# ============================================================================
# 知识图谱相关工具
# ============================================================================

@kg_server.tool
async def get_knowledge_graph_statistics(ctx: Context) -> Dict[str, Any]:
    """
    获取知识图谱统计信息
    
    Returns:
        知识图谱统计数据
    """
    try:
        await ctx.info("正在获取知识图谱统计信息")
        
        # 这里需要实现获取知识图谱统计的逻辑
        # 由于具体实现依赖于知识图谱的存储方式，这里提供一个示例框架
        statistics = {
            "total_entities": 0,
            "total_relationships": 0,
            "entity_types": {},
            "relationship_types": {}
        }
        
        await ctx.info("知识图谱统计信息获取完成")
        return {
            "status": "success",
            "statistics": statistics
        }
        
    except Exception as e:
        error_msg = f"获取知识图谱统计时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@kg_server.tool
async def search_knowledge_entities(query: str, entity_types: Optional[List[str]] = None, limit: int = 20, ctx: Context = None) -> Dict[str, Any]:
    """
    搜索知识图谱中的实体（对应FastAPI接口 /search）
    
    Args:
        query: 搜索查询
        entity_types: 要搜索的实体类型（可选）
        limit: 返回结果数量限制
        
    Returns:
        搜索结果
    """
    try:
        from utils.kg_utils import KnowledgeGraphManager
        
        await ctx.info(f"正在搜索知识图谱实体: {query}")
        
        kg_manager = KnowledgeGraphManager.get_instance()
        entities = await kg_manager.search_entities(query=query, entity_types=entity_types, limit=limit)
        
        await ctx.info(f"搜索完成，找到 {len(entities)} 个实体")
        return {
            "status": "success",
            "query": query,
            "entity_types": entity_types,
            "entities": entities,
            "limit": limit,
            "count": len(entities)
        }
        
    except Exception as e:
        error_msg = f"搜索知识图谱实体时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@kg_server.tool
async def get_entity_details(entity_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取实体的详细信息（对应FastAPI接口 /entity/{entity_id}）
    
    Args:
        entity_id: 实体ID
        
    Returns:
        实体详细信息
    """
    try:
        from utils.kg_utils import KnowledgeGraphManager
        
        await ctx.info(f"正在获取实体 {entity_id} 的详细信息")
        
        kg_manager = KnowledgeGraphManager.get_instance()
        details = await kg_manager.get_entity_details(entity_id)
        
        if "error" in details:
            return {
                "status": "not_found",
                "entity_id": entity_id,
                "message": details["error"]
            }
        
        await ctx.info(f"成功获取实体详细信息")
        return {
            "status": "success",
            "entity_id": entity_id,
            "details": details
        }
        
    except Exception as e:
        error_msg = f"获取实体详细信息时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@kg_server.tool
async def search_entity_relationships(entity_name: Optional[str] = None, entity_type: Optional[str] = None, relation_type: Optional[str] = None, max_nodes: int = 50, ctx: Context = None) -> Dict[str, Any]:
    """
    搜索实体关系（对应FastAPI接口 /graph）
    
    Args:
        entity_name: 实体名称（可选）
        entity_type: 实体类型（可选）
        relation_type: 关系类型（可选）
        max_nodes: 最大节点数量
        
    Returns:
        图谱数据
    """
    try:
        from utils.kg_utils import KnowledgeGraphManager
        
        await ctx.info("正在查询知识图谱关系")
        
        kg_manager = KnowledgeGraphManager.get_instance()
        graph_data = await kg_manager.query_knowledge_graph(
            entity_name=entity_name,
            entity_type=entity_type,
            relation_type=relation_type,
            max_nodes=max_nodes
        )
        
        nodes_count = len(graph_data.get("nodes", []))
        links_count = len(graph_data.get("links", []))
        
        await ctx.info(f"查询完成，返回 {nodes_count} 个节点和 {links_count} 个关系")
        return {
            "status": "success",
            "filter_params": {
                "entity_name": entity_name,
                "entity_type": entity_type,
                "relation_type": relation_type,
                "max_nodes": max_nodes
            },
            "graph_data": graph_data,
            "statistics": {
                "nodes_count": nodes_count,
                "links_count": links_count
            }
        }
        
    except Exception as e:
        error_msg = f"查询实体关系时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@kg_server.tool
async def get_document_knowledge_graph(doc_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取特定文档的知识图谱（对应FastAPI接口 /document/{doc_id}）
    
    Args:
        doc_id: 文档ID
        
    Returns:
        文档知识图谱数据
    """
    try:
        from utils.kg_utils import KnowledgeGraphManager
        
        await ctx.info(f"正在获取文档 {doc_id} 的知识图谱")
        
        kg_manager = KnowledgeGraphManager.get_instance()
        doc_graph = await kg_manager.get_document_entities(doc_id)
        
        nodes_count = len(doc_graph.get("nodes", []))
        links_count = len(doc_graph.get("links", []))
        
        await ctx.info(f"文档图谱获取完成，包含 {nodes_count} 个实体和 {links_count} 个关系")
        return {
            "status": "success",
            "doc_id": doc_id,
            "graph_data": doc_graph,
            "statistics": {
                "nodes_count": nodes_count,
                "links_count": links_count
            }
        }
        
    except Exception as e:
        error_msg = f"获取文档知识图谱时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@kg_server.tool
async def create_knowledge_graph_from_text(text: str, ctx: Context) -> Dict[str, Any]:
    """
    从文本创建知识图谱
    
    Args:
        text: 输入文本
        
    Returns:
        创建结果
    """
    try:
        await ctx.info("正在从文本创建知识图谱")
        
        # 这里需要实现从文本提取实体和关系的逻辑
        result = {
            "extracted_entities": 0,
            "extracted_relationships": 0,
            "created_successfully": True
        }
        
        await ctx.info("知识图谱创建完成")
        return {
            "status": "success",
            "input_text_length": len(text),
            "result": result
        }
        
    except Exception as e:
        error_msg = f"从文本创建知识图谱时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

# ============================================================================
# 知识图谱提示模板
# ============================================================================

@kg_server.prompt
def knowledge_graph_usage_prompt() -> str:
    """知识图谱使用指南提示模板"""
    return """
    你是一个专业的知识图谱助手。你可以帮助用户：
    
    **知识抽取与构建：**
    - 从文本中自动抽取实体和关系
    - 构建领域专用的知识图谱
    - 知识融合和去重处理
    - 知识质量评估和验证
    
    **图谱查询与检索：**
    - 实体搜索和语义匹配
    - 关系路径查询和推理
    - 复杂图模式匹配
    - 知识推荐和发现
    
    **图谱分析与挖掘：**
    - 图结构分析和统计
    - 社区发现和聚类分析
    - 影响力分析和排名
    - 异常模式检测
    
    **知识问答：**
    - 基于知识图谱的问答
    - 多跳推理和路径查询
    - 知识解释和溯源
    - 交互式知识探索
    
    **图谱可视化：**
    - 交互式图谱浏览
    - 多层次视图展示
    - 定制化图谱布局
    - 动态图谱演化展示
    
    请告诉我你需要什么帮助，我会为你提供专业的知识图谱解决方案。
    """

@kg_server.prompt
def knowledge_extraction_prompt() -> str:
    """知识抽取提示模板"""
    return """
    知识抽取最佳实践：
    
    **实体抽取：**
    - 命名实体识别：人名、地名、机构名等
    - 专业术语抽取：领域特定概念和术语
    - 实体消歧：区分同名不同义的实体
    - 实体链接：连接到已有知识库
    
    **关系抽取：**
    - 语法关系：基于依存句法的关系识别
    - 语义关系：概念间的逻辑关系
    - 时空关系：时间和空间维度的关系
    - 因果关系：原因结果链条识别
    
    **属性抽取：**
    - 实体属性：描述实体特征的属性
    - 数值属性：量化的属性信息
    - 分类属性：类别和标签信息
    - 动态属性：随时间变化的属性
    
    **质量控制：**
    - 准确性验证：多源验证抽取结果
    - 完整性检查：确保重要信息不遗漏
    - 一致性维护：保持图谱内部一致
    - 更新机制：跟踪知识的时效性
    """

@kg_server.prompt
def graph_reasoning_prompt() -> str:
    """图推理提示模板"""
    return """
    知识图谱推理技术：
    
    **符号推理：**
    - 基于规则的推理：逻辑规则和约束
    - 路径推理：多跳关系链推理
    - 类型推理：基于本体的类型推断
    - 时序推理：时间维度的逻辑推理
    
    **统计推理：**
    - 链接预测：预测缺失的关系
    - 实体对齐：跨图谱实体匹配
    - 知识补全：填补知识图谱空缺
    - 异常检测：识别不合理的知识
    
    **神经推理：**
    - 图神经网络：学习图结构表示
    - 知识图嵌入：实体关系向量化
    - 注意力机制：关注重要的推理路径
    - 多模态推理：结合文本和图像信息
    
    **复合推理：**
    - 混合推理：结合多种推理方法
    - 增量推理：支持动态知识更新
    - 不确定性推理：处理模糊和不确定信息
    - 可解释推理：提供推理过程解释
    """

@kg_server.prompt
def graph_applications_prompt() -> str:
    """知识图谱应用提示模板"""
    return """
    知识图谱应用场景：
    
    **智能问答：**
    - 事实问答：基于图谱的准确回答
    - 复杂推理：多步骤逻辑推理问答
    - 对话系统：上下文感知的智能对话
    - 专业咨询：领域专家知识问答
    
    **推荐系统：**
    - 内容推荐：基于知识关联的推荐
    - 协同过滤：用户行为和知识结合
    - 冷启动：利用知识图谱缓解冷启动
    - 解释性推荐：提供推荐理由
    
    **搜索引擎：**
    - 语义搜索：理解用户搜索意图
    - 知识面板：搜索结果的知识展示
    - 查询扩展：基于知识关联扩展查询
    - 个性化搜索：结合用户知识偏好
    
    **决策支持：**
    - 风险评估：基于知识的风险分析
    - 策略规划：知识驱动的决策制定
    - 趋势预测：基于历史知识的预测
    - 异常监测：识别异常模式和行为
    
    **行业应用：**
    - 金融科技：反欺诈、信贷评估
    - 医疗健康：诊断辅助、药物发现
    - 教育培训：个性化学习、知识图谱
    - 智能制造：故障诊断、工艺优化
    """ 