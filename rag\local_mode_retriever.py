import asyncio
from typing import List, Dict, Any, Optional, Tuple
import json
import re
from llama_index.core import Settings
import time

from config import settings
from utils.kg_utils import KnowledgeGraphManager
from .entity_vectorizer import EntityVectorizer
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()
class LocalModeRetriever:
    """
    局部模式检索器(Local Mode)
    基于实体的检索策略，从用户查询中提取低级关键词，通过向量检索查找匹配实体，
    然后通过实体查找相关文本块和关系
    """
    
    def __init__(self, top_k: Optional[int] = None, 
                 use_contextual: Optional[bool] = None,
                 milvus_uri: Optional[str] = None,
                 milvus_collection_prefix: Optional[str] = None,
                 embed_model = None):
        """
        初始化局部模式检索器
        
        参数:
            top_k: 检索返回的最大实体数量，如果为None则使用默认配置
            use_contextual: 是否使用上下文增强的实体检索，如果为None则使用默认配置
            milvus_uri: Milvus服务URI，如果为None则使用默认配置
            milvus_collection_prefix: Milvus集合名称前缀，如果为None则使用默认配置
            embed_model: 嵌入模型实例，如果为None则使用全局默认模型
        """
        self.top_k = top_k if top_k is not None else settings.configuration.kg_entity_top_k
        self.kg_manager = KnowledgeGraphManager.get_instance()
        self.vectorizer = EntityVectorizer(
            milvus_uri=milvus_uri,
            milvus_collection_prefix=milvus_collection_prefix,
            embed_model=embed_model
        )
        self.graph_field_sep = "|"  # 用于分隔source_id字段中的多个ID
        self.use_contextual = use_contextual if use_contextual is not None else settings.configuration.kg_use_contextual
        
    async def _ensure_initialized(self,collection_name: str):
        """确保数据库和向量索引已初始化"""
        try:
            await self.kg_manager.setup_database()
            # 获取实体索引以确保它已经初始化
            await self.vectorizer.get_entity_index(collection_name)
        except Exception as e:
            logger.error(f"初始化资源时出错: {e}")
    
    async def extract_low_level_keywords(self, query: str) -> List[str]:
        """
        从查询中提取低级关键词
        
        参数:
            query: 用户查询文本
            
        返回:
            List[str]: 提取的低级关键词列表
        """
        llm = Settings.llm
        
        prompt = f"""
请从以下查询中提取关键词，这些关键词应该主要是具体的实体名称、专业术语或特定概念，而非抽象概念。
返回格式应该是一个JSON数组，只包含关键词，不要有其他解释。

例如，对于查询"深度学习在医疗影像诊断中的应用"，应该返回：
["深度学习", "医疗影像", "诊断"]

用户查询: {query}

关键词:
"""
        
        try:
            response = await llm.acomplete(prompt)
            # 针对qwen3.0.0版本，去除<think>标签内的内容，防止后续执行时报错
            filter_response_text = re.sub(r'<think>.*?</think>', '', response.text, flags=re.DOTALL)
            # 尝试提取JSON数组
            keywords_match = re.search(r'\[.*\]', filter_response_text, re.DOTALL)
            if keywords_match:
                keywords_json = keywords_match.group(0)
                try:
                    keywords = json.loads(keywords_json)
                    return keywords
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试使用更宽松的提取
                    words = re.findall(r'"([^"]+)"', keywords_json)
                    return words
            
            # 如果未找到JSON数组，尝试直接拆分文本
            words = [w.strip('"\'[](), ') for w in response.text.split()]
            words = [w for w in words if len(w) > 1 and not w.startswith(('http', 'www'))]
            return words[:5]  # 限制关键词数量
            
        except Exception as e:
            logger.error(f"提取关键词时出错: {e}")
            # 如果提取失败，返回简单的文本分割结果
            words = query.split()
            return [w for w in words if len(w) > 1][:5]  # 过滤掉太短的词
    
    async def search_entities_by_keywords(self, keywords: List[str],collection_name="default") -> List[Dict[str, Any]]:
        """
        根据关键词搜索实体，使用向量检索而不是直接Neo4j查询
        
        参数:
            keywords: 关键词列表
            
        返回:
            List[Dict]: 实体列表，每个实体包含id、name、type等属性
        """
        all_entities = []
        seen_entities = set()
        
        # 使用上下文实体检索或标准实体检索
        search_func = self.vectorizer.search_contextual_entities if self.use_contextual else self.vectorizer.search_entities
        
        # 对每个关键词分别搜索实体
        for keyword in keywords:
            if not keyword or len(keyword) < 2:
                continue
                
            try:
                # 使用向量搜索查询实体
                vector_entities = await search_func(keyword, top_k=self.top_k, collection_name=collection_name)
                
                if not vector_entities:
                    # 如果向量搜索没有结果，尝试使用Neo4j搜索作为备份
                    neo4j_entities = await self.kg_manager.search_entities(query=keyword, limit=self.top_k, collection_name=collection_name)
                    entities = neo4j_entities
                else:
                    entities = vector_entities
                
                # 过滤重复实体
                for entity in entities:
                    entity_id = entity.get("id")
                    if entity_id and entity_id not in seen_entities:
                        seen_entities.add(entity_id)
                        all_entities.append(entity)
                        
                        # 如果已经找到足够多的实体，就停止
                        if len(all_entities) >= self.top_k:
                            break
            except Exception as e:
                logger.error(f"搜索关键词 '{keyword}' 的实体时出错: {e}")
        
        # 按相关度/分数降序排序，如果有分数的话
        all_entities.sort(key=lambda x: x.get("score", 0) if x.get("score") is not None else 0, reverse=True)
        
        # 限制返回的实体数量
        return all_entities[:self.top_k]
    
    async def search_entities_by_semantic(self, query: str,collection_name="default") -> List[Dict[str, Any]]:
        """
        使用语义向量检索直接搜索实体，无需先提取关键词
        
        参数:
            query: 用户查询文本
            
        返回:
            List[Dict]: 实体列表，每个实体包含id、name、type等属性
        """
        # 使用上下文实体检索或标准实体检索
        search_func = self.vectorizer.search_contextual_entities if self.use_contextual else self.vectorizer.search_entities
        
        try:
            # 直接用完整查询进行语义检索
            entities = await search_func(query, top_k=self.top_k, collection_name=collection_name)
            
            # 如果结果不足，尝试使用备选方法
            if len(entities) < self.top_k // 2:
                # 提取关键词
                keywords = await self.extract_low_level_keywords(query)
                
                # 对每个关键词搜索实体
                seen_ids = {entity.get("id") for entity in entities if entity.get("id")}
                
                for keyword in keywords:
                    if len(entities) >= self.top_k:
                        break
                        
                    keyword_entities = await search_func(keyword, top_k=self.top_k,collection_name=collection_name)
                    
                    # 添加未见过的实体
                    for entity in keyword_entities:
                        entity_id = entity.get("id")
                        if entity_id and entity_id not in seen_ids:
                            seen_ids.add(entity_id)
                            entities.append(entity)
                            
                            if len(entities) >= self.top_k:
                                break
            
            # 按相关度/分数降序排序
            entities.sort(key=lambda x: x.get("score", 0) if x.get("score") is not None else 0, reverse=True)
            for i, entity in enumerate(entities[:self.top_k], 1):
                logger.info(f"语义检索返回实体  {i}. {entity.get('name')} ({entity.get('type')}) {entity.get('score')}")
            return entities[:self.top_k]
            
        except Exception as e:
            logger.error(f"语义搜索实体时出错: {e}")
            # 如果语义搜索失败，回退到关键词搜索
            keywords = await self.extract_low_level_keywords(query)
            return await self.search_entities_by_keywords(keywords,collection_name=collection_name)
    
    async def get_entity_details_batch(self, entity_ids: List[str]) -> List[Dict[str, Any]]:
        """
        批量获取实体详细信息
        
        参数:
            entity_ids: 实体ID列表
            
        返回:
            List[Dict]: 实体详细信息列表
        """
        tasks = [self.kg_manager.get_entity_details(entity_id) for entity_id in entity_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉错误的结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"获取实体 '{entity_ids[i]}' 详细信息时出错: {result}")
            elif "error" in result:
                logger.error(f"获取实体 '{entity_ids[i]}' 详细信息时出错: {result['error']}")
            else:
                valid_results.append(result)
                
        return valid_results

    async def get_entity_batch(self, entity_ids: List[str]) -> List[Dict[str, Any]]:
        """
        批量获取实体信息

        参数:
            entity_ids: 实体ID列表

        返回:
            List[Dict]: 实体信息列表
        """
        tasks = [self.kg_manager.get_entity(entity_id) for entity_id in entity_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤掉错误的结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"获取实体 '{entity_ids[i]}' 信息时出错: {result}")
            elif "error" in result:
                logger.error(f"获取实体 '{entity_ids[i]}' 信息时出错: {result['error']}")
            else:
                valid_results.append(result)

        return valid_results
    
    async def find_related_text_units(self, entity_details: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        查找与实体相关的文本块
        
        参数:
            entity_details: 实体详细信息列表
            
        返回:
            List[Dict]: 相关文本块列表
        """
        text_units = []
        seen_docs = set()
        
        # 从实体详细信息中提取文档
        for entity in entity_details:
            documents = entity.get("documents", [])
            
            for doc in documents:
                doc_id = doc.get("id")
                if doc_id and doc_id not in seen_docs:
                    seen_docs.add(doc_id)
                    
                    # 获取文档内容
                    try:
                        doc_entities = await self.kg_manager.get_document_entities(doc_id)
                        if doc_entities and "doc_id" in doc_entities:
                            # 构建文本块数据
                            text_unit = {
                                "id": doc_id,
                                "title": doc.get("title", "未知标题"),
                                "type": doc.get("type", "text"),
                                "entities": [node["name"] for node in doc_entities.get("nodes", [])],
                                "relations": [f"{link['source']} - {link['type']} -> {link['target']}" 
                                             for link in doc_entities.get("links", [])]
                            }
                            text_units.append(text_unit)
                    except Exception as e:
                        logger.error(f"获取文档 '{doc_id}' 内容时出错: {e}")
        
        return text_units
    
    async def find_related_relations(self, entity_details: List[Dict[str, Any]], query: str = None, collection_name="default") -> List[Dict[str, Any]]:
        """
        查找与实体相关的关系，可选择使用向量检索查询关系
        
        参数:
            entity_details: 实体详细信息列表
            query: 用户查询，用于向量检索相关关系
            collection_name: 集合名称
            
        返回:
            List[Dict]: 相关关系列表
        """
        relations = []
        seen_relations = set()
        
        # 如果提供了查询，尝试使用向量检索查找相关关系
        if query:
            try:
                vector_relations = await self.vectorizer.search_relations(query, top_k=self.top_k, collection_name=collection_name)
                for rel in vector_relations:
                    relation_key = f"{rel.get('source')}-{rel.get('relation_type')}-{rel.get('target')}"
                    if relation_key not in seen_relations:
                        seen_relations.add(relation_key)
                        relations.append(rel)
            except Exception as e:
                logger.error(f"向量搜索关系时出错: {e}")
        
        # 从实体详细信息中提取关系
        for entity in entity_details:
            # 处理出向关系
            outgoing = entity.get("outgoing_relations", [])
            for rel in outgoing:
                relation_key = f"{entity['entity']['name']}-{rel['relation_type']}-{rel['name']}"
                if relation_key not in seen_relations:
                    seen_relations.add(relation_key)
                    relations.append({
                        "source": entity['entity']['name'],
                        "source_type": entity['entity']['type'],
                        "target": rel['name'],
                        "target_type": rel['type'],
                        "relation": rel['relation_type'],
                        "description": rel['description']
                    })
            
            # 处理入向关系
            incoming = entity.get("incoming_relations", [])
            for rel in incoming:
                relation_key = f"{rel['name']}-{rel['relation_type']}-{entity['entity']['name']}"
                if relation_key not in seen_relations:
                    seen_relations.add(relation_key)
                    relations.append({
                        "source": rel['name'],
                        "source_type": rel['type'],
                        "target": entity['entity']['name'],
                        "target_type": entity['entity']['type'],
                        "relation": rel['relation_type'],
                        "description": rel['description']
                    })
        
        return relations
    
    async def index_doc_entities(self, doc_id: str, doc_title: str = None) -> Tuple[int, int]:
        """
        将文档中的实体和关系索引到向量数据库
        
        参数:
            doc_id: 文档ID
            doc_title: 文档标题
            
        返回:
            Tuple[int, int]: 索引的实体数量和关系数量
        """
        if self.use_contextual:
            # 使用增强的上下文实体索引
            entity_count, relation_count, contextual_count = await self.vectorizer.index_document_entities_with_contextual(doc_id, doc_title)
            logger.info(f"已为文档 '{doc_id}' 索引 {entity_count} 个基本实体, {relation_count} 个关系, {contextual_count} 个上下文实体")
            return entity_count, relation_count
        else:
            # 使用标准实体索引
            return await self.vectorizer.index_document_entities(doc_id, doc_title)
    
    async def retrieve(self, query: str,collection_name="default") -> Dict[str, Any]:
        """
        执行局部模式检索
        
        参数:
            query: 用户查询
            
        返回:
            Dict: 包含实体、关系和文本块的检索结果
        """
        # 确保数据库和向量索引已初始化
        await self._ensure_initialized(collection_name)
        
        start_time = time.time()
        
        # 1. 优先使用语义检索找实体(向量搜索)
        semantic_entities = await self.search_entities_by_semantic(query,collection_name)
        
        # 2. 如果语义检索没有足够结果，使用关键词检索
        entities = semantic_entities
        if len(entities) < self.top_k // 2:
            # 3. 提取低级关键词作为备份或增强
            keywords = await self.extract_low_level_keywords(query)
            logger.info(f"从查询中提取的低级关键词: {keywords}")
            keyword_entities = await self.search_entities_by_keywords(keywords,collection_name)
            
            # 合并结果，去重
            seen_ids = {entity.get("id") for entity in entities if entity.get("id")}
            for entity in keyword_entities:
                entity_id = entity.get("id")
                if entity_id and entity_id not in seen_ids:
                    seen_ids.add(entity_id)
                    entities.append(entity)
                    
                    if len(entities) >= self.top_k:
                        break
        
        if not entities:
            logger.error("未找到相关实体")
            return {
                "entities": [],
                "relations": [],
                "text_units": [],
                "raw_entities": []
            }
            
        logger.success(f"找到 {len(entities)} 个相关实体，耗时 {time.time() - start_time:.4f}秒")
        
        # 4. 获取实体信息（图谱搜索）
        # todo 后续如果需要查询多跳关系可以在完善这一步
        entity_ids = [entity["id"] for entity in entities]
        kg_entities = await self.get_entity_batch(entity_ids)
        
        # 5. 查找相关文本块和关系 (可选择使用向量检索查询关系),base_rag外部实现了相关功能，暂时不使用
        # text_units = await self.find_related_text_units(entity_details)
        # relations = await self.find_related_relations(entity_details, query=query)
        
        # 6. 构建并返回结果
        retrieval_time = time.time() - start_time
        return {
            "entities": [entity["entity"] for entity in kg_entities],
            # "relations": relations,
            # "text_units": text_units,
            "contextual_entities": entities,  # 根据语义匹配到的增强实体
            # "keywords": keywords,  # 包含提取的关键词
            "retrieval_time": retrieval_time  # 检索耗时
        }
    
    async def enhance_retrieval_results(self, query: str, retrieval_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        使用局部模式增强检索结果
        
        参数:
            query: 用户查询
            retrieval_results: 向量检索的结果列表
            
        返回:
            Dict: 增强后的检索结果
        """
        # 执行局部模式检索
        local_results = await self.retrieve(query)
        
        # 合并结果
        combined_results = {
            "original_results": retrieval_results,
            "local_mode_results": local_results,
            "knowledge_graph": {
                "nodes": local_results["entities"],
                "links": local_results["relations"]
            },
            "text_units": local_results["text_units"]
        }
        
        return combined_results
    
    async def close(self):
        """关闭资源"""
        await self.kg_manager.close()
        await self.vectorizer.close() 