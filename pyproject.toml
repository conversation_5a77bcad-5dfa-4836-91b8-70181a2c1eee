[project]
name = "yq-ai-v3"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles>=23.2.1",
    "aiohttp>=3.12.0",
    "autogen-agentchat>=0.5.1",
    "autogen-core>=0.5.1",
    "autogen-ext[ollama]>=0.5.1",
    "datasets>=3.2.0",
    "fastapi>=0.103.1",
    "flagembedding==1.3.5",
    "googleapis-common-protos>=1.68.0",
    "llama-index-core>=0.12.22",
    "llama-index-embeddings-huggingface>=0.5.2",
    "llama-index-embeddings-modelscope>=0.4.0",
    "llama-index-llms-huggingface-api>=0.4.1",
    "llama-index-llms-ollama>=0.5.2",
    "llama-index-llms-openai>=0.3.25",
    "llama-index-multi-modal-llms-ollama>=0.4.0",
    "llama-index-node-parser-docling>=0.3.1",
    "llama-index-readers-file>=0.4.5",
    "llama-index-vector-stores-milvus>=0.5.0",
    "minio>=7.2.15",
    "nest-asyncio>=1.6.0",
    "numpy>=1.26.4",
    "openai>=1.69.0",
    "openpyxl>=3.1.5",
    "pandas>=2.0.0",
    "pathlib>=1.0.1",
    "plotly>=6.0.0",
    "protobuf>=5.29.3",
    "pycryptodome>=3.21.0",
    "pydantic[email]>=2.0.0",
    "pyjwt>=2.10.1",
    "pymilvus[model]>=2.5.4",
    "python-docx>=1.1.2",
    "python-dotenv>=0.21.0",
    "python-pptx>=1.0.2",
    "requests>=2.32.3",
    "sqlalchemy>=2.0.20",
    "sqlparse>=0.5.3",
    "starlette>=0.41.3",
    "torch",
    "uvicorn>=0.23.2",
    "llama-index-tools-tavily-research>=0.3.0",
    "literalai>=0.1.201",
    "redis>=5.0.1",
    "pillow>=10.1.0",
    "sqlmodel",
    "aiomysql",
    "clickhouse-connect>=0.8.17",
    "duckdb>=1.3.0",
    "pyodbc>=5.2.0",
    "pyhive>=0.7.0",
    "neo4j>=5.28.1",
    "loguru>=0.7.3",
    "oracledb>=3.1.1",
    "lazify>=0.4.0",
    "tomli>=2.2.1",
    "syncer>=2.0.3",
    "asyncer>=0.0.8",
    "mcp>=1.9.3",
    "asyncpg>=0.30.0",
    "python-socketio>=5.13.0",
    "watchfiles>=1.0.5",
    "uptrace>=1.31.0",
    "pymupdf>=1.26.0",
    "fastapi-offline>=1.7.3",
    "fastmcp>=2.8.1",
    "psycopg2-binary==2.9.10",
    "bitsandbytes>=0.46.1",
    "xlrd>=2.0.2",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

[tool.uv.sources]
torch = { path = "E:/迅雷下载/torch-2.6.0+cu124-cp311-cp311-win_amd64.whl" }
