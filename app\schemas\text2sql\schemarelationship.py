from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class Text2sqlSchemarelationshipBase(BaseModel):
    """数据库关系基础模型"""
    connection_id: str = Field(..., description="数据源连接ID", max_length=100)
    source_table_id: int = Field(..., description="源表ID")
    source_column_id: int = Field(..., description="源列ID")
    target_table_id: int = Field(..., description="目标表ID")
    target_column_id: int = Field(..., description="目标列ID")
    relationship_type: Optional[str] = Field(None, description="关系类型", max_length=50)
    description: Optional[str] = Field(None, description="关系描述")


class Text2sqlSchemarelationshipCreate(Text2sqlSchemarelationshipBase):
    """创建数据库关系请求模型"""
    pass


class Text2sqlSchemarelationshipUpdate(BaseModel):
    """更新数据库关系请求模型"""
    relationship_type: Optional[str] = Field(None, description="关系类型", max_length=50)
    description: Optional[str] = Field(None, description="关系描述")


class Text2sqlSchemarelationshipResponse(Text2sqlSchemarelationshipBase):
    """数据库关系响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="关系ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

# Properties with detailed information
class Text2sqlSchemarelationshipDetailed(Text2sqlSchemarelationshipResponse):
    source_table_name: str
    source_column_name: str
    target_table_name: str
    target_column_name: str