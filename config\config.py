import os
from pydantic import BaseModel, Field
from typing import Optional

class RAGConfig(BaseModel):
    #宇擎后台管理系统数据库地址
    yq_dbconnection: str = Field(default=os.getenv("YQ_DBCONNECTION"), description="YQ dbconnection")
    milvus_uri: str = Field(default=os.getenv("MILVUS_URI"), description="Milvus URI")
    embedding_model_dim_bge_small: int = Field(default=os.getenv("EMBEDDING_SMALL_DIM"), description="bge-small")
    embedding_model_dim_bge_large: int = Field(default=os.getenv("EMBEDDING_LARGE_DIM"), description="bge-large")
    embedding_model_dim_bce_base: int = Field(default=os.getenv("EMBEDDING_BASE_DIM"), description="bce-embedding-base_v1:latest ")
    moonshot_api_key: str = Field(default=os.getenv("MOONSHOT_API_KEY"), description="Moonshot API key")
    moonshot_api_base: str = Field(default=os.getenv("MOONSHOT_API_BASE"), description="Moonshot API BASE")
    llm_api_key: str = Field(default=os.getenv("LLM_API_KEY"), description="LLM API key")
    llm_api_base: str = Field(default=os.getenv("LLM_API_BASE"), description="LLM API base")
    llm_model_name: str = Field(default=os.getenv("LLM_MODEL_NAME"), description="LLM model name")
    sf_llm_api_key: str = Field(default=os.getenv("SF_LLM_API_KEY"), description="LLM API key")
    sf_llm_api_base: str = Field(default=os.getenv("SF_LLM_API_BASE"), description="LLM API base")
    sf_llm_model_name: str = Field(default=os.getenv("SF_LLM_MODEL_NAME"), description="LLM model name")
    pg_connection_string: str = Field(default=os.getenv("PG_CONNECTION_STRING"), description="Postgres connection string")
    mysql_connection_string: str = Field(default=os.getenv("MYSQL_CONNECTION_STRING"),
                                      description="mysql connection string")
    ocr_download_dir: str = Field(default=os.getenv("OCR_DOWNLOAD_PATH"), description="OCR download directory")
    ocr_base_url: str = Field(default=os.getenv("OCR_BASE_URL"), description="OCR base URL")
    vllm_api_key: str = Field(default=os.getenv("VLLM_API_KEY"), description="VLLM API key")
    vllm_base_url: str = Field(default=os.getenv("VLLM_BASE_URL"), description="VLLM base URL")
    vllm_model_name: str = Field(default=os.getenv("VLLM_MODEL_NAME"), description="VLLM model name")
    minio_endpoint: str = Field(default=os.getenv("MINIO_ENDPOINT"), description="Minio endpoint")
    minio_access_key: str = Field(default=os.getenv("MINIO_ACCESS_KEY"), description="Minio access key")
    minio_secret_key: str = Field(default=os.getenv("MINIO_SECRET_KEY"), description="Minio secret key")
    minio_bucket_name: str = Field(default=os.getenv("MINIO_BUCKET_NAME"), description="Minio bucket name")
    # 外部访问的minio端点，用于生成浏览器可访问的URL
    minio_external_endpoint: str = Field(default=os.getenv("MINIO_EXTERNAL_ENDPOINT", os.getenv("MINIO_ENDPOINT")), description="Minio external endpoint for public URLs")
    
    # MinIO连接池配置
    minio_pool_config: dict = Field(default={
        'min_connections': int(os.getenv("MINIO_POOL_MIN_CONNECTIONS", "2")),
        'max_connections': int(os.getenv("MINIO_POOL_MAX_CONNECTIONS", "10")),
        'connection_timeout': float(os.getenv("MINIO_POOL_CONNECTION_TIMEOUT", "300.0")),
        'health_check_interval': float(os.getenv("MINIO_POOL_HEALTH_CHECK_INTERVAL", "60.0")),
        'max_idle_time': float(os.getenv("MINIO_POOL_MAX_IDLE_TIME", "600.0"))
    }, description="MinIO连接池配置")

    db_connection_string: str = Field(default=os.getenv("DB_CONNECTION_STRING"),
                                      description="Database connection string")

    milvus_collection_doc_name: str = Field(default=os.getenv("MILVUS_COLLECTION_DOC_NAME","qh_doc"),
                                           description="Milvus collection name")
    milvus_collection_ddl_name: str = Field(default=os.getenv("MILVUS_COLLECTION_DDL_NAME", "qh_ddl"),description="Milvus collection name")
    milvus_collection_sql_name: str = Field(default=os.getenv("MILVUS_COLLECTION_SQL_NAME", "qh_sql"),description="Milvus collection name")

    mysql_host: str = Field(default=os.getenv("MYSQL_HOST"), description="MySQL host")
    mysql_user: str = Field(default=os.getenv("MYSQL_USER"), description="MySQL user")
    mysql_password: str = Field(default=os.getenv("MYSQL_PASSWORD"), description="MySQL password")
    mysql_db: str = Field(default=os.getenv("MYSQL_DB"), description="MySQL database")
    mysql_port: int = Field(default=os.getenv("MYSQL_PORT"), description="MySQL port")
    thirdapp_login_api: str = Field(default=os.getenv("THIRDAPP_LOGIN_API"), description="thirdapp login api")
    model_cache_folder: str = Field(default=os.getenv("MODEL_CACHE_FOLDER"), description="model cache folder")
    model_path_inuse: str = Field(default=os.getenv("MODEL_PATH_INUSE"), description="model path in use")
    model_path_qwen: str = Field(default=os.getenv("MODEL_PATH_QWEN"), description="model path qwen embedding")
    sparse_embedding_function: str = Field(default=os.getenv("SPARSE_EMBEDDING_FUNCTION"), description="sparse embedding function")

    tavily_api_key: str = Field(default=os.getenv("TAVILY_API_KEY"), description="tavily api key")

    # 知识图谱Neo4j配置
    kg_neo4j_uri: str = Field(default=os.getenv("KG_NEO4J_URI", "bolt://localhost:7687"),
                           description="知识图谱Neo4j URI")
    kg_neo4j_user: str = Field(default=os.getenv("KG_NEO4J_USER", "neo4j"), 
                            description="知识图谱Neo4j用户名")
    kg_neo4j_password: str = Field(default=os.getenv("KG_NEO4J_PASSWORD", "123456"),
                                description="知识图谱Neo4j密码")
    kg_neo4j_database: str = Field(default=os.getenv("KG_NEO4J_DATABASE", "neo4j"),
                               description="知识图谱数据库名称")
    kg_neo4j_max_connections: str = Field(default=int(os.getenv("KG_NEO4J_MAX_CONNECTIONS", "10")),
                                   description="知识图谱Neo4j最大连接数")
    
    # 实体检索配置
    kg_entity_top_k: int = Field(default=int(os.getenv("KG_ENTITY_TOP_K", "5")), 
                              description="实体检索返回的最大结果数")
    kg_use_contextual: bool = Field(default=os.getenv("KG_USE_CONTEXTUAL", "True").lower() in ('true', '1', 't'), 
                                 description="是否使用上下文增强的实体检索")
    # Milvus知识图谱向量化配置
    kg_milvus_uri: str = Field(default=os.getenv("KG_MILVUS_URI", os.getenv("MILVUS_URI", "http://localhost:19530")),
                               description="知识图谱Milvus URI")
    kg_milvus_collection_prefix: str = Field(default=os.getenv("KG_MILVUS_COLLECTION_PREFIX", "kg_"),
                                          description="知识图谱Milvus集合前缀")
    # 实体集合名称
    kg_entity_collection: str = Field(default=os.getenv("KG_ENTITY_COLLECTION", "entities"), 
                                   description="实体集合名称")
    kg_relation_collection: str = Field(default=os.getenv("KG_RELATION_COLLECTION", "relations"), 
                                     description="关系集合名称")
    kg_contextual_entity_collection: str = Field(default=os.getenv("KG_CONTEXTUAL_ENTITY_COLLECTION", "contextual_entities"), 
                                              description="上下文增强实体集合名称")
    # 实体批处理配置
    kg_entity_batch_size: int = Field(default=int(os.getenv("KG_ENTITY_BATCH_SIZE", "50")),
                                   description="实体批处理大小")
    
    # Redis缓存配置
    # Redis连接配置
    redis_host: str = Field(default=os.getenv("REDIS_HOST", "localhost"), description="Redis服务器地址")
    redis_port: int = Field(default=int(os.getenv("REDIS_PORT", "6379")), description="Redis服务器端口")
    redis_db: int = Field(default=int(os.getenv("REDIS_DB", "0")), description="Redis数据库编号")
    redis_password: Optional[str] = Field(default=os.getenv("REDIS_PASSWORD", "123456"), description="Redis密码")
    
    # 连接池配置
    redis_max_connections: int = Field(default=int(os.getenv("REDIS_MAX_CONNECTIONS", "20")), description="Redis最大连接数")
    redis_socket_timeout: float = Field(default=float(os.getenv("REDIS_SOCKET_TIMEOUT", "5.0")), description="Redis套接字超时时间")
    redis_socket_connect_timeout: float = Field(default=float(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "5.0")), description="Redis连接超时时间")
    redis_health_check_interval: int = Field(default=int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30")), description="Redis健康检查间隔")
    
    # 缓存时间配置（秒）
    cache_time_short: int = Field(default=int(os.getenv("CACHE_TIME_SHORT", "300")), description="短时间缓存时长")
    cache_time_medium: int = Field(default=int(os.getenv("CACHE_TIME_MEDIUM", "600")), description="中等时间缓存时长")
    cache_time_long: int = Field(default=int(os.getenv("CACHE_TIME_LONG", "1800")), description="长时间缓存时长")
    cache_time_very_long: int = Field(default=int(os.getenv("CACHE_TIME_VERY_LONG", "3600")), description="超长时间缓存时长")
    
    # 统计接口专用缓存时间配置
    statistics_overview_cache_time: int = Field(default=int(os.getenv("STATISTICS_OVERVIEW_CACHE_TIME", "300")), description="概览数据缓存时间")
    statistics_trend_cache_time: int = Field(default=int(os.getenv("STATISTICS_TREND_CACHE_TIME", "600")), description="趋势数据缓存时间")
    statistics_distribution_cache_time: int = Field(default=int(os.getenv("STATISTICS_DISTRIBUTION_CACHE_TIME", "900")), description="分布数据缓存时间")
    statistics_detailed_cache_time: int = Field(default=int(os.getenv("STATISTICS_DETAILED_CACHE_TIME", "1800")), description="详细分析缓存时间")
    statistics_chart_cache_time: int = Field(default=int(os.getenv("STATISTICS_CHART_CACHE_TIME", "300")), description="图表数据缓存时间")
    
    # 缓存键前缀配置
    cache_prefix: str = Field(default=os.getenv("CACHE_PREFIX", "yq_ai_cache:"), description="总缓存前缀")
    statistics_cache_prefix: str = Field(default=os.getenv("STATISTICS_CACHE_PREFIX", "stats_cache:"), description="统计缓存前缀")
    database_stats_prefix: str = Field(default=os.getenv("DATABASE_STATS_PREFIX", "db_stats_"), description="数据库统计前缀")
    knowledge_stats_prefix: str = Field(default=os.getenv("KNOWLEDGE_STATS_PREFIX", "know_stats_"), description="知识统计前缀")
    
    # 缓存开关配置
    enable_cache: bool = Field(default=os.getenv("ENABLE_CACHE", "true").lower() == "true", description="是否启用缓存")
    enable_statistics_cache: bool = Field(default=os.getenv("ENABLE_STATISTICS_CACHE", "true").lower() == "true", description="是否启用统计缓存")
    
    # 缓存清理配置
    auto_clear_expired_cache: bool = Field(default=os.getenv("AUTO_CLEAR_EXPIRED_CACHE", "true").lower() == "true", description="自动清理过期缓存")
    cache_cleanup_interval: int = Field(default=int(os.getenv("CACHE_CLEANUP_INTERVAL", "3600")), description="缓存清理间隔")
    
    # 性能配置
    cache_serialization_format: str = Field(default=os.getenv("CACHE_SERIALIZATION_FORMAT", "json"), description="缓存序列化格式")
    compress_cache_data: bool = Field(default=os.getenv("COMPRESS_CACHE_DATA", "false").lower() == "true", description="是否压缩缓存数据")
    
    # 模型内存配置
    model_max_memory_gpu: str = Field(default=os.getenv("MODEL_MAX_MEMORY_GPU", "1GiB"), description="模型在GPU上使用的最大内存")
    model_max_memory_cpu: str = Field(default=os.getenv("MODEL_MAX_MEMORY_CPU", "2GiB"), description="模型在CPU上使用的最大内存")
    
    # Tokenizer配置
    tokenizer_padding_side: str = Field(default=os.getenv("TOKENIZER_PADDING_SIDE", "left"), description="Tokenizer填充方向")