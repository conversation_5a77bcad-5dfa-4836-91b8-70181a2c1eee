import redis
from typing import Optional, Dict, Any
import json
# 配置日志
from utils.logger import get_logger
# 导入统一的配置管理
from config.cache_config import redis_config

# 获取logger实例
logger = get_logger()

class RedisClient:
    """
    Redis客户端单例类
    使用单例模式确保整个应用只有一个Redis连接实例
    """
    _instance: Optional["RedisClient"] = None
    _client: Optional[redis.Redis] = None
    
    def __new__(cls, *args, **kwargs):
        """
        确保只创建一个实例
        """
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, **override_params):
        """
        初始化Redis连接
        
        Args:
            **override_params: 可选的连接参数覆盖，用于特殊情况下的自定义配置
        """
        # 防止重复初始化
        if self._initialized:
            return
            
        try:
            # 获取基础连接参数
            conn_params = redis_config.get_connection_params()
            
            # 允许通过参数覆盖配置（用于特殊场景）
            if override_params:
                conn_params.update(override_params)
                logger.info(f"使用自定义Redis连接参数: {list(override_params.keys())}")
            
            self._client = redis.Redis(**conn_params)
            
            # 测试连接
            self._client.ping()
            logger.info(f"成功连接到Redis服务器: {conn_params['host']}:{conn_params['port']}")
            self._initialized = True
            
        except redis.ConnectionError as e:
            logger.warning(f"无法连接到Redis服务器: {e}, 将以无连接模式继续执行")
            self._client = None
            self._initialized = True  # 仍然标记为已初始化，避免重复尝试连接
        except Exception as e:
            logger.error(f"Redis初始化时发生未知错误: {e}")
            self._client = None
            self._initialized = True  # 仍然标记为已初始化，避免重复尝试连接
            
    @property
    def client(self) -> redis.Redis:
        """
        获取Redis客户端实例
        
        Returns:
            redis.Redis: Redis客户端实例
        """
        if self._client is None:
            raise RuntimeError("Redis客户端未初始化")
        return self._client
    
    def is_connected(self) -> bool:
        """
        检查Redis连接是否正常
        
        Returns:
            bool: 连接状态
        """
        if self._client is None:
            return False
            
        try:
            self._client.ping()
            return True
        except (redis.ConnectionError, AttributeError):
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息
        
        Returns:
            dict: 连接信息
        """
        info = {
            "connected": self.is_connected(),
            "config": {
                "host": redis_config.REDIS_HOST,
                "port": redis_config.REDIS_PORT,
                "db": redis_config.REDIS_DB,
                "has_password": redis_config.REDIS_PASSWORD is not None
            }
        }
        
        if self.is_connected():
            try:
                redis_info = self._client.info()
                info["server_info"] = {
                    "version": redis_info.get("redis_version"),
                    "mode": redis_info.get("redis_mode"),
                    "memory_used": redis_info.get("used_memory_human"),
                    "connected_clients": redis_info.get("connected_clients")
                }
            except Exception as e:
                logger.warning(f"获取Redis服务器信息失败: {e}")
                
        return info


class RedisPublisher:
    """
    Redis发布者类
    负责将消息发布到指定的频道
    """
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        """
        初始化发布者
        
        Args:
            redis_client: Redis客户端实例，如不提供则创建默认实例
        """
        self._redis_client = redis_client or RedisClient()
        
    def publish(self, channel: str, message: Any) -> int:
        """
        发布消息到指定频道
        
        Args:
            channel: 发布频道名称
            message: 要发布的消息，非字符串消息会被转换为JSON
            
        Returns:
            int: 收到消息的订阅者数量
        """
        if not self._redis_client.is_connected():
            logger.warning("Redis连接不可用，消息将不会发送")
            return 0
            
        # 将消息转换为字符串
        if not isinstance(message, str):
            try:
                message = json.dumps(message, ensure_ascii=False)
            except Exception as e:
                logger.error(f"序列化消息失败: {e}")
                return 0
                
        try:
            # 发布消息并返回收到消息的订阅者数量
            receivers = self._redis_client.client.publish(channel, message)
            logger.debug(f"消息已发布到频道 '{channel}'，{receivers} 个订阅者接收")
            return receivers
        except Exception as e:
            logger.error(f"发布消息到频道 '{channel}' 失败: {e}")
            return 0
            
    def publish_dict(self, channel: str, data: Dict[str, Any]) -> int:
        """
        发布字典数据到指定频道
        
        Args:
            channel: 发布频道名称
            data: 要发布的字典数据
            
        Returns:
            int: 收到消息的订阅者数量
        """
        return self.publish(channel, data)


# 便捷函数：获取默认Redis客户端实例
def get_redis_client() -> RedisClient:
    """
    获取默认的Redis客户端实例
    
    Returns:
        RedisClient: Redis客户端实例
    """
    return RedisClient()


# 便捷函数：获取默认Redis发布者实例
def get_redis_publisher() -> RedisPublisher:
    """
    获取默认的Redis发布者实例
    
    Returns:
        RedisPublisher: Redis发布者实例
    """
    return RedisPublisher()


# 便捷函数：检查Redis连接状态
def check_redis_health() -> bool:
    """
    检查Redis连接健康状态
    
    Returns:
        bool: 连接是否健康
    """
    try:
        client = get_redis_client()
        return client.is_connected()
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
        return False
