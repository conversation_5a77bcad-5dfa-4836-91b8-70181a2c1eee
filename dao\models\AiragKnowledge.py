from typing import Optional

from sqlalchemy import DateTime, String, text
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
import datetime

class Base(DeclarativeBase):
    pass


class AiragKnowledge(Base):
    __tablename__ = 'airag_knowledge'

    id: Mapped[str] = mapped_column(String(36, 'utf8mb4_unicode_ci'), primary_key=True)
    create_by: Mapped[Optional[str]] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='创建人')
    create_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='创建日期')
    update_by: Mapped[Optional[str]] = mapped_column(String(50, 'utf8mb4_unicode_ci'), comment='更新人')
    update_time: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, comment='更新日期')
    sys_org_code: Mapped[Optional[str]] = mapped_column(String(64, 'utf8mb4_unicode_ci'), comment='所属部门')
    tenant_id: Mapped[Optional[str]] = mapped_column(String(32, 'utf8mb4_unicode_ci'), comment='租户id')
    name: Mapped[Optional[str]] = mapped_column(String(100, 'utf8mb4_unicode_ci'), comment='知识库名称')
    descr: Mapped[Optional[str]] = mapped_column(String(500, 'utf8mb4_unicode_ci'), comment='描述')
    embed_id: Mapped[Optional[str]] = mapped_column(String(32, 'utf8mb4_unicode_ci'), comment='向量模型id')
    status: Mapped[Optional[str]] = mapped_column(String(32, 'utf8mb4_unicode_ci'), comment='状态')
    use_kg: Mapped[Optional[int]] = mapped_column(TINYINT(1), server_default=text("'0'"),comment='是否启用图谱功能 0：不启用   1:启用')
    applicable_dept: Mapped[Optional[str]] = mapped_column(String(200, 'utf8mb4_unicode_ci'), comment='适用部门')
