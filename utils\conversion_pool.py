"""
LibreOffice转换线程池管理模块

该模块提供统一的LibreOffice转换线程池，确保所有LibreOffice操作都通过同一个单线程池执行，
避免LibreOffice多实例冲突问题。
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建全局的LibreOffice转换线程池执行器（单线程确保不冲突）
_conversion_executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="libreoffice_conversion")

def get_conversion_executor() -> ThreadPoolExecutor:
    """
    获取LibreOffice转换线程池执行器
    
    Returns:
        ThreadPoolExecutor: 单线程的LibreOffice转换执行器
    """
    return _conversion_executor

async def run_conversion_task(func: Callable, *args, **kwargs) -> Any:
    """
    在转换线程池中异步执行LibreOffice转换任务
    
    Args:
        func: 要执行的转换函数
        *args: 传递给函数的位置参数
        **kwargs: 传递给函数的关键字参数
        
    Returns:
        转换函数的返回结果
    """
    try:
        logger.debug(f"提交LibreOffice转换任务到线程池: {func.__name__}")
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(_conversion_executor, func, *args, **kwargs)
        logger.debug(f"LibreOffice转换任务完成: {func.__name__}")
        return result
    except Exception as e:
        logger.error(f"LibreOffice转换任务执行失败: {func.__name__}, 错误: {e}")
        raise

def shutdown_conversion_executor():
    """
    关闭LibreOffice转换线程池执行器
    
    注意：应在应用程序关闭时调用
    """
    try:
        _conversion_executor.shutdown(wait=True)
        logger.info("LibreOffice转换线程池已安全关闭")
    except Exception as e:
        logger.error(f"关闭LibreOffice转换线程池时出错: {e}")

# 模块级函数，直接使用转换池
async def safe_convert_doc_to_docx_async(doc_file_path: str) -> str:
    """
    异步安全的DOC文件转DOCX函数，使用统一的转换线程池
    
    Args:
        doc_file_path: DOC文件路径
        
    Returns:
        DOCX文件路径，如果转换失败则返回None
    """
    from utils.rag_utils import safe_convert_doc_to_docx
    return await run_conversion_task(safe_convert_doc_to_docx, doc_file_path)

async def safe_convert_ppt_to_pptx_async(ppt_file_path: str) -> str:
    """
    异步安全的PPT文件转PPTX函数，使用统一的转换线程池
    
    Args:
        ppt_file_path: PPT文件路径
        
    Returns:
        PPTX文件路径，如果转换失败则返回None
    """
    from utils.rag_utils import safe_convert_ppt_to_pptx
    return await run_conversion_task(safe_convert_ppt_to_pptx, ppt_file_path)

async def safe_convert_office_to_pdf_async(office_file_path: str) -> str:
    """
    异步安全的Office文件转PDF函数，使用统一的转换线程池
    
    Args:
        office_file_path: Office文件路径
        
    Returns:
        PDF文件路径，如果转换失败则返回None
    """
    from utils.rag_utils import safe_convert_office_to_pdf
    return await run_conversion_task(safe_convert_office_to_pdf, office_file_path) 