/* 自定义样式 - 覆盖原有水印 */

/* 隐藏原有的水印 */
.watermark,
[class*="watermark"],
[data-testid*="watermark"],
.MuiBox-root[style*="watermark"] {
    display: none !important;
    visibility: hidden !important;
}

/* 添加自定义水印 */
body::after {
    content: "宇擎智脑科技|www.yuqingteck.com";
    position: fixed;
    bottom: 5px;
    right: 10px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.5);
    font-family: Arial, sans-serif;
    font-weight: 400;
    z-index: 9999;
    pointer-events: none;
    user-select: none;
}

/* 深色模式下的水印样式 */
@media (prefers-color-scheme: dark) {
    body::after {
        color: rgba(255, 255, 255, 0.25);
    }
}

/* 移动设备上的水印样式 */
@media (max-width: 768px) {
    body::after {
        font-size: 9px;
        bottom: 8px;
        right: 8px;
    }
}

/* 确保在所有可能的容器中都隐藏水印 */
.chainlit-app .watermark,
.chainlit-container .watermark,
.MuiContainer-root .watermark,
.MuiBox-root .watermark {
    display: none !important;
    opacity: 0 !important;
}

/* 如果水印是通过 SVG 显示的，也隐藏它 */
svg[class*="watermark"],
svg[data-testid*="watermark"] {
    display: none !important;
}

/* 隐藏可能的 "Powered by" 文本 */
[class*="powered"],
[data-testid*="powered"] {
    display: none !important;
}

 