import os

# 在导入任何其他模块之前，设置环境变量禁用OpenTelemetry追踪
os.environ["OTEL_SDK_DISABLED"] = "true"
os.environ["OTEL_TRACES_EXPORTER"] = "none"
os.environ["OTEL_METRICS_EXPORTER"] = "none"
os.environ["OTEL_LOGS_EXPORTER"] = "none"

import uvicorn
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

if __name__ == "__main__":
    logger.info("启动应用服务器")
    try:
        # 使用uvicorn启动FastAPI应用
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,  # 开发模式下自动重载
            log_level="info"
        )
    except Exception as e:
        logger.error(f"应用服务器启动失败: {e}")
    else:
        logger.info("应用服务器已启动") 