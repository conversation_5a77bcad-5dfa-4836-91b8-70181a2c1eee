import time
from typing import List, Dict, Any
from abc import ABC, abstractmethod
from datetime import datetime

from autogen_core import RoutedAgent, TopicId
from app.core.llms import model_client
from app.schemas import ResponseMessage

# 获取logger实例
from utils.logger import get_logger
from utils.encode_utils import clean_dict_for_json

logger = get_logger()

# 自定义异常类
class Text2SQLException(Exception):
    """Text2SQL服务基础异常类"""
    pass

class DatabaseConnectionException(Text2SQLException):
    """数据库连接异常"""
    pass

class SchemaRetrievalException(Text2SQLException):
    """表结构检索异常"""
    pass

class SQLGenerationException(Text2SQLException):
    """SQL生成异常"""
    pass

class SQLExecutionException(Text2SQLException):
    """SQL执行异常"""
    pass

class VisualizationException(Text2SQLException):
    """可视化异常"""
    pass

# 定义主题类型
schema_retriever_topic_type = "schema_retriever"  # 表结构检索智能体主题类型
query_analyzer_topic_type = "query_analyzer"
sql_generator_topic_type = "sql_generator"
sql_explainer_topic_type = "sql_explainer"
sql_executor_topic_type = "sql_executor"
visualization_recommender_topic_type = "visualization_recommender"
stream_output_topic_type = "stream_output"

# 定义智能体名称常量
AGENT_NAMES = {
    "schema_retriever": "表结构检索智能体",
    "query_analyzer": "查询分析智能体",
    "sql_generator": "SQL生成智能体",
    "sql_explainer": "SQL解释智能体",
    "sql_executor": "SQL执行智能体",
    "visualization_recommender": "可视化推荐智能体"
}

# 定义数据库类型（可配置）
DB_TYPE = "MySQL"  # 可选值: "MySQL", "PostgreSQL", "SQLite", "Oracle", "SQL Server"

class BaseAgent(RoutedAgent, ABC):
    """智能体基类，提供公共方法和异常处理"""
    
    def __init__(self, agent_name: str, db_type: str = None, analytics_service=None, query_id: str = None):
        """初始化基础智能体
        
        Args:
            agent_name: 智能体名称
            db_type: 数据库类型
            analytics_service: 分析服务实例
            query_id: 查询ID
        """
        super().__init__(agent_name)
        self.model_client = model_client
        self.db_type = db_type or DB_TYPE
        self.agent_display_name = self._get_display_name()
        self.analytics_service = analytics_service
        self.query_id = query_id
        self.agent_execution_id = None
        self.execution_start_time = None
        self.model_calls_count = 0
        self.model_tokens_used = 0
    
    @abstractmethod
    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        pass
    
    async def start_execution(self, input_data: Dict[str, Any] = None) -> None:
        """开始执行，记录开始状态"""
        if self.analytics_service and self.query_id:
            try:
                from dao.models.Text2sqlAnalytics import AgentStatus
                # 获取执行顺序
                execution_order = self._get_execution_order()
                
                # 清理输入数据中的不可序列化对象
                cleaned_input_data = clean_dict_for_json(input_data) if input_data else {}
                
                # 创建智能体执行记录
                self.agent_execution_id = await self.analytics_service.create_agent_execution(
                    query_id=self.query_id,
                    agent_name=self._get_agent_name(),
                    agent_display_name=self.agent_display_name,
                    execution_order=execution_order,
                    input_data=cleaned_input_data
                )
                
                self.execution_start_time = time.time()
                # 设置初始状态为RUNNING
                self._current_status = AgentStatus.RUNNING
                logger.info(f"{self.agent_display_name} 开始执行，执行ID: {self.agent_execution_id}")
                
            except Exception as e:
                logger.error(f"{self.agent_display_name} 记录执行开始失败: {str(e)}")
    
    async def finish_execution(self, status: str, output_data: Dict[str, Any] = None, error_message: str = None) -> None:
        """结束执行，记录结束状态"""
        if self.analytics_service and self.agent_execution_id:
            try:
                from dao.models.Text2sqlAnalytics import AgentStatus
                
                # 将字符串状态转换为AgentStatus枚举
                if status == "SUCCESS":
                    agent_status = AgentStatus.SUCCESS
                elif status == "FAILED":
                    agent_status = AgentStatus.FAILED
                elif status == "SKIPPED":
                    agent_status = AgentStatus.SKIPPED
                else:
                    agent_status = AgentStatus.RUNNING
                
                # 保存当前状态，供close方法使用
                self._current_status = agent_status
                
                # 清理输出数据中的不可序列化对象
                cleaned_output_data = clean_dict_for_json(output_data) if output_data else {}
                
                await self.analytics_service.update_agent_execution(
                    agent_execution_id=self.agent_execution_id,
                    status=agent_status,
                    output_data=cleaned_output_data,
                    error_message=error_message,
                    model_calls_count=self.model_calls_count,
                    model_tokens_used=self.model_tokens_used
                )
                
                logger.info(f"{self.agent_display_name} 执行完成，状态: {status}")
                
            except Exception as e:
                logger.error(f"{self.agent_display_name} 记录执行结束失败: {str(e)}")
    
    async def record_error(self, error: Exception, error_context: Dict[str, Any] = None) -> None:
        """记录错误信息"""
        if self.analytics_service and self.query_id:
            try:
                from dao.models.Text2sqlAnalytics import ErrorSeverity
                
                await self.analytics_service.record_error_log(
                    query_id=self.query_id,
                    error_type=type(error).__name__,
                    error_message=str(error),
                    agent_name=self._get_agent_name(),
                    error_stack=None,  # 可以添加traceback
                    error_context=error_context,
                    severity=ErrorSeverity.ERROR
                )
                
                logger.info(f"{self.agent_display_name} 记录错误信息成功")
                
            except Exception as e:
                logger.error(f"{self.agent_display_name} 记录错误信息失败: {str(e)}")
    
    async def record_stream_message(self, content: str, message_sequence: int, is_final: bool = False) -> None:
        """记录流式消息"""
        if self.analytics_service and self.query_id and self.agent_execution_id:
            try:
                await self.analytics_service.record_stream_message(
                    query_id=self.query_id,
                    agent_execution_id=self.agent_execution_id,
                    message_sequence=message_sequence,
                    source=self.agent_display_name,
                    content=content,
                    is_final=is_final
                )
                
            except Exception as e:
                logger.error(f"{self.agent_display_name} 记录流式消息失败: {str(e)}")
    
    def _get_agent_name(self) -> str:
        """获取智能体名称"""
        class_name = self.__class__.__name__.lower()
        if "schema" in class_name:
            return "schema_retriever"
        elif "query" in class_name:
            return "query_analyzer"
        elif "generator" in class_name:
            return "sql_generator"
        elif "explainer" in class_name:
            return "sql_explainer"
        elif "executor" in class_name:
            return "sql_executor"
        elif "visualization" in class_name:
            return "visualization_recommender"
        else:
            return class_name.replace("agent", "")
    
    def _get_execution_order(self) -> int:
        """获取执行顺序"""
        agent_name = self._get_agent_name()
        execution_orders = {
            "schema_retriever": 1,
            "query_analyzer": 2,
            "sql_generator": 3,
            "sql_explainer": 4,
            "sql_executor": 5,
            "visualization_recommender": 6
        }
        return execution_orders.get(agent_name, 0)
    
    async def increment_model_usage(self, calls_count: int = 1, tokens_used: int = 0) -> None:
        """增加模型使用统计"""
        self.model_calls_count += calls_count
        self.model_tokens_used += tokens_used

    async def close(self) -> None:
        """关闭智能体，更新执行状态并持久化
        
        当运行时停止时会调用此方法，用于清理资源和更新执行状态
        """
        try:
            logger.info(f"{self.agent_display_name} 开始关闭...")
            
            # 如果智能体有执行记录且尚未完成，则更新状态
            if (self.analytics_service and 
                self.agent_execution_id and 
                self.query_id):
                
                # 检查当前执行状态，只有正在运行中的智能体才需要更新为被中断
                try:
                    from dao.models.Text2sqlAnalytics import AgentStatus
                    
                    # 计算执行时长
                    execution_duration = None
                    if self.execution_start_time:
                        execution_duration = int((time.time() - self.execution_start_time) * 1000)
                    
                    # 获取当前状态，判断是否需要更新
                    # 这里我们假设只有RUNNING状态的智能体需要被标记为中断
                    # 已经成功或失败的智能体不需要再次更新状态
                    current_status = getattr(self, '_current_status', AgentStatus.RUNNING)
                    
                    if current_status == AgentStatus.RUNNING:
                        # 准备输出数据，清理不可序列化的对象
                        output_data = clean_dict_for_json({
                            "close_reason": "runtime_shutdown",
                            "execution_duration_ms": execution_duration,
                            "final_model_calls": self.model_calls_count,
                            "final_tokens_used": self.model_tokens_used,
                            "interrupted": True
                        })
                        
                        # 更新智能体执行状态为已中断
                        await self.analytics_service.update_agent_execution(
                            agent_execution_id=self.agent_execution_id,
                            status=AgentStatus.FAILED,  # 使用FAILED状态表示被中断
                            output_data=output_data,
                            error_message="智能体因运行时关闭而中断执行",
                            model_calls_count=self.model_calls_count,
                            model_tokens_used=self.model_tokens_used
                        )
                        
                        logger.info(f"{self.agent_display_name} 执行状态已更新为被中断 (FAILED)")
                        
                        # 更新整个查询的执行状态为FAILED
                        # 使用执行顺序来避免多个智能体同时更新查询状态的竞态条件（并未实现，因为当前的智能体执行顺序是固定的）
                        # 只有第一个被中断的智能体（执行顺序最小的）才更新查询状态
                        try:
                            from dao.models.Text2sqlAnalytics import ExecutionStatus
                            
                            # 构造错误消息，包含更多上下文信息
                            error_context = {
                                "interrupted_agent": self.agent_display_name,
                                "agent_execution_order": self._get_execution_order(),
                                "interruption_time": datetime.utcnow().isoformat(),
                                "reason": "runtime_shutdown"
                            }
                            
                            await self.analytics_service.update_execution_status(
                                query_id=self.query_id,
                                status=ExecutionStatus.FAILED,
                                error_message=f"执行被中断：{self.agent_display_name} 在运行时关闭时被中断"
                            )
                            
                            # 记录错误日志，提供更详细的中断信息
                            from dao.models.Text2sqlAnalytics import ErrorSeverity
                            await self.analytics_service.record_error_log(
                                query_id=self.query_id,
                                error_type="ExecutionInterrupted",
                                error_message=f"智能体 {self.agent_display_name} 执行被中断",
                                agent_name=self._get_agent_name(),
                                error_context=error_context,
                                severity=ErrorSeverity.ERROR
                            )
                            
                            logger.info(f"查询 {self.query_id} 执行状态已更新为FAILED (由于{self.agent_display_name}被中断)")
                        except Exception as e:
                            logger.error(f"更新查询执行状态失败: {str(e)}")
                        
                        # 记录关闭事件的性能指标
                        if execution_duration:
                            from dao.models.Text2sqlAnalytics import MetricCategory
                            await self.analytics_service.record_performance_metric(
                                query_id=self.query_id,
                                metric_name=f"{self._get_agent_name()}_interrupted_duration",
                                metric_value=float(execution_duration),
                                metric_unit="ms",
                                metric_category=MetricCategory.PERFORMANCE
                            )
                            
                            # 记录查询中断的性能指标
                            await self.analytics_service.record_performance_metric(
                                query_id=self.query_id,
                                metric_name="query_interrupted_by_agent",
                                metric_value=float(self._get_execution_order()),
                                metric_unit="order",
                                metric_category=MetricCategory.PERFORMANCE
                            )
                    else:
                        logger.info(f"{self.agent_display_name} 当前状态为 {current_status}，无需更新")
                    
                except Exception as e:
                    logger.error(f"{self.agent_display_name} 更新关闭状态失败: {str(e)}")
            else:
                logger.info(f"{self.agent_display_name} 没有执行记录或analytics服务不可用，跳过状态更新")
            
            # 清理智能体资源
            self.agent_execution_id = None
            self.execution_start_time = None
            
            logger.info(f"{self.agent_display_name} 关闭完成")
            
        except Exception as e:
            logger.error(f"{self.agent_display_name} 关闭过程中出错: {str(e)}")
            # 即使出错也要继续，确保基类的close方法被调用
        finally:
            # 调用父类的close方法（如果存在）
            try:
                # RoutedAgent可能有自己的清理逻辑
                if hasattr(super(), 'close'):
                    await super().close()
            except Exception as e:
                logger.error(f"{self.agent_display_name} 调用父类close方法失败: {str(e)}")

    async def send_stream_message(self, content: str, is_final: bool = False, result: Dict[str, Any] = None, 
                                content_format: str = "markdown", message_sequence: int = None) -> None:
        """发送流式消息的通用方法
        
        Args:
            content: 消息内容
            is_final: 是否为最终消息
            result: 可选的结果数据
            content_format: 内容格式，默认为 markdown
            message_sequence: 消息序号
        """
        try:
            # 记录流式消息到数据库
            if message_sequence is not None:
                await self.record_stream_message(content, message_sequence, is_final)
            
            # 如果指定为 markdown 格式，对内容进行格式化
            formatted_content = self._format_as_markdown(content) if content_format == "markdown" else content
            
            await self.publish_message(
                ResponseMessage(
                    source=self.agent_display_name,
                    content=formatted_content,
                    is_final=is_final,
                    result=result,
                    content_format=content_format
                ),
                topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
            )
        except Exception as e:
            logger.error(f"{self.agent_display_name}发送流式消息失败: {str(e)}")
    
    def _format_as_markdown(self, content: str) -> str:
        """将内容格式化为 markdown 格式
        
        Args:
            content: 原始内容
            
        Returns:
            str: markdown 格式化后的内容
        """
        # 如果内容已经包含 markdown 标记，直接返回
        if any(marker in content for marker in ['**', '*', '`', '#', '>', '-', '1.', '|']):
            return content
        
        # 基础格式化：为智能体名称添加标题格式
        if self.agent_display_name in content:
            content = content.replace(self.agent_display_name, f"## {self.agent_display_name}")
        
        # 检测并格式化常见的结构化内容
        lines = content.split('\n')
        formatted_lines = []
        in_list = False
        
        for i, line in enumerate(lines):
            original_line = line
            line = line.strip()
            
            if not line:
                formatted_lines.append('')
                in_list = False
                continue
            
            # 检测是否是列表项（简单的单词或短语，没有标点符号结尾）
            is_simple_list_item = (
                len(line.split()) <= 3 and  # 短词组
                not line.endswith(('。', '！', '？', '.', '!', '?', ':', '：')) and  # 不以标点结尾
                not line.startswith(('步骤', '第', '错误', '异常', '失败', '成功', '完成')) and  # 不是特殊格式
                not any(keyword in line for keyword in ['SQL', '数据库', '表结构', '可视化', '图表']) and  # 不包含特殊关键词
                not line.startswith('#')  # 不是标题
            )
            
            # 检测是否是表名列表（如 album, artist, customer 等）
            is_table_name = (
                len(line.split()) == 1 and  # 单个词
                line.islower() and  # 小写
                line.isalpha()  # 只包含字母
            )
            
            # 如果检测到连续的简单列表项或表名，将其格式化为无序列表
            if is_simple_list_item or is_table_name:
                if not in_list:
                    # 检查前一行是否是列表的标题
                    if i > 0 and formatted_lines and formatted_lines[-1].strip():
                        prev_line = formatted_lines[-1].strip()
                        if not prev_line.startswith(('#', '-', '*', '>', '|')) and prev_line.endswith((':', '：', '表', '列表')):
                            # 将前一行格式化为标题
                            formatted_lines[-1] = f"### {prev_line}"
                    in_list = True
                formatted_lines.append(f"- {line}")
                continue
            else:
                in_list = False
            
            # 格式化步骤或编号列表项
            if line.startswith(('步骤', '第', '1.', '2.', '3.', '4.', '5.')):
                if not line.startswith('#'):
                    formatted_lines.append(f"### {line}")
                else:
                    formatted_lines.append(line)
            # 格式化错误信息
            elif '错误' in line or '异常' in line or '失败' in line:
                formatted_lines.append(f"⚠️ **{line}**")
            # 格式化成功信息
            elif '成功' in line or '完成' in line:
                formatted_lines.append(f"✅ **{line}**")
            # 格式化 SQL 相关内容
            elif 'SQL' in line.upper() and ('生成' in line or '执行' in line or '查询' in line):
                formatted_lines.append(f"🔍 **{line}**")
            # 格式化数据库相关内容
            elif '数据库' in line or '表结构' in line:
                formatted_lines.append(f"🗄️ **{line}**")
            # 格式化可视化相关内容
            elif '可视化' in line or '图表' in line:
                formatted_lines.append(f"📊 **{line}**")
            else:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    async def send_error_message(self, error: Exception, context: str = "") -> None:
        """发送错误消息的通用方法
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        error_msg = f"{context}发生错误: {str(error)}" if context else f"发生错误: {str(error)}"
        logger.error(f"{self.agent_display_name}: {error_msg}")
        # 使用 markdown 格式发送错误消息
        await self.send_stream_message(error_msg, is_final=True, content_format="markdown")
    
    async def handle_exception(self, e: Exception, context: str = "") -> None:
        """统一异常处理方法
        
        Args:
            e: 异常对象
            context: 异常上下文
        """
        import traceback
        logger.error(f"{self.agent_display_name}异常: {context} - {str(e)}")
        logger.error(traceback.format_exc())
        await self.send_error_message(e, context)
    
    def validate_message(self, message: Any, required_fields: List[str]) -> bool:
        """验证消息是否包含必需字段
        
        Args:
            message: 消息对象
            required_fields: 必需字段列表
            
        Returns:
            bool: 验证是否通过
        """
        for field in required_fields:
            if not hasattr(message, field) or getattr(message, field) is None:
                logger.warning(f"{self.agent_display_name}: 消息缺少必需字段 {field}")
                return False
        return True
    
    def format_sql_as_markdown(self, sql: str, title: str = "生成的SQL语句") -> str:
        """将SQL语句格式化为markdown代码块
        
        Args:
            sql: SQL语句
            title: 标题
            
        Returns:
            str: markdown格式的SQL代码块
        """
        return f"### {title}\n\n```sql\n{sql}\n```"
    
    def format_results_as_markdown(self, results: List[Dict[str, Any]], title: str = "查询结果") -> str:
        """将查询结果格式化为markdown表格
        
        Args:
            results: 查询结果列表
            title: 标题
            
        Returns:
            str: markdown格式的表格
        """
        if not results:
            return f"### {title}\n\n*暂无数据*"
        
        # 获取表头
        headers = list(results[0].keys())
        
        # 构建markdown表格
        markdown_table = f"### {title}\n\n"
        markdown_table += "| " + " | ".join(headers) + " |\n"
        markdown_table += "| " + " | ".join(["---"] * len(headers)) + " |\n"
        
        # 添加数据行（限制显示前10行）
        for i, row in enumerate(results[:10]):
            values = [str(row.get(header, "")) for header in headers]
            markdown_table += "| " + " | ".join(values) + " |\n"
        
        if len(results) > 10:
            markdown_table += f"\n*显示前10行，共{len(results)}行数据*"
        
        return markdown_table
    
    def format_explanation_as_markdown(self, explanation: str, title: str = "SQL解释") -> str:
        """将解释内容格式化为markdown
        
        Args:
            explanation: 解释内容
            title: 标题
            
        Returns:
            str: markdown格式的解释
        """
        return f"### {title}\n\n{explanation}"
    
    def format_progress_as_markdown(self, step: str, description: str = "") -> str:
        """将进度信息格式化为markdown
        
        Args:
            step: 步骤名称
            description: 步骤描述
            
        Returns:
            str: markdown格式的进度信息
        """
        if description:
            return f"🔄 **{step}**\n\n{description}"
        else:
            return f"🔄 **{step}**"
    
    def format_table_list_as_markdown(self, tables: List[str], title: str = "相关表") -> str:
        """将表名列表格式化为markdown列表
        
        Args:
            tables: 表名列表
            title: 标题
            
        Returns:
            str: markdown格式的表名列表
        """
        if not tables:
            return f"### {title}\n\n*暂无相关表*"
        
        markdown_list = f"### {title}\n\n"
        for table in tables:
            markdown_list += f"- `{table}`\n"
        
        return markdown_list
    
    def format_schema_info_as_markdown(self, schema_info: str, title: str = "表结构信息") -> str:
        """将表结构信息格式化为markdown
        
        Args:
            schema_info: 表结构信息字符串
            title: 标题
            
        Returns:
            str: markdown格式的表结构信息
        """
        if not schema_info:
            return f"### {title}\n\n*暂无表结构信息*"
        
        # 检测是否包含表名列表
        lines = schema_info.strip().split('\n')
        
        # 如果是简单的表名列表
        if all(line.strip() and len(line.strip().split()) == 1 and line.strip().islower() and line.strip().isalpha() for line in lines if line.strip()):
            table_names = [line.strip() for line in lines if line.strip()]
            return self.format_table_list_as_markdown(table_names, title)
        
        # 否则使用通用格式化
        return f"### {title}\n\n```\n{schema_info}\n```" 