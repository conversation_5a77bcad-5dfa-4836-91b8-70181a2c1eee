"""
RAG相关的请求实体类

按功能分类:
- document: 文档和集合管理相关实体
- query: RAG查询相关实体  
- session: 会话管理相关实体
"""

# 文档和集合管理相关实体
from .document import (
    MilvusEntity,
    UploadFileByDocIdEntity,
    EmbeddingStatusEntity,
    CreateCollectionEntity
)

# RAG查询相关实体
from .query import (
    RAGStreamQueryEntity,
    KnowledgeGraphChatEntity
)

# 会话管理相关实体
from .session import (
    RAGSessionInfo,
    ChatHistoryQueryEntity,
    UserSessionsQueryEntity,
    SessionDeleteEntity,
    HistoryCleanupEntity
)

__all__ = [
    # 文档和集合管理
    "MilvusEntity",
    "UploadFileByDocIdEntity", 
    "EmbeddingStatusEntity",
    "CreateCollectionEntity",
    
    # RAG查询
    "RAGStreamQueryEntity",
    "KnowledgeGraphChatEntity",
    
    # 会话管理
    "RAGSessionInfo",
    "ChatHistoryQueryEntity",
    "UserSessionsQueryEntity", 
    "SessionDeleteEntity",
    "HistoryCleanupEntity"
] 