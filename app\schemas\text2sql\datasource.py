from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class Text2sqlDatasourceBase(BaseModel):
    """数据源基础模型"""
    code: str = Field(..., description="数据源编码", max_length=100)
    name: Optional[str] = Field(None, description="数据源名称", max_length=100)
    remark: Optional[str] = Field(None, description="备注", max_length=200)
    db_type: Optional[str] = Field(None, description="数据库类型", max_length=10)
    db_driver: Optional[str] = Field(None, description="驱动类", max_length=100)
    db_url: Optional[str] = Field(None, description="数据源地址", max_length=500)
    db_name: Optional[str] = Field(None, description="数据库名称", max_length=100)
    db_username: Optional[str] = Field(None, description="用户名", max_length=100)
    db_password: Optional[str] = Field(None, description="密码", max_length=100)
    db_host: Optional[str] = Field(None, description="主机地址", max_length=100)
    db_port: Optional[int] = Field(None, description="端口号")
    tenant_id: Optional[int] = Field(0, description="租户ID")


class Text2sqlDatasourceCreate(Text2sqlDatasourceBase):
    """创建数据源请求模型"""
    create_by: Optional[str] = Field(None, description="创建人", max_length=50)
    sys_org_code: Optional[str] = Field(None, description="所属部门", max_length=64)


class Text2sqlDatasourceUpdate(BaseModel):
    """更新数据源请求模型"""
    name: Optional[str] = Field(None, description="数据源名称", max_length=100)
    remark: Optional[str] = Field(None, description="备注", max_length=200)
    db_type: Optional[str] = Field(None, description="数据库类型", max_length=10)
    db_driver: Optional[str] = Field(None, description="驱动类", max_length=100)
    db_url: Optional[str] = Field(None, description="数据源地址", max_length=500)
    db_name: Optional[str] = Field(None, description="数据库名称", max_length=100)
    db_username: Optional[str] = Field(None, description="用户名", max_length=100)
    db_password: Optional[str] = Field(None, description="密码", max_length=100)
    db_host: Optional[str] = Field(None, description="主机地址", max_length=100)
    db_port: Optional[int] = Field(None, description="端口号")
    update_by: Optional[str] = Field(None, description="更新人", max_length=50)


class Text2sqlDatasourceResponse(Text2sqlDatasourceBase):
    """数据源响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="数据源ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    sys_org_code: Optional[str] = Field(None, description="所属部门")


class Text2sqlDatasourceFilter(BaseModel):
    """数据源查询过滤器"""
    code: Optional[str] = Field(None, description="数据源编码")
    name: Optional[str] = Field(None, description="数据源名称")
    db_type: Optional[str] = Field(None, description="数据库类型")
    tenant_id: Optional[int] = Field(None, description="租户ID") 