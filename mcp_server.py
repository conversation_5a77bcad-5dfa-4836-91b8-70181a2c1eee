import os
import json
import asyncio
import sys
import argparse
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
from pathlib import Path

# 在导入任何其他模块之前，设置环境变量禁用OpenTelemetry追踪
os.environ["OTEL_SDK_DISABLED"] = "true"
os.environ["OTEL_TRACES_EXPORTER"] = "none"
os.environ["OTEL_METRICS_EXPORTER"] = "none"
os.environ["OTEL_LOGS_EXPORTER"] = "none"

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastmcp import FastMCP, Context
from pydantic import BaseModel

from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

def safe_log_info(message: str, use_in_stdio: bool = False):
    """安全的日志输出函数，在stdio模式下可选择是否输出"""
    # 移除或替换emoji字符以避免编码问题
    safe_message = message
    replacements = {
        '✅': '[OK]',
        '🔗': '[MOUNT]',
        '🎉': '[DONE]',
        '📊': '[STATS]',
        '❌': '[ERROR]',
        '⚠️': '[WARN]'
    }
    
    for emoji, replacement in replacements.items():
        safe_message = safe_message.replace(emoji, replacement)
    
    # 在stdio模式下，只有显式允许的消息才输出
    if use_in_stdio:
        logger.info(safe_message)
    else:
        # 检查当前是否在stdio模式（通过环境变量或其他方式）
        # 这里我们暂时总是输出，让调用者决定
        logger.info(safe_message)

def safe_print(text: str):
    """安全打印函数，处理Windows控制台Unicode问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，移除Emoji字符
        import re
        # 移除所有Emoji和特殊Unicode字符
        cleaned_text = re.sub(r'[^\u0000-\u007F\u4e00-\u9fff]', '', text)
        # 替换常见的Emoji为文本
        replacements = {
            '🚀': '[启动]',
            '📡': '[传输]', 
            '🌐': '[网络]',
            '🔗': '[链接]',
            '💚': '[健康]',
            'ℹ️': '[信息]',
            '📝': '[STDIO]',
            '📊': '[日志]',
            '🔍': '[详细]',
            '🔄': '[初始化]',
            '✅': '[成功]',
            '🛠️': '[工具]',
            '📚': '[资源]',
            '💭': '[提示]',
            '🎯': '[模块]',
            '🗃️': '[数据库]',
            '🕸️': '[知识图谱]',
            '📈': '[统计]',
            '💾': '[缓存]',
            '🎓': '[训练]',
            '💡': '[提示]',
            '🛑': '[停止]',
            '❌': '[错误]',
            '⚠️': '[警告]'
        }
        
        for emoji, replacement in replacements.items():
            cleaned_text = cleaned_text.replace(emoji, replacement)
        
        print(cleaned_text)

# 支持的传输协议枚举
class TransportType(str, Enum):
    STDIO = "stdio"
    STREAMABLE_HTTP = "streamable-http"
    SSE = "sse"

# 服务器配置
class ServerConfig(BaseModel):
    transport: TransportType = TransportType.STDIO
    host: str = "127.0.0.1"
    port: int = 8001
    path: str = "/mcp"
    log_level: str = "info"
    verbose: bool = False

# 创建主MCP服务器实例
mcp = FastMCP("YQ AI V3 MCP Server")

# ============================================================================
# 导入子服务器的工具和资源
# ============================================================================

async def import_all_server_components(silent_mode: bool = False):
    """使用FastMCP标准方式导入所有子服务器的工具和资源"""
    try:
        # 导入RAG服务器
        from app.api.api_v1.servers.rag_server import rag_server
        if not silent_mode:
            safe_log_info("✅ RAG服务器模块已加载")
        
        # 导入Text2SQL服务器
        from app.api.api_v1.servers.text2sql_server import text2sql_server
        if not silent_mode:
            safe_log_info("✅ Text2SQL服务器模块已加载")
        
        # 导入知识图谱服务器
        from app.api.api_v1.servers.knowledge_graph_server import kg_server
        if not silent_mode:
            safe_log_info("✅ 知识图谱服务器模块已加载")
        
        # 导入统计服务器
        from app.api.api_v1.servers.statistics_server import stats_server
        if not silent_mode:
            safe_log_info("✅ 统计服务器模块已加载")
        
        # 导入缓存服务器
        from app.api.api_v1.servers.cache_server import cache_server
        if not silent_mode:
            safe_log_info("✅ 缓存服务器模块已加载")
        
        # 导入训练服务器
        from app.api.api_v1.servers.text2sql_train_server import train_server
        if not silent_mode:
            safe_log_info("✅ 训练服务器模块已加载")
        
        # 导入分析服务器
        from app.api.api_v1.servers.text2sql_analytics_server import analytics_server
        if not silent_mode:
            safe_log_info("✅ 分析服务器模块已加载")
        
        # 使用FastMCP的标准import_server方法导入子服务器
        # 这是静态组合，一次性复制组件并加前缀
        sub_servers_config = [
            ("rag", rag_server, "RAG服务器"),
            ("text2sql", text2sql_server, "Text2SQL生成服务器"),
            ("kg", kg_server, "知识图谱服务器"),
            ("stats", stats_server, "统计分析服务器"),
            ("cache", cache_server, "缓存管理服务器"),
            ("train", train_server, "训练服务器"),
            ("analytics", analytics_server, "分析服务器")
        ]
        
        tools_count = 0
        resources_count = 0
        prompts_count = 0
        
        for prefix, server, description in sub_servers_config:
            try:
                # 使用FastMCP的import_server方法
                await mcp.import_server(prefix, server)
                if not silent_mode:
                    safe_log_info(f"✅ 已导入{description} (前缀: {prefix})")
                
                # 统计导入的组件数量（这里只是估算）
                server_tools = await server.get_tools() if hasattr(server, 'get_tools') else {}
                server_resources = await server.get_resources() if hasattr(server, 'get_resources') else {}
                server_prompts = await server.get_prompts() if hasattr(server, 'get_prompts') else {}
                
                tools_count += len(server_tools)
                resources_count += len(server_resources)
                prompts_count += len(server_prompts)
                
            except Exception as e:
                if not silent_mode:
                    logger.warning(f"导入{description}时出错: {e}")
                # 如果import_server失败，尝试mount方法作为备选
                try:
                    mcp.mount(prefix, server)
                    if not silent_mode:
                        safe_log_info(f"🔗 已挂载{description} (前缀: {prefix}) - 使用动态组合")
                except Exception as mount_error:
                    if not silent_mode:
                        logger.error(f"挂载{description}失败: {mount_error}")
        
        # 获取最终的工具和资源统计
        try:
            final_tools = await mcp.get_tools()
            final_resources = await mcp.get_resources() 
            final_prompts = await mcp.get_prompts()
            
            if not silent_mode:
                safe_log_info("🎉 子服务器组件导入完成")
                safe_log_info(f"📊 最终统计: {len(final_tools)} 个工具, {len(final_resources)} 个资源, {len(final_prompts)} 个提示模板")
            
        except Exception as stats_error:
            if not silent_mode:
                logger.warning(f"获取最终统计时出错: {stats_error}")
                safe_log_info("🎉 子服务器组件导入完成")
        
    except Exception as e:
        if not silent_mode:
            logger.error(f"❌ 导入子服务器组件失败: {e}")
        raise

# ============================================================================
# 自定义路由（用于HTTP传输时的健康检查等）
# ============================================================================

try:
    from starlette.requests import Request
    from starlette.responses import JSONResponse, PlainTextResponse
    
    @mcp.custom_route("/health", methods=["GET"])
    async def health_check(request: Request) -> JSONResponse:
        """健康检查端点"""
        return JSONResponse({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "server": "YQ AI V3 MCP Server",
            "transport": "HTTP",
            "version": "1.0.0",
            "tools_count": len(getattr(mcp, '_tools', getattr(mcp, 'tools', {}))),
            "resources_count": len(getattr(mcp, '_resources', getattr(mcp, 'resources', {}))),
            "prompts_count": len(getattr(mcp, '_prompts', getattr(mcp, 'prompts', {})))
        })
    
    @mcp.custom_route("/info", methods=["GET"])
    async def server_info(request: Request) -> JSONResponse:
        """服务器信息端点"""
        return JSONResponse({
            "name": "YQ AI V3 MCP Server",
            "description": "为YQ AI V3系统提供MCP协议支持（模块化架构）",
            "version": "1.0.0",
            "capabilities": [
                "RAG查询和管理",
                "Text2SQL生成和训练",
                "数据库Schema管理",
                "知识图谱操作",
                "统计分析",
                "缓存管理"
            ],
            "architecture": "modular",
            "modules": [
                "rag_server",
                "text2sql_server", 
                "knowledge_graph_server",
                "statistics_server",
                "cache_server",
                "train_server",
                "analytics_server"
            ],
            "transport_support": ["stdio", "streamable-http", "sse"],
            "statistics": {
                "tools": len(getattr(mcp, '_tools', getattr(mcp, 'tools', {}))),
                "resources": len(getattr(mcp, '_resources', getattr(mcp, 'resources', {}))),
                "prompts": len(getattr(mcp, '_prompts', getattr(mcp, 'prompts', {})))
            },
            "last_updated": datetime.now().isoformat()
        })
    
    @mcp.custom_route("/", methods=["GET"])
    async def root(request: Request) -> PlainTextResponse:
        """根路径响应"""
        return PlainTextResponse("YQ AI V3 MCP Server is running! Visit /health for health check.")
        
except ImportError:
    logger.warning("Starlette不可用，HTTP路由功能将被禁用")

# ============================================================================
# 主服务器资源定义
# ============================================================================

@mcp.resource("config://version")
async def get_version() -> str:
    """获取系统版本信息"""
    return "YQ AI V3 - Version 1.0.0"

@mcp.resource("config://server_info")
async def get_server_info() -> Dict[str, Any]:
    """获取服务器信息"""
    return {
        "name": "YQ AI V3 MCP Server",
        "description": "为YQ AI V3系统提供MCP协议支持（模块化架构）",
        "version": "1.0.0",
        "capabilities": [
            "RAG查询和管理",
            "Text2SQL生成和训练",
            "数据库Schema管理",
            "知识图谱操作",
            "统计分析",
            "缓存管理"
        ],
        "architecture": "modular",
        "modules": [
            "rag_server",
            "text2sql_server", 
            "knowledge_graph_server",
            "statistics_server",
            "cache_server",
            "train_server",
            "analytics_server"
        ],
        "transport_support": [t.value for t in TransportType],
        "statistics": {
            "tools": len(getattr(mcp, '_tools', getattr(mcp, 'tools', {}))),
            "resources": len(getattr(mcp, '_resources', getattr(mcp, 'resources', {}))),
            "prompts": len(getattr(mcp, '_prompts', getattr(mcp, 'prompts', {})))
        },
        "last_updated": datetime.now().isoformat()
    }

@mcp.resource("stats://system_health")
async def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "rag": "operational",
            "text2sql": "operational", 
            "database": "operational",
            "cache": "operational",
            "knowledge_graph": "operational"
        },
        "mcp_server": "operational",
        "components": {
            "tools": len(getattr(mcp, '_tools', getattr(mcp, 'tools', {}))),
            "resources": len(getattr(mcp, '_resources', getattr(mcp, 'resources', {}))),
            "prompts": len(getattr(mcp, '_prompts', getattr(mcp, 'prompts', {})))
        }
    }

# ============================================================================
# 主服务器提示模板
# ============================================================================

@mcp.prompt
def help_prompt() -> str:
    """显示系统帮助信息"""
    tools_list = []
    tools_dict = getattr(mcp, '_tools', getattr(mcp, 'tools', {}))
    for name, tool in tools_dict.items():
        doc = tool.__doc__ if tool.__doc__ else "工具描述"
        desc = doc.split('\n')[1].strip() if '\n' in doc else doc.strip()
        tools_list.append(f"- `{name}`: {desc}")
    tools_str = "\n".join(tools_list)
    
    return f"""
# YQ AI V3 MCP Server 使用指南 (FastMCP多传输支持)

本MCP服务器采用模块化架构，基于FastMCP框架，支持多种传输协议：

## 🚀 支持的传输协议

### 1. STDIO (默认)
- **用途**: 本地工具和命令行集成
- **适用场景**: Claude Desktop集成、本地脚本
- **启动**: `python mcp_server.py` 或 `fastmcp run mcp_server.py`

### 2. Streamable HTTP (推荐)
- **用途**: Web部署、微服务、网络访问
- **适用场景**: Web应用集成、API服务
- **启动**: `python mcp_server.py --transport streamable-http --port 8001`
- **访问**: `http://localhost:8001/mcp`

### 3. SSE (已弃用，但仍支持)
- **用途**: 基于SSE的实时通信
- **适用场景**: 现有SSE部署
- **启动**: `python mcp_server.py --transport sse --port 8001`

## 🏗️ 架构说明
系统由以下模块组成：
- RAG Server: 检索增强生成服务
- Text2SQL Server: 自然语言转SQL服务  
- Knowledge Graph Server: 知识图谱服务
- Statistics Server: 统计分析服务
- Cache Server: 缓存管理服务
- Train Server: 训练服务
- Analytics Server: 分析服务

## 📚 可用工具 ({len(getattr(mcp, '_tools', getattr(mcp, 'tools', {})))} 个)

{tools_str}

## 🔧 启动方式

### 使用Python直接启动
```bash
# STDIO模式（默认）
python mcp_server.py

# Streamable HTTP模式
python mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001

# SSE模式
python mcp_server.py --transport sse --host 0.0.0.0 --port 8001
```

### 使用FastMCP CLI
```bash
# 使用CLI运行（推荐）
fastmcp run mcp_server.py --transport streamable-http --port 8001

# 开发模式（带MCP Inspector）
fastmcp dev mcp_server.py

# 传递额外参数
fastmcp run mcp_server.py --transport streamable-http -- --verbose
```

## 🌐 HTTP端点（Streamable HTTP模式）
- `/`: 根路径
- `/health`: 健康检查
- `/info`: 服务器信息
- `/mcp`: MCP协议端点

## 💡 使用建议
1. 本地开发使用STDIO模式
2. Web部署推荐使用Streamable HTTP模式
3. 避免使用SSE模式（已弃用）
4. 使用FastMCP CLI进行灵活部署

请根据需要选择合适的传输协议！
"""

@mcp.prompt
def deployment_guide_prompt() -> str:
    """部署指南"""
    return """
# YQ AI V3 MCP Server 部署指南

## 🚀 快速部署

### 1. 本地开发
```bash
# 默认STDIO模式
python mcp_server.py

# 或使用FastMCP CLI
fastmcp dev mcp_server.py
```

### 2. Web服务部署
```bash
# 推荐：Streamable HTTP
python mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001

# 或使用CLI
fastmcp run mcp_server.py --transport streamable-http --port 8001
```

### 3. 生产环境
```bash
# 后台运行
nohup python mcp_server.py --transport streamable-http --host 0.0.0.0 --port 8001 > mcp.log 2>&1 &

# 或使用systemd、supervisor等进程管理工具
```

## 📡 客户端连接

### Python客户端
```python
from mcp import Client
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client

# STDIO连接
async with stdio_client(
    command="python",
    args=["mcp_server.py"]
) as client:
    result = await client.call_tool("query_rag", {"query": "什么是AI?"})

# HTTP连接
async with sse_client("http://localhost:8001/mcp") as client:
    result = await client.call_tool("query_rag", {"query": "什么是AI?"})
```

### Claude Desktop配置
```json
{
  "mcpServers": {
    "yq-ai-v3": {
      "command": "python",
      "args": ["path/to/mcp_server.py"]
    }
  }
}
```

## 🔧 高级配置
- 支持自定义主机和端口
- 支持日志级别配置
- 支持详细模式
- 支持自定义路径

## 📊 监控和调试
- 使用 `/health` 端点进行健康检查
- 使用 `/info` 端点获取服务器信息
- 启用verbose模式查看详细日志
"""

# ============================================================================
# 服务器启动函数
# ============================================================================

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="YQ AI V3 MCP Server - FastMCP多传输支持（根目录启动版本）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
传输协议说明：
  stdio           - 标准输入输出（默认，用于本地工具）
  streamable-http - HTTP流式传输（推荐，用于Web部署）
  sse             - Server-Sent Events（已弃用）

示例用法：
  %(prog)s
  %(prog)s --transport streamable-http --port 8001
  %(prog)s --transport sse --host 0.0.0.0 --port 8080
  %(prog)s --verbose

使用FastMCP CLI（推荐）：
  fastmcp run mcp_server.py --transport streamable-http --port 8001
  fastmcp dev mcp_server.py
        """
    )
    
    parser.add_argument(
        "--transport", "-t",
        choices=[t.value for t in TransportType],
        default=TransportType.STDIO.value,
        help="传输协议 (默认: stdio)"
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="服务器主机地址 (仅HTTP/SSE模式，默认: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port", "-p",
        type=int,
        default=8001,
        help="服务器端口 (仅HTTP/SSE模式，默认: 8001)"
    )
    
    parser.add_argument(
        "--path",
        default="/mcp",
        help="MCP端点路径 (仅HTTP模式，默认: /mcp)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="日志级别 (默认: info)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细日志输出"
    )
    
    return parser.parse_args()

def print_basic_startup_info(config: ServerConfig):
    """打印基本启动信息（不包含工具统计）"""
    safe_print("\n" + "="*60)
    safe_print("🚀 YQ AI V3 MCP Server 启动中... (根目录启动)")
    safe_print("="*60)
    safe_print(f"📡 传输协议: {config.transport.value}")
    
    if config.transport in [TransportType.STREAMABLE_HTTP, TransportType.SSE]:
        safe_print(f"🌐 服务地址: http://{config.host}:{config.port}")
        if config.transport == TransportType.STREAMABLE_HTTP:
            safe_print(f"🔗 MCP端点: http://{config.host}:{config.port}{config.path}")
            safe_print(f"💚 健康检查: http://{config.host}:{config.port}/health")
            safe_print(f"ℹ️  服务信息: http://{config.host}:{config.port}/info")
        elif config.transport == TransportType.SSE:
            safe_print("⚠️  注意: SSE传输协议已弃用，建议使用streamable-http")
    else:
        safe_print("📝 STDIO模式: 等待客户端连接...")
    
    safe_print(f"📊 日志级别: {config.log_level}")
    safe_print(f"🔍 详细模式: {'开启' if config.verbose else '关闭'}")
    safe_print("🔄 正在初始化服务器组件...")
    safe_print("="*60)

async def get_server_statistics():
    """异步获取服务器统计信息"""
    try:
        tools = await mcp.get_tools()
        resources = await mcp.get_resources()
        prompts = await mcp.get_prompts()
        return len(tools), len(resources), len(prompts)
    except Exception as e:
        logger.debug(f"异步获取统计信息失败: {e}")
        # 回退到同步方法
        try:
            tools_count = len(getattr(mcp, '_tools', getattr(mcp, 'tools', {})))
            resources_count = len(getattr(mcp, '_resources', getattr(mcp, 'resources', {})))
            prompts_count = len(getattr(mcp, '_prompts', getattr(mcp, 'prompts', {})))
            return tools_count, resources_count, prompts_count
        except:
            return 0, 0, 0

def print_complete_startup_info(config: ServerConfig):
    """打印完整的启动信息（包含导入后的工具统计）"""
    safe_print("\n" + "="*60)
    safe_print("✅ YQ AI V3 MCP Server 组件加载完成! (根目录启动)")
    safe_print("="*60)
    
    try:
        # 使用异步方法获取统计信息
        tools_count, resources_count, prompts_count = asyncio.run(get_server_statistics())
        
        safe_print(f"🛠️  可用工具: {tools_count} 个")
        safe_print(f"📚 可用资源: {resources_count} 个")
        safe_print(f"💭 提示模板: {prompts_count} 个")
    except Exception as e:
        safe_print("📊 工具统计: 获取中...")
        logger.debug(f"获取工具统计时出错: {e}")
    
    safe_print("="*60)
    safe_print("🎯 服务器模块:")
    modules = [
        "🔍 RAG Server (rag_*)",
        "🗃️  Text2SQL Server (text2sql_*)", 
        "🕸️  Knowledge Graph Server (kg_*)",
        "📈 Statistics Server (stats_*)",
        "💾 Cache Server (cache_*)",
        "🎓 Train Server (train_*)",
        "📊 Analytics Server (analytics_*)"
    ]
    for module in modules:
        safe_print(f"  {module}")
    
    safe_print("="*60)
    
    if config.transport == TransportType.STDIO:
        safe_print("💡 提示: STDIO模式通常由MCP客户端启动，如Claude Desktop")
    else:
        safe_print("💡 提示: 按 Ctrl+C 停止服务器")
    
    safe_print("="*60 + "\n")

def setup_logging(log_level: str, verbose: bool, stdio_mode: bool = False):
    """设置日志"""
    if stdio_mode:
        # 在stdio模式下，完全禁用日志输出到控制台
        import logging
        # 创建一个空的handler来丢弃所有日志
        logging.getLogger().handlers.clear()
        logging.getLogger().addHandler(logging.NullHandler())
        logging.getLogger().setLevel(logging.CRITICAL)
    elif verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("已启用详细日志输出")

def run_server(config: ServerConfig):
    """运行服务器"""
    try:
        # 在非stdio模式下显示启动信息
        if config.transport != TransportType.STDIO:
            # 先显示基本启动信息（不包含统计）
            print_basic_startup_info(config)
        
        # 在服务器启动前执行子服务器导入
        is_stdio_mode = config.transport == TransportType.STDIO
        
        setup_logging(config.log_level, config.verbose, is_stdio_mode)
        
        if not is_stdio_mode:
            logger.info("正在导入子服务器组件...")
        
        asyncio.run(import_all_server_components(silent_mode=is_stdio_mode))
        
        # 在非stdio模式下显示完整启动信息
        if config.transport != TransportType.STDIO:
            # 导入完成后显示完整的启动信息
            print_complete_startup_info(config)
        else:
            # stdio模式下静默启动，不输出任何用户可见信息
            pass
        
        # 根据传输协议运行服务器
        if config.transport == TransportType.STDIO:
            # stdio模式下静默启动
            mcp.run(transport="stdio")
            
        elif config.transport == TransportType.STREAMABLE_HTTP:
            logger.info(f"启动Streamable HTTP模式MCP服务器: http://{config.host}:{config.port}{config.path}")
            mcp.run(
                transport="streamable-http",
                host=config.host,
                port=config.port,
                path=config.path,
                log_level=config.log_level
            )
            
        elif config.transport == TransportType.SSE:
            logger.warning("SSE传输协议已弃用，建议使用streamable-http")
            logger.info(f"启动SSE模式MCP服务器: http://{config.host}:{config.port}")
            mcp.run(
                transport="sse",
                host=config.host,
                port=config.port,
                log_level=config.log_level
            )
            
    except KeyboardInterrupt:
        safe_print("\n\n🛑 服务器已停止")
        logger.info("用户中断，服务器已停止")
        
    except Exception as e:
        safe_print(f"\n❌ 服务器启动失败: {e}")
        logger.error(f"服务器启动失败: {e}")
        raise

# 主函数，用于启动MCP服务器
if __name__ == "__main__":
    # 设置输出编码为UTF-8（用于stdio模式）
    import io
    import os
    
    # 首先解析参数以确定是否为stdio模式
    args = parse_arguments()
    
    # 如果是stdio模式，禁用日志输出
    if args.transport == "stdio":
        import logging
        # 在导入其他模块之前设置日志级别为CRITICAL
        logging.basicConfig(level=logging.CRITICAL, handlers=[logging.NullHandler()])
        # 设置环境变量禁用其他库的日志
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['CHAINLIT_DEBUG'] = 'false'
    
    # 设置环境变量确保UTF-8编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 尝试设置UTF-8编码
    try:
        if hasattr(sys.stdout, 'buffer') and sys.stdout.encoding != 'utf-8':
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.encoding != 'utf-8':
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
    except Exception as e:
        # 如果设置编码失败，继续使用默认编码
        pass
    
    config = ServerConfig(
        transport=TransportType(args.transport),
        host=args.host,
        port=args.port,
        path=args.path,
        log_level=args.log_level,
        verbose=args.verbose
    )
    
    # 运行服务器
    run_server(config) 