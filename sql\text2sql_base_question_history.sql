-- 创建问题历史表
CREATE TABLE IF NOT EXISTS `text2sql_base_question_history` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `question` text NOT NULL COMMENT '用户问题',
  `sql` text DEFAULT NULL COMMENT '生成的SQL',
  `execution_status` varchar(20) DEFAULT NULL COMMENT '执行状态: success, failed, running, generated',
  `query_results` longtext DEFAULT NULL COMMENT '查询结果JSON数据',
  `results_count` int(11) DEFAULT NULL COMMENT '查询结果行数',
  `plotly_figure` longtext DEFAULT NULL COMMENT 'Plotly图表JSON数据',
  `followup_questions` text DEFAULT NULL COMMENT '后续问题JSON数据',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `ix_question_history_id` (`id`),
  KEY `ix_question_history_session_id` (`session_id`),
  KEY `ix_question_history_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问题历史表'; 