"""
异步版本的Text2SQL系统工具函数
提供异步的数据库操作方法
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from typing import List, Optional, Dict, Any
import datetime
import uuid
import json

from dao.DatabaseEngine import DatabaseEngine
from dao.models.Text2sqlSys import (
    Text2sqlDatasource, Text2sqlSchematable, Text2sqlSchemacolumn, 
    Text2sqlSchemarelationship, Text2sqlValuemapping
)
from utils.logger import get_logger

logger = get_logger()


# =================== 异步数据库会话管理 ===================

async def get_async_session() -> AsyncSession:
    """获取异步数据库会话"""
    return await DatabaseEngine.get_session()


# =================== Text2sqlDatasource 异步CRUD 操作 ===================

async def create_datasource(datasource_data: dict) -> Text2sqlDatasource:
    """异步创建数据源"""
    async with await get_async_session() as session:
        try:
            # 如果没有提供id，自动生成
            if 'id' not in datasource_data:
                datasource_data['id'] = str(uuid.uuid4())
            
            # 设置创建时间
            if 'create_time' not in datasource_data:
                datasource_data['create_time'] = datetime.datetime.now()
                
            datasource = Text2sqlDatasource(**datasource_data)
            session.add(datasource)
            await session.commit()
            await session.refresh(datasource)
            return datasource
        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating datasource: {str(e)}")
            raise
        finally:
            await session.close()


async def get_datasource_by_id(datasource_id: str) -> Optional[Text2sqlDatasource]:
    """异步根据ID获取数据源"""
    async with await get_async_session() as session:
        try:
            result = await session.get(Text2sqlDatasource, datasource_id)
            return result
        except Exception as e:
            logger.error(f"Error getting datasource by id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_datasource_by_code(code: str) -> Optional[Text2sqlDatasource]:
    """异步根据编码获取数据源"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlDatasource).where(Text2sqlDatasource.code == code)
            result = await session.execute(statement)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting datasource by code: {str(e)}")
            raise
        finally:
            await session.close()


async def get_all_datasources() -> List[Text2sqlDatasource]:
    """异步获取所有数据源"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlDatasource)
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all datasources: {str(e)}")
            raise
        finally:
            await session.close()


async def update_datasource(datasource_id: str, update_data: dict) -> Optional[Text2sqlDatasource]:
    """异步更新数据源"""
    async with await get_async_session() as session:
        try:
            datasource = await session.get(Text2sqlDatasource, datasource_id)
            if not datasource:
                return None
            
            # 设置更新时间
            update_data['update_time'] = datetime.datetime.now()
            
            for key, value in update_data.items():
                if hasattr(datasource, key):
                    setattr(datasource, key, value)
            
            session.add(datasource)
            await session.commit()
            await session.refresh(datasource)
            return datasource
        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating datasource: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_datasource(datasource_id: str) -> bool:
    """异步删除数据源"""
    async with await get_async_session() as session:
        try:
            datasource = await session.get(Text2sqlDatasource, datasource_id)
            if not datasource:
                return False
            
            await session.delete(datasource)
            await session.commit()
            return True
        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting datasource: {str(e)}")
            raise
        finally:
            await session.close()


# =================== Text2sqlSchematable 异步CRUD 操作 ===================

async def create_schematable(table_data: dict) -> Text2sqlSchematable:
    """异步创建数据表"""
    async with await get_async_session() as session:
        try:
            if 'created_at' not in table_data:
                table_data['created_at'] = datetime.datetime.now()
                
            table = Text2sqlSchematable(**table_data)
            session.add(table)
            await session.commit()
            await session.refresh(table)
            return table
        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating schematable: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schematable_by_id(table_id: int) -> Optional[Text2sqlSchematable]:
    """异步根据ID获取数据表"""
    async with await get_async_session() as session:
        try:
            result = await session.get(Text2sqlSchematable, table_id)
            return result
        except Exception as e:
            logger.error(f"Error getting schematable by id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schematable_by_connection_id(connection_id: str) -> List[Text2sqlSchematable]:
    """异步根据连接ID获取所有表"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchematable).where(Text2sqlSchematable.connection_id == connection_id)
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schematable by connection id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schematable_by_name(connection_id: str, table_name: str) -> Optional[Text2sqlSchematable]:
    """异步根据连接ID和表名获取表"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchematable).where(
                Text2sqlSchematable.connection_id == connection_id,
                Text2sqlSchematable.table_name == table_name
            )
            result = await session.execute(statement)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting schematable by name: {str(e)}")
            raise
        finally:
            await session.close()


async def update_schematable(table_id: int, update_data: dict) -> Optional[Text2sqlSchematable]:
    """异步更新数据表"""
    async with await get_async_session() as session:
        try:
            table = await session.get(Text2sqlSchematable, table_id)
            if not table:
                return None
            
            update_data['updated_at'] = datetime.datetime.now()
            
            for key, value in update_data.items():
                if hasattr(table, key):
                    setattr(table, key, value)
            
            session.add(table)
            await session.commit()
            await session.refresh(table)
            return table
        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating schematable: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_schematable(table_id: int) -> bool:
    """异步删除数据表"""
    async with await get_async_session() as session:
        try:
            table = await session.get(Text2sqlSchematable, table_id)
            if not table:
                return False
            
            await session.delete(table)
            await session.commit()
            return True
        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting schematable: {str(e)}")
            raise
        finally:
            await session.close()


# =================== Text2sqlSchemacolumn 异步CRUD 操作 ===================

async def create_schemacolumn(column_data: dict) -> Text2sqlSchemacolumn:
    """异步创建数据列"""
    async with await get_async_session() as session:
        try:
            if 'created_at' not in column_data:
                column_data['created_at'] = datetime.datetime.now()
                
            column = Text2sqlSchemacolumn(**column_data)
            session.add(column)
            await session.commit()
            await session.refresh(column)
            return column
        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating schemacolumn: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemacolumn_by_id(column_id: int) -> Optional[Text2sqlSchemacolumn]:
    """异步根据ID获取数据列"""
    async with await get_async_session() as session:
        try:
            result = await session.get(Text2sqlSchemacolumn, column_id)
            return result
        except Exception as e:
            logger.error(f"Error getting schemacolumn by id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemacolumn_by_table_id(table_id: int) -> List[Text2sqlSchemacolumn]:
    """异步根据表ID获取所有列"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemacolumn).where(Text2sqlSchemacolumn.table_id == table_id)
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schemacolumn by table id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemacolumn_by_name(table_id: int, column_name: str) -> Optional[Text2sqlSchemacolumn]:
    """异步根据表ID和列名获取列"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemacolumn).where(
                Text2sqlSchemacolumn.table_id == table_id,
                Text2sqlSchemacolumn.column_name == column_name
            )
            result = await session.execute(statement)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting schemacolumn by name: {str(e)}")
            raise
        finally:
            await session.close()


async def update_schemacolumn(column_id: int, update_data: dict) -> Optional[Text2sqlSchemacolumn]:
    """异步更新数据列"""
    async with await get_async_session() as session:
        try:
            column = await session.get(Text2sqlSchemacolumn, column_id)
            if not column:
                return None
            
            update_data['updated_at'] = datetime.datetime.now()
            
            for key, value in update_data.items():
                if hasattr(column, key):
                    setattr(column, key, value)
            
            session.add(column)
            await session.commit()
            await session.refresh(column)
            return column
        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating schemacolumn: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_schemacolumn(column_id: int) -> bool:
    """异步删除数据列"""
    async with await get_async_session() as session:
        try:
            column = await session.get(Text2sqlSchemacolumn, column_id)
            if not column:
                return False
            
            await session.delete(column)
            await session.commit()
            return True
        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting schemacolumn: {str(e)}")
            raise
        finally:
            await session.close()


# =================== Text2sqlSchemarelationship 异步CRUD 操作 ===================

async def create_schemarelationship(relationship_data: dict) -> Text2sqlSchemarelationship:
    """异步创建数据关系"""
    async with await get_async_session() as session:
        try:
            if 'created_at' not in relationship_data:
                relationship_data['created_at'] = datetime.datetime.now()
                
            relationship = Text2sqlSchemarelationship(**relationship_data)
            session.add(relationship)
            await session.commit()
            await session.refresh(relationship)
            return relationship
        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating schemarelationship: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_id(relationship_id: int) -> Optional[Text2sqlSchemarelationship]:
    """异步根据ID获取数据关系"""
    async with await get_async_session() as session:
        try:
            result = await session.get(Text2sqlSchemarelationship, relationship_id)
            return result
        except Exception as e:
            logger.error(f"Error getting schemarelationship by id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_connection_id(connection_id: str) -> List[Text2sqlSchemarelationship]:
    """异步根据连接ID获取所有关系"""
    async with await get_async_session() as session:
        try:
            statement = (
                select(Text2sqlSchemarelationship)
                .join(Text2sqlSchematable, Text2sqlSchemarelationship.source_table_id == Text2sqlSchematable.id)
                .where(Text2sqlSchematable.connection_id == connection_id)
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schemarelationship by connection id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_table_id(table_id: int) -> List[Text2sqlSchemarelationship]:
    """异步根据表ID获取所有关系（作为源表或目标表）"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemarelationship).where(
                (Text2sqlSchemarelationship.source_table_id == table_id) |
                (Text2sqlSchemarelationship.target_table_id == table_id)
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schemarelationship by table id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_source_table_id(table_id: int) -> List[Text2sqlSchemarelationship]:
    """异步根据源表ID获取所有关系"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemarelationship).where(
                Text2sqlSchemarelationship.source_table_id == table_id
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schemarelationship by source table id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_target_table_id(table_id: int) -> List[Text2sqlSchemarelationship]:
    """异步根据目标表ID获取所有关系"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemarelationship).where(
                Text2sqlSchemarelationship.target_table_id == table_id
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting schemarelationship by target table id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schemarelationship_by_column_ids(source_column_id: int, target_column_id: int) -> Optional[Text2sqlSchemarelationship]:
    """异步根据源列ID和目标列ID获取关系"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemarelationship).where(
                Text2sqlSchemarelationship.source_column_id == source_column_id,
                Text2sqlSchemarelationship.target_column_id == target_column_id
            )
            result = await session.execute(statement)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting schemarelationship by column ids: {str(e)}")
            raise
        finally:
            await session.close()


async def update_schemarelationship(relationship_id: int, update_data: dict) -> Optional[Text2sqlSchemarelationship]:
    """异步更新数据关系"""
    async with await get_async_session() as session:
        try:
            relationship = await session.get(Text2sqlSchemarelationship, relationship_id)
            if not relationship:
                return None
            
            update_data['updated_at'] = datetime.datetime.now()
            
            for key, value in update_data.items():
                if hasattr(relationship, key):
                    setattr(relationship, key, value)
            
            session.add(relationship)
            await session.commit()
            await session.refresh(relationship)
            return relationship
        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating schemarelationship: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_schemarelationship(relationship_id: int) -> bool:
    """异步删除数据关系"""
    async with await get_async_session() as session:
        try:
            relationship = await session.get(Text2sqlSchemarelationship, relationship_id)
            if not relationship:
                return False
            
            await session.delete(relationship)
            await session.commit()
            return True
        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting schemarelationship: {str(e)}")
            raise
        finally:
            await session.close()


# =================== Text2sqlValuemapping 异步CRUD 操作 ===================

async def create_valuemapping(mapping_data: dict) -> Text2sqlValuemapping:
    """异步创建值映射"""
    async with await get_async_session() as session:
        try:
            if 'created_at' not in mapping_data:
                mapping_data['created_at'] = datetime.datetime.now()
                
            mapping = Text2sqlValuemapping(**mapping_data)
            session.add(mapping)
            await session.commit()
            await session.refresh(mapping)
            return mapping
        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating valuemapping: {str(e)}")
            raise
        finally:
            await session.close()


async def get_valuemapping_by_id(mapping_id: int) -> Optional[Text2sqlValuemapping]:
    """异步根据ID获取值映射"""
    async with await get_async_session() as session:
        try:
            result = await session.get(Text2sqlValuemapping, mapping_id)
            return result
        except Exception as e:
            logger.error(f"Error getting valuemapping by id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_valuemapping_by_column_id(column_id: int, skip: int = 0, limit: int = 100) -> List[Text2sqlValuemapping]:
    """异步根据列ID获取值映射（支持分页）"""
    async with await get_async_session() as session:
        try:
            statement = (
                select(Text2sqlValuemapping)
                .where(Text2sqlValuemapping.column_id == column_id)
                .offset(skip)
                .limit(limit)
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting valuemapping by column id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_valuemapping_by_column_id_and_term(column_id: int, nl_term: str) -> List[Text2sqlValuemapping]:
    """异步根据列ID和自然语言术语获取值映射"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlValuemapping).where(
                Text2sqlValuemapping.column_id == column_id,
                Text2sqlValuemapping.nl_term == nl_term
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting valuemapping by column id and term: {str(e)}")
            raise
        finally:
            await session.close()


async def get_valuemapping(skip: int = 0, limit: int = 100) -> List[Text2sqlValuemapping]:
    """异步获取所有值映射（支持分页）"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlValuemapping).offset(skip).limit(limit)
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting valuemapping: {str(e)}")
            raise
        finally:
            await session.close()


async def get_valuemapping_by_nl_term(nl_term: str) -> List[Text2sqlValuemapping]:
    """异步根据自然语言术语获取值映射"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlValuemapping).where(Text2sqlValuemapping.nl_term == nl_term)
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting valuemapping by nl term: {str(e)}")
            raise
        finally:
            await session.close()


async def update_valuemapping(mapping_id: int, update_data: dict) -> Optional[Text2sqlValuemapping]:
    """异步更新值映射"""
    async with await get_async_session() as session:
        try:
            mapping = await session.get(Text2sqlValuemapping, mapping_id)
            if not mapping:
                return None
            
            update_data['updated_at'] = datetime.datetime.now()
            
            for key, value in update_data.items():
                if hasattr(mapping, key):
                    setattr(mapping, key, value)
            
            session.add(mapping)
            await session.commit()
            await session.refresh(mapping)
            return mapping
        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating valuemapping: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_valuemapping(mapping_id: int) -> Optional[Text2sqlValuemapping]:
    """异步删除值映射"""
    async with await get_async_session() as session:
        try:
            mapping = await session.get(Text2sqlValuemapping, mapping_id)
            if not mapping:
                return None
            
            await session.delete(mapping)
            await session.commit()
            return mapping
        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting valuemapping: {str(e)}")
            raise
        finally:
            await session.close()


# =================== 批量操作 ===================

async def batch_create_schemacolumns(table_id: int, columns_data: List[dict]) -> List[Text2sqlSchemacolumn]:
    """异步批量创建数据列"""
    async with await get_async_session() as session:
        try:
            columns = []
            for column_data in columns_data:
                if 'created_at' not in column_data:
                    column_data['created_at'] = datetime.datetime.now()
                column_data['table_id'] = table_id
                column = Text2sqlSchemacolumn(**column_data)
                columns.append(column)
                session.add(column)
            
            await session.commit()
            
            # 刷新所有对象以获取生成的ID
            for column in columns:
                await session.refresh(column)
            
            return columns
        except Exception as e:
            await session.rollback()
            logger.error(f"Error batch creating schemacolumns: {str(e)}")
            raise
        finally:
            await session.close()


async def get_datasource_with_tables(datasource_code: str) -> Optional[Text2sqlDatasource]:
    """异步获取数据源及其相关表"""
    async with await get_async_session() as session:
        try:
            # 先获取数据源
            statement = select(Text2sqlDatasource).where(Text2sqlDatasource.code == datasource_code)
            result = await session.execute(statement)
            datasource = result.scalar_one_or_none()
            
            if datasource:
                # 获取相关表
                tables_statement = select(Text2sqlSchematable).where(Text2sqlSchematable.connection_id == datasource.id)
                tables_result = await session.execute(tables_statement)
                datasource.tables = tables_result.scalars().all()
            
            return datasource
        except Exception as e:
            logger.error(f"Error getting datasource with tables: {str(e)}")
            raise
        finally:
            await session.close()


async def get_table_with_columns(table_id: int) -> Optional[Text2sqlSchematable]:
    """异步获取表及其相关列"""
    async with await get_async_session() as session:
        try:
            # 先获取表
            table = await session.get(Text2sqlSchematable, table_id)
            
            if table:
                # 获取相关列
                columns_statement = select(Text2sqlSchemacolumn).where(Text2sqlSchemacolumn.table_id == table_id)
                columns_result = await session.execute(columns_statement)
                table.columns = columns_result.scalars().all()
            
            return table
        except Exception as e:
            logger.error(f"Error getting table with columns: {str(e)}")
            raise
        finally:
            await session.close()


# =================== 搜索和查询功能 ===================

async def search_tables_by_name(table_name_pattern: str) -> List[Text2sqlSchematable]:
    """异步按表名模式搜索表"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchematable).where(
                Text2sqlSchematable.table_name.like(f"%{table_name_pattern}%")
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error searching tables by name: {str(e)}")
            raise
        finally:
            await session.close()


async def search_columns_by_name(column_name_pattern: str) -> List[Text2sqlSchemacolumn]:
    """异步按列名模式搜索列"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemacolumn).where(
                Text2sqlSchemacolumn.column_name.like(f"%{column_name_pattern}%")
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error searching columns by name: {str(e)}")
            raise
        finally:
            await session.close()


async def get_primary_key_columns(table_id: int) -> List[Text2sqlSchemacolumn]:
    """异步获取主键列"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemacolumn).where(
                Text2sqlSchemacolumn.table_id == table_id,
                Text2sqlSchemacolumn.is_primary_key == True
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting primary key columns: {str(e)}")
            raise
        finally:
            await session.close()


async def get_foreign_key_columns(table_id: int) -> List[Text2sqlSchemacolumn]:
    """异步获取外键列"""
    async with await get_async_session() as session:
        try:
            statement = select(Text2sqlSchemacolumn).where(
                Text2sqlSchemacolumn.table_id == table_id,
                Text2sqlSchemacolumn.is_foreign_key == True
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting foreign key columns: {str(e)}")
            raise
        finally:
            await session.close()


async def get_all_schemacolumns_by_connection_id(connection_id: str) -> List[Text2sqlSchemacolumn]:
    """异步根据连接ID批量获取所有列信息"""
    async with await get_async_session() as session:
        try:
            # 使用JOIN查询一次性获取所有相关的列信息
            statement = (
                select(Text2sqlSchemacolumn)
                .join(Text2sqlSchematable, Text2sqlSchemacolumn.table_id == Text2sqlSchematable.id)
                .where(Text2sqlSchematable.connection_id == connection_id)
            )
            result = await session.execute(statement)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all schemacolumns by connection id: {str(e)}")
            raise
        finally:
            await session.close()


async def get_schema_data_by_connection_id(connection_id: str) -> Dict[str, Any]:
    """
    异步批量获取连接的所有表、列和关系信息，减少数据库查询次数
    根据ui_metadata中的is_on_canvas属性过滤掉已丢弃的表及其相关数据
    
    Returns:
        Dict包含:
        - tables: 表列表（仅包含is_on_canvas为true的表）
        - columns: 列列表（仅包含有效表的列）
        - relationships: 关系列表（仅包含有效表之间的关系）
        - table_id_to_obj: 表ID到表对象的映射
        - column_id_to_obj: 列ID到列对象的映射
        - table_id_to_columns: 表ID到列列表的映射
    """
    async with await get_async_session() as session:
        try:
            # 1. 获取所有表
            tables_statement = select(Text2sqlSchematable).where(Text2sqlSchematable.connection_id == connection_id)
            tables_result = await session.execute(tables_statement)
            all_tables = tables_result.scalars().all()
            
            # 2. 过滤掉已丢弃的表（is_on_canvas为false的表）
            valid_tables = []
            valid_table_ids = set()
            
            for table in all_tables:
                ui_metadata = {}
                if table.ui_metadata:
                    try:
                        if isinstance(table.ui_metadata, str):
                            ui_metadata = json.loads(table.ui_metadata)
                        else:
                            ui_metadata = table.ui_metadata
                    except (json.JSONDecodeError, TypeError):
                        ui_metadata = {}
                
                # 检查is_on_canvas标志，默认为true（即如果没有设置，认为表是有效的）
                is_on_canvas = ui_metadata.get('is_on_canvas', True)
                if is_on_canvas:
                    valid_tables.append(table)
                    valid_table_ids.add(table.id)
            
            # 3. 获取有效表的所有列
            if valid_table_ids:
                columns_statement = select(Text2sqlSchemacolumn).where(
                    Text2sqlSchemacolumn.table_id.in_(valid_table_ids)
                )
                columns_result = await session.execute(columns_statement)
                all_columns = columns_result.scalars().all()
            else:
                all_columns = []
            
            # 4. 获取有效表之间的关系
            if valid_table_ids:
                relationships_statement = select(Text2sqlSchemarelationship).where(
                    Text2sqlSchemarelationship.source_table_id.in_(valid_table_ids),
                    Text2sqlSchemarelationship.target_table_id.in_(valid_table_ids)
                )
                relationships_result = await session.execute(relationships_statement)
                all_relationships = relationships_result.scalars().all()
            else:
                all_relationships = []
            
            # 5. 构建映射关系
            table_id_to_obj = {table.id: table for table in valid_tables}
            column_id_to_obj = {column.id: column for column in all_columns}
            
            # 6. 按表ID分组列
            table_id_to_columns = {}
            for table in valid_tables:
                table_id_to_columns[table.id] = []
            
            for column in all_columns:
                if column.table_id in table_id_to_columns:
                    table_id_to_columns[column.table_id].append(column)
            
            logger.info(f"异步获取连接 {connection_id} 的模式数据: "
                       f"{len(valid_tables)} 个有效表, {len(all_columns)} 个列, {len(all_relationships)} 个关系")
            
            return {
                "tables": valid_tables,
                "columns": all_columns,
                "relationships": all_relationships,
                "table_id_to_obj": table_id_to_obj,
                "column_id_to_obj": column_id_to_obj,
                "table_id_to_columns": table_id_to_columns,
                "valid_table_ids": valid_table_ids
            }
        except Exception as e:
            logger.error(f"Error getting schema data by connection id: {str(e)}")
            raise
        finally:
            await session.close()


async def delete_connection_all_data(connection_id: str) -> Dict[str, Any]:
    """
    异步删除连接的所有相关数据
    按照外键依赖顺序删除：value_mappings -> relationships -> columns -> tables
    
    Returns:
        删除统计信息，包含success标志和详细统计
    """
    async with await get_async_session() as session:
        try:
            delete_stats = {
                "success": True,
                "deleted_value_mappings": 0,
                "deleted_relationships": 0,
                "deleted_columns": 0,
                "deleted_tables": 0,
                "errors": []
            }
            
            # 1. 获取该连接的所有表ID
            tables_statement = select(Text2sqlSchematable.id).where(Text2sqlSchematable.connection_id == connection_id)
            tables_result = await session.execute(tables_statement)
            table_ids = [row[0] for row in tables_result.fetchall()]
            
            if not table_ids:
                logger.info(f"连接 {connection_id} 没有找到相关表，无需删除")
                return delete_stats
            
            # 2. 获取该连接的所有列ID（用于删除value mappings）
            columns_statement = select(Text2sqlSchemacolumn.id).where(
                Text2sqlSchemacolumn.table_id.in_(table_ids)
            )
            columns_result = await session.execute(columns_statement)
            column_ids = [row[0] for row in columns_result.fetchall()]
            
            # 3. 删除value mappings
            if column_ids:
                value_mappings_delete = text("""
                    DELETE FROM text2sql_sys_valuemapping 
                    WHERE column_id IN :column_ids
                """)
                vm_result = await session.execute(value_mappings_delete, {"column_ids": tuple(column_ids)})
                delete_stats["deleted_value_mappings"] = vm_result.rowcount
            
            # 4. 删除relationships
            relationships_delete = text("""
                DELETE FROM text2sql_sys_schemarelationship 
                WHERE source_table_id IN :table_ids OR target_table_id IN :table_ids
            """)
            rel_result = await session.execute(relationships_delete, {"table_ids": tuple(table_ids)})
            delete_stats["deleted_relationships"] = rel_result.rowcount
            
            # 5. 删除columns
            columns_delete = text("""
                DELETE FROM text2sql_sys_schemacolumn 
                WHERE table_id IN :table_ids
            """)
            col_result = await session.execute(columns_delete, {"table_ids": tuple(table_ids)})
            delete_stats["deleted_columns"] = col_result.rowcount
            
            # 6. 删除tables
            tables_delete = text("""
                DELETE FROM text2sql_sys_schematable 
                WHERE connection_id = :connection_id
            """)
            table_result = await session.execute(tables_delete, {"connection_id": connection_id})
            delete_stats["deleted_tables"] = table_result.rowcount
            
            await session.commit()
            
            logger.info(f"异步删除连接 {connection_id} 的所有数据完成: {delete_stats}")
            return delete_stats
            
        except Exception as e:
            await session.rollback()
            error_msg = f"删除连接数据时出错: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "deleted_value_mappings": 0,
                "deleted_relationships": 0,
                "deleted_columns": 0,
                "deleted_tables": 0,
                "errors": [error_msg]
            }
        finally:
            await session.close()


