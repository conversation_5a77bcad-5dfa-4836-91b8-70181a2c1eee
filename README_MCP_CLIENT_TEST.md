# MCP Client Test 使用手册

## 概述

`mcp_client_test.py` 是一个专门用于测试 YQ AI V3 MCP服务器功能的测试工具。它基于 FastMCP 2.0+ 客户端，支持多种传输协议，能够全面测试服务器的工具、资源和提示功能。

## 功能特性

### 🔧 支持的传输协议
- **Stdio模式**: 标准输入输出通信（本地测试）
- **HTTP模式**: HTTP流式传输（远程测试）
- **自动检测**: 根据输入自动选择传输类型

### 🎯 测试目标功能
1. **RAG服务器**: `rag_get_rag_collections` 工具调用
2. **知识图谱服务器**: `kg_get_knowledge_graph_statistics` 工具调用
3. **Text2SQL服务器**: `text2sql_text2sql_usage_prompt` 提示获取
4. **训练服务器**: `train_training_troubleshooting_prompt` 提示获取

### 📊 诊断功能
- 服务器文件存在性检查
- 语法错误检测
- 快速启动测试
- HTTP服务器连接检查
- 详细错误报告和解决建议

## 安装依赖

```bash
# 基础依赖
pip install fastmcp

# HTTP服务器诊断依赖（可选）
pip install aiohttp

# 完整项目依赖（推荐）
pip install -r requirements.txt
```

## 使用方法

### 基本语法

```bash
python mcp_client_test.py <服务器地址或路径>
```

### 参数说明

| 参数 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `服务器地址` | 必需 | 服务器URL或脚本路径 | `http://127.0.0.1:8001/mcp` |
| `服务器路径` | 必需 | 本地服务器脚本路径 | `mcp_server.py` |

## 使用示例

### 1. 测试本地Stdio模式服务器

```bash
# 测试当前目录的MCP服务器
python mcp_client_test.py mcp_server.py

# 测试指定路径的服务器
python mcp_client_test.py /path/to/your/mcp_server.py
```

**输出示例**:
```
YQ AI V3 FastMCP服务器功能测试工具
============================================================
📡 使用自定义服务器路径: mcp_server.py
🚀 开始FastMCP服务器功能测试
============================================================

🔍 开始服务器诊断...
✅ 服务器文件存在: mcp_server.py
✅ 服务器文件语法检查通过
🧪 尝试快速启动服务器测试...
✅ 服务器可以正常启动
🔗 正在测试MCP服务器连接...
```

### 2. 测试HTTP模式服务器

```bash
# 测试本地HTTP服务器
python mcp_client_test.py http://127.0.0.1:8001/mcp

# 测试远程HTTP服务器
python mcp_client_test.py http://your-server.com:8001/mcp

# 测试不同端口
python mcp_client_test.py http://127.0.0.1:8080/mcp
```

**输出示例**:
```
YQ AI V3 FastMCP服务器功能测试工具
============================================================
📡 使用自定义服务器路径: http://127.0.0.1:8001/mcp
🚀 开始FastMCP服务器功能测试
============================================================

🔍 开始服务器诊断...
🌐 检查HTTP服务器连接: http://127.0.0.1:8001/mcp
✅ 服务器响应 http://127.0.0.1:8001/health: 200
🔗 正在测试MCP服务器连接...
```

## 测试报告

### 成功测试输出

```
============================================================
📊 测试结果统计
============================================================
✅ 测试通过: 4/4
⏱️  总耗时: 2.34秒
📁 结果文件: fastmcp_test_results_20250619_152614.json

详细结果:
✅ RAG服务器 - rag_get_rag_collections: 成功
✅ 知识图谱服务器 - kg_get_knowledge_graph_statistics: 成功  
✅ Text2SQL服务器 - text2sql_text2sql_usage_prompt: 成功
✅ 训练服务器 - train_training_troubleshooting_prompt: 成功

🎉 所有测试通过！MCP服务器功能正常
```

### 测试结果文件

测试完成后会生成JSON格式的详细报告：

```json
{
  "timestamp": "2025-06-19T15:26:14.123456",
  "server_url": "http://127.0.0.1:8001/mcp",
  "transport_type": "StreamableHttpTransport",
  "total_tests": 4,
  "passed_tests": 4,
  "failed_tests": 0,
  "total_duration": 2.34,
  "results": {
    "rag_get_rag_collections": {
      "status": "success",
      "duration": 0.45,
      "response": "获取到3个RAG集合"
    },
    "kg_get_knowledge_graph_statistics": {
      "status": "success", 
      "duration": 0.67,
      "response": "节点数: 1250, 边数: 3420"
    }
  }
}
```

## 故障排除

### 常见错误及解决方案

#### 1. 编码错误 (已修复)

**错误**: `UnicodeDecodeError: 'utf-8' codec can't decode byte`

**解决方案**: 
- 确保使用最新版本的 `mcp_server.py`
- 问题已在stdio模式下修复

#### 2. 连接失败

**错误**: `❌ 连接MCP服务器失败: Connection closed`

**可能原因**:
- 服务器脚本有语法错误
- 缺少必要依赖
- 服务器启动时崩溃

**解决步骤**:
```bash
# 1. 检查服务器语法
python -m py_compile mcp_server.py

# 2. 单独启动服务器查看错误
python mcp_server.py

# 3. 安装缺失依赖
pip install -r requirements.txt
```

#### 3. HTTP服务器无响应

**错误**: `❌ 连接MCP服务器失败: Session terminated`

**解决步骤**:
```bash
# 1. 确认服务器正在运行
python mcp_server.py --transport streamable-http --port 8001

# 2. 检查端口占用
netstat -an | findstr 8001

# 3. 测试健康检查端点
curl http://127.0.0.1:8001/health
```

#### 4. 缺少依赖

**错误**: `ModuleNotFoundError: No module named 'xxx'`

**解决方案**:
```bash
# 安装特定模块
pip install pandas redis autogen-ext

# 或安装完整依赖
pip install -r requirements.txt
```

### 诊断命令

```bash
# 检查Python环境
python --version

# 检查已安装包
pip list | grep -E "(fastmcp|mcp|pandas|redis)"

# 检查服务器帮助信息
python mcp_server.py --help

# 启动HTTP服务器进行调试
python mcp_server.py --transport streamable-http --port 8001 --verbose
```

## 高级用法

### 自定义测试

如果需要测试其他功能，可以修改 `mcp_client_test.py` 中的测试配置：

```python
# 在文件中找到这些配置并修改
TOOLS_TO_TEST = [
    "rag_get_rag_collections",
    "kg_get_knowledge_graph_statistics",
    # 添加新的工具测试
    "your_custom_tool"
]

PROMPTS_TO_TEST = [
    "text2sql_text2sql_usage_prompt", 
    "train_training_troubleshooting_prompt",
    # 添加新的提示测试
    "your_custom_prompt"
]
```

### 批量测试

```bash
# 创建测试脚本
cat > batch_test.sh << 'EOF'
#!/bin/bash
echo "测试Stdio模式..."
python mcp_client_test.py mcp_server.py

echo "测试HTTP模式..."
python mcp_client_test.py http://127.0.0.1:8001/mcp

echo "测试完成"
EOF

# 运行批量测试
bash batch_test.sh
```

## 最佳实践

### 1. 测试前准备

```bash
# 确保环境干净
pip install --upgrade fastmcp

# 检查服务器配置
python mcp_server.py --help

# 准备测试环境
python mcp_server.py --transport streamable-http --port 8001 &
```

### 2. 定期测试

建议在以下情况下运行测试：
- 修改服务器代码后
- 部署到新环境前
- 添加新功能后
- 依赖更新后

### 3. 日志记录

```bash
# 保存测试日志
python mcp_client_test.py mcp_server.py 2>&1 | tee test_log.txt

# 查看历史测试结果
ls -la fastmcp_test_results_*.json
```

## 技术支持

如果遇到问题，请：

1. 查看生成的测试报告JSON文件
2. 检查服务器日志输出
3. 确认所有依赖已正确安装
4. 参考本手册的故障排除章节

---

**版本**: 1.0  
**更新日期**: 2025-06-19  
**兼容性**: FastMCP 2.0+, Python 3.11+ 