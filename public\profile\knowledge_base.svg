<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1"/>
    </linearGradient>
    
    <!-- 书本渐变 -->
    <linearGradient id="book" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5"/>
      <stop offset="100%" style="stop-color:#7c3aed"/>
    </linearGradient>
    
    <!-- 知识节点渐变 -->
    <radialGradient id="node" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </radialGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 强发光效果 -->
    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 - 添加脉冲动画 -->
  <circle cx="128" cy="128" r="120" fill="url(#bg)" stroke="#e2e8f0" stroke-width="2">
    <animate attributeName="r" values="120;125;120" dur="6s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="2;3;2" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="stroke-opacity" values="1;0.6;1" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主书本 - 添加翻页和浮动效果 -->
  <g filter="url(#shadow)">
    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-5; 0,0" dur="5s" repeatCount="indefinite"/>
    
    <!-- 书本主体 -->
    <rect x="60" y="60" width="136" height="120" rx="8" fill="url(#book)" opacity="0.9">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="3s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="skewY" values="0;2;0" dur="8s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 书脊装饰 - 添加发光效果 -->
    <rect x="60" y="60" width="12" height="120" rx="6" fill="#dc2626" filter="url(#glow)">
      <animate attributeName="fill" values="#dc2626;#ef4444;#dc2626" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="width" values="12;14;12" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 书页效果 - 添加翻页动画 -->
    <g>
      <animateTransform attributeName="transform" type="skewX" values="0;1;0;-1;0" dur="10s" repeatCount="indefinite"/>
      <rect x="72" y="64" width="120" height="112" rx="4" fill="white" opacity="0.95">
        <animate attributeName="opacity" values="0.95;1;0.95" dur="2s" repeatCount="indefinite"/>
      </rect>
    </g>

    <!-- 书页翻动效果 -->
    <g opacity="0.8">
      <rect x="75" y="67" width="50" height="106" rx="2" fill="white">
        <animateTransform attributeName="transform" type="rotateY" values="0;15;0" dur="6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.95;0.8" dur="6s" repeatCount="indefinite"/>
      </rect>
      <rect x="130" y="67" width="55" height="106" rx="2" fill="white" opacity="0.9">
        <animateTransform attributeName="transform" type="rotateY" values="0;-8;0" dur="7s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.9;1;0.9" dur="7s" repeatCount="indefinite"/>
      </rect>
    </g>
  </g>
  
  <!-- 知识网络节点 - 增强脉冲和连接动画 -->
  <g>
    <!-- 中央核心节点 - 最强动画效果 -->
    <circle cx="128" cy="128" r="8" fill="url(#node)" filter="url(#strongGlow)">
      <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.8;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 周围主要节点 - 添加脉冲效果 -->
    <circle cx="100" cy="100" r="6" fill="url(#node)" opacity="0.8" filter="url(#glow)">
      <animate attributeName="r" values="6;9;6" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="156" cy="100" r="6" fill="url(#node)" opacity="0.8" filter="url(#glow)">
      <animate attributeName="r" values="6;9;6" dur="2.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="100" cy="156" r="6" fill="url(#node)" opacity="0.8" filter="url(#glow)">
      <animate attributeName="r" values="6;9;6" dur="2.3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="156" cy="156" r="6" fill="url(#node)" opacity="0.8" filter="url(#glow)">
      <animate attributeName="r" values="6;9;6" dur="2.7s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.7s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 次级节点 - 添加呼吸效果 -->
    <circle cx="128" cy="85" r="5" fill="url(#node)" opacity="0.7" filter="url(#glow)">
      <animate attributeName="r" values="5;7;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="85" cy="128" r="5" fill="url(#node)" opacity="0.7" filter="url(#glow)">
      <animate attributeName="r" values="5;7;5" dur="3.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="171" cy="128" r="5" fill="url(#node)" opacity="0.7" filter="url(#glow)">
      <animate attributeName="r" values="5;7;5" dur="2.9s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="2.9s" repeatCount="indefinite"/>
    </circle>
    <circle cx="128" cy="171" r="5" fill="url(#node)" opacity="0.7" filter="url(#glow)">
      <animate attributeName="r" values="5;7;5" dur="3.1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3.1s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 连接线 - 添加数据流动效果 -->
    <g stroke="#10b981" stroke-width="2" opacity="0.6">
      <!-- 主要连接线 -->
      <line x1="128" y1="128" x2="100" y2="100" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 40;20 20;0 40" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;4;2" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="156" y2="100" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 40;20 20;0 40" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;4;2" dur="3.5s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="100" y2="156" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 40;20 20;0 40" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;4;2" dur="2.8s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="156" y2="156" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 40;20 20;0 40" dur="3.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;1;0.6" dur="3.2s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;4;2" dur="3.2s" repeatCount="indefinite"/>
      </line>
      
      <!-- 轴向连接线 -->
      <line x1="128" y1="128" x2="128" y2="85" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 45;22 23;0 45" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;3.5;2" dur="2.5s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="85" y2="128" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 45;22 23;0 45" dur="2.7s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.7s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;3.5;2" dur="2.7s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="171" y2="128" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 45;22 23;0 45" dur="2.9s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.9s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;3.5;2" dur="2.9s" repeatCount="indefinite"/>
      </line>
      <line x1="128" y1="128" x2="128" y2="171" filter="url(#glow)">
        <animate attributeName="stroke-dasharray" values="0 45;22 23;0 45" dur="2.6s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.6s" repeatCount="indefinite"/>
        <animate attributeName="stroke-width" values="2;3.5;2" dur="2.6s" repeatCount="indefinite"/>
      </line>
      
      <!-- 节点间连接 - 添加微妙流动效果 -->
      <line x1="100" y1="100" x2="156" y2="100" opacity="0.3">
        <animate attributeName="stroke-dasharray" values="0 60;30 30;0 60" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite"/>
      </line>
      <line x1="100" y1="156" x2="156" y2="156" opacity="0.3">
        <animate attributeName="stroke-dasharray" values="0 60;30 30;0 60" dur="4.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4.5s" repeatCount="indefinite"/>
      </line>
      <line x1="100" y1="100" x2="100" y2="156" opacity="0.3">
        <animate attributeName="stroke-dasharray" values="0 60;30 30;0 60" dur="4.2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4.2s" repeatCount="indefinite"/>
      </line>
      <line x1="156" y1="100" x2="156" y2="156" opacity="0.3">
        <animate attributeName="stroke-dasharray" values="0 60;30 30;0 60" dur="4.7s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4.7s" repeatCount="indefinite"/>
      </line>
    </g>
  </g>
  
  <!-- 装饰文本线 - 添加打字机效果 -->
  <g stroke="#6b7280" stroke-width="1.5" opacity="0.6">
    <line x1="85" y1="95" x2="140" y2="95">
      <animate attributeName="x2" values="85;140;85;140" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="3s" repeatCount="indefinite"/>
    </line>
    <line x1="85" y1="105" x2="130" y2="105">
      <animate attributeName="x2" values="85;130;85;130" dur="5.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.7s" repeatCount="indefinite"/>
    </line>
    <line x1="85" y1="145" x2="145" y2="145">
      <animate attributeName="x2" values="85;145;85;145" dur="6.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="3.2s" repeatCount="indefinite"/>
    </line>
    <line x1="85" y1="155" x2="125" y2="155">
      <animate attributeName="x2" values="85;125;85;125" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.5s" repeatCount="indefinite"/>
    </line>
  </g>
  
  <!-- 智能图标 - 增强发光和旋转效果 -->
  <g transform="translate(170, 70)">
    <animateTransform attributeName="transform" type="translate" values="170,70; 175,65; 170,70" dur="4s" repeatCount="indefinite"/>
    
    <circle cx="12" cy="12" r="10" fill="#fbbf24" opacity="0.2" filter="url(#glow)">
      <animate attributeName="r" values="10;14;10" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <path d="M8 8 Q12 4 16 8 Q12 12 8 8" fill="#f59e0b" opacity="0.8" filter="url(#glow)">
      <animateTransform attributeName="transform" type="rotate" values="0 12 8;360 12 8" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 知识粒子流动效果 -->
  <g opacity="0.6">
    <!-- 流动的知识粒子 -->
    <circle cx="50" cy="50" r="2" fill="#10b981" filter="url(#glow)">
      <animateTransform attributeName="transform" type="translate" values="0,0; 156,78; 0,156; -106,78; 0,0" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="r" values="2;4;2" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="80" r="1.5" fill="#8b5cf6" filter="url(#glow)">
      <animateTransform attributeName="transform" type="translate" values="0,0; -72,48; -144,96; -72,48; 0,0" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="r" values="1.5;3.5;1.5" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="60" cy="200" r="2.5" fill="#f59e0b" filter="url(#glow)">
      <animateTransform attributeName="transform" type="translate" values="0,0; 68,-72; 136,-144; 68,-72; 0,0" dur="12s" repeatCount="indefinite"/>
      <animate attributeName="r" values="2.5;5;2.5" dur="12s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 旋转装饰环 -->
  <g transform="translate(128, 128)" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" values="0 128 128;360 128 128" dur="30s" repeatCount="indefinite"/>
    <circle cx="0" cy="0" r="105" fill="none" stroke="#4f46e5" stroke-width="1" stroke-dasharray="6 12">
      <animate attributeName="stroke-opacity" values="0.3;0.7;0.3" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 知识标签飞舞效果 -->
  <g opacity="0.5" font-family="Arial" font-size="8">
    <text x="50" y="40" fill="#4f46e5" opacity="0.7">
      知识库
      <animateTransform attributeName="transform" type="translate" values="0,0; 80,40; 160,0; 80,-40; 0,0" dur="20s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="5s" repeatCount="indefinite"/>
    </text>
    <text x="200" y="220" fill="#10b981" opacity="0.6">
      智能检索
      <animateTransform attributeName="transform" type="translate" values="0,0; -100,-50; -200,0; -100,50; 0,0" dur="18s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="4s" repeatCount="indefinite"/>
    </text>
    <text x="30" y="130" fill="#f59e0b" opacity="0.6">
      网络
      <animateTransform attributeName="transform" type="translate" values="0,0; 50,60; 100,0; 50,-60; 0,0" dur="15s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </text>
  </g>
</svg> 