"""
RAG 对话历史服务类
提供对话会话和历史记录的业务逻辑处理
"""
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from dao.rag_chat_utils import RagChatSessionDAO, RagChatHistoryDAO
from dao.models.RagChatModels import RagChatSession, RagChatHistory
from utils.logger import get_logger

logger = get_logger()


class RagChatService:
    """RAG对话历史服务类"""
    
    def __init__(self):
        self.session_dao = RagChatSessionDAO()
        self.history_dao = RagChatHistoryDAO()
    
    async def create_or_get_session(
        self,
        session_id: str,
        user_id: str,
        collection_name: str,
        session_title: Optional[str] = None
    ) -> RagChatSession:
        """
        创建或获取对话会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            collection_name: 知识库集合名称
            session_title: 会话标题
            
        Returns:
            RagChatSession: 会话对象
        """
        try:
            # 先尝试获取现有会话
            session = await self.session_dao.get_session_by_id(session_id)
            
            if session:
                logger.debug(f"获取到现有会话: {session_id}")
                return session
            
            # 如果不存在，创建新会话
            session = await self.session_dao.create_session(
                session_id=session_id,
                user_id=user_id,
                collection_name=collection_name,
                session_title=session_title,
                create_by=user_id
            )
            
            logger.info(f"创建新会话: {session_id}")
            return session
            
        except Exception as e:
            logger.error(f"创建或获取会话失败: {str(e)}")
            raise
    
    async def save_chat_history(
        self,
        session_id: str,
        chat_history: List[Dict[str, str]]
    ) -> bool:
        """
        保存对话历史到数据库
        
        Args:
            session_id: 会话ID
            chat_history: 对话历史列表
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 先清空现有历史（如果需要完全替换）
            await self.history_dao.clear_session_history(session_id)
            
            # 逐条保存历史记录
            for history_item in chat_history:
                role = history_item.get("role", "user")
                content = history_item.get("content", "")
                
                if role and content:
                    await self.history_dao.add_message(
                        session_id=session_id,
                        role=role,
                        content=content
                    )
            
            logger.info(f"保存对话历史成功: session_id={session_id}, count={len(chat_history)}")
            return True
            
        except Exception as e:
            logger.error(f"保存对话历史失败: {str(e)}")
            return False
    
    async def add_message_to_history(
        self,
        session_id: str,
        role: str,
        content: str,
        token_count: Optional[int] = None,
        source_info: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> RagChatHistory:
        """
        添加单条消息到对话历史
        
        Args:
            session_id: 会话ID
            role: 角色 (user/assistant)
            content: 消息内容
            token_count: Token数量
            source_info: 数据来源信息
            metadata: 消息元数据
            
        Returns:
            RagChatHistory: 历史记录对象
        """
        try:
            history = await self.history_dao.add_message(
                session_id=session_id,
                role=role,
                content=content,
                token_count=token_count,
                source_info=source_info,
                metadata=metadata
            )
            
            logger.debug(f"添加消息成功: session_id={session_id}, role={role}")
            return history
            
        except Exception as e:
            logger.error(f"添加消息失败: {str(e)}")
            raise
    
    async def get_session_chat_history(
        self,
        session_id: str,
        max_pairs: int = 5
    ) -> List[Dict[str, str]]:
        """
        获取会话的对话历史（适合传递给LLM）
        
        Args:
            session_id: 会话ID
            max_pairs: 最大对话轮数
            
        Returns:
            List[Dict[str, str]]: 格式化的对话历史
        """
        try:
            history = await self.history_dao.get_recent_history(
                session_id=session_id,
                max_pairs=max_pairs
            )
            
            logger.debug(f"获取对话历史成功: session_id={session_id}, count={len(history)}")
            return history
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {str(e)}")
            return []
    
    async def update_session_config(
        self,
        session_id: str,
        config: Dict[str, Any]
    ) -> bool:
        """
        更新会话配置
        
        Args:
            session_id: 会话ID
            config: 配置信息
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 这里可以扩展更新会话配置的逻辑
            # 目前先通过获取会话然后更新的方式
            session = await self.session_dao.get_session_by_id(session_id)
            if session:
                # 这里需要在 DAO 类中添加更新配置的方法
                logger.info(f"更新会话配置: session_id={session_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"更新会话配置失败: {str(e)}")
            return False

    async def get_user_session_list(
            self,
            user_id: str,
            page: int = 1,
            page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取用户的会话列表（包含分页信息）

        Args:
            user_id: 用户ID
            page: 页码
            page_size: 每页大小

        Returns:
            Dict[str, Any]: 包含分页信息的会话数据
        """
        try:
            # 获取会话列表和总数
            sessions = await self.session_dao.get_user_sessions(
                user_id=user_id,
                page=page,
                page_size=page_size,
                # status=1  # 只获取活跃会话
            )

            total = await self.session_dao.get_user_sessions_count(
                user_id=user_id,
                # status=1  # 只获取活跃会话
            )

            # 转换为字典格式
            session_list = []
            for session in sessions:
                session_dict = {
                    "id": session.id,
                    "session_id": session.session_id,
                    "user_id": session.user_id,
                    "collection_name": session.collection_name,
                    "session_title": session.session_title,
                    "status": session.status,
                    "total_messages": session.total_messages,
                    "last_message_time": session.last_message_time.isoformat() if session.last_message_time else None,
                    "create_time": session.create_time.isoformat() if session.create_time else None,
                }
                session_list.append(session_dict)

            # 构建分页响应
            result = {
                "records": session_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size if total > 0 else 0
            }

            logger.debug(f"获取用户会话列表成功: user_id={user_id}, count={len(session_list)}, total={total}")
            return result

        except Exception as e:
            logger.error(f"获取用户会话列表失败: {str(e)}")
            return {
                "records": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "pages": 0
            }

    async def end_session(
            self,
            session_id: str
    ) -> bool:
        """
        结束会话

        Args:
            session_id: 会话ID

        Returns:
            bool: 结束是否成功
        """
        try:
            # 先检查会话是否存在
            session = await self.session_dao.get_session_by_id(session_id)

            if not session:
                # 会话不存在，可能是新建的会话还没有保存到数据库
                # 这种情况下直接返回成功，不需要进行任何操作
                logger.info(f"会话 {session_id} 不存在于数据库中，可能是未保存的新会话，直接返回成功")
                return True

            # 会话存在，执行状态更新
            success = await self.session_dao.update_session_status(
                session_id=session_id,
                status=2  # 结束状态
            )

            if success:
                logger.info(f"结束会话成功: session_id={session_id}")
            else:
                logger.warning(f"结束会话失败，状态更新失败: session_id={session_id}")

            return success

        except Exception as e:
            logger.error(f"结束会话失败: {str(e)}")
            # 即使出现异常，也不应该影响用户体验，返回True
            logger.info(f"结束会话出现异常但返回成功，避免影响用户体验: session_id={session_id}")
            return True
    
    async def delete_session(
        self,
        session_id: str
    ) -> bool:
        """
        删除会话（软删除）
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            success = await self.session_dao.update_session_status(
                session_id=session_id,
                status=3  # 已删除状态
            )
            
            if success:
                logger.info(f"删除会话成功: session_id={session_id}")
            else:
                logger.warning(f"删除会话失败，会话不存在: session_id={session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            return False
    
    async def cleanup_old_data(
        self,
        days: int = 30
    ) -> Dict[str, int]:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            Dict[str, int]: 清理结果统计
        """
        try:
            # 清理历史消息
            deleted_messages = await self.history_dao.delete_old_messages(days)
            
            # 这里可以扩展清理会话的逻辑
            
            result = {
                "deleted_messages": deleted_messages,
                "deleted_sessions": 0  # 暂时不实现会话删除
            }
            
            logger.info(f"清理旧数据完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {str(e)}")
            return {"deleted_messages": 0, "deleted_sessions": 0}


    # === 接口兼容方法 ===
    
    async def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        source_info: Optional[Dict[str, Any]] = None
    ) -> RagChatHistory:
        """
        添加消息（接口兼容方法）
        
        Args:
            session_id: 会话ID
            role: 角色
            content: 内容
            metadata: 元数据
            source_info: 数据来源信息
            
        Returns:
            RagChatHistory: 历史记录对象
        """
        token_count = metadata.get('token_count') if metadata else None
        return await self.add_message_to_history(
            session_id=session_id,
            role=role,
            content=content,
            token_count=token_count,
            source_info=source_info,
            metadata=metadata
        )
    
    async def get_session_history(
        self,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        获取会话历史（接口兼容方法）
        
        Args:
            session_id: 会话ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        try:
            history_records = await self.history_dao.get_session_history(
                session_id=session_id,
                limit=limit,
                offset=offset
            )
            
            # 转换为字典格式
            history_list = []
            for record in history_records:
                history_dict = {
                    "id": record.id,
                    "session_id": record.session_id,
                    "role": record.role,
                    "content": record.content,
                    "message_order": record.message_order,
                    "token_count": record.token_count,
                    "create_time": record.create_time.isoformat() if record.create_time else None,
                    "source_info": record.source_info,
                    "metadata": record.message_metadata
                }
                history_list.append(history_dict)
            
            return history_list
            
        except Exception as e:
            logger.error(f"获取会话历史失败: {str(e)}")
            return []

    async def get_user_sessions(
            self,
            user_id: str,
            limit: int = 20,
            offset: int = 0
    ) -> Dict[str, Any]:
        """
        获取用户会话列表（接口兼容方法，返回分页结构）

        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            Dict[str, Any]: 包含分页信息的会话数据
        """
        page = (offset // limit) + 1
        result = await self.get_user_session_list(
            user_id=user_id,
            page=page,
            page_size=limit
        )

        # 返回完整的分页结构，前端会根据需要解析
        return result
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话详细信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[Dict[str, Any]]: 会话信息
        """
        try:
            session = await self.session_dao.get_session_by_id(session_id)
            if not session:
                return None
            
            return {
                "id": session.id,
                "session_id": session.session_id,
                "user_id": session.user_id,
                "collection_name": session.collection_name,
                "session_title": session.session_title,
                "status": session.status,
                "total_messages": session.total_messages,
                "last_message_time": session.last_message_time.isoformat() if session.last_message_time else None,
                "create_time": session.create_time.isoformat() if session.create_time else None,
                "session_config": session.session_config
            }
            
        except Exception as e:
            logger.error(f"获取会话详细信息失败: {str(e)}")
            return None
    
    async def cleanup_old_history(
        self,
        user_id: Optional[str] = None,
        days_before: int = 30
    ) -> int:
        """
        清理历史数据（接口兼容方法）
        
        Args:
            user_id: 用户ID（可选）
            days_before: 天数
            
        Returns:
            int: 删除的记录数
        """
        result = await self.cleanup_old_data(days_before)
        return result.get('deleted_messages', 0)
    
    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 这里可以实现更复杂的统计逻辑
            sessions = await self.session_dao.get_user_sessions(
                user_id=user_id,
                page=1,
                page_size=1000  # 获取所有会话用于统计
            )
            
            total_sessions = len(sessions)
            active_sessions = len([s for s in sessions if s.status == 1])
            total_messages = sum(s.total_messages or 0 for s in sessions)
            
            last_activity_time = None
            if sessions:
                latest_session = max(sessions, key=lambda s: s.last_message_time or s.create_time)
                last_activity_time = (latest_session.last_message_time or latest_session.create_time).isoformat()
            
            avg_messages = total_messages / total_sessions if total_sessions > 0 else 0
            
            return {
                "total_sessions": total_sessions,
                "total_messages": total_messages,
                "active_sessions": active_sessions,
                "last_activity_time": last_activity_time,
                "avg_messages_per_session": avg_messages
            }
            
        except Exception as e:
            logger.error(f"获取用户统计信息失败: {str(e)}")
            return {
                "total_sessions": 0,
                "total_messages": 0,
                "active_sessions": 0,
                "last_activity_time": None,
                "avg_messages_per_session": 0
            }
    
    async def update_session_title(self, session_id: str, title: str) -> bool:
        """
        更新会话标题
        
        Args:
            session_id: 会话ID
            title: 新标题
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证标题不能为空
            if not title or not title.strip():
                logger.warning(f"更新会话标题失败: 标题不能为空")
                return False
            
            # 调用 DAO 层方法更新标题
            result = await self.session_dao.update_session_title(session_id, title.strip())
            
            if result:
                logger.info(f"更新会话标题成功: session_id={session_id}, title={title}")
            else:
                logger.warning(f"更新会话标题失败: session_id={session_id}, title={title}")
            
            return result
            
        except Exception as e:
            logger.error(f"更新会话标题失败: {str(e)}")
            return False
    
    async def search_history(
        self,
        user_id: str,
        keyword: str,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        搜索对话历史
        
        Args:
            user_id: 用户ID
            keyword: 关键词
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            # 验证关键词不能为空
            if not keyword or not keyword.strip():
                logger.warning("搜索关键词不能为空")
                return []
            
            keyword = keyword.strip()
            page = (offset // limit) + 1
            
            # 调用 DAO 层搜索方法
            sessions = await self.session_dao.search_sessions_by_title(
                user_id=user_id,
                keyword=keyword,
                page=page,
                page_size=limit
            )
            
            # 转换为字典格式
            search_results = []
            for session in sessions:
                session_dict = {
                    "id": session.id,
                    "session_id": session.session_id,
                    "user_id": session.user_id,
                    "collection_name": session.collection_name,
                    "session_title": session.session_title,
                    "status": session.status,
                    "total_messages": session.total_messages,
                    "last_message_time": session.last_message_time.isoformat() if session.last_message_time else None,
                    "create_time": session.create_time.isoformat() if session.create_time else None,
                    "match_type": "title",  # 标识匹配类型
                    "match_keyword": keyword  # 记录匹配的关键词
                }
                search_results.append(session_dict)
            
            logger.info(f"搜索对话历史成功: user_id={user_id}, keyword={keyword}, count={len(search_results)}")
            return search_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {str(e)}")
            return []

    async def export_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        导出会话记录

        Args:
            session_id: 会话ID

        Returns:
            Optional[Dict[str, Any]]: 导出的会话数据
        """
        try:
            session_info = await self.get_session_info(session_id)
            if not session_info:
                logger.warning(f"导出失败，会话不存在: session_id={session_id}")
                return None

            history = await self.get_session_history(session_id, limit=1000)

            # 返回与前端期望格式一致的数据结构
            export_data = {
                "session": {
                    "id": session_info["session_id"],
                    "title": session_info["session_title"] or "未命名会话",
                    "session_title": session_info["session_title"] or "未命名会话",
                    "user_id": session_info["user_id"],
                    "collection_name": session_info["collection_name"],
                    "status": session_info["status"],
                    "total_messages": session_info["total_messages"],
                    "created_at": session_info["create_time"],
                    "last_message_time": session_info["last_message_time"]
                },
                "messages": history,
                "exported_at": datetime.now().isoformat(),
                "export_info": {
                    "total_messages": len(history),
                    "session_id": session_id,
                    "export_version": "1.0"
                }
            }

            logger.info(f"导出会话记录成功: session_id={session_id}, 消息数量={len(history)}")
            return export_data

        except Exception as e:
            logger.error(f"导出会话记录失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None


# 创建全局服务实例
rag_chat_service = RagChatService() 