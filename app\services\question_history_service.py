from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, update
from dao.DatabaseEngine import DatabaseEngine
from dao.models.Text2sqlSys import Text2sqlQuestionHistory
from utils.logger import get_logger

logger = get_logger()


class QuestionHistoryService:
    """问题历史持久化服务"""
    
    @staticmethod
    async def save_question_history(
        question: str,
        sql: str = None,
        session_id: str = None,
        execution_status: str = "running"
    ) -> str:
        """
        保存问题历史
        
        Args:
            question: 用户问题
            sql: 生成的SQL（可选）
            session_id: 会话ID（可选）
            execution_status: 执行状态
            
        Returns:
            str: 历史记录ID
        """
        try:
            session_factory = DatabaseEngine.get_session_factory()
            async with session_factory() as session:
                history_id = str(uuid.uuid4())
                
                history = Text2sqlQuestionHistory(
                    id=history_id,
                    session_id=session_id,
                    question=question,
                    sql=sql,
                    execution_status=execution_status,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                session.add(history)
                await session.commit()
                
                logger.info(f"已保存问题历史: {history_id}")
                return history_id
                
        except Exception as e:
            logger.error(f"保存问题历史失败: {str(e)}")
            raise
    
    @staticmethod
    async def update_question_history(
        history_id: str,
        sql: str = None,
        execution_status: str = None,
        query_results: str = None,
        results_count: int = None,
        plotly_figure: str = None,
        followup_questions: str = None
    ) -> bool:
        """
        更新问题历史
        
        Args:
            history_id: 历史记录ID
            sql: 生成的SQL（可选）
            execution_status: 执行状态（可选）
            query_results: 查询结果JSON（可选）
            results_count: 查询结果行数（可选）
            plotly_figure: Plotly图表JSON（可选）
            followup_questions: 后续问题JSON（可选）
            
        Returns:
            bool: 是否更新成功
        """
        try:
            session_factory = DatabaseEngine.get_session_factory()
            async with session_factory() as session:
                update_data = {"updated_at": datetime.now()}
                
                if sql is not None:
                    update_data["sql"] = sql
                if execution_status is not None:
                    update_data["execution_status"] = execution_status
                if query_results is not None:
                    update_data["query_results"] = query_results
                if results_count is not None:
                    update_data["results_count"] = results_count
                if plotly_figure is not None:
                    update_data["plotly_figure"] = plotly_figure
                if followup_questions is not None:
                    update_data["followup_questions"] = followup_questions
                
                stmt = (
                    update(Text2sqlQuestionHistory)
                    .where(Text2sqlQuestionHistory.id == history_id)
                    .values(**update_data)
                )
                
                result = await session.execute(stmt)
                await session.commit()
                
                if result.rowcount > 0:
                    logger.info(f"已更新问题历史: {history_id}")
                    return True
                else:
                    logger.warning(f"未找到问题历史记录: {history_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"更新问题历史失败: {str(e)}")
            raise
    
    @staticmethod
    async def get_question_history(
        limit: int = 50,
        session_id: str = None
    ) -> List[Dict[str, Any]]:
        """
        获取问题历史列表
        
        Args:
            limit: 返回数量限制
            session_id: 会话ID过滤（可选）
            
        Returns:
            List[Dict]: 问题历史列表
        """
        try:
            session_factory = DatabaseEngine.get_session_factory()
            async with session_factory() as session:
                query = select(Text2sqlQuestionHistory)
                
                if session_id:
                    query = query.where(Text2sqlQuestionHistory.session_id == session_id)
                
                query = query.order_by(desc(Text2sqlQuestionHistory.created_at)).limit(limit)
                
                result = await session.execute(query)
                histories = result.scalars().all()
                
                return [
                    {
                        "id": history.id,
                        "session_id": history.session_id,
                        "question": history.question,
                        "sql": history.sql,
                        "execution_status": history.execution_status,
                        "query_results": history.query_results,
                        "results_count": history.results_count,
                        "plotly_figure": history.plotly_figure,
                        "followup_questions": history.followup_questions,
                        "created_at": history.created_at.isoformat() if history.created_at else None,
                        "updated_at": history.updated_at.isoformat() if history.updated_at else None
                    }
                    for history in histories
                ]
                
        except Exception as e:
            logger.error(f"获取问题历史失败: {str(e)}")
            raise
    
    @staticmethod
    async def get_question_by_id(history_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取问题历史
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            Optional[Dict]: 问题历史详情
        """
        try:
            session_factory = DatabaseEngine.get_session_factory()
            async with session_factory() as session:
                query = select(Text2sqlQuestionHistory).where(
                    Text2sqlQuestionHistory.id == history_id
                )
                
                result = await session.execute(query)
                history = result.scalar_one_or_none()
                
                if history:
                    return {
                        "id": history.id,
                        "session_id": history.session_id,
                        "question": history.question,
                        "sql": history.sql,
                        "execution_status": history.execution_status,
                        "query_results": history.query_results,
                        "results_count": history.results_count,
                        "plotly_figure": history.plotly_figure,
                        "followup_questions": history.followup_questions,
                        "created_at": history.created_at.isoformat() if history.created_at else None,
                        "updated_at": history.updated_at.isoformat() if history.updated_at else None
                    }
                return None
                
        except Exception as e:
            logger.error(f"获取问题历史详情失败: {str(e)}")
            raise
    
    @staticmethod
    async def delete_question_history(history_id: str) -> bool:
        """
        删除问题历史
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            session_factory = DatabaseEngine.get_session_factory()
            async with session_factory() as session:
                query = select(Text2sqlQuestionHistory).where(
                    Text2sqlQuestionHistory.id == history_id
                )
                
                result = await session.execute(query)
                history = result.scalar_one_or_none()
                
                if history:
                    await session.delete(history)
                    await session.commit()
                    logger.info(f"已删除问题历史: {history_id}")
                    return True
                else:
                    logger.warning(f"未找到问题历史记录: {history_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"删除问题历史失败: {str(e)}")
            raise 