from typing import Callable, Optional, Awaitable
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import message_handler, TopicId, MessageContext
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType
from autogen_core import type_subscription

from .base_agent import (
    BaseAgent, AGENT_NAMES, query_analyzer_topic_type, 
    sql_generator_topic_type, stream_output_topic_type
)
from app.schemas import (
    SchemaContextMessage, AnalysisMessage, ResponseMessage
)

# 获取logger实例
from utils.logger import get_logger
logger = get_logger()


@type_subscription(topic_type=query_analyzer_topic_type)
class QueryAnalyzerAgent(BaseAgent):
    """查询分析智能体，负责分析用户查询和表结构的关系"""
    
    def __init__(self, db_type=None, input_func=None, analytics_service=None, query_id: str = None):
        super().__init__("query_analyzer_agent", db_type, analytics_service, query_id)
        self.input_func = input_func
        self._prompt_template = f"""
            你是一名专业的数据库查询分析专家，具备以下核心能力：

            **角色定位：**
            - 深度理解自然语言查询意图
            - 精确分析数据库表结构关系
            - 生成结构化的SQL分析报告
            
            **分析方法：**
            1. **意图识别**：准确理解用户查询的核心目标和业务需求
            2. **实体映射**：将自然语言概念映射到数据库表和字段
            3. **关系分析**：识别表间关系和必要的连接操作
            4. **条件提取**：从查询中提取筛选、分组、排序等条件
            5. **结构设计**：构思合理的SQL查询结构框架
            
            **输出要求：**
            - 使用Markdown格式，结构清晰
            - 分析详尽但重点突出
            - 为后续SQL生成提供准确指导
            - 识别潜在歧义并提出解决方案
            
            **交互原则：**
            - 如果用户提出修改建议，只输出修改部分，不重复整个报告
            - 保持专业性和准确性
            - 确保分析结果可操作性强

            **请基于以下信息生成SQL命令分析报告：**

            **数据库环境：**
            - 数据库类型：[[db_type]]
            - 数据库结构：
            ```sql
            [[db_schema]]
            ```
            
            **值映射信息：**
            ```
            [[mappings_str]]
            ```
            
            **用户查询：**
            [[query]]
            
            **请按以下markdown格式输出详细的分析报告：**
            
            ## SQL 命令分析报告
            
            ### 1. 查询意图分析
            [详细描述用户查询的核心意图和业务目标]
            
            ### 2. 涉及的数据实体
            **主要表：**
            - [表名1] - [用途说明]
            - [表名2] - [用途说明]
            
            **关键字段：**
            - 表名1: [字段1], [字段2] - [用途说明]
            - 表名2: [字段1], [字段2] - [用途说明]
            
            ### 3. 表关系与连接
            [描述需要的表连接关系和连接条件]
            
            ### 4. 查询条件分析
            **筛选条件：**
            - [条件1] - [说明]
            - [条件2] - [说明]
            
            **分组要求：**
            [是否需要分组及分组字段]
            
            **排序要求：**
            [是否需要排序及排序规则]
            
            ### 5. SQL结构框架
            ```sql
            -- 基于分析的SQL查询结构
            SELECT [字段列表]
            FROM [主表]
            [连接语句]
            WHERE [筛选条件]
            [GROUP BY 分组]
            [ORDER BY 排序]
            [LIMIT 限制]
            ```
            
            ### 6. 潜在问题与建议
            [识别查询中的歧义或需要澄清的地方]
        """

    def _get_display_name(self) -> str:
        """获取智能体显示名称"""
        return AGENT_NAMES["query_analyzer"]

    def _prepare_prompt(self, query: str, schema_context: str, mappings_str: str = "") -> str:
        """准备提示模板

        Args:
            query: 用户查询
            schema_context: 表结构信息
            mappings_str: 值映射字符串，默认为空字符串

        Returns:
            str: 准备好的提示
        """
        if not query or not query.strip():
            raise ValueError("用户查询不能为空")

        # 填充模板
        prompt = self._prompt_template.replace("[[query]]", query)
        prompt = prompt.replace("[[db_type]]", self.db_type)
        prompt = prompt.replace("[[db_schema]]", schema_context or "未提供数据库结构信息")
        prompt = prompt.replace("[[mappings_str]]", mappings_str or "无特殊值映射")

        return prompt

    async def _create_agent_and_run(self, system_message: str, query: str, use_user_proxy: bool = False) -> ListMemory:
        """创建智能体并运行任务
        
        Args:
            system_message: 系统消息
            query: 用户查询
            use_user_proxy: 是否使用用户代理
            
        Returns:
            ListMemory: 对话记忆
        """
        memory = ListMemory()
        
        try:
            # 创建agent并执行任务
            agent = AssistantAgent(
                name="query_analyzer",
                model_client=self.model_client,
                system_message=system_message,
                model_client_stream=True,
            )

            if use_user_proxy and self.input_func:
                logger.info(f"QueryAnalyzerAgent {self.id.key}: 创建用户代理和群组聊天")
                user_proxy = UserProxyAgent(
                    name="user_proxy",
                    input_func=self.input_func
                )
                termination_en = TextMentionTermination("APPROVE")
                termination_zh = TextMentionTermination("同意")
                
                # 支持用户对分析报告进行多次修改
                logger.info(f"QueryAnalyzerAgent {self.id.key}: 创建群组聊天，支持终止条件")
                team = RoundRobinGroupChat([agent, user_proxy], termination_condition=termination_en | termination_zh)
                stream = team.run_stream(task=query)
                logger.info(f"QueryAnalyzerAgent {self.id.key}: 开始群组聊天流...")
                
                async for msg in stream:
                    # 模拟流式输出
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        await self.send_stream_message(content=msg.content,content_format="")
                        continue
                    # 记录每次对话历史记录
                    if isinstance(msg, TextMessage):
                        # 保存历史记忆
                        await memory.add(MemoryContent(content=msg.model_dump_json(), mime_type=MemoryMimeType.JSON.value))
                        continue
                    # 等待用户输入对分析报告的修改建议
                    if isinstance(msg, UserInputRequestedEvent) and msg.source == "user_proxy":
                        logger.info(f"QueryAnalyzerAgent {self.id.key}: 收到用户输入请求，等待输入")
                        # await self.send_stream_message("请输入修改建议或者直接点击同意")
                        await self.publish_message(
                            ResponseMessage(
                                source="user_proxy",
                                content="请输入修改建议或者直接点击同意",
                                is_final=False,
                                result=None
                            ),
                            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
                        )
                        continue
            else:
                # 如果用户没有参与修改，则直接生成分析报告
                stream = agent.run_stream(task=query)
                async for event in stream:
                    if isinstance(event, ModelClientStreamingChunkEvent):
                        # 确保内容以Markdown格式正确渲染
                        await self.send_stream_message(event.content,content_format="markdown")
                        continue
                    if isinstance(event, TextMessage):
                        await memory.add(MemoryContent(content=event.model_dump_json(), mime_type=MemoryMimeType.JSON.value))
                        
        except Exception as e:
            logger.error(f"运行智能体时出错: {str(e)}")
            raise
            
        return memory

    @message_handler
    async def handle_message(self, message: SchemaContextMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，分析查询意图和所需表结构"""
        try:
            # 开始执行，记录到数据库
            await self.start_execution(input_data={
                "query": message.query,
                "schema_context": message.schema_context or "",
                "mappings_str": getattr(message, 'mappings_str', "") or ""
            })
            
            # 验证消息
            if not self.validate_message(message, ['query']):
                error = ValueError("消息格式无效，缺少必需字段")
                await self.record_error(error)
                await self.finish_execution("FAILED", error_message=str(error))
                await self.send_error_message(error, "消息验证")
                return

            # 获取连接ID、表结构信息和值映射
            schema_context = message.schema_context or ""
            query = message.query
            mappings_str = getattr(message, 'mappings_str', "") or ""
            
            # 发送开始分析的消息
            await self.send_stream_message("正在分析查询意图...\n\n", message_sequence=1)
            
            # 准备提示
            try:
                system_message = self._prepare_prompt(query, schema_context, mappings_str)
            except ValueError as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.send_error_message(e, "准备提示")
                return
            
            # 运行智能体
            try:
                memory = await self._create_agent_and_run(
                    system_message, 
                    query, 
                    use_user_proxy=bool(self.input_func)
                )
                # 记录模型调用统计（这里简化处理，实际可以从memory中获取更准确的统计）
                await self.increment_model_usage(calls_count=1, tokens_used=len(system_message))
                
            except Exception as e:
                await self.record_error(e)
                await self.finish_execution("FAILED", error_message=str(e))
                await self.handle_exception(e, "运行分析智能体")
                return

            await self.send_stream_message("\n\n分析已完成", is_final=True, message_sequence=2)

            # 将 ListMemory 转换为可序列化的格式
            memory_content = []
            try:
                for content in memory.content:  # 使用.content属性而不是.contents
                    memory_content.append({
                        "content": content.content,
                        "mime_type": content.mime_type,  # 将MemoryMimeType转换为字符串
                        "metadata": content.metadata  # 加入metadata字段以保存完整信息
                    })
            except Exception as e:
                logger.warning(f"转换记忆内容时出错: {str(e)}")
                memory_content = []

            # 完成执行，记录成功状态
            output_data = {
                "memory_content": memory_content,
                "analysis_completed": True,
                "analysis_content_size": len(str(memory_content))
            }
            await self.finish_execution("SUCCESS", output_data=output_data)

            analysis_message = AnalysisMessage(
                query=message.query,
                memory_content=memory_content,  # 使用转换后的内容
                analysis="",  # 分析内容已在流式输出中发送
                role="assistant",
                schema_context=schema_context,  # 传递表结构信息
                mappings_str=mappings_str  # 传递值映射字符串
            )

            await self.publish_message(
                analysis_message,
                topic_id=TopicId(type=sql_generator_topic_type, source=self.id.key)
            )
            
        except Exception as e:
            # 记录错误并完成执行
            await self.record_error(e)
            await self.finish_execution("FAILED", error_message=str(e))
            await self.handle_exception(e, "处理分析消息") 