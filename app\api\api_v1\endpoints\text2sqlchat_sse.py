from autogen_core import CancellationToken, MessageContext, ClosureContext
from fastapi import APIRouter, Request, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
import asyncio
import uuid
import json
from typing import Dict, Optional, Set
from pydantic import BaseModel
import time

# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()
# 禁用autogen_core在控制台的输出（仅保留WARNING及以上级别）
import logging
autogen_core_logger = logging.getLogger("autogen_core")
autogen_core_logger.setLevel(logging.WARNING)

from app.schemas import ResponseMessage
from app.services.enhanced_text2sql_service import EnhancedText2SQLService, EnhancedStreamResponseCollector
from app.services.agents import AGENT_NAMES

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)

# 会话信息数据结构
class SessionInfo:
    def __init__(self, session_id: str, user_id: str):
        self.session_id = session_id
        self.user_id = user_id
        self.feedback_queue = asyncio.Queue()
        self.event_queue = asyncio.Queue()
        self.is_connected = False
        self.task: Optional[asyncio.Task] = None
        self.created_at = time.time()
        self.last_activity = time.time()
        self.current_query_id: Optional[str] = None  # 添加当前查询ID跟踪

# 会话管理字典 - 使用session_id作为主键
sessions: Dict[str, SessionInfo] = {}

# 用户到会话的映射 - 一个用户可以有多个会话
user_sessions: Dict[str, Set[str]] = {}

# 请求模型
class QueryRequest(BaseModel):
    query: str
    connection_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
class QueryStatusResponse(BaseModel):
    query_id: str
    status: str
    session_id: str
    user_id: str
    query: str
    connection_id: str

class FeedbackRequest(BaseModel):
    session_id: str
    content: str
    is_feedback: bool = True

# 会话管理函数
def create_session(user_id: str, session_id: str = None) -> SessionInfo:
    """创建新会话"""
    if not session_id:
        session_id = str(uuid.uuid4())
    
    # 创建会话信息
    session_info = SessionInfo(session_id, user_id)
    sessions[session_id] = session_info
    
    # 更新用户会话映射
    if user_id not in user_sessions:
        user_sessions[user_id] = set()
    user_sessions[user_id].add(session_id)
    
    logger.info(f"创建新会话: session_id={session_id}, user_id={user_id}")
    return session_info

def get_session(session_id: str) -> Optional[SessionInfo]:
    """获取会话信息"""
    session_info = sessions.get(session_id)
    if session_info:
        session_info.last_activity = time.time()
    return session_info

def cleanup_session(session_id: str):
    """清理会话"""
    session_info = sessions.get(session_id)
    if not session_info:
        return
    
    user_id = session_info.user_id
    
    # 取消任务
    if session_info.task and not session_info.task.done():
        session_info.task.cancel()
    
    # 从映射中移除
    if user_id in user_sessions:
        user_sessions[user_id].discard(session_id)
        if not user_sessions[user_id]:  # 如果用户没有其他会话了
            del user_sessions[user_id]
    
    # 删除会话
    del sessions[session_id]
    logger.info(f"清理会话: session_id={session_id}, user_id={user_id}")

def cleanup_expired_sessions():
    """清理过期会话"""
    current_time = time.time()
    expired_sessions = []
    
    for session_id, session_info in sessions.items():
        # 如果会话超过1小时没有活动，标记为过期
        if current_time - session_info.last_activity > 3600:
            expired_sessions.append(session_id)
    
    for session_id in expired_sessions:
        cleanup_session(session_id)
    
    if expired_sessions:
        logger.info(f"清理了 {len(expired_sessions)} 个过期会话")

# 检查会话连接是否有效
def is_session_connected(session_id: str) -> bool:
    """检查会话连接是否仍然有效"""
    session_info = get_session(session_id)
    return session_info is not None and session_info.is_connected

# 安全发送SSE消息的函数
async def safe_send_session_message(session_id: str, message: dict) -> bool:
    """安全地发送SSE消息到指定会话"""
    try:
        session_info = get_session(session_id)
        if not session_info or not session_info.is_connected:
            logger.debug(f"会话 {session_id} 连接无效，跳过消息发送")
            return False
            
        # 将消息放入会话的事件队列
        await session_info.event_queue.put(message)
        return True
        
    except Exception as e:
        logger.warning(f"发送SSE消息到会话 {session_id} 失败: {str(e)}")
        # 标记连接为无效
        session_info = sessions.get(session_id)
        if session_info:
            session_info.is_connected = False
        return False

def format_sse_message(data: dict, event: str = "message") -> str:
    """格式化SSE消息"""
    from utils.encode_utils import safe_json_dumps
    json_data = safe_json_dumps(data)
    return f"event: {event}\ndata: {json_data}\n\n"

@router.post("/query", 
             summary="启动Text2SQL查询处理",
             description="启动一个新的Text2SQL查询处理任务，支持多智能体协作模式，返回任务标识符用于后续的流式响应获取")
async def start_text2sql_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """启动Text2SQL查询处理"""
    try:
        # 清理过期会话
        cleanup_expired_sessions()
        
        # 获取或生成用户ID
        user_id = request.user_id
        if not user_id:
            user_id = str(uuid.uuid4())
            logger.info(f"新查询请求未提供用户ID，生成临时ID: {user_id}")
        
        # 获取或创建会话
        session_id = request.session_id
        if session_id and session_id in sessions:
            session_info = get_session(session_id)
            # 如果会话存在但用户ID不匹配，创建新会话
            if session_info and session_info.user_id != user_id:
                session_info = create_session(user_id)
        else:
            session_info = create_session(user_id, session_id)
        
        session_id = session_info.session_id
        
        logger.info(f"收到查询请求: session_id={session_id}, user_id={user_id}, query={request.query}")
        
        # 如果会话已有正在运行的任务，先取消它
        if session_info.task and not session_info.task.done():
            logger.info(f"取消会话 {session_id} 的上一个查询任务")
            session_info.task.cancel()
        
        # 创建新的查询任务
        task = asyncio.create_task(process_session_query(
            request.query, 
            request.connection_id, 
            session_id
        ))
        session_info.task = task
        
        # 添加清理任务到后台任务
        background_tasks.add_task(cleanup_session_resources, session_id)
        
        logger.info(f"会话 {session_id} 的查询处理任务已在后台启动")
        
        return {
            "status": "success",
            "message": "查询处理已启动",
            "session_id": session_id,
            "user_id": user_id,
            "query": request.query,
            "connection_id": request.connection_id
        }
        
    except Exception as e:
        logger.error(f"启动查询处理时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"启动查询处理失败: {str(e)}")

@router.get("/stream/{session_id}",
           summary="获取会话流式响应",
           description="通过SSE（Server-Sent Events）获取指定会话的实时响应流，包括查询进度、结果数据和错误信息")
async def text2sql_sse_stream(session_id: str, request: Request):
    """获取会话的SSE流式响应"""
    logger.info(f"会话 {session_id} 建立SSE连接")
    
    # 从查询参数中获取user_id
    user_id = request.query_params.get("user_id")
    
    session_info = get_session(session_id)
    if not session_info:
        # 如果会话不存在，检查是否提供了user_id
        if not user_id:
            raise HTTPException(
                status_code=400, 
                detail=f"会话 {session_id} 不存在，请提供 user_id 参数来创建新会话"
            )
        
        logger.info(f"会话 {session_id} 不存在，为用户 {user_id} 创建新会话")
        session_info = create_session(user_id, session_id)
    else:
        # 如果会话存在但提供了不同的user_id，验证是否匹配
        if user_id and session_info.user_id != user_id:
            raise HTTPException(
                status_code=403, 
                detail=f"会话 {session_id} 属于用户 {session_info.user_id}，不能被用户 {user_id} 访问"
            )
    
    # 标记连接为活动状态
    session_info.is_connected = True
    
    async def event_generator():
        try:
            # 发送连接建立消息
            welcome_msg = {
                "type": "message",
                "source": "系统",
                "content": "SSE连接已建立，可以开始查询",
                "region": "process",
                "sessionId": session_id,
                "userId": session_info.user_id
            }
            yield format_sse_message(welcome_msg)
            
            # 持续监听事件队列
            while True:
                try:
                    # 检查客户端是否断开连接
                    if await request.is_disconnected():
                        logger.info(f"会话 {session_id} 的SSE客户端已断开连接")
                        break
                    
                    # 检查会话状态
                    if not is_session_connected(session_id):
                        logger.info(f"会话 {session_id} 的SSE连接已标记为关闭")
                        break
                    
                    # 从队列获取事件，设置超时避免无限等待
                    try:
                        message = await asyncio.wait_for(
                            session_info.event_queue.get(), 
                            timeout=1.0
                        )
                        yield format_sse_message(message)
                    except asyncio.TimeoutError:
                        # 发送心跳消息保持连接
                        yield format_sse_message({"type": "heartbeat"}, "heartbeat")
                        continue
                        
                except asyncio.CancelledError:
                    logger.info(f"会话 {session_id} 的SSE事件生成器被取消")
                    break
                except Exception as e:
                    logger.error(f"会话 {session_id} 的SSE事件生成器出错: {str(e)}")
                    error_msg = {
                        "type": "error",
                        "content": f"SSE连接出错: {str(e)}",
                        "sessionId": session_id,
                        "userId": session_info.user_id
                    }
                    yield format_sse_message(error_msg)
                    break
                    
        except Exception as e:
            logger.error(f"会话 {session_id} 的SSE连接处理出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 标记连接为关闭状态
            session_info.is_connected = False
            logger.info(f"会话 {session_id} 的SSE连接已关闭")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.get("/stream/{user_id}/{session_id}",
           summary="获取用户会话流式响应",
           description="通过用户ID和会话ID获取SSE流式响应，提供额外的用户权限验证")
async def text2sql_sse_stream_with_user(user_id: str, session_id: str, request: Request):
    """获取用户会话的SSE流式响应"""
    logger.info(f"用户 {user_id} 的会话 {session_id} 建立SSE连接")
    
    session_info = get_session(session_id)
    if not session_info:
        logger.info(f"会话 {session_id} 不存在，为用户 {user_id} 创建新会话")
        session_info = create_session(user_id, session_id)
    else:
        # 验证会话是否属于该用户
        if session_info.user_id != user_id:
            raise HTTPException(
                status_code=403, 
                detail=f"会话 {session_id} 属于用户 {session_info.user_id}，不能被用户 {user_id} 访问"
            )
    
    # 标记连接为活动状态
    session_info.is_connected = True
    
    async def event_generator():
        try:
            # 发送连接建立消息
            welcome_msg = {
                "type": "message",
                "source": "系统",
                "content": "SSE连接已建立，可以开始查询",
                "region": "process",
                "sessionId": session_id,
                "userId": session_info.user_id
            }
            yield format_sse_message(welcome_msg)
            
            # 持续监听事件队列
            while True:
                try:
                    # 检查客户端是否断开连接
                    if await request.is_disconnected():
                        logger.info(f"会话 {session_id} 的SSE客户端已断开连接")
                        break
                    
                    # 检查会话状态
                    if not is_session_connected(session_id):
                        logger.info(f"会话 {session_id} 的SSE连接已标记为关闭")
                        break
                    
                    # 从队列获取事件，设置超时避免无限等待
                    try:
                        message = await asyncio.wait_for(
                            session_info.event_queue.get(), 
                            timeout=1.0
                        )
                        yield format_sse_message(message)
                    except asyncio.TimeoutError:
                        # 发送心跳消息保持连接
                        yield format_sse_message({"type": "heartbeat"}, "heartbeat")
                        continue
                        
                except asyncio.CancelledError:
                    logger.info(f"会话 {session_id} 的SSE事件生成器被取消")
                    break
                except Exception as e:
                    logger.error(f"会话 {session_id} 的SSE事件生成器出错: {str(e)}")
                    error_msg = {
                        "type": "error",
                        "content": f"SSE连接出错: {str(e)}",
                        "sessionId": session_id,
                        "userId": session_info.user_id
                    }
                    yield format_sse_message(error_msg)
                    break
                    
        except Exception as e:
            logger.error(f"会话 {session_id} 的SSE连接处理出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 标记连接为关闭状态
            session_info.is_connected = False
            logger.info(f"会话 {session_id} 的SSE连接已关闭")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.post("/feedback",
            summary="提交会话反馈",
            description="向指定会话提交用户反馈信息，用于改进查询结果和用户体验")
async def submit_feedback(request: FeedbackRequest):
    """提交会话反馈"""
    try:
        session_id = request.session_id
        
        # 检查会话是否存在
        session_info = get_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在或已过期")
        
        # 将反馈消息放入会话的反馈队列
        feedback_data = {
            "content": request.content,
            "is_feedback": request.is_feedback
        }
        await session_info.feedback_queue.put(feedback_data)
        
        logger.info(f"收到会话 {session_id} 的反馈消息: {request.content}")
        
        return {
            "status": "success",
            "message": "反馈已提交"
        }
        
    except Exception as e:
        logger.error(f"处理会话反馈时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交反馈失败: {str(e)}")

# 处理查询的独立函数，供SSE调用
async def process_session_query(query: str, connection_id: str, session_id: str):
    """处理Text2SQL查询并通过SSE发送结果"""
    try:
        session_info = get_session(session_id)
        if not session_info:
            logger.error(f"会话 {session_id} 不存在，无法处理查询")
            return
        
        # 检查任务是否被取消
        if asyncio.current_task().cancelled():
            logger.info(f"会话 {session_id} 的查询任务在开始前就被取消")
            return
            
        # 创建增强版Text2SQL服务
        service = EnhancedText2SQLService()
        
        # 设置消息回调函数，将消息发送到SSE
        async def message_callback(ctx: ClosureContext, message: ResponseMessage, message_ctx: MessageContext) -> None:
            try:
                # 检查任务是否被取消
                if asyncio.current_task().cancelled():
                    logger.debug(f"会话 {session_id} 的查询任务已被取消，跳过消息发送")
                    return
                    
                # 检查会话连接是否仍然有效
                if not is_session_connected(session_id):
                    logger.debug(f"会话 {session_id} 的SSE连接已关闭，跳过消息发送")
                    return
                
                # 转换为字典，添加消息类型
                from utils.encode_utils import clean_dict_for_json
                msg_dict = clean_dict_for_json(message.model_dump())
                
                # 根据消息来源确定区域
                region = "process"  # 默认
                if message.source == AGENT_NAMES["schema_retriever"] or message.source == AGENT_NAMES["query_analyzer"]:
                    region = "analysis"
                elif message.source == AGENT_NAMES["sql_generator"]:
                    region = "sql"
                elif message.source == AGENT_NAMES["sql_explainer"]:
                    region = "explanation"
                elif message.source == AGENT_NAMES["sql_executor"]:
                    region = "data"
                elif message.source == AGENT_NAMES["visualization_recommender"]:
                    region = "visualization"
                elif message.source == "user_proxy":
                    region = "user_proxy"

                msg_dict["region"] = region
                msg_dict["type"] = "message"
                msg_dict["sessionId"] = session_id
                msg_dict["userId"] = session_info.user_id
                
                # 使用安全发送函数
                success = await safe_send_session_message(session_id, msg_dict)
                if not success:
                    logger.debug(f"向会话 {session_id} 发送SSE消息失败，连接可能已断开")

            except asyncio.CancelledError:
                logger.debug(f"会话 {session_id} 的消息回调被取消")
                raise
            except Exception as e:
                logger.error(f"处理消息回调时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                # 标记连接为关闭状态
                session_info.is_connected = False

        async def user_input(prompt: str, cancellation_token: CancellationToken | None) -> str:
            logger.info(f"等待会话 {session_id} 的用户输入: {prompt}")
            
            try:
                # 检查任务是否被取消
                if asyncio.current_task().cancelled():
                    logger.info(f"会话 {session_id} 的查询任务已被取消，中断用户输入过程")
                    raise asyncio.CancelledError
                
                # 检查会话连接是否仍然有效
                if not is_session_connected(session_id):
                    logger.info(f"会话 {session_id} 的SSE连接已关闭，中断用户输入过程")
                    raise asyncio.CancelledError
                
                # 获取会话信息
                session_info = get_session(session_id)
                if not session_info:
                    logger.error(f"会话 {session_id} 不存在")
                    raise asyncio.CancelledError
                    
                # 等待会话反馈队列中的消息
                logger.info(f"会话 {session_id} 开始等待 feedback_queue.get()")
                data = await session_info.feedback_queue.get()
                logger.info(f"会话 {session_id} 从 feedback_queue.get() 收到反馈: {data}")
                return data.get("content", "")
            except asyncio.CancelledError:
                logger.info(f"会话 {session_id} 的用户输入等待被取消")
                return "同意"

        # 创建用户输入处理适配器
        async def enhanced_message_callback(ctx: ClosureContext, message: ResponseMessage, message_ctx: MessageContext) -> None:
            """增强版消息回调适配器"""
            await message_callback(ctx, message, message_ctx)
        
        # 获取或生成用户ID作为session_id传递给服务
        user_id = session_info.user_id
        
        # 检查会话连接状态并发送开始处理的消息
        if is_session_connected(session_id):
            await safe_send_session_message(session_id, {
                "type": "message",
                "source": "系统",
                "content": f"🚀 开始处理查询: {query}",
                "region": "process",
                "sessionId": session_id,
                "userId": session_info.user_id
            })
        else:
            logger.info(f"会话 {session_id} 的SSE连接已关闭，不发送开始处理消息")
            return
        
        # 处理查询
        try:
            result = await service.process_query(
                query=query, 
                session_id=session_id,
                connection_id=connection_id, 
                collector_callback=enhanced_message_callback,
                user_input_func=user_input
            )
            
            # 存储query_id到session_info
            if result and result.get('query_id'):
                session_info.current_query_id = result['query_id']
                
            logger.info(f"会话 {session_id} 的查询处理完成: {query}, 查询ID: {result.get('query_id') if result else 'N/A'}")
            
            # 发送完成消息，包含执行结果信息
            if is_session_connected(session_id):
                completion_content = f"✅ 查询处理完成"
                if result:
                    completion_content += f"，查询ID: {result.get('query_id', 'N/A')}"
                    if result.get('duration_ms'):
                        completion_content += f"，耗时: {result['duration_ms']:.2f}ms"
                
                await safe_send_session_message(session_id, {
                    "type": "complete",
                    "source": "系统",
                    "content": completion_content,
                    "region": "process",
                    "sessionId": session_id,
                    "userId": session_info.user_id,
                    "result": result  # 添加完整的结果信息
                })
                
        except asyncio.CancelledError:
            logger.info(f"会话 {session_id} 的查询处理被取消")
            raise
        except Exception as e:
            logger.error(f"会话 {session_id} 的查询处理错误: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 检查会话连接状态并发送错误消息
            if is_session_connected(session_id):
                await safe_send_session_message(session_id, {
                    "type": "error",
                    "content": f"❌ 处理查询时出错: {str(e)}",
                    "region": "process",
                    "sessionId": session_id,
                    "userId": session_info.user_id
                })
    
    except asyncio.CancelledError:
        logger.info(f"会话 {session_id} 的SSE查询处理任务被取消")
        # 不需要重新抛出，让任务正常结束
    except Exception as e:
        logger.error(f"会话 {session_id} 的SSE查询处理异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 检查会话连接状态并发送错误消息
        if is_session_connected(session_id):
            await safe_send_session_message(session_id, {
                "type": "error",
                "content": f"💥 处理查询时发生错误: {str(e)}",
                "region": "process",
                "sessionId": session_id,
                "userId": session_info.user_id
            })

async def cleanup_session_resources(session_id: str):
    """清理会话资源的后台任务"""
    try:
        session_info = get_session(session_id)
        if not session_info:
            return
        
        # 等待查询任务完成或被取消
        if session_info.task:
            try:
                await session_info.task
            except asyncio.CancelledError:
                logger.info(f"会话 {session_id} 的查询任务已被取消")
            except Exception as e:
                logger.warning(f"会话 {session_id} 的查询任务执行出错: {str(e)}")
        
        # 等待一段时间后清理资源（给SSE连接时间关闭）
        await asyncio.sleep(30)
        
        # 检查会话是否仍然活跃
        session_info = get_session(session_id)
        if session_info and not session_info.is_connected:
            # 如果连接已关闭且没有活动，清理会话
            cleanup_session(session_id)
        
        logger.info(f"会话 {session_id} 的资源清理完成")
        
    except Exception as e:
        logger.error(f"清理会话 {session_id} 资源时出错: {str(e)}")

@router.get("/query/{query_id}/status",
           summary="获取查询状态",
           description="获取指定查询任务的当前执行状态，包括运行中、已完成、失败等状态信息")
async def get_query_status(query_id: str):
    """获取查询状态"""
    try:
        service = EnhancedText2SQLService()
        status = await service.get_execution_status(query_id)
        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        logger.error(f"获取查询状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取查询状态失败: {str(e)}")

@router.delete("/query/{query_id}",
              summary="取消查询任务",
              description="取消指定的查询任务，停止其执行并释放相关资源")
async def cancel_query(query_id: str):
    """取消查询任务"""
    try:
        service = EnhancedText2SQLService()
        result = await service.cancel_execution(query_id)
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        logger.error(f"取消查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消查询失败: {str(e)}")

@router.delete("/session/{session_id}",
              summary="取消会话",
              description="取消指定会话的所有查询任务，清理会话资源并断开连接")
async def cancel_session(session_id: str):
    """取消会话"""
    try:
        session_info = get_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")
        
        # 取消查询任务
        if session_info.task and not session_info.task.done():
            logger.info(f"取消会话 {session_id} 的查询任务")
            session_info.task.cancel()
        
        # 标记连接为关闭状态
        session_info.is_connected = False
        
        # 发送取消消息
        await session_info.event_queue.put({
            "type": "cancelled",
            "source": "系统",
            "content": "查询已被取消",
            "region": "process",
            "sessionId": session_id,
            "userId": session_info.user_id
        })
        
        logger.info(f"会话 {session_id} 已取消")
        
        return {
            "status": "success",
            "message": "会话已取消"
        }
        
    except Exception as e:
        logger.error(f"取消会话 {session_id} 时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消会话失败: {str(e)}")

@router.get("/session/{session_id}/status",
           summary="获取会话状态",
           description="获取指定会话的详细状态信息，包括连接状态、当前任务、活动时间等")
async def get_session_status(session_id: str):
    """获取会话状态"""
    try:
        session_info = get_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")
        
        status_data = {
            "session_id": session_id,
            "user_id": session_info.user_id,
            "is_connected": session_info.is_connected,
            "has_active_task": session_info.task and not session_info.task.done(),
            "created_at": session_info.created_at,
            "last_activity": session_info.last_activity,
            "current_query_id": session_info.current_query_id
        }
        
        # 如果有当前查询ID，获取查询的详细状态
        if session_info.current_query_id:
            try:
                service = EnhancedText2SQLService()
                query_status = await service.get_execution_status(session_info.current_query_id)
                status_data["query_status"] = query_status
            except Exception as e:
                logger.warning(f"获取查询状态失败: {str(e)}")
                status_data["query_status"] = {"error": str(e)}
        
        return {
            "status": "success",
            "data": status_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话状态失败: {str(e)}")

@router.get("/user/{user_id}/sessions",
           summary="获取用户所有会话",
           description="获取指定用户的所有活跃会话列表，包括会话ID、状态和创建时间")
async def get_user_sessions(user_id: str):
    """获取用户所有会话"""
    try:
        cleanup_expired_sessions()
        
        user_session_ids = user_sessions.get(user_id, set())
        active_sessions = []
        
        for session_id in user_session_ids:
            session_info = get_session(session_id)
            if session_info:
                active_sessions.append({
                    "session_id": session_id,
                    "user_id": session_info.user_id,
                    "is_connected": session_info.is_connected,
                    "has_active_task": session_info.task and not session_info.task.done(),
                    "created_at": session_info.created_at,
                    "last_activity": session_info.last_activity,
                    "current_query_id": session_info.current_query_id
                })
        
        return {
            "status": "success",
            "user_id": user_id,
            "sessions": active_sessions
        }
        
    except Exception as e:
        logger.error(f"获取用户 {user_id} 会话列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@router.delete("/user/{user_id}/sessions",
              summary="取消用户所有会话",
              description="取消指定用户的所有活跃会话，清理所有相关资源")
async def cancel_all_user_sessions(user_id: str):
    """取消用户所有会话"""
    try:
        user_session_ids = user_sessions.get(user_id, set()).copy()
        cancelled_count = 0
        
        for session_id in user_session_ids:
            try:
                session_info = get_session(session_id)
                if session_info:
                    # 取消查询任务
                    if session_info.task and not session_info.task.done():
                        session_info.task.cancel()
                    
                    # 标记连接为关闭状态
                    session_info.is_connected = False
                    
                    # 发送取消消息
                    await session_info.event_queue.put({
                        "type": "cancelled",
                        "source": "系统",
                        "content": "所有会话已被取消",
                        "region": "process",
                        "sessionId": session_id,
                        "userId": user_id
                    })
                    
                    cancelled_count += 1
            except Exception as e:
                logger.warning(f"取消会话 {session_id} 时出错: {str(e)}")
        
        logger.info(f"用户 {user_id} 的 {cancelled_count} 个会话已取消")
        
        return {
            "status": "success",
            "message": f"已取消 {cancelled_count} 个会话"
        }
        
    except Exception as e:
        logger.error(f"取消用户 {user_id} 所有会话时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消所有会话失败: {str(e)}") 