import asyncio
from typing import Dict, Any, List, Optional

from fastmcp import FastMC<PERSON>, Context
from pydantic import BaseModel

# 导入服务层
from app.services.text2sql_sys_service import Text2SQLSysService
from app.schemas import Text2sqlSchematableUpdate, Text2sqlSchemacolumnUpdate, Text2sqlValuemappingCreate
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

# 创建Text2SQL MCP服务器实例
text2sql_server = FastMCP("Text2SQL Server")

# 创建服务实例
text2sql_sys_service = Text2SQLSysService()

# ============================================================================
# Text2SQL相关数据模型
# ============================================================================

class Text2SQLTrainRequest(BaseModel):
    question: str = ""
    sql: str = ""
    connection_id: Optional[str] = None

class DatabaseConnectionRequest(BaseModel):
    connection_id: str

class Text2SQLChatRequest(BaseModel):
    query: str
    connection_id: str
    user_id: str = "default_user"
    session_id: Optional[str] = None

# ============================================================================
# Text2SQL系统管理相关工具
# ============================================================================

@text2sql_server.tool
async def get_all_database_connections(ctx: Context) -> Dict[str, Any]:
    """
    获取所有数据库连接（对应FastAPI接口 /connections/）
    
    Returns:
        数据库连接列表
    """
    try:
        await ctx.info("正在获取数据库连接列表")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.get_all_connections()
        
        await ctx.info(f"成功获取 {len(result.get('data', []))} 个数据库连接")
        return result
        
    except Exception as e:
        error_msg = f"获取数据库连接时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def get_database_connection(connection_id: str, ctx: Context) -> Dict[str, Any]:
    """
    根据connection_id获取数据库连接（对应FastAPI接口 /connections/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        
    Returns:
        数据库连接信息
    """
    try:
        await ctx.info(f"正在获取数据库连接: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.get_connection_by_id(connection_id)
        
        await ctx.info("数据库连接信息获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取数据库连接时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def get_database_schema(connection_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取数据库模式信息（对应FastAPI接口 /schema/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        
    Returns:
        数据库模式信息
    """
    try:
        await ctx.info(f"正在获取数据库模式: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.get_saved_schema(connection_id)
        
        await ctx.info("数据库模式信息获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取数据库模式时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def discover_and_save_schema(connection_id: str, ctx: Context) -> Dict[str, Any]:
    """
    发现并保存数据库模式（对应FastAPI接口 /schema/discover/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        
    Returns:
        发现结果
    """
    try:
        await ctx.info(f"正在发现并保存数据库模式: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.discover_and_save_schema(connection_id)
        
        await ctx.info("数据库模式发现和保存完成")
        return result
        
    except Exception as e:
        error_msg = f"发现并保存数据库模式时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def get_schema_metadata(connection_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取模式元数据（对应FastAPI接口 /schema/metadata/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        
    Returns:
        模式元数据
    """
    try:
        await ctx.info(f"正在获取模式元数据: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.get_schema_metadata(connection_id)
        
        await ctx.info("模式元数据获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取模式元数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def publish_schema(connection_id: str, schema_data: Dict[str, Any], ctx: Context) -> Dict[str, Any]:
    """
    发布模式（对应FastAPI接口 /schema/publish/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        schema_data: 模式数据
        
    Returns:
        发布结果
    """
    try:
        await ctx.info(f"正在发布模式: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.publish_schema(connection_id, schema_data)
        
        await ctx.info("模式发布完成")
        return result
        
    except Exception as e:
        error_msg = f"发布模式时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def update_table_metadata(table_id: int, table_data: Dict[str, Any], ctx: Context) -> Dict[str, Any]:
    """
    更新表元数据（对应FastAPI接口 /schema/table/{table_id}）
    
    Args:
        table_id: 表ID
        table_data: 表数据
        
    Returns:
        更新结果
    """
    try:
        await ctx.info(f"正在更新表元数据: {table_id}")
        
        # 复用服务层的业务逻辑
        # 需要将table_data转换为Text2sqlSchematableUpdate对象
        table_update = Text2sqlSchematableUpdate(**table_data)
        result = await text2sql_sys_service.update_table(table_id, table_update)
        
        await ctx.info("表元数据更新完成")
        return result
        
    except Exception as e:
        error_msg = f"更新表元数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def update_column_metadata(column_id: int, column_data: Dict[str, Any], ctx: Context) -> Dict[str, Any]:
    """
    更新列元数据（对应FastAPI接口 /schema/column/{column_id}）
    
    Args:
        column_id: 列ID
        column_data: 列数据
        
    Returns:
        更新结果
    """
    try:
        await ctx.info(f"正在更新列元数据: {column_id}")
        
        # 复用服务层的业务逻辑
        # 需要将column_data转换为Text2sqlSchemacolumnUpdate对象
        column_update = Text2sqlSchemacolumnUpdate(**column_data)
        result = await text2sql_sys_service.update_column(column_id, column_update)
        
        await ctx.info("列元数据更新完成")
        return result
        
    except Exception as e:
        error_msg = f"更新列元数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def get_graph_visualization_data(connection_id: str, ctx: Context) -> Dict[str, Any]:
    """
    获取图形可视化数据（对应FastAPI接口 /graph/{connection_id}）
    
    Args:
        connection_id: 数据库连接ID
        
    Returns:
        图形可视化数据
    """
    try:
        await ctx.info(f"正在获取图形可视化数据: {connection_id}")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.get_graph_data(connection_id)
        
        await ctx.info("图形可视化数据获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取图形可视化数据时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def get_value_mappings(column_id: Optional[int] = None, skip: int = 0, limit: int = 100, ctx: Context = None) -> Dict[str, Any]:
    """
    获取值映射（对应FastAPI接口 /value-mappings/）
    
    Args:
        column_id: 列ID（可选）
        skip: 跳过数量
        limit: 限制数量
        
    Returns:
        值映射列表
    """
    try:
        await ctx.info("正在获取值映射")
        
        # 复用服务层的业务逻辑
        result = await text2sql_sys_service.read_value_mappings(column_id, skip, limit)
        
        await ctx.info("值映射获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取值映射时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

@text2sql_server.tool
async def create_value_mapping(mapping_data: Dict[str, Any], ctx: Context) -> Dict[str, Any]:
    """
    创建值映射（对应FastAPI接口 POST /value-mappings/）
    
    Args:
        mapping_data: 映射数据
        
    Returns:
        创建结果
    """
    try:
        await ctx.info("正在创建值映射")
        
        # 复用服务层的业务逻辑
        # 需要将mapping_data转换为Text2sqlValuemappingCreate对象
        mapping_create = Text2sqlValuemappingCreate(**mapping_data)
        result = await text2sql_sys_service.create_value_mapping(mapping_create)
        
        await ctx.info("值映射创建完成")
        return result
        
    except Exception as e:
        error_msg = f"创建值映射时发生错误: {str(e)}"
        await ctx.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

# ============================================================================
# Text2SQL相关提示模板
# ============================================================================

@text2sql_server.prompt
def text2sql_usage_prompt() -> str:
    """Text2SQL系统使用指南"""
    return """# Text2SQL系统使用指南

本Text2SQL系统基于重构后的服务层架构，提供以下核心功能：

## 主要功能

### 1. 数据库连接管理
- `get_all_database_connections`: 获取所有数据库连接
- `get_database_connection`: 获取指定数据库连接信息
- 支持多种数据库类型（MySQL、PostgreSQL、SQLite等）

### 2. 数据库模式管理
- `get_database_schema`: 获取数据库模式信息
- `discover_and_save_schema`: 自动发现并保存数据库模式
- `get_schema_metadata`: 获取模式元数据
- `publish_schema`: 发布模式配置

### 3. 元数据管理
- `update_table_metadata`: 更新表元数据
- `update_column_metadata`: 更新列元数据
- `get_value_mappings`: 获取值映射配置
- `create_value_mapping`: 创建值映射


### 4. 可视化支持
- `get_graph_visualization_data`: 获取数据库关系图可视化数据
- 支持表关系和依赖关系的图形化展示

## 架构优势
- 复用服务层业务逻辑，避免重复实现
- 基于多智能体架构的智能SQL生成
- 统一的错误处理和日志记录
- 更好的代码维护性和扩展性
"""

@text2sql_server.prompt
def text2sql_best_practices_prompt() -> str:
    """Text2SQL系统最佳实践"""
    return """# Text2SQL系统最佳实践

## 数据库连接最佳实践
1. **连接配置**
   - 使用专用的只读账户进行查询
   - 配置合理的连接超时时间
   - 启用连接池优化性能

2. **安全考虑**
   - 限制用户访问权限
   - 使用SSL连接确保数据传输安全
   - 定期轮换数据库密码

## 模式管理最佳实践
1. **模式发现**
   - 定期同步数据库模式变更
   - 为表和列添加有意义的注释
   - 维护完整的元数据信息

2. **值映射配置**
   - 为枚举值建立清晰的映射关系
   - 定义业务术语的标准化表达
   - 保持映射关系的及时更新

## 查询优化建议
1. **问题表达**
   - 使用清晰、具体的自然语言描述
   - 包含必要的业务上下文信息
   - 避免歧义和模糊表达

2. **性能优化**
   - 合理设置查询超时时间
   - 使用索引优化复杂查询
   - 避免全表扫描的大数据查询

3. **结果验证**
   - 检查生成的SQL语句合理性
   - 验证查询结果的准确性
   - 理解并确认查询逻辑

## 多智能体协作
1. **智能体分工**
   - 查询分析：理解用户意图
   - 模式检索：找到相关表和字段
   - SQL生成：构建准确的SQL语句
   - 结果解释：提供查询结果的业务解释

2. **质量保证**
   - 多层验证确保SQL正确性
   - 智能体间的交叉检查
   - 基于反馈的持续改进
"""

@text2sql_server.prompt
def text2sql_query_patterns_prompt() -> str:
    """Text2SQL查询模式指南"""
    return """# Text2SQL查询模式指南

## 常见查询模式

### 1. 基础查询模式
**简单筛选查询**
- 用户问题："显示所有活跃用户"
- SQL模式：`SELECT * FROM users WHERE status = 'active'`

**聚合统计查询**
- 用户问题："每个部门有多少员工？"
- SQL模式：`SELECT department, COUNT(*) FROM employees GROUP BY department`

### 2. 时间相关查询
**时间范围查询**
- 用户问题："显示上个月的销售记录"
- SQL模式：`SELECT * FROM sales WHERE date >= DATEADD(month, -1, GETDATE())`

**趋势分析查询**
- 用户问题："展示每月销售趋势"
- SQL模式：`SELECT MONTH(date), SUM(amount) FROM sales GROUP BY MONTH(date)`

### 3. 关联查询模式
**简单关联**
- 用户问题："显示用户及其订单信息"
- SQL模式：`SELECT u.name, o.* FROM users u JOIN orders o ON u.id = o.user_id`

**复杂关联**
- 用户问题："找出购买了特定产品的用户"
- SQL模式：`SELECT u.name FROM users u JOIN orders o ON u.id = o.user_id JOIN order_items oi ON o.id = oi.order_id WHERE oi.product_id = ?`

### 4. 分析查询模式
**排名查询**
- 用户问题："销售额前10的产品"
- SQL模式：`SELECT TOP 10 product_name, SUM(sales) FROM sales_data GROUP BY product_name ORDER BY SUM(sales) DESC`

**比较分析**
- 用户问题："比较今年和去年的销售情况"
- SQL模式：`SELECT YEAR(date), SUM(amount) FROM sales WHERE YEAR(date) IN (YEAR(GETDATE()), YEAR(GETDATE())-1) GROUP BY YEAR(date)`

## 查询优化技巧
1. **使用索引字段进行筛选**
2. **避免使用SELECT *，明确指定需要的字段**
3. **合理使用LIMIT限制结果集大小**
4. **优化JOIN操作的顺序和条件**
"""

@text2sql_server.prompt
def text2sql_troubleshooting_prompt() -> str:
    """Text2SQL故障排查指南"""
    return """# Text2SQL故障排查指南

## 常见问题及解决方案

### 1. 连接问题
**问题**: 数据库连接失败
**排查步骤**:
- 检查数据库服务是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认网络连接是否正常
- 检查防火墙设置

### 2. 模式识别问题
**问题**: 无法识别表或字段
**排查步骤**:
- 确认数据库模式是否已正确发现
- 检查表和字段的命名规范
- 验证用户是否有足够的访问权限
- 更新模式元数据信息

### 3. SQL生成问题
**问题**: 生成的SQL不正确
**排查步骤**:
- 检查用户问题的表达是否清晰
- 验证相关的值映射配置
- 确认表关系是否正确建立
- 分析智能体的推理过程

### 4. 查询执行问题
**问题**: SQL执行超时或失败
**排查步骤**:
- 检查查询的复杂度和数据量
- 优化查询条件和索引使用
- 调整查询超时设置
- 分析数据库性能指标

### 5. 结果准确性问题
**问题**: 查询结果不符合预期
**排查步骤**:
- 验证生成的SQL逻辑
- 检查数据质量和完整性
- 确认业务规则的正确性
- 与预期结果进行对比分析

## 监控和维护
1. **定期检查**
   - 数据库连接状态
   - 模式变更情况
   - 查询性能指标
   - 错误日志记录

2. **元数据维护**
   - 同步数据库结构变更
   - 更新业务规则和映射
   - 清理过期的配置信息
   - 优化模式注释和描述

3. **性能调优**
   - 监控查询执行时间
   - 优化常用查询模式
   - 调整系统资源配置
   - 改进智能体推理效率
"""

@text2sql_server.prompt
def schema_management_prompt() -> str:
    """数据库模式管理指南"""
    return """# 数据库模式管理指南

## 模式管理核心功能

### 1. 自动模式发现
- **功能描述**: 自动扫描和识别数据库结构
- **适用场景**: 新数据库接入、结构变更检测
- **最佳实践**: 定期执行以保持模式同步

### 2. 元数据管理
- **表级元数据**: 表名、描述、业务含义
- **字段级元数据**: 字段名、类型、约束、注释
- **关系元数据**: 外键关系、依赖关系

### 3. 值映射配置
- **枚举值映射**: 数据库编码到业务术语的映射
- **同义词管理**: 业务术语的多种表达方式
- **上下文映射**: 特定业务场景下的值映射

## 模式优化建议

### 1. 命名规范
- 使用有意义的表名和字段名
- 保持命名的一致性和可读性
- 避免使用保留字和特殊字符

### 2. 注释完善
- 为每个表添加清晰的业务描述
- 为重要字段提供详细说明
- 标注字段的业务规则和约束

### 3. 关系维护
- 正确定义外键关系
- 维护表间的逻辑依赖关系
- 建立清晰的数据流向图

## 可视化管理
1. **关系图生成**
   - 自动生成数据库关系图
   - 支持交互式的图形操作
   - 提供多种布局和样式选项

2. **依赖分析**
   - 分析表间的依赖关系
   - 识别数据流向和影响范围
   - 支持变更影响评估

3. **元数据浏览**
   - 提供友好的元数据浏览界面
   - 支持搜索和过滤功能
   - 实现元数据的在线编辑
""" 