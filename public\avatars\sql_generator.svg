<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <defs>
    <!-- 背景渐变 -->
    <radialGradient id="bgGen" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.05"/>
    </radialGradient>
    
    <!-- 代码渐变 -->
    <linearGradient id="codeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1e40af"/>
    </linearGradient>
    
    <!-- AI魔法渐变 -->
    <radialGradient id="magicGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8b5cf6"/>
      <stop offset="100%" style="stop-color:#7c3aed"/>
    </radialGradient>
    
    <!-- 输出渐变 -->
    <linearGradient id="outputGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadowGen" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="3" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glowGen" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGen)" stroke="#e2e8f0" stroke-width="2"/>
  
  <!-- 左侧代码编辑器 -->
  <g transform="translate(30, 60)" filter="url(#shadowGen)">
    <!-- 编辑器窗口 -->
    <rect x="0" y="0" width="80" height="100" rx="8" fill="url(#codeGrad)" opacity="0.9"/>
    <rect x="3" y="3" width="74" height="94" rx="6" fill="#1e293b" opacity="0.95"/>
    
    <!-- 标题栏 -->
    <rect x="3" y="3" width="74" height="15" rx="6" fill="url(#codeGrad)" opacity="0.8"/>
    <circle cx="12" cy="10" r="2" fill="#ef4444" opacity="0.8"/>
    <circle cx="20" cy="10" r="2" fill="#f59e0b" opacity="0.8"/>
    <circle cx="28" cy="10" r="2" fill="#10b981" opacity="0.8"/>
    
         <!-- 代码行条纹 -->
     <g opacity="0.6">
       <rect x="8" y="25" width="25" height="2" fill="#06b6d4" rx="1"/>
       <rect x="8" y="35" width="15" height="2" fill="#fbbf24" rx="1"/>
       <rect x="8" y="45" width="35" height="2" fill="#e5e7eb" rx="1"/>
       <rect x="8" y="55" width="20" height="2" fill="#06b6d4" rx="1"/>
       <rect x="8" y="65" width="5" height="2" fill="#f97316" rx="1"/>
     </g>
    
    <!-- 光标闪烁 -->
    <rect x="20" y="75" width="1" height="8" fill="#06b6d4">
      <animate attributeName="opacity" values="1;0;1" dur="1s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- AI魔法棒 -->
  <g transform="translate(120, 80)" filter="url(#shadowGen)">
    <!-- 魔法棒主体 -->
    <line x1="0" y1="40" x2="25" y2="15" stroke="url(#magicGrad)" stroke-width="4" stroke-linecap="round"/>
    
    <!-- 魔法棒顶端 -->
    <circle cx="25" cy="15" r="6" fill="url(#magicGrad)" filter="url(#glowGen)"/>
    <circle cx="25" cy="15" r="4" fill="#a855f7" opacity="0.9"/>
    
    <!-- 魔法星星 -->
    <g fill="url(#magicGrad)" opacity="0.8">
      <path d="M30 10 L32 15 L37 15 L33 18 L35 23 L30 20 L25 23 L27 18 L23 15 L28 15 Z" filter="url(#glowGen)"/>
      <circle cx="15" cy="5" r="1.5" fill="#fbbf24">
        <animate attributeName="r" values="1.5;3;1.5" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="40" cy="25" r="1" fill="#10b981">
        <animate attributeName="r" values="1;2;1" dur="1.8s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="35" cy="5" r="1.2" fill="#06b6d4">
        <animate attributeName="r" values="1.2;2.5;1.2" dur="2.3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="2.3s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- 魔法粒子轨迹 -->
    <path d="M25 15 Q35 25 45 35 Q55 45 65 55" stroke="#a855f7" stroke-width="2" 
          fill="none" opacity="0.4" stroke-dasharray="3,3">
      <animate attributeName="stroke-dashoffset" values="0;10;0" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 转换箭头 -->
  <g transform="translate(125, 128)">
    <defs>
      <marker id="genArrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
        <polygon points="0 0, 12 4, 0 8" fill="#f59e0b"/>
      </marker>
    </defs>
    <path d="M0 0 L30 0" stroke="#f59e0b" stroke-width="3" marker-end="url(#genArrow)" filter="url(#glowGen)"/>
    
    <!-- 数据流 -->
    <circle cx="10" cy="0" r="2" fill="#fbbf24" opacity="0.6">
      <animate attributeName="cx" values="0;30;0" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 右侧输出结果 -->
  <g transform="translate(165, 90)" filter="url(#shadowGen)">
    <!-- 输出框 -->
    <rect x="0" y="0" width="60" height="80" rx="8" fill="url(#outputGrad)" opacity="0.9"/>
    <rect x="3" y="3" width="54" height="74" rx="6" fill="white" opacity="0.95"/>
    
    <!-- 成功标识 -->
    <circle cx="12" cy="12" r="5" fill="#10b981" opacity="0.8"/>
    <path d="M9 12 L11 14 L15 10" stroke="white" stroke-width="1.5" fill="none" stroke-linecap="round"/>
    
         <!-- 生成的SQL条纹 -->
     <g opacity="0.7">
       <rect x="8" y="25" width="30" height="1.5" fill="#059669" rx="1"/>
       <rect x="8" y="33" width="25" height="1.5" fill="#059669" rx="1"/>
       <rect x="8" y="41" width="28" height="1.5" fill="#059669" rx="1"/>
       <rect x="8" y="49" width="20" height="1.5" fill="#059669" rx="1"/>
       <rect x="8" y="57" width="35" height="1.5" fill="#059669" rx="1"/>
       <rect x="8" y="65" width="15" height="1.5" fill="#059669" rx="1"/>
     </g>
     
     <!-- 质量指示器 -->
     <g transform="translate(45, 8)">
       <rect x="0" y="0" width="8" height="2" fill="#10b981" opacity="0.8"/>
     </g>
  </g>
  
     <!-- 智能提示泡泡 -->
   <g transform="translate(80, 180)" opacity="0.7">
     <ellipse cx="15" cy="8" rx="12" ry="6" fill="#3b82f6" opacity="0.2"/>
   </g>
   
   <!-- 性能指标 -->
   <g transform="translate(35, 180)">
     <rect x="0" y="0" width="2" height="8" fill="#10b981" opacity="0.8"/>
     <rect x="4" y="2" width="2" height="6" fill="#fbbf24" opacity="0.7"/>
     <rect x="8" y="4" width="2" height="4" fill="#3b82f6" opacity="0.9"/>
   </g>
   
   <!-- 代码括号装饰 -->
   <g transform="translate(40, 40)" stroke="url(#codeGrad)" stroke-width="2" fill="none" opacity="0.3">
     <path d="M0 15 Q0 0 15 0"/>
     <path d="M0 25 Q0 40 15 40"/>
   </g>
   
   <g transform="translate(180, 40)" stroke="url(#outputGrad)" stroke-width="2" fill="none" opacity="0.3">
     <path d="M15 0 Q30 0 30 15"/>
     <path d="M15 40 Q30 40 30 25"/>
   </g>
</svg> 