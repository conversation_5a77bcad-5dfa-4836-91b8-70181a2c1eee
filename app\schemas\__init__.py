# Text2SQL 基础功能schemas
from app.schemas.text2sql.text2sqlchat import (
    Text2SQLRequest,
    Text2SQLResponse,
    ResponseMessage,
    QueryMessage,
    SqlMessage,
    SqlExplanationMessage,
    SqlResultMessage,
    AnalysisMessage,
    VisualizationMessage,
    SchemaContextMessage
)

# 数据源相关schemas
from app.schemas.text2sql.datasource import (
    Text2sqlDatasourceBase,
    Text2sqlDatasourceCreate,
    Text2sqlDatasourceUpdate,
    Text2sqlDatasourceResponse,
    Text2sqlDatasourceFilter
)

# 表相关schemas
from app.schemas.text2sql.schematable import (
    Text2sqlSchematableBase,
    Text2sqlSchematableCreate,
    Text2sqlSchematableUpdate,
    Text2sqlSchematableResponse,
    Text2sqlSchematableFilter,
    Text2sqlSchematableWithRelationships
)

# 列相关schemas
from app.schemas.text2sql.schemacolumn import (
    Text2sqlSchemacolumnBase,
    Text2sqlSchemacolumnCreate,
    Text2sqlSchemacolumnUpdate,
    Text2sqlSchemacolumnResponse,
    Text2sqlSchemacolumnFilter
)

# 关系相关schemas
from app.schemas.text2sql.schemarelationship import (
    Text2sqlSchemarelationshipBase,
    Text2sqlSchemarelationshipCreate,
    Text2sqlSchemarelationshipUpdate,
    Text2sqlSchemarelationshipResponse,
    Text2sqlSchemarelationshipDetailed
)

# 值映射相关schemas
from app.schemas.text2sql.valuemapping import (
    Text2sqlValuemappingBase,
    Text2sqlValuemappingCreate,
    Text2sqlValuemappingUpdate,
    Text2sqlValuemappingResponse,
    Text2sqlValuemappingFilter
)

# 复合响应模型和批量操作模型
from app.schemas.text2sql.composite import (
    Text2sqlSchematableWithColumns,
    Text2sqlSchemacolumnWithMappings,
    Text2sqlDatasourceWithTables,
    Text2sqlSchemaFullResponse,
    BatchText2sqlSchematableCreate,
    BatchText2sqlSchemacolumnCreate,
    BatchText2sqlValuemappingCreate
)

__all__ = [
    # Text2SQL 基础功能
    "Text2SQLRequest",
    "Text2SQLResponse", 
    "ResponseMessage",
    "QueryMessage",
    "SqlMessage",
    "SqlExplanationMessage",
    "SqlResultMessage",
    "AnalysisMessage",
    "VisualizationMessage",
    
    # 数据源相关
    "Text2sqlDatasourceBase",
    "Text2sqlDatasourceCreate",
    "Text2sqlDatasourceUpdate", 
    "Text2sqlDatasourceResponse",
    "Text2sqlDatasourceFilter",
    
    # 表相关
    "Text2sqlSchematableBase",
    "Text2sqlSchematableCreate",
    "Text2sqlSchematableUpdate",
    "Text2sqlSchematableResponse", 
    "Text2sqlSchematableFilter",
    "Text2sqlSchematableWithRelationships",
    
    # 列相关
    "Text2sqlSchemacolumnBase",
    "Text2sqlSchemacolumnCreate",
    "Text2sqlSchemacolumnUpdate",
    "Text2sqlSchemacolumnResponse",
    "Text2sqlSchemacolumnFilter",
    
    # 关系相关
    "Text2sqlSchemarelationshipBase",
    "Text2sqlSchemarelationshipCreate", 
    "Text2sqlSchemarelationshipUpdate",
    "Text2sqlSchemarelationshipResponse",
    "Text2sqlSchemarelationshipDetailed",
    
    # 值映射相关
    "Text2sqlValuemappingBase",
    "Text2sqlValuemappingCreate",
    "Text2sqlValuemappingUpdate",
    "Text2sqlValuemappingResponse",
    "Text2sqlValuemappingFilter",
    
    # 复合响应模型
    "Text2sqlSchematableWithColumns",
    "Text2sqlSchemacolumnWithMappings",
    "Text2sqlDatasourceWithTables",
    "Text2sqlSchemaFullResponse",
    
    # 批量操作模型
    "BatchText2sqlSchematableCreate",
    "BatchText2sqlSchemacolumnCreate", 
    "BatchText2sqlValuemappingCreate"
] 