from fastapi import APIRouter, BackgroundTasks, HTTPException
from typing import Dict, Optional, Set
import asyncio
import uuid
import json
import time

from app.services.rag_service import rag_service
from app.schemas.rag import RAGStreamQueryEntity, RAGSessionInfo
from utils.r import R
from utils.logger import get_logger

# 获取logger实例
logger = get_logger()

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)

# 导入会话管理函数（这些函数仍在rag.py中）
from .rag import (
    get_rag_session,
    create_rag_session,
    cleanup_rag_session_resources,
    is_rag_session_connected,
    safe_send_rag_message
)


@router.post("/chat_stream",
             summary="AI助手流式查询",
             description="启动AI助手流式查询任务")
async def rag_stream_query(request: RAGStreamQueryEntity, background_tasks: BackgroundTasks):
    """
    启动AI助手流式查询任务

    支持功能：
    - 混合检索（向量+稀疏）
    - 重排序优化
    - 对话历史记忆
    - 异步处理
    """
    try:
        # 验证必需参数
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="问题内容不能为空")

        # 生成会话ID
        session_id = request.session_id or str(uuid.uuid4())
        user_id = request.user_id or "anonymous"

        # 创建或获取会话信息
        session_info = get_rag_session(session_id)
        if not session_info:
            session_info = create_rag_session(user_id, session_id)

        # 如果请求中包含对话历史，更新会话历史
        if request.chat_history:
            # 清空现有历史，使用请求提供的历史
            session_info.clear_history()
            for msg in request.chat_history:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role and content:
                    session_info.add_to_history(role, content)
            logger.info(f"AI助手会话 {session_id} 已更新对话历史，共 {len(request.chat_history)} 条记录")

        # 更新会话信息
        session_info.current_query = request.question
        session_info.last_activity = time.time()
        session_info.is_connected = True

        # 启动后台处理任务
        task = asyncio.create_task(process_assistant_query(
            question=request.question,
            collection_name=request.collection_name,
            top_k=request.top_k,
            use_hybrid=request.use_hybrid,
            use_rerank=request.use_rerank,
            session_id=session_id
        ))

        # 保存任务引用
        session_info.task = task

        # 启动资源清理任务
        background_tasks.add_task(cleanup_rag_session_resources, session_id)

        logger.info(f"AI助手流式查询已启动: session_id={session_id}, question='{request.question}'")

        return {
            "status": "success",
            "message": "AI助手流式查询已启动",
            "session_id": session_id,
            "user_id": user_id,
            "stream_url": f"/rag/stream/{session_id}",
            "chat_history_length": len(session_info.chat_history)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI助手流式查询任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI助手流式查询任务失败: {str(e)}")


# 处理AI助手查询的独立函数
async def process_assistant_query(
        question: str,
        collection_name: str,
        top_k: int,
        use_hybrid: bool,
        use_rerank: bool,
        session_id: str
):
    """处理查询任务"""
    session_info = get_rag_session(session_id)
    if not session_info:
        logger.error(f"AI助手会话 {session_id} 不存在")
        return

    try:
        logger.info(f"正在启动流式查询: {question}")

        # 获取当前会话的对话历史
        chat_history = session_info.get_history()

        # 添加当前问题到对话历史
        session_info.add_to_history("user", question)

        # 收集流式响应并返回概要
        response_parts = []
        async for sse_message in rag_service.stream_rag_query(
                question=question,
                collection_name=collection_name,
                top_k=top_k,
                use_hybrid=use_hybrid,
                use_rerank=use_rerank,
                chat_history=chat_history  # 传递对话历史
        ):
            # 检查任务是否被取消
            if asyncio.current_task().cancelled():
                logger.info(f"AI助手 {session_id} 的查询任务已被取消")
                return

            # 检查会话连接是否仍然有效
            if not is_rag_session_connected(session_id):
                logger.debug(f"AI助手 {session_id} 的SSE连接已关闭，停止发送消息")
                return

            # 解析SSE消息
            try:
                # 提取消息数据
                lines = sse_message.strip().split('\n')
                data_line = None
                for line in lines:
                    if line.startswith('data: '):
                        data_line = line[6:]  # 移除 'data: ' 前缀
                        break

                if data_line:
                    message_data = json.loads(data_line)
                    # 添加会话信息
                    message_data["sessionId"] = session_id
                    message_data["userId"] = session_info.user_id

                    # 记录token内容（仅对token类型消息）
                    if message_data.get("type") == "token":
                        token_content = message_data.get("content", "")
                        if token_content:
                            response_parts.append(token_content)

                    # 检查是否是完成消息，如果是，则更新对话历史
                    if message_data.get("type") == "complete":
                        full_answer = message_data.get("answer", "")
                        if full_answer:
                            # 添加AI回答到对话历史
                            session_info.add_to_history("assistant", full_answer)
                            logger.info(
                                f"AI助手 {session_id} 已更新对话历史，当前历史长度: {len(session_info.chat_history)}")

                    # 发送消息
                    await safe_send_rag_message(session_id, message_data)

                    # 确保流式效果，添加微小延迟
                    await asyncio.sleep(0.001)  # 1ms 微延迟

            except json.JSONDecodeError as e:
                logger.warning(f"解析SSE消息失败: {str(e)}")
                continue
            except Exception as e:
                logger.error(f"处理SSE消息时出错: {str(e)}")
                continue

        logger.info(f"AI助手 {session_id} 的查询处理完成: {question}")

    except asyncio.CancelledError:
        logger.info(f"AI助手 {session_id} 的查询处理被取消")
        raise
    except Exception as e:
        logger.error(f"AI助手 {session_id} 的查询处理错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 检查会话连接状态并发送错误消息
        if is_rag_session_connected(session_id):
            await safe_send_rag_message(session_id, {
                "type": "error",
                "content": f"❌ 处理AI助手查询时出错: {str(e)}",
                "sessionId": session_id,
                "userId": session_info.user_id,
                "timestamp": time.time()
            }) 