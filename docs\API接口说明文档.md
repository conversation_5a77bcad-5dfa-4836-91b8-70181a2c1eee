# 宇擎智库中台系统API接口说明文档

## 概述

本文档详细描述了宇擎智库中台系统的所有API接口，包括Text2SQL查询、RAG检索增强生成、知识图谱管理、统计分析等功能模块。

**基础信息**
- 基础URL: `http://your-domain/api/v1`
- 协议: HTTP/HTTPS
- 数据格式: JSON
- 编码: UTF-8

## 目录

1. [Text2SQL 聊天服务](#text2sql-聊天服务)
2. [RAG 检索增强生成](#rag-检索增强生成)  
3. [Text2SQL 系统管理](#text2sql-系统管理)
4. [Text2SQL 训练管理](#text2sql-训练管理)
5. [知识图谱管理](#知识图谱管理)
6. [缓存管理](#缓存管理)
7. [数据库统计分析](#数据库统计分析)
8. [Text2SQL 性能分析](#text2sql-性能分析)
9. [知识库统计分析](#知识库统计分析)

---

## Text2SQL 聊天服务

### 1. 启动Text2SQL查询处理
**路径**: `POST /text2sqlchatsse/query`

**功能**: 启动一个新的Text2SQL查询处理任务，支持多智能体协作模式

**请求参数**:
```json
{
  "query": "查询销售额前10的产品",
  "connection_id": "db_connection_1",
  "user_id": "user_123",
  "session_id": "session_456"
}
```

**返回值**:
```json
{
  "status": "success",
  "session_id": "session_456",
  "query_id": "query_789",
  "message": "查询处理已启动"
}
```

### 2. 获取会话流式响应
**路径**: `GET /text2sqlchatsse/stream/{session_id}`

**功能**: 通过SSE获取指定会话的实时响应流

**使用示例**:
```javascript
const eventSource = new EventSource('/api/v1/text2sqlchatsse/stream/session_456');
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log(data);
};
```

### 3. 会话管理接口
- `POST /text2sqlchatsse/feedback` - 提交会话反馈
- `GET /text2sqlchatsse/query/{query_id}/status` - 获取查询状态
- `DELETE /text2sqlchatsse/query/{query_id}` - 取消查询任务
- `DELETE /text2sqlchatsse/session/{session_id}` - 取消会话
- `GET /text2sqlchatsse/session/{session_id}/status` - 获取会话状态
- `GET /text2sqlchatsse/user/{user_id}/sessions` - 获取用户所有会话
- `DELETE /text2sqlchatsse/user/{user_id}/sessions` - 取消用户所有会话

---

## RAG 检索增强生成

### 1. 文档上传与向量化

#### 根据文档ID上传文件
**路径**: `POST /rag/uploadfilesbydocid`

**功能**: 根据指定的文档ID上传文件进行向量化处理，默认使用多模态模式，支持知识图谱增强模式

**请求参数**:
```json
{
  "collection_name": "knowledge_base",
  "use_kg": true,
  "docId": "doc_123",
  "embedding_model": "local"
}
```

**返回值**:
```json
{
  "success": true,
  "message": "文件上传成功，正在后台处理"
}
```

#### 上传文件进行向量化
**路径**: `POST /rag/uploadfiles`

**功能**: 上传多个文件进行向量化处理，默认使用多模态模式，支持知识图谱集成

**请求参数**: 
- `files`: 文件列表（multipart/form-data）
- `collection_name`: 集合名称（默认："default"）
- `docId`: 文档ID（默认：""）

**返回值**:
```json
{
  "success": true,
  "message": "文件上传成功，正在后台处理"
}
```

### 2. 向量集合管理

#### 获取向量集合列表
**路径**: `GET /rag/getcollections`

**功能**: 获取当前用户可访问的知识库集合列表及其基本信息

**查询参数**:
- `identifier`: 用户标识符（默认："admin"）

**返回值**:
```json
{
  "success": true,
  "data": [
    {
      "collection_name": "default",
      "description": "默认集合",
      "document_count": 100,
      "embedding_model": "local"
    }
  ]
}
```

#### 创建向量集合
**路径**: `POST /rag/createcollection`

**功能**: 创建新的向量数据库集合，用于存储文档向量数据

**请求参数**:
```json
{
  "collection_name": "new_collection",
  "embedding_model": "local"
}
```

**返回值**:
```json
{
  "success": true,
  "message": "集合创建成功"
}
```

#### 删除向量集合
**路径**: `POST /rag/deletecollection`

**功能**: 删除指定的向量数据库集合及其所有数据，此操作不可逆

**请求参数**:
```json
{
  "collection_name": "collection_to_delete"
}
```

#### 查询集合文档
**路径**: `POST /rag/querydocuments`

**功能**: 查询指定向量集合中的所有文档列表及其元数据信息

**请求参数**:
```json
{
  "collection_name": "default",
  "file_name": "document.pdf"
}
```

#### 删除文档
**路径**: `POST /rag/deletedocument`

**功能**: 从向量集合中删除指定的文档及其向量数据

**请求参数**:
```json
{
  "collection_name": "default",
  "file_name": "document.pdf"
}
```

### 3. RAG 查询服务

#### 获取RAG流式响应
**路径**: `GET /rag/stream/{session_id}`

**功能**: 通过SSE获取RAG查询任务的实时响应流，包括检索结果和生成内容

**查询参数**:
- `user_id`: 用户ID（可选，用于创建新会话）

**使用示例**:
```javascript
const eventSource = new EventSource('/api/v1/rag/stream/session_123?user_id=user_456');
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log(data);
};
```

#### 智能对话流式查询
**路径**: `POST /rag/knowledge_graph_chat_stream`

**功能**: 启动智能对话流式查询任务，根据配置自动选择知识图谱模式或向量模式，支持历史对话持久化

**请求参数**:
```json
{
  "question": "什么是人工智能？",
  "collection_name": "default",
  "session_id": "session_123",
  "user_id": "user_456",
  "files": ["file1.pdf", "file2.txt"],
  "chat_history": [
    {
      "role": "user",
      "content": "你好"
    },
    {
      "role": "assistant",
      "content": "你好，有什么可以帮助你的吗？"
    }
  ]
}
```

**返回值**:
```json
{
  "status": "success",
  "message": "智能对话流式查询任务已启动",
  "session_id": "session_123",
  "user_id": "user_456",
  "stream_url": "/rag/stream/session_123",
  "chat_history_length": 2,
  "database_session": true,
  "file_count": 2,
  "has_files": true
}
```

#### 问答测试
**路径**: `GET /rag/qatest`

**功能**: 使用默认集合进行问答测试，快速验证RAG系统的基本功能

**查询参数**:
- `question`: 测试问题

#### 获取文本向量
**路径**: `GET /rag/getEmebddingText`

**功能**: 获取指定文本的向量表示，用于调试和分析

**查询参数**:
- `query`: 输入文本

### 4. 会话管理

#### 取消RAG会话
**路径**: `DELETE /rag/rag_session/{session_id}`

**功能**: 取消指定的RAG查询会话，停止处理并清理相关资源

**返回值**:
```json
{
  "status": "success",
  "message": "RAG会话已取消"
}
```

#### 获取RAG会话状态
**路径**: `GET /rag/rag_session/{session_id}/status`

**功能**: 获取指定RAG会话的详细状态信息，包括查询进度和连接状态

**返回值**:
```json
{
  "status": "success",
  "data": {
    "session_id": "session_123",
    "user_id": "user_456",
    "is_connected": true,
    "has_active_task": true,
    "created_at": 1640995200,
    "last_activity": 1640995800,
    "current_query": "什么是人工智能？"
  }
}
```

### 5. 历史对话管理

#### 获取用户会话列表
**路径**: `GET /rag/chat_sessions/user/{user_id}`

**功能**: 获取指定用户的所有对话会话列表，包括会话基本信息和最后消息

**查询参数**:
- `limit`: 返回数量限制（默认：20）
- `offset`: 分页偏移量（默认：0）

**返回值**:
```json
{
  "success": true,
  "data": [
    {
      "session_id": "session_123",
      "user_id": "user_456",
      "session_title": "人工智能讨论",
      "collection_name": "default",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:30:00Z",
      "message_count": 10,
      "last_message": "谢谢你的解答"
    }
  ]
}
```

#### 搜索对话历史
**路径**: `GET /rag/chat_sessions/search`

**功能**: 在对话历史中搜索包含特定关键词的消息

**查询参数**:
- `user_id`: 用户ID
- `keyword`: 搜索关键词
- `limit`: 返回结果数量限制（默认：20）
- `offset`: 分页偏移量（默认：0）

#### 获取用户对话统计
**路径**: `GET /rag/chat_sessions/stats/user/{user_id}`

**功能**: 获取指定用户的对话统计信息，包括会话数量、消息数量等

**返回值**:
```json
{
  "success": true,
  "data": {
    "total_sessions": 50,
    "total_messages": 500,
    "avg_messages_per_session": 10,
    "active_sessions": 5,
    "last_activity": "2024-01-01T15:30:00Z"
  }
}
```

#### 获取会话详情
**路径**: `GET /rag/chat_sessions/{session_id}`

**功能**: 获取指定会话的详细信息，包括会话配置和统计信息

#### 获取会话历史记录
**路径**: `GET /rag/chat_sessions/{session_id}/history`

**功能**: 获取指定会话的对话历史记录，支持分页和消息过滤

**查询参数**:
- `limit`: 返回数量限制（默认：50）
- `offset`: 分页偏移量（默认：0）

**返回值**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "session_id": "session_123",
      "role": "user",
      "content": "什么是人工智能？",
      "timestamp": "2024-01-01T10:00:00Z"
    },
    {
      "id": 2,
      "session_id": "session_123",
      "role": "assistant",
      "content": "人工智能是...",
      "timestamp": "2024-01-01T10:00:30Z"
    }
  ]
}
```

#### 删除会话
**路径**: `DELETE /rag/chat_sessions/{session_id}`

**功能**: 删除指定的对话会话及其所有历史记录

#### 更新会话标题
**路径**: `PUT /rag/chat_sessions/{session_id}/title`

**功能**: 更新指定会话的标题

**查询参数**:
- `title`: 新的会话标题

#### 导出会话记录
**路径**: `POST /rag/chat_sessions/{session_id}/export`

**功能**: 导出指定会话的完整对话记录为结构化数据

#### 结束会话
**路径**: `POST /rag/chat_sessions/{session_id}/end`

**功能**: 将指定会话的状态设置为结束状态

#### 清理历史数据
**路径**: `POST /rag/chat_sessions/cleanup`

**功能**: 批量清理过期的历史对话数据，支持按用户和时间范围筛选

**请求参数**:
```json
{
  "user_id": "user_456",
  "days_before": 30
}
```

### 6. 任务管理

#### 获取文档嵌入状态
**路径**: `POST /rag/embedding_status`

**功能**: 查询指定文档的向量化处理状态，包括进度、完成状态和错误信息

**请求参数**:
```json
{
  "docId": "doc_123"
}
```

#### 获取所有嵌入任务
**路径**: `GET /rag/all_embedding_tasks`

**功能**: 获取当前系统中所有文档嵌入任务的状态列表，包括运行中、已完成和失败的任务

#### 清除嵌入任务
**路径**: `DELETE /rag/clear_embedding_task/{doc_id}`

**功能**: 清除指定文档ID的嵌入任务记录，释放相关资源

#### 清除已完成任务
**路径**: `GET /rag/clear_completed_tasks`

**功能**: 清除所有已完成的嵌入任务记录，保持任务列表清洁

---

## Text2SQL 系统管理

### 1. 数据库连接管理

#### 获取所有数据库连接
**路径**: `GET /text2sqlsys/connections/`

**功能**: 获取系统中配置的所有数据库连接信息

**返回值**:
```json
[
  {
    "id": 1,
    "connection_name": "MySQL生产库",
    "db_type": "mysql",
    "host": "localhost",
    "port": 3306,
    "database": "production",
    "status": "active"
  }
]
```

#### 获取指定数据库连接
**路径**: `GET /text2sqlsys/connections/{connection_id}`

**功能**: 根据连接ID获取特定数据库连接的详细信息

### 2. 数据库结构管理

#### 发现并保存数据库结构
**路径**: `POST /text2sqlsys/connections/{connection_id}/discover-and-save`

**功能**: 自动发现指定数据库连接的表结构、字段信息和关系

**返回值**:
```json
{
  "status": "success",
  "message": "数据库结构发现完成",
  "tables_discovered": 25,
  "columns_discovered": 150,
  "relationships_discovered": 12
}
```

#### 获取数据库表结构元数据
**路径**: `GET /text2sqlsys/schema/{connection_id}/metadata`

**功能**: 获取指定数据库连接的维护的表结构元数据

#### 发布数据库结构
**路径**: `POST /text2sqlsys/schema/{connection_id}/publish`

**功能**: 将数据库结构元数据发布到MySQL和图数据库中

### 3. 表和字段管理
- `PUT /text2sqlsys/schema/tables/{table_id}` - 更新表信息
- `PUT /text2sqlsys/schema/columns/{column_id}` - 更新字段信息
- `GET /text2sqlsys/relationship-tips/` - 获取关系类型提示

### 4. 值映射管理
- `GET /text2sqlsys/value-mappings/` - 获取值映射列表
- `POST /text2sqlsys/value-mappings/` - 创建值映射
- `GET /text2sqlsys/value-mappings/{mapping_id}` - 获取值映射详情
- `PUT /text2sqlsys/value-mappings/{mapping_id}` - 更新值映射
- `DELETE /text2sqlsys/value-mappings/{mapping_id}` - 删除值映射

---

## Text2SQL 训练管理

### 1. 问题和SQL生成

#### 生成示例问题
**路径**: `GET /text2sqltrain/generate_questions`

**功能**: 基于数据库结构自动生成可询问的示例问题列表

**返回值**:
```json
{
  "type": "question_list",
  "questions": [
    "查询销售额最高的产品",
    "统计每个月的订单数量",
    "找出活跃用户的分布情况"
  ],
  "header": "这里是一些您可以问的问题:"
}
```

#### 生成SQL查询
**路径**: `GET /text2sqltrain/generate_sql`

**功能**: 根据自然语言问题生成对应的SQL查询语句

**查询参数**:
- `question`: 自然语言问题
- `session_id`: 会话ID（可选）

**返回值**:
```json
{
  "type": "sql",
  "id": "history_123",
  "text": "SELECT product_name, SUM(sales_amount) FROM products GROUP BY product_name ORDER BY SUM(sales_amount) DESC LIMIT 10"
}
```

### 2. SQL执行和结果处理

#### 执行SQL查询
**路径**: `GET /text2sqltrain/run_sql`

**功能**: 执行指定问题历史记录中的SQL查询

**查询参数**:
- `history_id`: 问题历史记录ID

**返回值**:
```json
{
  "type": "df",
  "id": "history_123",
  "df": [
    {"product_name": "产品A", "sales_amount": 10000},
    {"product_name": "产品B", "sales_amount": 8000}
  ]
}
```

#### 下载CSV文件
**路径**: `GET /text2sqltrain/download_csv`

**功能**: 将查询结果导出为CSV文件

#### 生成可视化图表
**路径**: `GET /text2sqltrain/generate_plotly_figure`

**功能**: 根据问题和查询结果生成Plotly可视化图表

### 3. 训练数据管理

#### 获取训练数据
**路径**: `GET /text2sqltrain/get_training_data`

**功能**: 获取系统中的训练数据集

#### 添加训练数据
**路径**: `POST /text2sqltrain/train`

**功能**: 向系统添加新的训练数据

**请求参数**:
```json
{
  "question": "查询销售额最高的产品",
  "sql": "SELECT * FROM products ORDER BY sales DESC LIMIT 1",
  "ddl": "CREATE TABLE products...",
  "documentation": "产品表包含所有产品信息"
}
```

#### 删除训练数据
**路径**: `POST /text2sqltrain/remove_training_data`

**请求参数**:
```json
{
  "id": "training_data_123"
}
```

### 4. 问题历史管理
- `GET /text2sqltrain/get_question_history` - 获取问题历史
- `GET /text2sqltrain/get_question_detail/{history_id}` - 获取问题详情
- `DELETE /text2sqltrain/delete_question_history/{history_id}` - 删除问题历史
- `POST /text2sqltrain/clear_question_history` - 清空问题历史

---

## 知识图谱管理

### 1. 统计信息

#### 获取知识图谱统计信息
**路径**: `GET /kg/statistics`

**功能**: 获取知识图谱统计信息，包括实体、关系和文档的数量

**返回值**:
```json
{
  "entities": {
    "total_count": 1000,
    "types": ["Person", "Organization", "Location"],
    "type_counts": {"Person": 400, "Organization": 300, "Location": 300}
  },
  "relations": {
    "total_count": 500,
    "types": ["WorksFor", "LocatedIn", "PartOf"],
    "type_counts": {"WorksFor": 200, "LocatedIn": 150, "PartOf": 150}
  },
  "documents": {
    "total_count": 100
  }
}
```

#### 获取实体类型
**路径**: `GET /kg/entity-types`

**功能**: 获取知识图谱中所有实体类型及其数量

#### 获取关系类型
**路径**: `GET /kg/relation-types`

**功能**: 获取知识图谱中所有关系类型及其数量

### 2. 实体和关系查询

#### 搜索实体
**路径**: `GET /kg/search`

**功能**: 搜索知识图谱中的实体

**查询参数**:
- `query`: 搜索关键词
- `entity_types`: 实体类型过滤（可选）
- `limit`: 返回结果数量限制（默认：20）

#### 获取实体详情
**路径**: `GET /kg/entity/{entity_id}`

**功能**: 获取指定实体的详细信息，包括相关文档和关系

#### 查询知识图谱
**路径**: `GET /kg/graph`

**功能**: 查询知识图谱数据，支持多种过滤条件

**查询参数**:
- `entity_name`: 实体名称（可选）
- `entity_type`: 实体类型（可选）
- `relation_type`: 关系类型（可选）
- `max_nodes`: 最大节点数量（默认：50）

### 3. 文档管理
- `GET /kg/documents` - 获取知识图谱中的所有文档
- `GET /kg/document/{doc_id}` - 获取特定文档的知识图谱表示

---

## 缓存管理

### 1. 缓存状态监控

#### 获取缓存管理状态
**路径**: `GET /cache/status`

**功能**: 获取缓存系统的详细状态信息

**返回值**:
```json
{
  "redis_status": {
    "connected": true,
    "memory_usage": "128MB",
    "keys_count": 1500
  },
  "warmup_status": {
    "last_warmup": "2024-01-01T10:00:00Z",
    "status": "completed",
    "success_count": 50,
    "failed_count": 0
  },
  "config": {
    "cache_enabled": true,
    "default_ttl": 3600
  }
}
```

#### 缓存性能统计
**路径**: `GET /cache/performance`

**功能**: 获取缓存性能统计信息

#### 缓存健康检查
**路径**: `GET /cache/health`

**功能**: 检查缓存系统的健康状态

### 2. 缓存操作

#### 手动触发缓存预热
**路径**: `POST /cache/warmup`

**功能**: 手动触发缓存预热操作

**请求参数**:
```json
{
  "include_database_stats": true,
  "include_knowledge_stats": true,
  "max_concurrent": 3
}
```

#### 快速缓存预热
**路径**: `POST /cache/warmup/quick`

**功能**: 快速预热高优先级缓存数据

#### 清除缓存
**路径**: `DELETE /cache/clear`

**功能**: 清除所有统计缓存

#### 按模式清除缓存
**路径**: `DELETE /cache/clear/pattern`

**功能**: 按指定模式清除缓存

**查询参数**:
- `pattern`: 缓存键模式（如：`database_*`、`knowledge_*`）

### 3. 缓存信息查询
- `GET /cache/warmup/status` - 获取预热任务状态
- `GET /cache/keys` - 列出缓存键
- `POST /cache/test` - 测试缓存操作（开发调试用）

---

## 数据库统计分析

### 1. 概览统计

#### 仪表板概览统计
**路径**: `GET /database-statistics/dashboard-overview`

**功能**: 获取数据库统计的仪表板概览信息

**返回值**:
```json
{
  "database_count": 100,
  "total_records": 50000,
  "relationship_count": 200,
  "datasource_count": 5,
  "avg_fields_coverage": 85.5
}
```

#### 数据库概览统计
**路径**: `GET /database-statistics/overview`

**功能**: 获取数据库基础统计信息

**返回值**:
```json
{
  "total_tables": 100,
  "total_fields": 1500,
  "total_relationships": 200,
  "total_datasources": 5
}
```

### 2. 分布统计

#### 按数据源分布统计
**路径**: `GET /database-statistics/distribution/by-datasource`

**功能**: 按数据源统计表数量分布

#### 按数据库类型分布统计
**路径**: `GET /database-statistics/distribution/by-database-type`

**功能**: 按数据库类型统计分布情况

#### 按关系类型分布统计
**路径**: `GET /database-statistics/distribution/by-relationship-type`

**功能**: 按关系类型统计分布情况

### 3. 详细分析

#### 字段分析统计
**路径**: `GET /database-statistics/field-analysis`

**功能**: 获取字段的详细分析统计

**返回值**:
```json
{
  "avg_fields_per_table": 15.5,
  "max_fields_table": "user_profile",
  "max_fields_count": 50,
  "min_fields_table": "settings",
  "min_fields_count": 3,
  "primary_key_coverage": 98.5,
  "foreign_key_coverage": 65.2
}
```

#### 数据类型分布
**路径**: `GET /database-statistics/data-type-distribution`

**功能**: 获取数据类型的分布统计

#### 表规模分布
**路径**: `GET /database-statistics/table-size-distribution`

**功能**: 获取表规模的分布统计

### 4. 图表数据
- `GET /database-statistics/chart-data/distribution` - 获取分布图表数据
- `GET /database-statistics/chart-data/monthly-trend` - 获取月度趋势图表数据
- `GET /database-statistics/chart-data/database-type-distribution` - 获取数据库类型分布饼图数据

### 5. 趋势分析和缓存管理
- `GET /database-statistics/trend-analysis` - 获取趋势分析数据
- `GET /database-statistics/comprehensive-stats` - 获取综合统计信息
- `DELETE /database-statistics/cache/clear` - 清除统计缓存
- `GET /database-statistics/cache/status` - 获取缓存状态

---

## Text2SQL 性能分析

### 1. 性能概览

#### 获取性能概览
**路径**: `GET /text2sqlanalytics/overview`

**功能**: 获取Text2SQL服务的性能概览数据

**查询参数**:
- `days`: 统计天数（默认：7天）

**返回值**:
```json
{
  "total_executions": 1000,
  "success_rate": 95.5,
  "avg_execution_time": 2.3,
  "avg_llm_calls": 3.2,
  "error_rate": 4.5,
  "period_days": 7
}
```

#### 获取每日性能统计
**路径**: `GET /text2sqlanalytics/daily`

**功能**: 获取每日的性能统计数据，用于趋势分析

**查询参数**:
- `days`: 统计天数（默认：30天）

### 2. 智能体性能分析

#### 获取智能体性能统计
**路径**: `GET /text2sqlanalytics/agents/performance`

**功能**: 获取各个智能体的性能统计数据

**查询参数**:
- `days`: 统计天数（默认：7天）

**返回值**:
```json
[
  {
    "agent_name": "sql_generator",
    "total_executions": 500,
    "success_count": 475,
    "success_rate": 95.0,
    "avg_execution_time": 1.8,
    "avg_llm_calls": 2.5,
    "error_count": 25
  }
]
```

### 3. 执行记录分析

#### 获取最近执行记录
**路径**: `GET /text2sqlanalytics/executions/recent`

**功能**: 获取最近的执行记录

**查询参数**:
- `limit`: 返回数量限制（默认：20）
- `status`: 执行状态过滤（可选）

#### 获取错误统计摘要
**路径**: `GET /text2sqlanalytics/errors/summary`

**功能**: 获取错误类型的统计摘要

**查询参数**:
- `days`: 统计天数（默认：7天）

**返回值**:
```json
{
  "sql_syntax_error": 10,
  "connection_error": 5,
  "timeout_error": 3,
  "llm_error": 2
}
```

### 4. 综合分析
- `GET /text2sqlanalytics/comprehensive` - 获取综合分析数据
- `GET /text2sqlanalytics/metrics/custom` - 获取自定义指标
- `GET /text2sqlanalytics/health` - 健康检查
- `GET /text2sqlanalytics/realtime/stats` - 获取实时统计
- `GET /text2sqlanalytics/export/csv` - 导出CSV数据

---

## 知识库统计分析

### 1. 概览统计

#### 获取AI对话统计概览
**路径**: `GET /knowledge-statistics/overview`

**功能**: 获取AI对话系统的统计概览

**返回值**:
```json
{
  "total_conversations": 5000,
  "active_users": 1200,
  "avg_session_duration": 15.5,
  "satisfaction_rate": 89.2
}
```

#### 获取用户统计信息
**路径**: `GET /knowledge-statistics/user-stats`

**功能**: 获取用户相关的统计信息

**返回值**:
```json
{
  "new_users": 100,
  "returning_users": 800,
  "total_active_users": 900
}
```

### 2. 趋势分析

#### 获取最近7天的对话趋势
**路径**: `GET /knowledge-statistics/weekly-trend`

**功能**: 获取最近7天的对话趋势数据

**返回值**:
```json
{
  "trends": [
    {"date": "2024-01-01", "conversations": 150},
    {"date": "2024-01-02", "conversations": 180},
    {"date": "2024-01-03", "conversations": 200}
  ],
  "total_conversations": 1200
}
```

#### 获取24小时内对话分布
**路径**: `GET /knowledge-statistics/hourly-stats`

**功能**: 获取24小时内的对话分布统计

#### 获取月度趋势
**路径**: `GET /knowledge-statistics/monthly-trend`

**功能**: 获取月度对话趋势数据

### 3. 内容分析

#### 获取热门问题统计
**路径**: `GET /knowledge-statistics/popular-questions`

**功能**: 获取用户最常问的问题统计

**返回值**:
```json
{
  "popular_questions": [
    {
      "question": "如何使用系统？",
      "count": 50,
      "percentage": 15.5
    },
    {
      "question": "价格是多少？",
      "count": 30,
      "percentage": 9.3
    }
  ]
}
```

#### 获取满意度统计
**路径**: `GET /knowledge-statistics/satisfaction-stats`

**功能**: 获取详细的满意度统计信息

**返回值**:
```json
{
  "total_feedbacks": 500,
  "positive_feedbacks": 450,
  "negative_feedbacks": 50,
  "satisfaction_rate": 90.0
}
```

### 4. 高级分析
- `GET /knowledge-statistics/comprehensive-report` - 获取综合报告
- `GET /knowledge-statistics/performance-metrics` - 获取性能指标
- `GET /knowledge-statistics/usage-patterns` - 获取使用模式分析
- `GET /knowledge-statistics/content-analysis` - 获取内容分析
- `GET /knowledge-statistics/user-engagement` - 获取用户参与度分析

### 5. 数据导出和缓存
- `GET /knowledge-statistics/export-data` - 导出统计数据
- `GET /knowledge-statistics/database-health` - 数据库健康检查
- `DELETE /knowledge-statistics/cache/clear` - 清除统计缓存
- `GET /knowledge-statistics/cache/status` - 获取缓存状态

---

## 错误码说明

### 通用错误码
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

### 业务错误码
- `1001`: 数据库连接失败
- `1002`: SQL语法错误
- `1003`: 权限不足
- `1004`: 会话已过期
- `1005`: 文件上传失败
- `1006`: 向量化处理失败
- `1007`: 缓存操作失败

## 使用示例

### 完整的Text2SQL查询流程

```python
import requests
import json
from sseclient import SSEClient

# 1. 启动查询
response = requests.post('/api/v1/text2sqlchatsse/query', json={
    "query": "查询销售额前10的产品",
    "connection_id": "db_connection_1",
    "user_id": "user_123"
})

session_id = response.json()['session_id']

# 2. 获取流式响应
url = f'/api/v1/text2sqlchatsse/stream/{session_id}'
messages = SSEClient(url)

for msg in messages:
    data = json.loads(msg.data)
    print(f"类型: {data['type']}, 内容: {data['content']}")
    
    if data['type'] == 'complete':
        break
```

### RAG查询示例

```
import requests
import json
from sseclient import SSEClient

# 1. 上传文档
files = {'files': open('document.pdf', 'rb')}
data = {'collection_name': 'my_kb', 'docId': 'doc_123'}

response = requests.post('/api/v1/rag/uploadfiles', files=files, data=data)
print(response.json())

# 2. 启动智能对话流式查询
response = requests.post('/api/v1/rag/knowledge_graph_chat_stream', json={
    'question': '什么是人工智能？',
    'collection_name': 'my_kb',
    'session_id': 'session_456',
    'user_id': 'user_123',
    'files': ['document.pdf'],
    'chat_history': [
        {'role': 'user', 'content': '你好'},
        {'role': 'assistant', 'content': '你好，有什么可以帮助你的吗？'}
    ]
})

session_id = response.json()['session_id']
stream_url = response.json()['stream_url']

# 3. 获取流式响应
url = f'/api/v1{stream_url}?user_id=user_123'
messages = SSEClient(url)

for msg in messages:
    if msg.event == 'message':
        data = json.loads(msg.data)
        print(f"类型: {data['type']}, 内容: {data['content']}")
        
        if data['type'] == 'complete':
            break
    elif msg.event == 'heartbeat':
        print("心跳消息")

# 4. 获取会话历史
response = requests.get(f'/api/v1/rag/chat_sessions/{session_id}/history', params={
    'limit': 10,
    'offset': 0
})
print("会话历史:", response.json())

# 5. 获取用户所有会话
response = requests.get('/api/v1/rag/chat_sessions/user/user_123', params={
    'limit': 20,
    'offset': 0
})
print("用户会话列表:", response.json())

# 6. 搜索对话历史
response = requests.get('/api/v1/rag/chat_sessions/search', params={
    'user_id': 'user_123',
    'keyword': '人工智能',
    'limit': 10
})
print("搜索结果:", response.json())

# 7. 获取用户对话统计
response = requests.get('/api/v1/rag/chat_sessions/stats/user/user_123')
print("用户统计:", response.json())

# 8. 简单问答测试
response = requests.get('/api/v1/rag/qatest', params={
    'question': '测试问题'
})
print("问答测试结果:", response.json())
```

## 注意事项

1. **认证**: 某些接口可能需要用户认证，请确保提供有效的用户凭证
2. **速率限制**: 部分接口有速率限制，请合理控制请求频率
3. **数据格式**: 所有请求和响应都使用UTF-8编码的JSON格式
4. **错误处理**: 请妥善处理错误响应，根据错误码进行相应的处理
5. **流式响应**: SSE接口需要保持长连接，注意连接管理
6. **文件上传**: 文件上传接口使用multipart/form-data格式
7. **缓存**: 统计类接口使用缓存机制，数据可能有一定延迟

---

**文档版本**: v1.0  
**最后更新**: 2024年1月  
**联系方式**: 请联系系统管理员获取更多支持 