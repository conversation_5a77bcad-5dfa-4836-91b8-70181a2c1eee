from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from utils.kg_utils import KnowledgeGraphManager

router = APIRouter(
    responses={
        400: {"description": "请求参数错误"},
        404: {"description": "资源不存在"},
        500: {"description": "服务器内部错误"}
    }
)

# 模型定义
class EntityBase(BaseModel):
    name: str = Field(..., description="实体名称")
    type: str = Field(..., description="实体类型")

class EntityResponse(EntityBase):
    id: str = Field(..., description="实体ID")

class RelationBase(BaseModel):
    source: str = Field(..., description="源实体名称")
    target: str = Field(..., description="目标实体名称")
    relation: str = Field(..., description="关系类型")
    description: str = Field(..., description="关系描述")

class GraphData(BaseModel):
    nodes: List[Dict[str, Any]] = Field(default_factory=list, description="图谱节点")
    links: List[Dict[str, Any]] = Field(default_factory=list, description="图谱连接")

class KGStatistics(BaseModel):
    entities: Dict[str, Any] = Field(..., description="实体统计信息")
    relations: Dict[str, Any] = Field(..., description="关系统计信息")
    documents: Dict[str, Any] = Field(..., description="文档统计信息")

class EntityDetail(BaseModel):
    entity: Dict[str, Any] = Field(..., description="实体基本信息")
    documents: List[Dict[str, Any]] = Field(default_factory=list, description="相关文档")
    outgoing_relations: List[Dict[str, Any]] = Field(default_factory=list, description="出向关系")
    incoming_relations: List[Dict[str, Any]] = Field(default_factory=list, description="入向关系")
    source_id: str = Field("", description="源文本块ID")

class DocumentGraph(BaseModel):
    doc_id: str = Field(..., description="文档ID")
    nodes: List[Dict[str, Any]] = Field(default_factory=list, description="图谱节点")
    links: List[Dict[str, Any]] = Field(default_factory=list, description="图谱连接")

class TextExtractionRequest(BaseModel):
    text: str = Field(..., description="待提取的文本内容")

class TextExtractionResponse(BaseModel):
    entities: List[Dict[str, str]] = Field(default_factory=list, description="提取的实体")
    relations: List[Dict[str, str]] = Field(default_factory=list, description="提取的关系")

class TypesResponse(BaseModel):
    types: List[str] = Field(..., description="类型列表")
    counts: Dict[str, int] = Field(..., description="每种类型的数量")

class DocumentInfo(BaseModel):
    id: str = Field(..., description="文档ID")
    title: str = Field(..., description="文档标题")
    type: Optional[str] = Field(None, description="文档类型")

# 获取知识图谱管理器实例的依赖函数
async def get_kg_manager():
    return KnowledgeGraphManager.get_instance()

@router.get("/statistics", response_model=KGStatistics)
async def get_statistics(kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)):
    """
    获取知识图谱统计信息，包括实体、关系和文档的数量和类型分布
    """
    try:
        statistics = await kg_manager.get_kg_statistics()
        return statistics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识图谱统计信息失败: {str(e)}")

@router.get("/entity-types", response_model=TypesResponse)
async def get_entity_types(kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)):
    """
    获取知识图谱中所有实体类型及其数量
    """
    try:
        statistics = await kg_manager.get_kg_statistics()
        entity_types = statistics.get("entities", {}).get("types", [])
        type_counts = statistics.get("entities", {}).get("type_counts", {})
        return {"types": entity_types, "counts": type_counts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体类型失败: {str(e)}")

@router.get("/relation-types", response_model=TypesResponse)
async def get_relation_types(kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)):
    """
    获取知识图谱中所有关系类型及其数量
    """
    try:
        statistics = await kg_manager.get_kg_statistics()
        relation_types = statistics.get("relations", {}).get("types", [])
        type_counts = statistics.get("relations", {}).get("type_counts", {})
        return {"types": relation_types, "counts": type_counts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取关系类型失败: {str(e)}")

@router.get("/documents", response_model=List[DocumentInfo])
async def get_documents(
    kg_manager: KnowledgeGraphManager = Depends(get_kg_manager),
    limit: int = Query(100, description="最大返回文档数量")
):
    """
    获取知识图谱中的所有文档
    """
    try:
        driver = await kg_manager.neo4j_driver
        
        with driver.session(database=kg_manager.neo4j_db) as session:
            query = """
            MATCH (d:Document)
            RETURN d.id as id, d.title as title, d.type as type
            LIMIT $limit
            """
            
            result = session.run(query, {"limit": limit})
            documents = [{"id": record["id"], "title": record["title"], "type": record["type"]} 
                        for record in result]
            
            return documents
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

@router.get("/search", response_model=List[EntityResponse])
async def search_entities(
    query: str = Query(..., description="搜索关键词"),
    entity_types: Optional[List[str]] = Query(None, description="要搜索的实体类型"),
    limit: int = Query(20, description="返回结果数量限制"),
    kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)
):
    """
    搜索知识图谱中的实体
    """
    try:
        entities = await kg_manager.search_entities(query=query, entity_types=entity_types, limit=limit)
        return entities
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索实体失败: {str(e)}")

@router.get("/entity/{entity_id}", response_model=EntityDetail)
async def get_entity_details(
    entity_id: str,
    kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)
):
    """
    获取指定实体的详细信息，包括相关文档和关系
    """
    try:
        details = await kg_manager.get_entity_details(entity_id)
        if "error" in details:
            raise HTTPException(status_code=404, detail=details["error"])
        return details
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体详情失败: {str(e)}")

@router.get("/graph", response_model=GraphData)
async def query_graph(
    entity_name: Optional[str] = Query(None, description="实体名称"),
    entity_type: Optional[str] = Query(None, description="实体类型"),
    relation_type: Optional[str] = Query(None, description="关系类型"),
    max_nodes: int = Query(50, description="最大节点数量"),
    kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)
):
    """
    查询知识图谱，可通过实体名称、类型和关系类型进行过滤
    """
    try:
        graph_data = await kg_manager.query_knowledge_graph(
            entity_name=entity_name,
            entity_type=entity_type,
            relation_type=relation_type,
            max_nodes=max_nodes
        )
        return graph_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询知识图谱失败: {str(e)}")

@router.get("/document/{doc_id}", response_model=DocumentGraph)
async def get_document_graph(
    doc_id: str,
    kg_manager: KnowledgeGraphManager = Depends(get_kg_manager)
):
    """
    获取特定文档的知识图谱表示
    """
    try:
        doc_graph = await kg_manager.get_document_entities(doc_id)
        return doc_graph
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档图谱失败: {str(e)}")
