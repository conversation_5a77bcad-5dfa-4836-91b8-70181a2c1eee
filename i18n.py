#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化配置文件
Internationalization configuration file

支持语言 / Supported Languages:
- zh_CN: 中文（简体）/ Chinese (Simplified)
- en: 英文 / English
"""

# 翻译字典 / Translation Dictionary
TRANSLATIONS = {
    "zh_CN": {
        "knowledge_starters": {
            "hazard_label": "危化品库隐患排查标准",
            "hazard_message": "以表格的形式展示危化品库隐患排查标准",
            "voucher_label": "原始凭证分类",
            "voucher_message": "描述一下原始凭证分类有哪些",
            "performance_label": "性能测试分析及瓶颈定位思路",
            "performance_message": "详细描述一下软件性能测试分析及瓶颈定位的核心思路。",
            "llm_label": "如何学习大模型应用的核心技术",
            "llm_message": "给出学习大语言模型的一些重要的技术和方法。"
        },
        "database_starters": {
            "users_label": "每个部门有多少用户",
            "users_message": "获取每个部门的用户数,并以饼状图输出",
            "devices_label": "每个部门有多少在用设备",
            "devices_message": "获取每个部门在用设备的数量，柱状图输出",
            "repair_label": "每个部门有多少维修中的设备",
            "repair_message": "获取每个部门维修中的设备的数量，柱状图输出",
            "scrap_label": "每个部门有多少报废的设备",
            "scrap_message": "获取每个部门报废设备的数量，柱状图输出"
        },
        "process_starters": {
            "daily_label": "每日一练报表",
            "daily_message": "查一下每日一练报表",
            "training_label": "培训教育矩阵",
            "training_message": "查一下培训教育矩阵"
        },
        "chat_profiles": {
            "knowledge_name": "知识库对话",
            "knowledge_desc": "与知识库对话",
            "database_name": "数据库对话",
            "database_desc": "与数据库对话",
            "process_name": "全流程对话",
            "process_desc": "与全流程对话"
        },
        "settings": {
            "collection_label": "知识库分类"
        },
        "status_messages": {
            "thinking": "🧠 正在思考您的问题...",
            "generating_sql": "🔎 正在生成SQL查询...",
            "validating_sql": "✔️ 验证SQL语法...",
            "executing_query": "🚀 执行数据库查询...",
            "query_complete": "🎉 查询完成！正在整理结果...",
            "sql_error": "⚠️ SQL语法错误，请重新描述您的需求",
            "database_error": "⚠️ 数据库查询失败",
            "processing_files": "💼 处理当前文件...",
            "checking_config": "🔎 检查知识库配置...",
            "generating_answer": "✨ 正在生成回答...",
            "processing_upload": "⏳ 正在处理您上传的文件...",
            "file_complete": "🎉 文件处理完成",
            "loading_personal": "🏛️ 加载私人知识库...",
            "searching_kg": "🔗 正在搜索知识图谱...",
            "searching_vector": "🔎 正在搜索向量数据库...",
            "searching_web": "🌍 正在搜索互联网...",
            "mcp_developing": "🚧 MCP服务功能正在开发中，敬请期待。",
            "no_response": "对不起，我无法为您的问题提供有效答案，请尝试重新表述您的问题。",
            "system_error": "⚠️ 系统未初始化聊天引擎，请刷新页面重试",
            "data_sources": "**数据来源**",
            "entity_info": "实体信息",
            "source_prefix": "文本来源_",
            "img_source_prefix": "图片来源_",
            "chart_content": "生成的图表如下：",
            "report_content": "这是您请求的数据报表:",
            "data_fetch_failed": "⚠️ 数据获取失败",
            "incomplete_result": "⚠️ 未获取到完整的SQL执行结果",
            "processing_result": "📊 处理查询结果...",
            "getting_report": "🌍 获取报表数据...",
            "generating_pdf": "📈 生成PDF报表..."
        },
        "system_prompts": {
            "vector_chat": "你是一个专业的助手，使用以下上下文来回答用户提出的问题。如果上下文和用户问题没有关联或者上下文不存在，就回答不知道答案，不要试图编造答案。",
            "web_search": "你的职责是根据用户的指令调用搜索互联网工具 `websearchtool`，根据搜索结果进行总结，注意格式美化，如果没有搜索结果，就回答不知道。",
            "web_search_tool_desc": "从互联网搜索信息"
        }
    },
    "en": {
        "knowledge_starters": {
            "hazard_label": "Hazardous Chemical Storage Inspection Standards",
            "hazard_message": "Display hazardous chemical storage inspection standards in table format",
            "voucher_label": "Original Voucher Classification",
            "voucher_message": "Describe the types of original voucher classifications",
            "performance_label": "Performance Testing Analysis and Bottleneck Identification",
            "performance_message": "Describe in detail the core ideas of software performance testing analysis and bottleneck identification.",
            "llm_label": "How to Learn Core Technologies of Large Model Applications",
            "llm_message": "Provide important technologies and methods for learning large language models."
        },
        "database_starters": {
            "users_label": "How many users in each department",
            "users_message": "Get the number of users in each department and output as a pie chart",
            "devices_label": "How many devices in use in each department",
            "devices_message": "Get the number of devices in use in each department, output as bar chart",
            "repair_label": "How many devices under repair in each department",
            "repair_message": "Get the number of devices under repair in each department, output as bar chart",
            "scrap_label": "How many scrapped devices in each department",
            "scrap_message": "Get the number of scrapped devices in each department, output as bar chart"
        },
        "process_starters": {
            "daily_label": "Daily Practice Report",
            "daily_message": "Check the daily practice report",
            "training_label": "Training Education Matrix",
            "training_message": "Check the training education matrix"
        },
        "chat_profiles": {
            "knowledge_name": "Knowledge Base Chat",
            "knowledge_desc": "Chat with knowledge base",
            "database_name": "Database Chat",
            "database_desc": "Chat with database",
            "process_name": "Full Process Chat",
            "process_desc": "Chat with full process"
        },
        "settings": {
            "collection_label": "Knowledge Base Category"
        },
        "status_messages": {
            "thinking": "🧠 Thinking about your question...",
            "generating_sql": "🔎 Generating SQL query...",
            "validating_sql": "✔️ Validating SQL syntax...",
            "executing_query": "🚀 Executing database query...",
            "query_complete": "🎉 Query completed! Organizing results...",
            "sql_error": "⚠️ SQL syntax error, please rephrase your request",
            "database_error": "⚠️ Database query failed",
            "processing_files": "💼 Processing current files...",
            "checking_config": "🔎 Checking knowledge base configuration...",
            "generating_answer": "✨ Generating answer...",
            "processing_upload": "⏳ Processing your uploaded files...",
            "file_complete": "🎉 File processing completed",
            "loading_personal": "🏛️ Loading personal knowledge base...",
            "searching_kg": "🔗 Searching knowledge graph...",
            "searching_vector": "🔎 Searching vector database...",
            "searching_web": "🌍 Searching the internet...",
            "mcp_developing": "🚧 MCP service functionality is under development, please stay tuned.",
            "no_response": "Sorry, I cannot provide a valid answer to your question. Please try rephrasing your question.",
            "system_error": "⚠️ System chat engine not initialized, please refresh the page and try again",
            "data_sources": "**Data Sources**",
            "entity_info": "Entity Information",
            "source_prefix": "TxtSource_",
            "img_source_prefix": "ImgSource_",
            "chart_content": "Generated chart:",
            "report_content": "This is the data report you requested:",
            "data_fetch_failed": "⚠️ Data retrieval failed",
            "incomplete_result": "⚠️ Incomplete SQL execution results",
            "processing_result": "📊 Processing query results...",
            "getting_report": "🌍 Getting report data...",
            "generating_pdf": "📈 Generating PDF report..."
        },
        "system_prompts": {
            "vector_chat": "You are a professional assistant. Use the following context to answer user questions. If the context is not related to the user's question or the context does not exist, answer that you don't know the answer and don't try to make up answers.",
            "web_search": "Your job is to call the `websearchtool` internet search tool according to the user's instructions, summarize based on the search results, pay attention to format beautification, and answer that you don't know if there are no search results.",
            "web_search_tool_desc": "Search for information from the internet"
        }
    }
}

def get_translation(locale: str = "zh_CN") -> dict:
    """
    获取对应语言的翻译
    Get translation for the specified language
    
    Args:
        locale (str): 语言代码 / Language code (zh_CN, en)
        
    Returns:
        dict: 翻译字典 / Translation dictionary
    """
    return TRANSLATIONS.get(locale, TRANSLATIONS["zh_CN"])

def get_supported_languages() -> list:
    """
    获取支持的语言列表
    Get list of supported languages
    
    Returns:
        list: 支持的语言代码列表 / List of supported language codes
    """
    return list(TRANSLATIONS.keys())

def add_translation(locale: str, translations: dict) -> bool:
    """
    添加新的翻译
    Add new translation
    
    Args:
        locale (str): 语言代码 / Language code
        translations (dict): 翻译字典 / Translation dictionary
        
    Returns:
        bool: 是否成功添加 / Whether successfully added
    """
    try:
        TRANSLATIONS[locale] = translations
        return True
    except Exception as e:
        print(f"Failed to add translation for {locale}: {e}")
        return False

def validate_translation_structure(translations: dict) -> bool:
    """
    验证翻译结构是否完整
    Validate translation structure completeness
    
    Args:
        translations (dict): 翻译字典 / Translation dictionary
        
    Returns:
        bool: 结构是否完整 / Whether structure is complete
    """
    required_keys = [
        "knowledge_starters", "database_starters", "process_starters",
        "chat_profiles", "settings", "status_messages", "system_prompts"
    ]
    
    for key in required_keys:
        if key not in translations:
            print(f"Missing required key: {key}")
            return False
    
    return True 