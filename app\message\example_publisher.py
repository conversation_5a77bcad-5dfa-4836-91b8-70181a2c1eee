from app.message.myredis import RedisClient, RedisPublisher, get_redis_client, get_redis_publisher
import json
import time

# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()

def main():
    """
    Redis发布者使用示例
    """
    try:
        # 方式1：创建默认Redis客户端（使用统一配置）
        redis_client = get_redis_client()
        
        # 方式2：如果需要自定义配置，可以覆盖特定参数
        # redis_client = RedisClient(password='custom_password')
        
        # 方式3：直接使用便捷函数获取发布者
        # publisher = get_redis_publisher()
        
        # 创建发布者
        publisher = RedisPublisher(redis_client)
        
        # 检查连接状态
        if not redis_client.is_connected():
            logger.warning("Redis连接失败，无法发送消息")
            return
        
        # 获取连接信息
        conn_info = redis_client.get_connection_info()
        logger.info(f"Redis连接信息: {conn_info}")
        
        # 发布频道名称
        channel = "embedding"
        
        # 发布字符串消息
        message = "这是一条测试消息"
        receivers = publisher.publish(channel, message)
        logger.info(f"发布字符串消息到'{channel}'频道，{receivers}个订阅者接收")
        
        # 发布JSON消息
        data = {
            "id": 1,
            "type": "notification",
            "content": "这是一条JSON测试消息",
            "timestamp": time.time()
        }
        receivers = publisher.publish_dict(channel, data)
        logger.info(f"发布JSON消息到'{channel}'频道，{receivers}个订阅者接收")
        
        # 演示配置管理
        from config.cache_config import redis_config
        logger.info(f"当前Redis配置: {redis_config.REDIS_HOST}:{redis_config.REDIS_PORT}")
        
    except Exception as e:
        logger.error(f"示例运行出错: {e}")

def demo_health_check():
    """
    演示Redis健康检查
    """
    from app.message.myredis import check_redis_health
    
    if check_redis_health():
        logger.info("✅ Redis连接健康")
    else:
        logger.warning("❌ Redis连接异常")

if __name__ == "__main__":
    # 运行基本示例
    main()
    
    # 演示健康检查
    demo_health_check() 