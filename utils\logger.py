import os
import sys
from datetime import datetime
from loguru import logger

# 定义日志文件存放目录
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 定义日志文件名格式
log_file_name = os.path.join(LOG_DIR, f"log_{datetime.now().strftime('%Y%m%d')}.log")

# 移除默认处理器
logger.remove()

# 添加控制台处理器，显示彩色日志
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO",
    colorize=True,
)

# 添加文件处理器，记录所有INFO级别以上的日志
logger.add(
    log_file_name,
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{line} - {message}",
    level="INFO",
    rotation="10 MB",  # 日志文件大小达到10MB时轮转
    retention="30 days",  # 保留30天的日志
    compression="zip",  # 压缩旧日志
    encoding="utf-8",  # 使用UTF-8编码
    backtrace=True,  # 显示完整的异常堆栈
    diagnose=True,   # 显示变量值，帮助诊断问题
)

# 设置异常捕获处理
# logger.add(
#     os.path.join(LOG_DIR, "error_{time}.log"),
#     format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{line} - {message}",
#     level="ERROR",
#     rotation="10 MB",
#     retention="30 days",
#     compression="zip",
#     encoding="utf-8",
#     backtrace=True,  # 显示完整的异常堆栈
#     diagnose=True,   # 显示变量值，帮助诊断问题
# )

# 拦截所有未处理的异常
def handle_exception(exc_type, exc_value, exc_traceback):
    """捕获未处理的异常并记录"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 对于键盘中断，使用系统默认处理
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.opt(exception=(exc_type, exc_value, exc_traceback)).error("未捕获的异常")

# 设置全局异常处理
sys.excepthook = handle_exception

# 设置常用函数，方便在项目中使用
def get_logger():
    """获取logger实例"""
    return logger 