from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class Text2sqlSchemacolumnBase(BaseModel):
    """数据库列基础模型"""
    table_id: int = Field(..., description="表ID")
    column_name: str = Field(..., description="列名", max_length=255)
    data_type: str = Field(..., description="数据类型", max_length=100)
    description: Optional[str] = Field(None, description="列描述")
    is_primary_key: Optional[bool] = Field(False, description="是否为主键")
    is_foreign_key: Optional[bool] = Field(False, description="是否为外键")
    is_unique: Optional[bool] = Field(False, description="是否唯一")


class Text2sqlSchemacolumnCreate(Text2sqlSchemacolumnBase):
    """创建数据库列请求模型"""
    pass


class Text2sqlSchemacolumnUpdate(BaseModel):
    """更新数据库列请求模型"""
    column_name: Optional[str] = Field(None, description="列名", max_length=255)
    data_type: Optional[str] = Field(None, description="数据类型", max_length=100)
    description: Optional[str] = Field(None, description="列描述")
    is_primary_key: Optional[bool] = Field(None, description="是否为主键")
    is_foreign_key: Optional[bool] = Field(None, description="是否为外键")
    is_unique: Optional[bool] = Field(None, description="是否唯一")


class Text2sqlSchemacolumnResponse(Text2sqlSchemacolumnBase):
    """数据库列响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="列ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class Text2sqlSchemacolumnFilter(BaseModel):
    """列查询过滤器"""
    table_id: Optional[int] = Field(None, description="表ID")
    column_name: Optional[str] = Field(None, description="列名")
    data_type: Optional[str] = Field(None, description="数据类型")
    is_primary_key: Optional[bool] = Field(None, description="是否为主键")
    is_foreign_key: Optional[bool] = Field(None, description="是否为外键") 