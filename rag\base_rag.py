import json
import os
import re
import asyncio
from abc import abstractmethod
from typing import List, Dict, Any, Optional
from llama_index.core import VectorStoreIndex, load_index_from_storage
from llama_index.core.indices.base import BaseIndex
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.storage.storage_context import DEFAULT_PERSIST_DIR, StorageContext
from llama_index.vector_stores.milvus import MilvusVectorStore
from pymilvus import MilvusClient

from rag.local_mode_retriever import LocalModeRetriever
from config import settings
from utils.CustomizedEmbeddingFunction import CustomizedEmbeddingFunction
import time
# 配置日志
from utils.logger import get_logger
# 获取logger实例
logger = get_logger()
# 添加知识图谱相关的导入


# from utils.CustomizedEmbeddingFunction import CustomizedEmbeddingFunction


# pip install llama-index-vector-stores-milvus
class RAG:
    def __init__(self, files: list[str] = None,
                 milvus_uri: Optional[str] = None,
                 milvus_collection_prefix: Optional[str] = None,
                 use_kg: bool = True,
                 embed_model = None):
        """
        初始化RAG系统

        参数:
            files: 要处理的文件列表，可以是本地文件路径或URL
            milvus_uri: Milvus服务的URI，如果为None则使用默认配置
            milvus_collection_prefix: Milvus集合名称前缀，如果为None则使用默认配置
            use_kg: 是否启用知识图谱功能
            embed_model: 嵌入模型实例，如果为None则使用全局默认模型
        """
        self.files = files or []
        self.milvus_uri = milvus_uri or settings.configuration.milvus_uri
        self.milvus_collection_prefix = milvus_collection_prefix
        self.documents = []
        self.kg_documents = []  # 知识图谱相关的文档
        self.use_kg = use_kg
        self.kg_manager = None  # 懒加载
        self.entity_vectorizer = None  # 懒加载
        self.embed_model = embed_model  # 存储嵌入模型

        # if self.use_kg:
        #     # 仅初始化引用，实际实例在需要时创建
        #     self._ensure_kg_initialized_future = asyncio.ensure_future(self._ensure_kg_initialized())
        # else:
        #     self._ensure_kg_initialized_future = None

    @staticmethod
    def is_remote_file(path):
        """判断是否为远程文件路径"""
        # 判断常见的URL协议
        if path.startswith(('http://', 'https://', 'ftp://', 'sftp://', 's3://')):
            return True

        # 检查是否为有效的网络路径格式
        url_pattern = re.compile(
            r'^(?:http|ftp)s?://'  # http://, https://, ftp://, ftps://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # 域名
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # 可选端口
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return bool(url_pattern.match(path))

    async def _ensure_kg_initialized(self):
        """确保知识图谱组件已初始化"""
        if not self.use_kg:
            return

        try:
            # 导入需要的模块
            from utils.kg_utils import KnowledgeGraphManager
            from rag.entity_vectorizer import EntityVectorizer

            # 初始化知识图谱管理器
            if self.kg_manager is None:
                self.kg_manager = KnowledgeGraphManager.get_instance()

            # 初始化实体向量化工具
            if self.entity_vectorizer is None:
                self.entity_vectorizer = EntityVectorizer(
                    milvus_uri=self.milvus_uri,
                    milvus_collection_prefix=self.milvus_collection_prefix,
                    embed_model=self.embed_model
                )

            # 确保Neo4j数据库结构已初始化
            await self.kg_manager.setup_database()

            logger.info("知识图谱组件初始化完成")

        except Exception as e:
            logger.info(f"初始化知识图谱组件时出错: {e}")
            import traceback
            traceback.print_exc()
            # 禁用知识图谱功能
            self.use_kg = False

    async def process_kg_nodes(self, nodes: List, doc_metadata: Optional[Dict[str, Any]] = None, collection_name="default") -> List:
        """
        从已分块的节点列表中提取知识图谱实体和关系，并处理知识图谱向量化
        
        优化点:
        - 增加批处理机制提高效率
        - 添加节点过滤，忽略低质量内容
        - 添加进度指示和详细日志
        - 错误处理和恢复机制

        参数:
            nodes: 分块后的节点列表
            doc_metadata: 可选的文档元数据

        返回:
            List: 处理后的节点列表
        """
        if not self.use_kg or not nodes:
            return nodes

        # 统计开始时间
        start_time = time.time()
        logger.info(f"开始处理知识图谱节点，共 {len(nodes)} 个节点")

        # 确保知识图谱已初始化
        await self._ensure_kg_initialized()

        # 过滤质量过低的节点（文本太短或无意义的节点）
        filtered_nodes = []
        filtered_count = 0
        
        for node in nodes:
            node_text = node.text
            
            # 过滤条件：文本长度小于10个字符，或者全是空白字符
            if len(node_text.strip()) < 10:
                filtered_count += 1
                continue
                
            # 基本质量检查：确保文本包含有意义的内容（至少有一个句号或问号） todo 这种过滤方法需要谨慎一点
            if not any(char in node_text for char in ['.', '?', '。', '？', '!', '！']):
                filtered_count += 1
                continue
                
            filtered_nodes.append(node)
            
        if filtered_count > 0:
            logger.info(f"已过滤 {filtered_count} 个低质量节点，剩余 {len(filtered_nodes)} 个节点")
            
        # 如果过滤后没有节点，直接返回原始节点列表
        if not filtered_nodes:
            logger.info("过滤后没有剩余节点，将使用原始节点列表")
            filtered_nodes = nodes

        # 准备知识图谱处理数据
        kg_documents = []
        total_nodes = len(filtered_nodes)

        # 从节点转换为知识图谱文档格式
        for i, node in enumerate(filtered_nodes):
            if i % 50 == 0:  # 每处理50个节点打印一次进度
                logger.info(f"正在准备知识图谱文档 {i}/{total_nodes}")
                
            # 从节点中提取文本和元数据
            node_text = node.text
            node_metadata = node.metadata if hasattr(node, "metadata") else {}

            # 合并文档元数据和节点元数据
            if doc_metadata:
                combined_metadata = {**doc_metadata, **node_metadata}
            else:
                combined_metadata = node_metadata

            # 确保metadata中包含chunk_id
            node_id = getattr(node, "id_", f"{id(node)}")
            chunk_id = combined_metadata.get("chunk_id", node_id)
            combined_metadata["chunk_id"] = chunk_id
            
            # 添加处理时间戳，有助于追踪处理流程
            combined_metadata["kg_processed_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

            # 更新节点的元数据
            node.metadata = combined_metadata

            #加入集合名称，方便后续查询
            combined_metadata["collection_name"] = collection_name

            # 创建知识图谱文档
            kg_doc = {
                "id": combined_metadata.get("source", node.id_),
                "text": node_text,
                "metadata": combined_metadata
            }
            kg_documents.append(kg_doc)

        # 批处理知识图谱文档
        try:
            logger.info(f"开始批量处理知识图谱，共 {len(kg_documents)} 个文档")
            batch_size = 50  # 每批处理的文档数，可根据性能进行调整
            
            # 将文档分批处理以提高效率
            all_batch_results = []
            for i in range(0, len(kg_documents), batch_size):
                batch = kg_documents[i:i+batch_size]
                logger.info(f"处理批次 {i//batch_size + 1}/{(len(kg_documents) + batch_size - 1)//batch_size}，共 {len(batch)} 个文档")
                
                # 这里复用原有的batch_process_documents方法
                batch_result = await self.kg_manager.batch_process_documents(batch)
                all_batch_results.append(batch_result)
                
                logger.info(f"批次 {i//batch_size + 1} 结果: 成功={batch_result['success']}, "
                      f"失败={batch_result['failure']}, "
                      f"实体数={batch_result['entities_count']}, "
                      f"关系数={batch_result['relations_count']}")

            # 汇总所有批次的结果
            total_success = sum(result['success'] for result in all_batch_results)
            total_failure = sum(result['failure'] for result in all_batch_results)
            total_entities = sum(result['entities_count'] for result in all_batch_results)
            total_relations = sum(result['relations_count'] for result in all_batch_results)
            # 输出处理耗时
            kg_build_time = time.time() - start_time
            logger.info(f"知识图谱批量处理总结果: 成功={total_success}, 失败={total_failure}, "
                  f"实体数={total_entities}, 关系数={total_relations}",
                  f"耗时={kg_build_time:.2f}秒")

            # 如果成功处理了文档，索引实体到向量数据库
            if total_success > 0:
                logger.info("正在为节点索引实体和关系到向量数据库...")
                index_start_time = time.time()

                # 收集处理过的文档ID并去重
                doc_ids = list(set([doc.get("metadata")["file_id"] for doc in kg_documents]))

                # 批量索引实体、关系和上下文增强实体到向量数据库
                index_result = await self.batch_index_kg_documents(doc_ids)

                # 输出处理耗时
                index_time = time.time() - index_start_time
                logger.info(f"知识图谱向量索引处理完成: "
                      f"成功文档={index_result['successful_docs']}, "
                      f"失败文档={index_result['failed_docs']}, "
                      f"实体数={index_result['total_entities']}, "
                      f"关系数={index_result['total_relations']}, "
                      f"上下文增强实体数={index_result['total_contextual']}, "
                      f"耗时={index_time:.2f}秒")

        except Exception as e:
            logger.error(f"处理知识图谱节点时出错: {e}")
            import traceback
            traceback.print_exc()
            
            # 尝试部分恢复 - 即使出错也返回节点，确保主流程可以继续
            logger.warning("尝试恢复处理并继续执行主流程")

        total_time = time.time() - start_time
        logger.success(f"知识图谱处理完成，总耗时: {total_time:.2f} 秒")
        
        return filtered_nodes  # 返回过滤后的节点列表

    async def batch_index_kg_documents(self, doc_ids: List[str]) -> Dict[str, int]:
        """
        批量索引多个文档的实体和关系，使用上下文增强索引
        
        优化点:
        - 并行处理提高效率
        - 批量处理减少网络往返
        - 详细进度日志
        - 错误恢复机制

        参数:
            doc_ids: 文档ID列表（可能包含重复ID）

        返回:
            Dict[str, int]: 统计信息，包括处理的文档数、索引的实体数和关系数
        """
        if not self.use_kg:
            return {
                "total_docs": 0,
                "successful_docs": 0,
                "failed_docs": 0,
                "total_entities": 0,
                "total_relations": 0,
                "total_contextual": 0
            }

        # 记录开始时间
        start_time = time.time()
            
        # 对文档ID进行去重处理
        unique_doc_ids = list(set(doc_ids))
        logger.info(f"开始批量索引处理，共 {len(unique_doc_ids)} 个唯一文档ID（原始列表包含 {len(doc_ids)} 个ID）")

        # 统计变量初始化
        total_entities = 0
        total_relations = 0
        total_contextual = 0
        successful_docs = 0
        failed_docs = 0
        errors = []

        # 实现并行处理批次
        async def process_batch(batch_ids):
            batch_results = {
                "entities": 0,
                "relations": 0,
                "contextual": 0,
                "successful": 0,
                "failed": 0,
                "errors": []
            }
            
            for doc_id in batch_ids:
                try:
                    entities_count, relations_count, contextual_count = await self.entity_vectorizer.index_document_entities_with_contextual(doc_id)
                    
                    if entities_count > 0 or relations_count > 0 or contextual_count > 0:
                        batch_results["successful"] += 1
                        batch_results["entities"] += entities_count
                        batch_results["relations"] += relations_count
                        batch_results["contextual"] += contextual_count
                    else:
                        batch_results["failed"] += 1
                        batch_results["errors"].append(f"文档 {doc_id} 未找到实体或关系")
                except Exception as e:
                    batch_results["failed"] += 1
                    error_msg = f"索引文档 {doc_id} 时出错: {str(e)}"
                    batch_results["errors"].append(error_msg)
                    logger.error(error_msg)
                    
            return batch_results
            
        # 将文档ID分成批次并行处理
        batch_size = 10  # 每批处理10个文档
        batches = [unique_doc_ids[i:i+batch_size] for i in range(0, len(unique_doc_ids), batch_size)]
        
        logger.info(f"将 {len(unique_doc_ids)} 个文档分为 {len(batches)} 个批次并行处理")
        
        # 创建并行任务
        tasks = [process_batch(batch) for batch in batches]
        batch_results = await asyncio.gather(*tasks)
        
        # 汇总结果
        for result in batch_results:
            total_entities += result["entities"]
            total_relations += result["relations"]
            total_contextual += result["contextual"]
            successful_docs += result["successful"]
            failed_docs += result["failed"]
            errors.extend(result["errors"])
            
        # 记录处理时间
        processing_time = time.time() - start_time
            
        # 构建汇总报告
        result = {
            "total_docs": len(unique_doc_ids),
            "successful_docs": successful_docs,
            "failed_docs": failed_docs,
            "total_entities": total_entities,
            "total_relations": total_relations,
            "total_contextual": total_contextual,
            "processing_time": processing_time,
            "errors": errors[:10]  # 只保留前10个错误，避免日志过大
        }

        # 打印详细的进度信息
        # print(f"向量索引结果: 总文档={result['total_docs']}, 成功={result['successful_docs']}, "
        #       f"失败={result['failed_docs']}, 实体数={result['total_entities']}, "
        #       f"关系数={result['total_relations']}, 上下文增强实体数={result['total_contextual']}, "
        #       f"总耗时={processing_time:.2f}秒, 平均每文档耗时={(processing_time/len(unique_doc_ids)):.4f}秒")
              
        # 如果有错误，打印部分错误信息
        if errors:
            logger.warning(f"处理过程中发生 {len(errors)} 个错误，前几个错误:")
            for i, error in enumerate(errors[:5]):
                logger.error(f"  {i+1}. {error}")
                
        return result

    async def close_kg(self):
        """关闭知识图谱相关资源"""
        if self.use_kg:
            try:
                await self.kg_manager.close()
                await self.entity_vectorizer.close()
            except Exception as e:
                logger.error(f"关闭知识图谱资源时出错: {e}")

    @abstractmethod
    async def load_data(self):
        """加载数据"""

    async def create_index_local(self, persist_dir=DEFAULT_PERSIST_DIR) -> BaseIndex:
        """
        创建本地索引，该函数是数据嵌入的重点优化模块
        入库优化：数据清洗优化--》分块优化
        参考LLmaindex的分块策略：https://docs.llamaindex.ai/en/stable/api_reference/node_parsers/
        :param persist_dir: 本地持久化路径
        :return: BaseIndex
        """
        # 加载数据
        all_docs = await self.load_data()

        # 检查数据是否为空
        if not all_docs:
            raise ValueError("无法创建索引：加载的数据为空")

        # 创建一个句子分割器，使用更大的chunk_size以适应长文档和元数据
        # 获取文件扩展名以选择合适的分块策略
        fileName = all_docs[0].metadata.get('file_name', '') if all_docs else ''
        fileExtension = os.path.splitext(fileName.lower())[1] if fileName else ''
        
        if fileExtension in ['.pptx', '.ppt']:
            # PPT文档可能有较长的幻灯片内容，使用更大的chunk_size
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=2048,
                chunk_overlap=100
            )
            logger.info(f"使用PPT专用分块策略（块大小: 2048, 重叠: 100）")
        elif fileExtension == '.md':
            # Markdown文档专用分块策略
            node_parser = SentenceSplitter.from_defaults(
                separator="##",
                chunk_size=4096,
                chunk_overlap=50
            )
            logger.info(f"使用Markdown专用分块策略（分隔符: '##', 块大小: 4096, 重叠: 50）")
        elif fileExtension == '.pdf':
            # PDF文档专用分块策略
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=1536,
                chunk_overlap=200
            )
            logger.info(f"使用PDF专用分块策略（块大小: 1536, 重叠: 200）")
        else:
            # 默认分块策略，使用更大的chunk_size以避免元数据过长的问题
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=1536,
                chunk_overlap=100
            )
            logger.info(f"使用默认分块策略（块大小: 1536, 重叠: 100）")

        # 为文档添加基本元数据
        base_metadata = {}
        if all_docs and len(all_docs) > 0:
            base_metadata["file_name"] = all_docs[0].metadata.get("file_name", "unknown")

        # 从文档中获取节点
        nodes = node_parser.get_nodes_from_documents(all_docs, show_progress=True)

        # 为每个节点添加唯一的chunk_id
        for node in nodes:
            node_id = getattr(node, "id_", f"{id(node)}")
            chunk_id = node_id
            node.metadata["chunk_id"] = chunk_id
            # 确保每个节点都有基本元数据
            for key, value in base_metadata.items():
                if key not in node.metadata:
                    node.metadata[key] = value

        # 处理知识图谱节点（提取实体、关系，并进行向量化）
        if self.use_kg:
            # 获取文档元数据（用于处理节点）
            doc_metadata = {}
            if all_docs and len(all_docs) > 0:
                doc_metadata = all_docs[0].metadata

            # 处理分块后的节点
            nodes = await self.process_kg_nodes(nodes, doc_metadata)

        # 创建向量存储索引，如果提供了embed_model则使用它
        if self.embed_model:
            index = VectorStoreIndex(nodes, embed_model=self.embed_model, show_progress=True)
        else:
            index = VectorStoreIndex(nodes, show_progress=True)
        # 每个用户一个本地持久化目录
        index.storage_context.persist(persist_dir=persist_dir)
        # 返回创建的索引
        return index

    async def create_index(self, collection_name="default") -> BaseIndex:
        """
        创建向量索引，优化文档处理和分块策略
        
        参数:
            collection_name: Milvus集合名称
            
        返回:
            BaseIndex: 创建的索引
        """
        # 加载数据
        start_time = time.time()
        all_docs = await self.load_data()

        # 检查数据是否为空
        if not all_docs:
            raise ValueError("无法创建索引：加载的数据为空")

        logger.success(f"数据加载完成，共 {len(all_docs)} 个文档，耗时 {time.time() - start_time:.2f} 秒")

        # 获取文件扩展名以选择合适的分块策略
        fileName = all_docs[0].metadata.get('file_name', '')
        fileExtension = os.path.splitext(fileName.lower())[1] if fileName else ''
        
        # 根据文件类型选择不同的分块策略
        if fileExtension == '.md':
            # Markdown文档专用分块策略
            node_parser = SentenceSplitter.from_defaults(
                separator="##",
                chunk_size=4096,
                chunk_overlap=50  # 少量重叠以确保上下文连贯性
            )
            logger.info(f"使用Markdown专用分块策略（分隔符: '##', 块大小: 4096, 重叠: 50）")
        elif fileExtension == '.pdf':
            # PDF文档专用分块策略
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=1024,
                chunk_overlap=200  # PDF文档需要更多重叠以处理可能的格式问题
            )
            logger.info(f"使用PDF专用分块策略（块大小: 1024, 重叠: 200）")
        elif fileExtension == '.txt' or fileExtension == '.csv':
            # 文本文件使用较小的块大小
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=512,
                chunk_overlap=50
            )
            logger.info(f"使用文本文件分块策略（块大小: 512, 重叠: 50）")
        else:
            # 默认分块策略
            node_parser = SentenceSplitter.from_defaults(
                chunk_size=1024,
                chunk_overlap=100
            )
            logger.info(f"使用默认分块策略（块大小: 1024, 重叠: 100）")

        # 为文档添加基本元数据
        base_metadata = {}
        if all_docs and len(all_docs) > 0:
            base_metadata["file_name"] = all_docs[0].metadata.get("file_name", "unknown")
            base_metadata["file_type"] = fileExtension[1:] if fileExtension else "unknown"
            base_metadata["created_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

        # 从文档中获取节点，启用进度显示
        logger.info("开始文档分块...")
        nodes_time = time.time()
        nodes = node_parser.get_nodes_from_documents(all_docs, show_progress=True)
        logger.success(f"文档分块完成，共 {len(nodes)} 个节点，耗时 {time.time() - nodes_time:.2f} 秒")

        # 为每个节点添加唯一的chunk_id和元数据
        for idx, node in enumerate(nodes):
            node_id = getattr(node, "id_", f"{id(node)}")
            node.metadata["chunk_id"] = f"{node_id}"
            # node.metadata["chunk_index"] = idx  # 添加块索引，便于排序和追踪

            # 确保每个节点都有基本元数据
            for key, value in base_metadata.items():
                if key not in node.metadata:
                    node.metadata[key] = value
                    
            # 添加文本长度信息，有助于后续分析
            node.metadata["text_length"] = len(node.text)

        # 处理知识图谱节点（提取实体、关系，并进行向量化）
        if self.use_kg:
            # 获取文档元数据（用于处理节点）
            doc_metadata = {}
            if all_docs and len(all_docs) > 0:
                doc_metadata = all_docs[0].metadata

            # 处理分块后的节点
            logger.info("开始知识图谱处理...")
            kg_time = time.time()
            nodes = await self.process_kg_nodes(nodes, doc_metadata)
            logger.success(f"知识图谱处理完成，耗时 {time.time() - kg_time:.2f} 秒")

        # 创建向量存储索引
        logger.info(f"开始创建向量索引，集合名称: {collection_name}...")
        index_time = time.time()
        
        # 使用自定义嵌入功能和混合检索
        vector_store = MilvusVectorStore(
            uri=settings.configuration.milvus_uri,
            collection_name=collection_name, 
            dim=int(settings.configuration.embedding_model_dim_bge_large), 
            overwrite=False,
            # 开启混合检索
            sparse_embedding_function=CustomizedEmbeddingFunction(),
            enable_sparse=True,
        )
        
        # 创建存储上下文和索引，如果提供了embed_model则使用它
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        if self.embed_model:
            index = VectorStoreIndex(nodes, storage_context=storage_context, embed_model=self.embed_model, show_progress=True)
        else:
            index = VectorStoreIndex(nodes, storage_context=storage_context, show_progress=True)
        
        logger.success(f"向量索引创建完成，耗时 {time.time() - index_time:.2f} 秒")
        logger.success(f"总处理时间: {time.time() - start_time:.2f} 秒")

        return index

    def create_index_sync(self, collection_name="default") -> BaseIndex:
        """
        同步版本的create_index方法，用于在线程池中执行

        Args:
            collection_name: Milvus集合名称

        Returns:
            BaseIndex: 创建的索引
        """
        # 在线程中创建新的事件循环
        new_loop = None
        try:
            # 获取数据（在新的事件循环中执行异步load_data方法）
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)

            # 加载所有文档
            all_docs = new_loop.run_until_complete(self.load_data())

            # 检查数据是否为空
            if not all_docs:
                raise ValueError("无法创建索引：加载的数据为空")

            # 获取文件名和扩展名
            fileName = all_docs[0].metadata['file_name']
            fileExtension = os.path.splitext(fileName.lower())[1]

            # 创建分块器，根据文件类型选择不同的配置
            node_parser = SentenceSplitter.from_defaults()
            if fileExtension == '.md':
                # 创建一个句子分割器(知识库.md专用配置)
                node_parser = SentenceSplitter.from_defaults(separator="##",
                                                             chunk_size=4096,
                                                             chunk_overlap=0)

            # 为文档添加基本元数据
            base_metadata = {}
            if all_docs and len(all_docs) > 0:
                base_metadata["file_name"] = all_docs[0].metadata.get("file_name", "unknown")

            # 从文档中获取节点
            nodes = node_parser.get_nodes_from_documents(all_docs)

            # 为每个节点添加唯一的chunk_id
            for node in nodes:
                node_id = getattr(node, "id_", f"{id(node)}")
                node.metadata["chunk_id"] = node_id
                # 确保每个节点都有基本元数据
                for key, value in base_metadata.items():
                    if key not in node.metadata:
                        node.metadata[key] = value

            # 处理知识图谱节点（提取实体、关系，并进行向量化）
            if self.use_kg:
                # 获取文档元数据（用于处理节点）
                doc_metadata = {}
                if all_docs and len(all_docs) > 0:
                    doc_metadata = all_docs[0].metadata

                # 处理分块后的节点
                nodes = new_loop.run_until_complete(self.process_kg_nodes(nodes, doc_metadata, collection_name))

            # 创建向量存储索引
            vector_store = MilvusVectorStore(
                uri=settings.configuration.milvus_uri,
                collection_name=collection_name, dim=int(settings.configuration.embedding_model_dim_bge_large),
                overwrite=False,
                # 开启混合检索
                sparse_embedding_function=CustomizedEmbeddingFunction(),
                enable_sparse=True,
            )
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            # 此处可以传入想要使用的嵌入模型，如果不传入嵌入模型，则使用默认嵌入模型
            if self.embed_model:
                index = VectorStoreIndex(nodes, storage_context=storage_context, embed_model=self.embed_model, show_progress=True)
            else:
                index = VectorStoreIndex(nodes, storage_context=storage_context, show_progress=True)
            return index
        except Exception as e:
            # 记录详细错误信息并重新抛出，确保调用方可以处理
            import traceback
            error_msg = f"创建索引时发生错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)  # 控制台打印错误
            raise RuntimeError(error_msg) from e
        finally:
            # 确保事件循环被正确关闭
            if new_loop is not None:
                try:
                    # 取消所有挂起的任务
                    pending = asyncio.all_tasks(new_loop)
                    for task in pending:
                        task.cancel()

                    # 运行直到取消所有任务
                    if pending:
                        new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                    # 关闭事件循环
                    new_loop.close()
                except Exception as e:
                    logger.error(f"关闭事件循环时发生错误: {e}")

    @staticmethod
    async def load_index(collection_name="default", embed_model=None) -> BaseIndex:
        vector_store = MilvusVectorStore(
            uri=settings.configuration.milvus_uri,
            collection_name=collection_name, dim=int(settings.configuration.embedding_model_dim_bge_large), overwrite=False,
            # 开启混合检索
            sparse_embedding_function=CustomizedEmbeddingFunction(),
            enable_sparse=True,
        )
        if embed_model:
            return VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=embed_model)
        else:
            return VectorStoreIndex.from_vector_store(vector_store=vector_store)

    @staticmethod
    async def load_index_with_graph(collection_name="default", query:str="", embed_model=None) -> tuple:
        """
        基于知识图谱的局部模式检索，构建LightRAG风格的提示词

        参数:
            collection_name: 集合名称
            query: 用户查询文本
            embed_model: 嵌入模型实例，如果为None则使用全局默认模型

        返回:
            tuple: (LightRAG风格的提示词, 来源文本列表)
        """
        # 初始化局部模式检索器
        contextual_retriever = LocalModeRetriever(
            top_k=5,
            use_contextual=True,
            embed_model=embed_model
        )

        # 使用局部模式检索相关实体和文本
        contextual_results = await contextual_retriever.retrieve(query,collection_name)

        # 打印检索结果信息
        for i, entity in enumerate(contextual_results.get("entities", []), 1):
            logger.info(f"增强检索最终获得实体  {i}. {entity.get('name')} ({entity.get('type')})")
        prompt_start_time = time.time()
        # 获取检索结果中的实体
        entities = contextual_results.get("entities", [])
        contextual_entities = contextual_results.get("contextual_entities", [])

        # 如果没有找到相关实体，返回空提示
        if not entities:
            logger.warning("未找到相关实体")
            return "", [], []

        # 从实体中提取关联的chunk_id
        chunk_ids = set()
        GRAPH_FIELD_SEP = "|"  # 定义分隔符，与LightRAG保持一致

        # 1. 获取实体详细信息和关联的文本块IDs
        for entity in entities:
            # 直接从source_id获取文本块IDs（LightRAG方式）
            source_id = entity.get("source_id", "")
            if source_id:
                # 使用分隔符拆分多个chunk_id
                for chunk_id in source_id.split(GRAPH_FIELD_SEP):
                    if chunk_id:
                        chunk_ids.add(chunk_id)
        client = MilvusClient(uri=settings.configuration.milvus_uri)
        # 从节点存储中获取块内容
        context_chunks = []
        chunks_found = set()  # 跟踪已找到的块ID
        source_images = []  # 存储图像URL
        added_image_names = set()  # 跟踪已添加的图片名称
        
        # 首先尝试直接从chunk_ids获取
        for chunk_id in chunk_ids:
            try:
                # 尝试从向量存储获取节点
                filter_expr = f"chunk_id == '{chunk_id}'"
                response = client.query(
                    collection_name=collection_name,
                    filter=filter_expr,
                    output_fields=["type","image","source","text","chunk_id","file_name"],
                    limit=1000
                )
                if response and response[0] :
                    if response[0]["chunk_id"] not in chunks_found:
                        if(response[0]["type"]=="image"):
                            image_name = response[0]["file_name"]
                            if image_name not in added_image_names:
                                source_images.append({"url": response[0]["image"], "name": image_name, "content": response[0]["text"]})
                                added_image_names.add(image_name)
                        else:
                            text_content = response[0]["text"]
                            context_chunks.append(text_content)
                        chunks_found.add(chunk_id)
                else:
                    logger.warning(f"未找到节点内容: {chunk_id}")
            except Exception as e:
                logger.error(f"从向量存储获取块 '{chunk_id}' 时出错: {e}")

        # 5. 构建LightRAG风格的提示词
        context_text = ""
        
        # 添加实体信息部分
        if entities:
            context_text += "## 相关实体\n\n"
            for i, entity in enumerate(entities[:5], 1):
                entity_name = entity.get("name", "未知实体")
                entity_type = entity.get("type", "未知类型")
                context_text += f"{i}. {entity_name} (类型: {entity_type})\n"
                
                # 查找该实体的增强信息
                entity_id = entity.get("id")
                entity_attributes = {}
                entity_relations = {}
                entity_documents = {}
                for detail in contextual_entities:
                    if detail.get("id") == entity_id:
                        entity_attributes = detail.get("attributes", {})
                        entity_documents = detail.get("documents", {})
                        entity_relations = detail.get("relations", {})
                        break
                
                # 添加实体的属性信息  ['实体类型: 地点', '遵循规范1: 低压配电设计规范 (电气线路防火封堵)', '遵循规范2: 常用危险化学品贮存通则 (电气线路设计)', '遵循规范3: 爆炸危险环境电力装置设计规范 (爆炸危险区域划分及电力装置设计)']
                if entity_attributes:
                    context_text += f"   相关属性:\n"
                    for attr_idx, attr in enumerate(entity_attributes, 1):
                        context_text += f"     - {attr}\n"

                #['危险化学品中间仓库 --[遵循]--> 低压配电设计规范 (危险化学品中间仓库的电气线路防火封堵需遵循该规范)', '危险化学品中间仓库 --[遵循]--> 常用危险化学品贮存通则 (危险化学品中间仓库的电气线路设计需遵循该通则)', '危险化学品中间仓库 --[遵循]--> 爆炸危险环境电力装置设计规范 (危险化学品中间仓库的爆炸危险区域划分及电力装置设计需遵循该规范)']
                if entity_relations:
                    context_text += f"   关联关系:\n"
                    for rel_idx, relation in enumerate(entity_relations, 1):
                        context_text += f"     - {relation}\n"

                #['知识库片段_1747112799177.txt (text)']
                if entity_documents:
                    context_text += f"   相关文档:\n"
                    for doc_idx, document in enumerate(entity_documents, 1):
                        context_text += f"     - {document}\n"
                
            context_text += "\n"
                
        # 添加文本内容部分
        context_text += "## 相关文本内容\n\n"
        
        # 从关联的文本块添加内容
        for i, chunk_str in enumerate(context_chunks, 1):
            text_content = chunk_str
            if text_content:
                context_text += f"[文本块 {i}] {text_content}\n\n"
        
        # 6. 构建完整提示模板，增加指导性指令
        prompt_template = f"""你是一个专业的知识库助手，请基于以下上下文信息回答用户的问题。
遵循以下指导:
1. 仔细分析提供的实体和关系信息，把握知识之间的内在联系
2. 基于相关文本内容提供详实准确的回答
3. 如果上下文中没有足够的信息回答问题，请直接说明你不知道，不要编造答案
4. 优先使用相关度较高的文本块内容
5. 回答应该简洁清晰，结构合理

上下文信息:
{context_text}

用户问题: {query}

回答:
"""
        prompt_build_time = time.time()-prompt_start_time
        logger.success(f"已构建提示词，包含 {len(context_chunks)} 个文本块,耗时 {prompt_build_time:.2f}秒")
        
        # 收集来源文本信息
        source_texts = []
        
        # 收集所有实体信息并拼接
        entity_texts = []
        for entity in entities:
            entity_name = entity.get("name", "未知实体")
            entity_type = entity.get("type", "未知类型")
            entity_texts.append(f"{entity_name} (类型: {entity_type})")
        
        # 如果有实体信息，添加为一个文本块
        if entity_texts:
            source_texts.append({"text": "\n".join(entity_texts), "type": "entity"})
            
        # 添加文本块内容
        for i, chunk_str in enumerate(context_chunks,1):
            source_texts.append({"text": chunk_str, "type": "chunk", "index": i})
            
        return prompt_template, source_texts,source_images

    @staticmethod
    async def load_index_local(persist_dir=DEFAULT_PERSIST_DIR, embed_model=None) -> BaseIndex:
        storage_context = StorageContext.from_defaults(persist_dir=persist_dir)
        if embed_model:
            return load_index_from_storage(storage_context=storage_context, embed_model=embed_model)
        else:
            return load_index_from_storage(storage_context=storage_context)
