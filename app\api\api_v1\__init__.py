from fastapi import APIRouter

from app.api.api_v1.endpoints import text2sqltrain,text2sqlchat_sse, text2sqlsys, text2sqlanalytics, rag, knowledge_graph, knowledge_statistics, database_statistics, cache_management,assistant, file_conversion

api_router = APIRouter()
api_router.include_router(rag.router, prefix="/rag", tags=["rag"])
api_router.include_router(text2sqltrain.router, prefix="/text2sqltrain", tags=["text2sqltrain"])
api_router.include_router(text2sqlchat_sse.router, prefix="/text2sqlchatsse", tags=["text2sqlchatsse"])
api_router.include_router(text2sqlsys.router, prefix="/text2sqlsys", tags=["text2sqlsys"])
api_router.include_router(text2sqlanalytics.router, prefix="/text2sqlanalytics", tags=["text2sqlanalytics"])
api_router.include_router(knowledge_graph.router, prefix="/kg", tags=["knowledge-graph"])
api_router.include_router(knowledge_statistics.router, prefix="/knowledge-statistics", tags=["knowledge-statistics"])
api_router.include_router(database_statistics.router, prefix="/database-statistics", tags=["database-statistics"])
api_router.include_router(cache_management.router, prefix="/cache", tags=["cache-management"])
api_router.include_router(assistant.router, prefix="/assistant", tags=["ai-assistant"])
api_router.include_router(file_conversion.router, prefix="/file-conversion", tags=["file-conversion"])
